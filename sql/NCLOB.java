package oracle.sql;

import java.sql.SQLException;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.internal.OracleNClob;

/* loaded from: ojdbc8.jar:oracle/sql/NCLOB.class */
public class NCLOB extends CLOB implements OracleNClob {
    protected NCLOB() {
    }

    public NCLOB(OracleConnection conn) throws SQLException {
        this(conn, null);
    }

    public NCLOB(OracleConnection conn, byte[] lob_descriptor) throws SQLException {
        super(conn, lob_descriptor, (short) 2);
    }

    public NCLOB(CLOB clob) throws SQLException {
        this(clob.getPhysicalConnection(), clob.getBytes());
    }
}
