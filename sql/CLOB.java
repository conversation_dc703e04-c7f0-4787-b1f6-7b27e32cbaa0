package oracle.sql;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.util.logging.Level;
import oracle.jdbc.LargeObjectAccessMode;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleNClob;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleClob;
import oracle.jdbc.internal.OracleConcreteProxy;
import oracle.jdbc.internal.OracleLargeObject;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableClob;
import oracle.jdbc.replay.driver.TxnReplayableConnection;
import oracle.jdbc.replay.driver.TxnReplayableNClob;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/sql/CLOB.class */
public class CLOB extends DatumWithConnection implements OracleClob, OracleConcreteProxy {
    public static final int MAX_CHUNK_SIZE = 32768;
    public static final int DURATION_INVALID = -1;
    public static final int DURATION_SESSION = 10;
    public static final int DURATION_CALL = 12;
    public static final int OLD_WRONG_DURATION_SESSION = 1;
    public static final int OLD_WRONG_DURATION_CALL = 2;
    public static final int MODE_READONLY = 0;
    public static final int MODE_READWRITE = 1;
    protected oracle.jdbc.driver.OracleClob target;
    private OracleClob ojiOracleClob;

    protected CLOB() {
        this.target = null;
        this.ojiOracleClob = null;
        oracle.jdbc.driver.OracleClob c = new oracle.jdbc.driver.OracleClob();
        setTarget(null, c, null, (short) 1, false);
    }

    protected CLOB(oracle.jdbc.driver.OracleClob c) {
        this.target = null;
        this.ojiOracleClob = null;
        setTarget(null, c, null, (short) 1, false);
    }

    public oracle.jdbc.driver.OracleClob getTarget() {
        return this.target;
    }

    private void setTarget(OracleConnection conn, oracle.jdbc.driver.OracleClob c, byte[] lob_descriptor, short csform, boolean recordConstructor) {
        Class proxyClass = csform == 2 ? TxnReplayableNClob.class : TxnReplayableClob.class;
        this.ojiOracleClob = (OracleClob) ConcreteProxyUtil.getProxyObject(conn, c, proxyClass, this);
        this.target = c;
        setShareBytes(this.target.shareBytes());
        this.targetDatumWithConnection = this.target;
        this.targetDatum = this.target;
        if (this.ojiOracleClob != null) {
            try {
                Object proxyObj = ConcreteProxyUtil.checkAndGetACProxyConnection(conn);
                if (proxyObj != null && recordConstructor) {
                    ((TxnReplayableConnection) proxyObj).CLOBConstructorRecording(lob_descriptor, csform, this);
                }
            } catch (SQLException sqe) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, "oracle/sql/CLOB", "setTarget", sqe.getMessage(), (String) null, (Throwable) null);
            }
            this.ojiOracleDatumWithConnection = this.ojiOracleClob;
            return;
        }
        this.ojiOracleClob = this.target;
    }

    public void createAndSetShardingLobProxy(Class proxyClass, Object creator) {
        this.ojiOracleClob = (OracleClob) ConcreteProxyUtil.getProxyObject(this.ojiOracleClob, proxyClass, creator);
        this.ojiOracleDatumWithConnection = this.ojiOracleClob;
    }

    @Override // oracle.jdbc.internal.OracleConcreteProxy
    public TxnReplayableBase getConcreteProxy() {
        if (this.ojiOracleClob instanceof TxnReplayableBase) {
            return (TxnReplayableBase) this.ojiOracleClob;
        }
        return null;
    }

    public CLOB(OracleConnection conn) throws SQLException {
        this(conn, null);
    }

    public CLOB(OracleConnection conn, byte[] lob_descriptor, boolean fromObject) throws SQLException {
        this(conn, lob_descriptor);
        this.target.setFromobject(fromObject);
    }

    public static final short getFormOfUseFromLocator(byte[] lob_descriptor) {
        short formOfUse = -1;
        if (lob_descriptor != null && lob_descriptor.length > 5) {
            formOfUse = ((lob_descriptor[5] & 64) == 0 || (lob_descriptor[5] & Byte.MIN_VALUE) != 0) ? (short) 1 : (short) 2;
        }
        return formOfUse;
    }

    public CLOB(OracleConnection conn, byte[] lob_descriptor) throws SQLException {
        this.target = null;
        this.ojiOracleClob = null;
        short csform = 1;
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate(conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleClob c = (lob_descriptor != null ? getFormOfUseFromLocator(lob_descriptor) : csform) == 2 ? new OracleNClob(conn, lob_descriptor) : new oracle.jdbc.driver.OracleClob(conn, lob_descriptor);
        setTarget(conn, c, lob_descriptor, c.getCsform(), delegateConn != null);
    }

    public CLOB(OracleConnection conn, byte[] lob_descriptor, short csform) throws SQLException {
        this(conn, lob_descriptor);
        this.target.setCsform(csform);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public boolean isNCLOB() {
        return this.ojiOracleClob.isNCLOB();
    }

    @Override // java.sql.Clob
    public long length() throws SQLException {
        return this.ojiOracleClob.length();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final long lengthInternal() throws SQLException {
        return this.ojiOracleClob.lengthInternal();
    }

    @Override // java.sql.Clob
    public String getSubString(long pos, int length) throws SQLException {
        return this.ojiOracleClob.getSubString(pos, length);
    }

    @Override // java.sql.Clob
    public Reader getCharacterStream() throws SQLException {
        return this.ojiOracleClob.getCharacterStream();
    }

    @Override // java.sql.Clob
    public InputStream getAsciiStream() throws SQLException {
        return this.ojiOracleClob.getAsciiStream();
    }

    public InputStream getAsciiStream(boolean isInternal) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.getAsciiStream(isInternal);
    }

    @Override // java.sql.Clob
    public long position(String searchstr, long start) throws SQLException {
        return this.ojiOracleClob.position(searchstr, start);
    }

    @Override // java.sql.Clob
    public long position(Clob searchstr, long start) throws SQLException {
        return this.ojiOracleClob.position(searchstr, start);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int getChars(long pos, int length, char[] buffer) throws SQLException {
        return this.ojiOracleClob.getChars(pos, length, buffer);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public Writer getCharacterOutputStream() throws SQLException {
        return this.ojiOracleClob.getCharacterOutputStream();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public OutputStream getAsciiOutputStream() throws SQLException {
        return this.ojiOracleClob.getAsciiOutputStream();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public byte[] getLocator() {
        return this.ojiOracleClob.getLocator();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public void setLocator(byte[] locator) {
        this.ojiOracleClob.setLocator(locator);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int putChars(long pos, char[] chars) throws SQLException {
        return this.ojiOracleClob.putChars(pos, chars);
    }

    public int putChars(long pos, char[] chars, int length) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.putChars(pos, chars, length);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int putChars(long pos, char[] chars, int offset, int length) throws SQLException {
        return this.ojiOracleClob.putChars(pos, chars, offset, length);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int putString(long pos, String str) throws SQLException {
        return this.ojiOracleClob.putString(pos, str);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int getChunkSize() throws SQLException {
        return this.ojiOracleClob.getChunkSize();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public int getBufferSize() throws SQLException {
        return this.ojiOracleClob.getBufferSize();
    }

    public static CLOB empty_lob() throws SQLException {
        return getEmptyCLOB();
    }

    public static CLOB getEmptyCLOB() throws SQLException {
        byte[] locator = new byte[86];
        locator[1] = 84;
        locator[5] = 24;
        CLOB clob = new CLOB();
        clob.setShareBytes(locator);
        oracle.jdbc.driver.OracleClob target = clob.getTarget();
        target.setShareBytes(locator);
        return clob;
    }

    @Override // oracle.jdbc.OracleClob
    public boolean isEmptyLob() throws SQLException {
        return this.ojiOracleClob.isEmptyLob();
    }

    @Override // oracle.jdbc.OracleClob
    public boolean isSecureFile() throws SQLException {
        return this.ojiOracleClob.isSecureFile();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public OutputStream getAsciiOutputStream(long pos) throws SQLException {
        return this.ojiOracleClob.getAsciiOutputStream(pos);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public Writer getCharacterOutputStream(long pos) throws SQLException {
        return this.ojiOracleClob.getCharacterOutputStream(pos);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public InputStream getAsciiStream(long pos) throws SQLException {
        return this.ojiOracleClob.getAsciiStream(pos);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public Reader getCharacterStream(long pos) throws SQLException {
        return this.ojiOracleClob.getCharacterStream(pos);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public void trim(long newlen) throws SQLException {
        this.ojiOracleClob.trim(newlen);
    }

    public static CLOB createTemporary(Connection conn, boolean cache, int _duration) throws SQLException {
        return createTemporary(conn, cache, _duration, (short) 1);
    }

    public static CLOB createTemporary(Connection conn, boolean cache, int _duration, short form_of_use) throws SQLException {
        int duration = _duration;
        if (_duration == 1) {
            duration = 10;
        }
        if (_duration == 2) {
            duration = 12;
        }
        if (conn == null || (duration != 10 && duration != 12)) {
            throw ((SQLException) DatabaseError.createSqlException(68, "'conn' should not be null and 'duration' should either be equal to DURATION_SESSION or DURATION_CALL").fillInStackTrace());
        }
        oracle.jdbc.internal.OracleConnection physConn = ((OracleConnection) conn).physicalConnectionWithin();
        CLOB result = getDBAccess(physConn).createTemporaryClob(physConn, cache, duration, form_of_use);
        if (result != null) {
            oracle.jdbc.driver.OracleClob target = result.getTarget();
            short csform = getFormOfUseFromLocator(result.shareBytes());
            target.setCsform(csform);
        }
        return result;
    }

    public static void freeTemporary(CLOB temp_lob) throws SQLException {
        if (temp_lob == null) {
            return;
        }
        temp_lob.freeTemporary();
    }

    public static boolean isTemporary(CLOB lob) throws SQLException {
        if (lob == null) {
            return false;
        }
        return lob.isTemporary();
    }

    public static short getDuration(CLOB lob) throws SQLException {
        if (lob == null) {
            return (short) -1;
        }
        return lob.getDuration();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public void freeTemporary() throws SQLException {
        this.ojiOracleClob.freeTemporary();
    }

    @Override // oracle.jdbc.OracleClob, oracle.jdbc.internal.OracleLargeObject
    public boolean isTemporary() throws SQLException {
        return this.ojiOracleClob.isTemporary();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public short getDuration() throws SQLException {
        return this.ojiOracleClob.getDuration();
    }

    @Override // oracle.jdbc.OracleClob
    public void openLob(LargeObjectAccessMode mode) throws SQLException {
        this.ojiOracleClob.openLob(mode);
    }

    @Deprecated
    public void open(int mode) throws SQLException {
        openLob(mode);
    }

    public void openLob(int mode) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        this.target.openLob(mode);
    }

    @Override // oracle.jdbc.OracleClob
    public void closeLob() throws SQLException {
        this.ojiOracleClob.closeLob();
    }

    @Override // oracle.jdbc.OracleClob
    public boolean isOpenLob() throws SQLException {
        return this.ojiOracleClob.isOpenLob();
    }

    @Override // java.sql.Clob
    public int setString(long pos, String str) throws SQLException {
        return this.ojiOracleClob.setString(pos, str);
    }

    @Override // java.sql.Clob
    public int setString(long pos, String str, int offset, int len) throws SQLException {
        return this.ojiOracleClob.setString(pos, str, offset, len);
    }

    @Override // java.sql.Clob
    public OutputStream setAsciiStream(long pos) throws SQLException {
        return this.ojiOracleClob.setAsciiStream(pos);
    }

    @Override // java.sql.Clob
    public Writer setCharacterStream(long pos) throws SQLException {
        return this.ojiOracleClob.setCharacterStream(pos);
    }

    @Override // java.sql.Clob
    public void truncate(long len) throws SQLException {
        this.ojiOracleClob.truncate(len);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        this.ojiOracleClob.toJdbc();
        return this;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return this.ojiOracleClob.isConvertibleTo(jClass);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Reader characterStreamValue() throws SQLException {
        return this.ojiOracleClob.characterStreamValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream asciiStreamValue() throws SQLException {
        return this.ojiOracleClob.asciiStreamValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream binaryStreamValue() throws SQLException {
        return this.ojiOracleClob.binaryStreamValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() throws SQLException {
        return this.ojiOracleClob.stringValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return this.ojiOracleClob.makeJdbcArray(arraySize);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public ClobDBAccess getDBAccess() throws SQLException {
        return this.ojiOracleClob.getDBAccess();
    }

    public static ClobDBAccess getDBAccess(Connection conn) throws SQLException {
        Monitor.CloseableLock lock = ((OracleConnection) conn).physicalConnectionWithin().acquireCloseableLock();
        Throwable th = null;
        try {
            ClobDBAccess clobDBAccessCreateClobDBAccess = ((OracleConnection) conn).physicalConnectionWithin().createClobDBAccess();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return clobDBAccessCreateClobDBAccess;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.DatumWithConnection, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Connection getJavaSqlConnection() throws SQLException {
        return this.ojiOracleClob.getJavaSqlConnection();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final void setLength(long _cachedLengthOfClobInChars) {
        this.ojiOracleClob.setLength(_cachedLengthOfClobInChars);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final void setChunkSize(int _dbChunkSize) {
        this.ojiOracleClob.setChunkSize(_dbChunkSize);
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final void setPrefetchData(OracleLargeObject.PrefetchData<char[]> prefetchData) {
        this.ojiOracleClob.setPrefetchData(prefetchData);
    }

    public final void setPrefetchedData(char[] _prefetchData) {
        setPrefetchedData(_prefetchData, _prefetchData == null ? 0 : _prefetchData.length);
    }

    public final void setPrefetchedData(char[] _prefetchData, int _size) {
        setPrefetchData(_prefetchData == null ? null : OracleLargeObject.PrefetchData.wrapArray(_prefetchData, _size));
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final OracleLargeObject.PrefetchData<char[]> getPrefetchData() {
        return this.ojiOracleClob.getPrefetchData();
    }

    public final char[] getPrefetchedData() {
        OracleLargeObject.PrefetchData<char[]> prefetchData = this.ojiOracleClob.getPrefetchData();
        if (prefetchData == null) {
            return null;
        }
        return prefetchData.share();
    }

    public final int getPrefetchedDataSize() {
        OracleLargeObject.PrefetchData<char[]> prefetchData = this.ojiOracleClob.getPrefetchData();
        if (prefetchData == null) {
            return 0;
        }
        return prefetchData.length();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final void setActivePrefetch(boolean _activePrefetch) {
        this.ojiOracleClob.setActivePrefetch(_activePrefetch);
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final void clearCachedData() {
        this.ojiOracleClob.clearCachedData();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public final boolean isActivePrefetch() {
        return this.ojiOracleClob.isActivePrefetch();
    }

    @Override // oracle.jdbc.internal.OracleClob
    public boolean canReadBasicLobDataInLocator() throws SQLException {
        return this.ojiOracleClob.canReadBasicLobDataInLocator();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public void freeLOB() throws SQLException {
        free();
    }

    @Override // java.sql.Clob
    public void free() throws SQLException {
        this.ojiOracleClob.free();
    }

    @Override // java.sql.Clob
    public Reader getCharacterStream(long pos, long length) throws SQLException {
        return this.ojiOracleClob.getCharacterStream(pos, length);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public void setBytes(byte[] locator) {
        this.ojiOracleClob.setBytes(locator);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.ojiOracleClob.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.ojiOracleClob.getACProxy();
    }

    public OracleClob getInternal() {
        return this.ojiOracleClob;
    }

    @Override // oracle.jdbc.OracleClob
    public SQLXML toSQLXML() throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return XMLType.createXML(getPhysicalConnection(), this);
    }

    @Override // oracle.jdbc.OracleClob
    public SQLXML toSQLXML(String schemaURL) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return XMLType.createXML(getPhysicalConnection(), this, schemaURL, true, true);
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final boolean isFree() {
        return this.ojiOracleClob.isFree();
    }
}
