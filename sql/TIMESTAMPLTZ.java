package oracle.sql;

import java.sql.Connection;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.TimeZone;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/sql/TIMESTAMPLTZ.class */
public class TIMESTAMPLTZ extends Datum {
    private static Calendar dbtz;
    static final long serialVersionUID = 2045880772054757133L;
    static final Calendar CAL_GMT_US = Calendar.getInstance(TimeZone.getTimeZone("GMT"), Locale.US);
    private static int SIZE_TIMESTAMPLTZ = 11;
    private static int SIZE_TIMESTAMPLTZ_NOFRAC = 7;
    private static int SIZE_DATE = 7;
    private static int ONE_HOUR = 3600000;
    private static int ONE_MINUTE = 60000;
    private static int JAVA_YEAR = 1970;
    private static int JAVA_MONTH = 0;
    private static int JAVA_DATE = 1;
    private static int MINYEAR = -4712;
    private static int MAXYEAR = 9999;
    private static int SECOND_MILLISECOND = 1000;
    private static boolean cached = false;
    private static final Monitor DBTZ_INIT_MONITOR = Monitor.newInstance();

    public TIMESTAMPLTZ() {
        super(initTimestampltz());
    }

    public TIMESTAMPLTZ(byte[] timestampltz) {
        super(timestampltz);
    }

    public TIMESTAMPLTZ(Connection conn, Time time, Calendar dbtz2) throws SQLException {
        super(toBytes(conn, time, dbtz2));
    }

    public TIMESTAMPLTZ(Connection conn, Date date, Calendar dbtz2) throws SQLException {
        super(toBytes(conn, date, dbtz2));
    }

    public TIMESTAMPLTZ(Connection conn, Timestamp timestamp, Calendar dbtz2) throws SQLException {
        super(toBytes(conn, timestamp, dbtz2));
    }

    public TIMESTAMPLTZ(Connection conn, DATE date, Calendar dbtz2) throws SQLException {
        super(toBytes(conn, date, dbtz2));
    }

    public TIMESTAMPLTZ(Connection conn, String str, Calendar dbtz2) throws SQLException {
        super(toBytes(conn, str, dbtz2));
    }

    public TIMESTAMPLTZ(Connection conn, Calendar sess, Time time) throws SQLException {
        super(toBytes(conn, sess, time));
    }

    public TIMESTAMPLTZ(Connection conn, Calendar sess, Date date) throws SQLException {
        super(toBytes(conn, sess, date));
    }

    public TIMESTAMPLTZ(Connection conn, Calendar sess, Timestamp timestamp) throws SQLException {
        super(toBytes(conn, sess, timestamp));
    }

    public TIMESTAMPLTZ(Connection conn, Calendar sess, DATE date) throws SQLException {
        super(toBytes(conn, sess, date));
    }

    public TIMESTAMPLTZ(Connection conn, Calendar sess, String str) throws SQLException {
        super(toBytes(conn, sess, str));
    }

    public TIMESTAMPLTZ(Connection conn, Time time) throws SQLException {
        super(toBytes(conn, getSessCalendar(conn), time));
    }

    public TIMESTAMPLTZ(Connection conn, Date date) throws SQLException {
        super(toBytes(conn, getSessCalendar(conn), date));
    }

    public TIMESTAMPLTZ(Connection conn, Timestamp timestamp) throws SQLException {
        super(toBytes(conn, getSessCalendar(conn), timestamp));
    }

    public TIMESTAMPLTZ(Connection conn, DATE date) throws SQLException {
        super(toBytes(conn, getSessCalendar(conn), date));
    }

    public TIMESTAMPLTZ(Connection conn, String str) throws SQLException {
        super(toBytes(conn, getSessCalendar(conn), parseTimestampLtz(conn, str)));
    }

    public TIMESTAMPLTZ(Connection conn, OffsetDateTime odt) throws SQLException {
        super(toBytes(conn, odt));
    }

    public TIMESTAMPLTZ(Connection conn, OffsetTime ot) throws SQLException {
        super(toBytes(conn, ot));
    }

    public TIMESTAMPLTZ(Connection conn, ZonedDateTime zdt) throws SQLException {
        super(toBytes(conn, zdt));
    }

    public TIMESTAMPLTZ(Connection conn, LocalDateTime ldt) throws SQLException {
        super(toBytes(conn, ldt));
    }

    public static Date toDate(Connection conn, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        Calendar cal = toCalendar(conn, Calendar.getInstance(), timestampltz, dbtz2);
        long millis = cal.getTime().getTime();
        return new Date(millis);
    }

    public static Time toTime(Connection conn, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        Calendar cal = toCalendar(conn, Calendar.getInstance(), timestampltz, dbtz2);
        cal.set(1, 1970);
        cal.set(2, 0);
        cal.set(5, 1);
        return new Time(cal.getTime().getTime());
    }

    public static Timestamp toTimestamp(Connection conn, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        return toTimestamp(conn, Calendar.getInstance(), timestampltz, dbtz2);
    }

    public static DATE toDATE(Connection conn, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        return new DATE(toTimestamp(conn, getSessCalendar(conn), timestampltz, null));
    }

    public Timestamp timestampValue(Connection conn, Calendar dbtz2) throws SQLException {
        return toTimestamp(conn, getBytes(), dbtz2);
    }

    public Timestamp timestampValue(Connection conn) throws SQLException {
        return toTimestamp(conn, getBytes());
    }

    public static String toString(Connection conn, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        Calendar cal = toCalendar(conn, null, timestampltz, dbtz2);
        int year = cal.get(1);
        if (cal.get(0) == 0) {
            year = -(year - 1);
        }
        int month = cal.get(2) + 1;
        int date = cal.get(5);
        int hour = cal.get(11);
        int minute = cal.get(12);
        int seconds = cal.get(13);
        int nanos = -1;
        if (timestampltz.length == SIZE_TIMESTAMPLTZ) {
            nanos = TIMESTAMP.getNanos(timestampltz, 7);
        }
        return TIMESTAMPTZ.toString(year, month, date, hour, minute, seconds, nanos, cal.getTimeZone().getID());
    }

    public byte[] toBytes() {
        return getBytes();
    }

    public static byte[] toBytes(Connection conn, Time time, Calendar dbtz2) throws SQLException {
        int defaultYear;
        if (time == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        if (((OracleConnection) conn.unwrap(OracleConnection.class)).getUse1900AsYearForTime()) {
            defaultYear = 1900;
        } else {
            defaultYear = 1970;
        }
        cal.set(1, defaultYear);
        cal.set(2, 0);
        cal.set(5, 1);
        byte[] result = toBytes(conn, cal, dbtz2, 0);
        return result;
    }

    public static byte[] toBytes(Connection conn, Date date, Calendar dbtz2) throws SQLException {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if (OracleDriver.getSystemPropertyDateZeroTime()) {
            cal.set(11, 0);
            cal.set(12, 0);
            cal.set(13, 0);
        }
        byte[] result = toBytes(conn, cal, dbtz2, 0);
        return result;
    }

    public static byte[] toBytes(Connection conn, Timestamp timestamp, Calendar dbtz2) throws SQLException {
        if (timestamp == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(timestamp);
        int nanos = timestamp.getNanos();
        byte[] result = toBytes(conn, cal, dbtz2, nanos);
        return result;
    }

    public static byte[] toBytes(Connection conn, DATE date, Calendar dbtz2) throws SQLException {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(DATE.toDate(date.toBytes()));
        byte[] result = toBytes(conn, cal, dbtz2, 0);
        return result;
    }

    public static byte[] toBytes(Connection conn, String str, Calendar dbtz2) throws SQLException {
        return toBytes(conn, parseTimestampLtz(conn, str), dbtz2);
    }

    public static Date toDate(Connection conn, byte[] timestampltz) throws SQLException {
        Calendar cal = toCalendar(conn, null, timestampltz, null);
        long millis = cal.getTime().getTime();
        return new Date(millis);
    }

    public LocalDateTime toLocalDateTime(Connection conn) throws SQLException {
        return toLocalDateTime(conn, getBytes());
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.time.LocalDateTime] */
    public static LocalDateTime toLocalDateTime(Connection conn, byte[] timestampltz) throws SQLException {
        return toZonedDateTime(conn, timestampltz).toLocalDateTime();
    }

    public static TIMESTAMPLTZ of(Connection conn, OffsetDateTime odt) throws SQLException {
        return new TIMESTAMPLTZ(conn, odt);
    }

    public static TIMESTAMPLTZ of(Connection conn, OffsetTime ot) throws SQLException {
        return new TIMESTAMPLTZ(conn, ot);
    }

    public static TIMESTAMPLTZ of(Connection conn, ZonedDateTime zdt) throws SQLException {
        return new TIMESTAMPLTZ(conn, zdt);
    }

    public static TIMESTAMPLTZ of(Connection conn, LocalDateTime ldt) throws SQLException {
        return new TIMESTAMPLTZ(conn, ldt);
    }

    public static OffsetDateTime toOffsetDateTime(Connection conn, byte[] timestampltz) throws SQLException {
        return toZonedDateTime(conn, timestampltz).toOffsetDateTime();
    }

    public static OffsetTime toOffsetTime(Connection conn, byte[] timestampltz) throws SQLException {
        return toOffsetDateTime(conn, timestampltz).toOffsetTime();
    }

    /* JADX WARN: Type inference failed for: r0v23, types: [java.time.ZonedDateTime] */
    public static ZonedDateTime toZonedDateTime(Connection conn, byte[] timestampltz) throws SQLException {
        int ltz_length = timestampltz.length;
        int[] result = new int[ltz_length];
        for (int i = 0; i < ltz_length; i++) {
            result[i] = timestampltz[i] & 255;
        }
        int nanos = 0;
        if (ltz_length == SIZE_TIMESTAMPLTZ) {
            nanos = TIMESTAMP.getNanos(timestampltz, 7);
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        OracleConnection oconn = (OracleConnection) conn.unwrap(OracleConnection.class);
        ZoneId db_tz = oconn.getDatabaseZoneId();
        ZonedDateTime db_zdt = ZonedDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1, nanos, db_tz);
        ZoneId sess_tz = oconn.getSessionZoneId();
        if (sess_tz == null) {
            sess_tz = ZoneId.systemDefault();
        }
        return db_zdt.withZoneSameInstant(sess_tz);
    }

    public static Time toTime(Connection conn, byte[] timestampltz) throws SQLException {
        Calendar cal = toCalendar(conn, null, timestampltz, null);
        cal.set(1, 1970);
        cal.set(2, 0);
        cal.set(5, 1);
        return new Time(cal.getTime().getTime());
    }

    public static Timestamp toTimestamp(Connection conn, byte[] timestampltz) throws SQLException {
        return toTimestamp(conn, null, timestampltz, null);
    }

    public static DATE toDATE(Connection conn, byte[] timestampltz) throws SQLException {
        Calendar cal = toCalendar(conn, null, timestampltz, null);
        long millis = cal.getTime().getTime();
        return new DATE(new Timestamp(millis));
    }

    public static TIMESTAMP toTIMESTAMP(Connection conn, byte[] timestampltz) throws SQLException {
        return new TIMESTAMP(toTimestamp(conn, getSessCalendar(conn), timestampltz, null));
    }

    public static TIMESTAMPTZ toTIMESTAMPTZ(Connection conn, byte[] timestampltz) throws SQLException {
        return new TIMESTAMPTZ(conn, toTimestamp(conn, getSessCalendar(conn), timestampltz, null), getSessCalendar(conn));
    }

    public static String toString(Connection conn, byte[] timestampltz) throws SQLException {
        return toString(conn, timestampltz, null);
    }

    public static byte[] toBytes(Connection conn, Calendar cal, Time time) throws SQLException {
        Calendar cal1;
        int defaultYear;
        if (time == null) {
            return null;
        }
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (cal == null) {
                    cal1 = getSessCalendar(conn);
                } else {
                    cal1 = (Calendar) cal.clone();
                }
                cal1.setTime(time);
                if (((OracleConnection) conn.unwrap(OracleConnection.class)).getUse1900AsYearForTime()) {
                    defaultYear = 1900;
                } else {
                    defaultYear = 1970;
                }
                cal1.set(1, defaultYear);
                cal1.set(2, 0);
                cal1.set(5, 1);
                initDbTimeZone(conn);
                Calendar dbclone = (Calendar) dbtz.clone();
                byte[] result = toBytes(conn, cal1, dbclone, 0);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return result;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static byte[] toBytes(Connection conn, Calendar cal, Date date) throws SQLException {
        Calendar cal1;
        if (date == null) {
            return null;
        }
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (cal == null) {
                    cal1 = getSessCalendar(conn);
                } else {
                    cal1 = (Calendar) cal.clone();
                }
                cal1.setTime(date);
                if (OracleDriver.getSystemPropertyDateZeroTime()) {
                    cal1.set(11, 0);
                    cal1.set(12, 0);
                    cal1.set(13, 0);
                }
                initDbTimeZone(conn);
                Calendar dbclone = (Calendar) dbtz.clone();
                byte[] result = toBytes(conn, cal1, dbclone, 0);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return result;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static byte[] toBytes(Connection conn, Calendar cal, Timestamp timestamp) throws SQLException {
        Calendar cal1;
        if (timestamp == null) {
            return null;
        }
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            if (cal == null) {
                cal1 = getSessCalendar(conn);
            } else {
                cal1 = (Calendar) cal.clone();
            }
            cal1.setTime(timestamp);
            int nanos = timestamp.getNanos();
            initDbTimeZone(conn);
            Calendar dbclone = (Calendar) dbtz.clone();
            byte[] result = toBytes(conn, cal1, dbclone, nanos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return result;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static byte[] toBytes(Connection conn, Calendar cal, DATE date) throws SQLException {
        Calendar cal1;
        if (date == null) {
            return null;
        }
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (cal == null) {
                    cal1 = getSessCalendar(conn);
                } else {
                    cal1 = (Calendar) cal.clone();
                }
                cal1.setTime(DATE.toDate(date.toBytes()));
                initDbTimeZone(conn);
                Calendar dbclone = (Calendar) dbtz.clone();
                byte[] result = toBytes(conn, cal1, dbclone, 0);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return result;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static byte[] toBytes(Connection conn, Calendar sess, String str) throws SQLException {
        return toBytes(conn, sess, parseTimestampLtz(conn, str));
    }

    public static byte[] toBytes(Connection conn, OffsetTime ot) throws SQLException {
        if (ot == null) {
            return null;
        }
        return toBytes(conn, ot.atDate(LocalDate.of(1970, 1, 1)));
    }

    /* JADX WARN: Type inference failed for: r0v7, types: [java.time.ZonedDateTime] */
    /* JADX WARN: Type inference failed for: r0v8, types: [java.time.LocalDateTime] */
    public static byte[] toBytes(Connection conn, ZonedDateTime zdt) throws SQLException {
        byte[] result;
        if (zdt == null) {
            return null;
        }
        OracleConnection oconn = (OracleConnection) conn.unwrap(OracleConnection.class);
        ZoneId db_tz = oconn.getDatabaseZoneId();
        ?? localDateTime = zdt.withZoneSameInstant(db_tz).toLocalDateTime();
        int year = localDateTime.getYear();
        int month = localDateTime.getMonthValue();
        int date = localDateTime.getDayOfMonth();
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        int second = localDateTime.getSecond();
        int nanos = localDateTime.getNano();
        if (nanos == 0) {
            result = new byte[SIZE_TIMESTAMPLTZ_NOFRAC];
        } else {
            result = new byte[SIZE_TIMESTAMPLTZ];
        }
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) month;
        result[3] = (byte) date;
        result[4] = (byte) (hour + 1);
        result[5] = (byte) (minute + 1);
        result[6] = (byte) (second + 1);
        if (nanos != 0) {
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        }
        return result;
    }

    public static byte[] toBytes(Connection conn, OffsetDateTime odt) throws SQLException {
        if (odt == null) {
            return null;
        }
        return toBytes(conn, odt.toZonedDateTime());
    }

    public static byte[] toBytes(Connection conn, LocalDateTime ldt) throws SQLException {
        if (ldt == null) {
            return null;
        }
        ZoneId sess_tz = ((OracleConnection) conn.unwrap(OracleConnection.class)).getSessionZoneId();
        if (sess_tz == null) {
            sess_tz = ZoneId.systemDefault();
        }
        return toBytes(conn, ZonedDateTime.of(ldt, sess_tz));
    }

    @Override // oracle.sql.Datum
    public String stringValue(Connection conn) throws SQLException {
        return toString(conn, getBytes());
    }

    public String stringValue(Connection conn, Calendar cal) throws SQLException {
        return toString(conn, getBytes(), cal);
    }

    public Date dateValue(Connection conn, Calendar cal) throws SQLException {
        return toDate(conn, getBytes(), cal);
    }

    public Date dateValue(Connection conn) throws SQLException {
        return toDate(conn, getBytes());
    }

    public LocalDateTime localDateTimeValue(Connection conn) throws SQLException {
        return toLocalDateTime(conn, getBytes());
    }

    public OffsetDateTime offsetDateTimeValue(Connection conn) throws SQLException {
        return toOffsetDateTime(conn, getBytes());
    }

    public OffsetTime offsetTimeValue(Connection conn) throws SQLException {
        return toOffsetTime(conn, getBytes());
    }

    public ZonedDateTime zonedDateTimeValue(Connection conn) throws SQLException {
        return toZonedDateTime(conn, getBytes());
    }

    public Time timeValue(Connection conn) throws SQLException {
        return toTime(conn, getBytes());
    }

    public Time timeValue(Connection conn, Calendar cal) throws SQLException {
        return toTime(conn, getBytes(), cal);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return this;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        Timestamp[] ts = new Timestamp[arraySize];
        return ts;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> cls) {
        if (cls.getName().compareTo("java.sql.Date") == 0 || cls.getName().compareTo("java.sql.Time") == 0 || cls.getName().compareTo("java.sql.Timestamp") == 0 || cls.getName().compareTo("java.time.LocalDateTime") == 0 || cls.getName().compareTo("java.time.OffsetDateTime") == 0 || cls.getName().compareTo("java.time.ZonedDateTime") == 0 || cls.getName().compareTo("java.lang.String") == 0) {
            return true;
        }
        return false;
    }

    public static void TimeZoneAdjust(Connection conn, Calendar cal1, Calendar cal2) throws SQLException {
        cal2.setTimeInMillis(cal1.getTimeInMillis());
    }

    public static long TimeZoneAdjustUTC(Connection conn, Calendar cal1) throws SQLException {
        return cal1.getTimeInMillis();
    }

    private static byte[] initTimestampltz() {
        byte[] tmp = new byte[SIZE_TIMESTAMPLTZ];
        tmp[0] = 119;
        tmp[1] = -86;
        tmp[2] = 1;
        tmp[3] = 1;
        tmp[4] = 1;
        tmp[5] = 1;
        tmp[6] = 1;
        return tmp;
    }

    private static byte[] toBytes(Connection conn, Calendar cal, Calendar dbclone, int nanos) throws SQLException {
        byte[] result;
        if (nanos == 0) {
            result = new byte[SIZE_TIMESTAMPLTZ_NOFRAC];
        } else {
            result = new byte[SIZE_TIMESTAMPLTZ];
        }
        TimeZoneAdjust(conn, cal, dbclone);
        int year = TIMESTAMP.getOracleYear(dbclone);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (dbclone.get(2) + 1);
        result[3] = (byte) dbclone.get(5);
        result[4] = (byte) (dbclone.get(11) + 1);
        result[5] = (byte) (dbclone.get(12) + 1);
        result[6] = (byte) (dbclone.get(13) + 1);
        if (nanos != 0) {
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        }
        return result;
    }

    private static Timestamp toTimestamp(Connection conn, Calendar incal, byte[] timestampltz, Calendar dbtz2) throws SQLException {
        Calendar cal = toCalendar(conn, incal, timestampltz, dbtz2);
        long millis = cal.getTime().getTime();
        Timestamp ts = new Timestamp(millis);
        int nanos = 0;
        if (timestampltz.length == SIZE_TIMESTAMPLTZ) {
            nanos = TIMESTAMP.getNanos(timestampltz, 7);
        }
        ts.setNanos(nanos);
        return ts;
    }

    private static final Calendar toCalendar(Connection conn, Calendar toCal, byte[] timestampltz, Calendar dbCal) throws SQLException {
        int[] result;
        Calendar cal2;
        int arrlength = timestampltz.length;
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (arrlength == SIZE_TIMESTAMPLTZ) {
                    result = new int[SIZE_TIMESTAMPLTZ];
                } else {
                    result = new int[SIZE_TIMESTAMPLTZ_NOFRAC];
                }
                for (int i = 0; i < timestampltz.length; i++) {
                    result[i] = timestampltz[i] & 255;
                }
                int year = TIMESTAMP.getJavaYear(result[0], result[1]);
                if (dbCal == null) {
                    initDbTimeZone(conn);
                    dbCal = (Calendar) dbtz.clone();
                }
                dbCal.set(1, year);
                dbCal.set(2, result[2] - 1);
                dbCal.set(5, result[3]);
                dbCal.set(11, result[4] - 1);
                dbCal.set(12, result[5] - 1);
                dbCal.set(13, result[6] - 1);
                int tsmillis = 0;
                if (arrlength == SIZE_TIMESTAMPLTZ) {
                    tsmillis = TIMESTAMP.getNanos(timestampltz, 7) / 1000000;
                }
                dbCal.set(14, tsmillis);
                if (toCal == null) {
                    cal2 = getSessCalendar(conn);
                } else {
                    cal2 = (Calendar) toCal.clone();
                }
                TimeZoneAdjust(conn, dbCal, cal2);
                Calendar calendar = cal2;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return calendar;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static byte getZoneOffset(Connection conn, Calendar cal, OffsetDST tempVar) throws SQLException, NullPointerException {
        byte olap = 0;
        String timeZone = cal.getTimeZone().getID();
        if (timeZone == "Custom" || (timeZone.startsWith("GMT") && timeZone.length() > 3)) {
            tempVar.setOFFSET(cal.getTimeZone().getRawOffset());
        } else {
            int regionID = ZONEIDMAP.getID(timeZone);
            if (!ZONEIDMAP.isValidID(regionID)) {
                throw ((SQLException) DatabaseError.createSqlException(199).fillInStackTrace());
            }
            TIMEZONETAB tzTab = getTIMEZONETAB(conn);
            if (tzTab.checkID(regionID)) {
                tzTab.updateTable(conn, regionID);
            }
            olap = tzTab.getLocalOffset(cal, regionID, tempVar);
        }
        return olap;
    }

    private static Calendar getDbTzCalendar(String dbTzStr) {
        String offset_str;
        char sign = dbTzStr.charAt(0);
        if (sign == '+' || sign == '-') {
            offset_str = "GMT" + dbTzStr;
        } else {
            offset_str = dbTzStr;
        }
        TimeZone tz = TimeZone.getTimeZone(offset_str);
        return new GregorianCalendar(tz);
    }

    private static Timestamp parseTimestampLtz(Connection conn, String ltzStr) throws SQLException, NumberFormatException {
        String sTz;
        int second;
        Calendar cal;
        if (ltzStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The value is null.").fillInStackTrace());
        }
        try {
            Timestamp ts1 = Timestamp.valueOf(ltzStr);
            return ts1;
        } catch (IllegalArgumentException e) {
            String sTemp = ltzStr.trim();
            int spacePos = sTemp.indexOf(32);
            if (spacePos == -1) {
                throw ((SQLException) DatabaseError.createSqlException(68, "There is a space expected after date. (yyyy-MM-dd HH:mm:ss[.fffffffff])").fillInStackTrace());
            }
            String sDate = sTemp.substring(0, spacePos).trim();
            String sTime = sTemp.substring(spacePos + 1).trim();
            if ((sDate == null) | (sTime == null)) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The value of date/time is null.").fillInStackTrace());
            }
            boolean nYear = false;
            if (sDate.charAt(0) == '-') {
                nYear = true;
                if (sDate.length() > 1) {
                    sDate = sDate.substring(1);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(68, "The date value is invalid.").fillInStackTrace());
                }
            }
            int posH1 = sDate.indexOf(45);
            int posH2 = sDate.indexOf(45, posH1 + 1);
            if (posH1 < 1 || posH2 < 1 || posH2 == sDate.length()) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The date should have two hyphens and the expected format is yyyy-mm-dd.").fillInStackTrace());
            }
            String sYear = sDate.substring(0, posH1);
            String sMonth = sDate.substring(posH1 + 1, posH2);
            String sDay = sDate.substring(posH2 + 1);
            if (sYear.length() != 4 || sMonth.length() != 2 || sDay.length() != 2) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The value of year/month/date is wrong. The format must be yyyy-mm-dd").fillInStackTrace());
            }
            int year = Integer.parseInt(sYear);
            int month = Integer.parseInt(sMonth) - 1;
            int day = Integer.parseInt(sDay);
            if (nYear) {
                year = -year;
            }
            int posC1 = sTime.indexOf(58);
            int posC2 = sTime.indexOf(58, posC1 + 1);
            int spacePos2 = sTime.indexOf(32);
            if (spacePos2 != -1) {
                sTz = sTime.substring(spacePos2 + 1).trim();
                sTime = sTime.substring(0, spacePos2).trim();
            } else {
                sTz = null;
            }
            if (posC1 < 1 || posC2 < 1 || posC2 == sTime.length()) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The format of time is expected to be hh:mm:ss[.fffffffff]").fillInStackTrace());
            }
            String sHour = sTime.substring(0, posC1);
            String sMinute = sTime.substring(posC1 + 1, posC2);
            if (sHour.length() != 2 || sMinute.length() != 2) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The value of hour/minute is wrong. The format must be hh:mm:ss").fillInStackTrace());
            }
            int hour = Integer.parseInt(sHour);
            int minute = Integer.parseInt(sMinute);
            int nanos = 0;
            int posDot = sTime.indexOf(46, posC2 + 1);
            if (posDot == -1) {
                second = Integer.parseInt(sTime.substring(posC2 + 1));
            } else if (posDot > 0 && posDot < sTime.length() - 1) {
                second = Integer.parseInt(sTime.substring(posC2 + 1, posDot));
                String sNanos = sTime.substring(posDot + 1);
                if (sNanos.length() > "000000000".length() || !Character.isDigit(sNanos.charAt(0))) {
                    throw ((SQLException) DatabaseError.createSqlException(68, "The number of digits of nanoseconds is more than 9 or it has non-numeric character.").fillInStackTrace());
                }
                nanos = Integer.parseInt(sNanos + "000000000".substring(0, "000000000".length() - sNanos.length()));
            } else {
                throw ((SQLException) DatabaseError.createSqlException(68, "The number of digits of nanoseconds is zero.").fillInStackTrace());
            }
            Calendar cal2 = null;
            if (sTz != null) {
                char cTz = sTz.charAt(0);
                if (cTz == '-') {
                    sTz = "GMT" + sTz;
                } else if (Character.isDigit(cTz)) {
                    sTz = "GMT+" + sTz;
                }
                cal2 = Calendar.getInstance(TimeZone.getTimeZone(sTz));
            }
            if (cal2 == null) {
                cal = getSessCalendar(conn);
            } else {
                cal = cal2;
            }
            cal.set(1, year);
            cal.set(2, month);
            cal.set(5, day);
            cal.set(11, hour);
            cal.set(12, minute);
            cal.set(13, second);
            cal.set(14, 0);
            Timestamp result = new Timestamp(cal.getTime().getTime());
            result.setNanos(nanos);
            return result;
        }
    }

    static Calendar getSessCalendar(Connection conn) {
        Calendar sessTzCal;
        String locTimeZone = ((oracle.jdbc.OracleConnection) conn).getSessionTimeZone();
        if (locTimeZone == null) {
            sessTzCal = Calendar.getInstance();
        } else {
            TimeZone zone = TimeZone.getTimeZone(locTimeZone);
            sessTzCal = Calendar.getInstance(zone);
        }
        return sessTzCal;
    }

    private static void initDbTimeZone(Connection conn) throws SQLException {
        if (!cached) {
            Monitor.CloseableLock lock = DBTZ_INIT_MONITOR.acquireCloseableLock();
            Throwable th = null;
            try {
                if (!cached) {
                    OracleConnection oconn = (OracleConnection) conn.unwrap(OracleConnection.class);
                    String db_tz_str = oconn.getDatabaseTimeZone();
                    dbtz = getDbTzCalendar(db_tz_str);
                    cached = true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
    }

    static TIMEZONETAB getTIMEZONETAB(Connection conn) throws SQLException {
        OracleConnection oconn = (OracleConnection) conn.unwrap(OracleConnection.class);
        return oconn.getTIMEZONETAB();
    }
}
