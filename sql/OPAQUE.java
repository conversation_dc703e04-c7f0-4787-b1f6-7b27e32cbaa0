package oracle.sql;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.OracleTypeMetaData;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleOpaque;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/sql/OPAQUE.class */
public class OPAQUE extends DatumWithConnection implements OracleOpaque {
    OpaqueDescriptor descriptor;
    byte[] value;
    long imageOffset;
    long imageLength;
    private static final String CLASS_NAME = OPAQUE.class.getName();
    Object acProxy;

    public OPAQUE(OpaqueDescriptor type, Connection conn, Object value) throws SQLException {
        if (type != null) {
            this.descriptor = type;
            if (conn != null) {
                setPhysicalConnectionOf(conn);
            }
            if (value instanceof ANYDATA) {
                ANYDATA anydata = (ANYDATA) value;
                byte[] image = new byte[anydata.getImageSize()];
                anydata.pickle(image, 0);
                this.value = image;
                return;
            }
            if (value instanceof byte[]) {
                this.value = (byte[]) value;
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59).fillInStackTrace());
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 61, "OPAQUE").fillInStackTrace());
    }

    public OPAQUE(OpaqueDescriptor type, byte[] bytes, Connection conn) throws SQLException {
        super(bytes);
        setPhysicalConnectionOf(conn);
        this.descriptor = type;
        this.value = null;
    }

    @Override // oracle.jdbc.OracleOpaque
    public String getSQLTypeName() throws SQLException {
        return this.descriptor.getName();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() throws SQLException, NoSuchMethodException, SecurityException {
        String ret = "OPAQUE";
        try {
            String retobj = null;
            Object obj = toJdbc();
            Class cl = obj.getClass();
            if (!cl.equals(getClass())) {
                try {
                    Method meth = cl.getMethod("getStringVal", new Class[0]);
                    if (meth.getDeclaringClass().isAssignableFrom(cl)) {
                        retobj = (String) meth.invoke(obj, new Object[0]);
                    }
                } catch (Exception e) {
                }
                if (retobj == null) {
                    try {
                        Method meth2 = cl.getMethod("stringValue", new Class[0]);
                        if (meth2.getDeclaringClass().isAssignableFrom(cl)) {
                            retobj = (String) meth2.invoke(obj, new Object[0]);
                        }
                    } catch (Exception e2) {
                    }
                }
                if (retobj != null) {
                    ret = ret + "(" + retobj + ")";
                }
            }
        } catch (Exception e3) {
        }
        return ret;
    }

    @Override // oracle.jdbc.OracleOpaque
    public OracleTypeMetaData getOracleMetaData() throws SQLException {
        return getDescriptor();
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public OpaqueDescriptor getDescriptor() throws SQLException {
        return this.descriptor;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public void setDescriptor(OpaqueDescriptor desc) {
        this.descriptor = desc;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public byte[] toBytes() throws SQLException {
        return this.descriptor.toBytes(this, false);
    }

    @Override // oracle.jdbc.OracleOpaque
    public Object getValue() throws SQLException {
        return this.descriptor.toValue(this, false);
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public byte[] getBytesValue() throws SQLException {
        return this.descriptor.toValue(this, false);
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public void setValue(byte[] value) throws SQLException {
        this.value = value;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return false;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return new Object[arraySize];
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public Map getMap() {
        try {
            return getInternalConnection().getTypeMap();
        } catch (SQLException e) {
            return null;
        }
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        Map map = getMap();
        return toJdbc(map);
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public Object toJdbc(Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Object jdbcObject = this;
        if (map != null) {
            Class c = this.descriptor.getClass(map);
            if (c != null) {
                jdbcObject = toClass(c, map);
            } else if (getSQLTypeName().compareTo("SYS.XMLTYPE") == 0) {
                return XMLType.createXML((OPAQUE) jdbcObject);
            }
        }
        return jdbcObject;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleStruct
    public Object toClass(Class clazz) throws SQLException {
        return toClass(clazz, getMap());
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public Object toClass(Class clazz, Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Object ret;
        Object obj;
        if (clazz == null || clazz == OPAQUE.class || clazz == oracle.jdbc.OracleOpaque.class || clazz == OracleOpaque.class) {
            ret = this;
        } else {
            try {
                Object i = clazz.newInstance();
                if (i instanceof ORADataFactory) {
                    ORADataFactory f = (ORADataFactory) i;
                    obj = f.create(this, OracleTypes.OPAQUE);
                } else if (i instanceof OracleDataFactory) {
                    OracleDataFactory f2 = (OracleDataFactory) i;
                    obj = f2.create(this, OracleTypes.OPAQUE);
                } else {
                    SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, this.descriptor.getName()).fillInStackTrace();
                    CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "toClass", "OPAQUE.toClass: 'clazz' should be oracle.sql.OPAQUE or 'clazz' should have a constructor that takes an oracle.sql.OPAQUE or 'clazz' should implement ORADataFactory or 'clazz' should implement OracleDataFactory", (String) null, sqlException);
                    throw sqlException;
                }
                ret = obj;
            } catch (IllegalAccessException ex) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "IllegalAccessException: " + ex.getMessage()).fillInStackTrace());
            } catch (InstantiationException ex2) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "InstantiationException: " + ex2.getMessage()).fillInStackTrace());
            }
        }
        return ret;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public void setImage(byte[] image, long offset, long length) throws SQLException {
        setShareBytes(image);
        this.imageOffset = offset;
        this.imageLength = length;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public void setImageLength(long length) throws SQLException {
        this.imageLength = length;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public long getImageOffset() {
        return this.imageOffset;
    }

    @Override // oracle.jdbc.internal.OracleOpaque
    public long getImageLength() {
        return this.imageLength;
    }

    @Override // oracle.sql.DatumWithConnection, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Connection getJavaSqlConnection() throws SQLException {
        return super.getJavaSqlConnection();
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.acProxy = w;
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.acProxy;
    }
}
