package oracle.sql;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleConnectionWrapper;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleTypeMetaData;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleTypeMetaData;
import oracle.jdbc.oracore.OracleNamedType;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.oracore.OracleTypeCOLLECTION;
import oracle.jdbc.oracore.OracleTypeOPAQUE;
import oracle.jdbc.oracore.PickleContext;
import oracle.jdbc.xa.OracleXAResource;

/* loaded from: ojdbc8.jar:oracle/sql/TypeDescriptor.class */
public class TypeDescriptor implements OracleTypeMetaData, Serializable, ORAData, OracleData, Diagnosable {
    static final long serialVersionUID = 2022598722047823723L;
    public static final int KOIDFLEN = 16;
    static final short KOTA_TRN = 1;
    static final short KOTA_PDF = 2;
    static final short KOTA_ITOID = 4;
    static final short KOTA_LOB = 8;
    static final short KOTA_AD = 16;
    static final short KOTA_NMHSH = 32;
    static final short KOTA_TEV = 64;
    static final short KOTA_INH = 128;
    static final short KOTA_10I = 256;
    static final short KOTA_RBF = 512;
    static final short KOTA_HBF = 1024;
    static final int ANYTYPE_IMAGE_SIZE_TOID = 23;
    static final int ANYTYPE_IMAGE_SIZE_NO_TOID = 5;
    static final byte KOTTDOID = 1;
    static final byte KOTTBOID = 2;
    static final byte KOTADOID = 3;
    static final byte KOTREFOID = 4;
    static final byte KOTMDOID = 5;
    static final byte KOTMIOID = 6;
    static final byte KOTEXOID = 7;
    static final byte KOTDATOID = 8;
    static final byte KOTBYTOID = 9;
    static final byte KOTSHOOID = 10;
    static final byte KOTLONOID = 11;
    static final byte KOTREAOID = 12;
    static final byte KOTDOUOID = 13;
    static final byte KOTFLOOID = 14;
    static final byte KOTNUMOID = 15;
    static final byte KOTDECOID = 16;
    static final byte KOTUBYOID = 17;
    static final byte KOTUSHOID = 18;
    static final byte KOTULOOID = 19;
    static final byte KOTOCTOID = 20;
    static final byte KOTSMLOID = 21;
    static final byte KOTINTOID = 22;
    static final byte KOTRAWOID = 23;
    static final byte KOTPTROID = 24;
    static final byte KOTVSIOID = 25;
    static final byte KOTFSIOID = 26;
    static final byte KOTVSOOID = 27;
    static final byte KOTMLSOID = 28;
    static final byte KOTVAROID = 29;
    static final byte KOTMSTOID = 30;
    static final byte KOTNATOID = 31;
    static final byte KOTDOMOID = 32;
    static final byte KOTUND1OID = 33;
    static final byte KOTCLBOID = 34;
    static final byte KOTBLBOID = 35;
    static final byte KOTCFLOID = 36;
    static final byte KOTBFLOID = 37;
    static final byte KOTOIDOID = 38;
    static final byte KOTCAROID = 39;
    static final byte KOTCANOID = 40;
    static final byte KOTLPTOID = 41;
    static final byte KOTBRIOID = 42;
    static final byte KOTUCOOID = 43;
    static final byte KOTRECOID = 44;
    static final byte KOTRCUOID = 45;
    static final byte KOTBOOOID = 46;
    static final byte KOTRIDOID = 47;
    static final byte KOTPLOOID = 48;
    static final byte KOTPLROID = 49;
    static final byte KOTPBIOID = 50;
    static final byte KOTPINOID = 51;
    static final byte KOTPNAOID = 52;
    static final byte KOTPNNOID = 53;
    static final byte KOTPPOOID = 54;
    static final byte KOTPPNOID = 55;
    static final byte KOTPSTOID = 56;
    static final byte KOTEX1OID = 57;
    static final byte KOTOPQOID = 58;
    static final byte KOTTMOID = 59;
    static final byte KOTTMTZOID = 60;
    static final byte KOTTSOID = 61;
    static final byte KOTTSTZOID = 62;
    static final byte KOTIYMOID = 63;
    static final byte KOTIDSOID = 64;
    static final byte KOTTSIMPTZOID = 65;
    static final byte KOTTBXOID = 66;
    static final byte KOTADXOID = 67;
    static final byte KOTOIDBFLT = 68;
    static final byte KOTOIDBDBL = 69;
    static final byte KOTURDOID = 70;
    static final byte KOTJSONOID = 71;
    static final byte KOTLASTOID = 72;
    static final short SQLT_NONE = 0;
    static final short SQLT_CHR = 1;
    static final short SQLT_NUM = 2;
    static final short SQLT_INT = 3;
    static final short SQLT_FLT = 4;
    static final short SQLT_STR = 5;
    static final short SQLT_VNU = 6;
    static final short SQLT_PDN = 7;
    static final short SQLT_LNG = 8;
    static final short SQLT_VCS = 9;
    static final short SQLT_NON = 10;
    static final short SQLT_RID = 11;
    static final short SQLT_DAT = 12;
    static final short SQLT_VBI = 15;
    static final short SQLT_BFLOAT = 21;
    static final short SQLT_BDOUBLE = 22;
    static final short SQLT_BIN = 23;
    static final short SQLT_LBI = 24;
    static final short SQLT_UIN = 68;
    static final short SQLT_SLS = 91;
    static final short SQLT_LVC = 94;
    static final short SQLT_LVB = 95;
    static final short SQLT_AFC = 96;
    static final short SQLT_AVC = 97;
    static final short SQLT_IBFLOAT = 100;
    static final short SQLT_IBDOUBLE = 101;
    static final short SQLT_CUR = 102;
    static final short SQLT_RDD = 104;
    static final short SQLT_LAB = 105;
    static final short SQLT_OSL = 106;
    static final short SQLT_NTY = 108;
    static final short SQLT_REF = 110;
    static final short SQLT_CLOB = 112;
    static final short SQLT_BLOB = 113;
    static final short SQLT_BFILEE = 114;
    static final short SQLT_FILE = 114;
    static final short SQLT_CFILEE = 115;
    static final short SQLT_RSET = 116;
    static final short SQLT_SVT = 118;
    static final short SQLT_NCO = 122;
    static final short SQLT_DTR = 152;
    static final short SQLT_DUN = 153;
    static final short SQLT_DOP = 154;
    static final short SQLT_VST = 155;
    static final short SQLT_ODT = 156;
    static final short SQLT_DOL = 172;
    static final short SQLT_DATE = 184;
    static final short SQLT_TIME = 185;
    static final short SQLT_TIME_TZ = 186;
    static final short SQLT_TIMESTAMP = 187;
    static final short SQLT_TIMESTAMP_TZ = 188;
    static final short SQLT_INTERVAL_YM = 189;
    static final short SQLT_INTERVAL_DS = 190;
    static final short SQLT_TIMESTAMP_LTZ = 232;
    static final short SQLT_PNTY = 241;
    static final short SQLT_CFILE = 115;
    static final short SQLT_BFILE = 114;
    static final short SQLT_REC = 250;
    static final short SQLT_TAB = 251;
    static final short SQLT_BOL = 252;
    static final short SQLCS_IMPLICIT = 1;
    static final short SQLCS_NCHAR = 2;
    static final short SQLCS_EXPLICIT = 3;
    static final short SQLCS_FLEXIBLE = 4;
    static final short SQLCS_LIT_NULL = 5;
    static final short SQLT_XDP = 103;
    static final short SQLT_OKO = 107;
    static final short SQLT_INTY = 109;
    static final short SQLT_IREF = 111;
    static final short SQLT_DCLOB = 195;
    public static final short TYPECODE_REF = 110;
    public static final short TYPECODE_DATE = 12;
    public static final short TYPECODE_SIGNED8 = 27;
    public static final short TYPECODE_SIGNED16 = 28;
    public static final short TYPECODE_SIGNED32 = 29;
    public static final short TYPECODE_REAL = 21;
    public static final short TYPECODE_DOUBLE = 22;
    public static final short TYPECODE_BFLOAT = 100;
    public static final short TYPECODE_BDOUBLE = 101;
    public static final short TYPECODE_FLOAT = 4;
    public static final short TYPECODE_NUMBER = 2;
    public static final short TYPECODE_DECIMAL = 7;
    public static final short TYPECODE_UNSIGNED8 = 23;
    public static final short TYPECODE_UNSIGNED16 = 25;
    public static final short TYPECODE_UNSIGNED32 = 26;
    public static final short TYPECODE_OCTET = 245;
    public static final short TYPECODE_SMALLINT = 246;
    public static final short TYPECODE_INTEGER = 3;
    public static final short TYPECODE_RAW = 95;
    public static final short TYPECODE_PTR = 32;
    public static final short TYPECODE_VARCHAR2 = 9;
    public static final short TYPECODE_CHAR = 96;
    public static final short TYPECODE_VARCHAR = 1;
    public static final short TYPECODE_MLSLABEL = 105;
    public static final short TYPECODE_VARRAY = 247;
    public static final short TYPECODE_TABLE = 248;
    public static final short TYPECODE_OBJECT = 108;
    public static final short TYPECODE_OPAQUE = 58;
    public static final short TYPECODE_NAMEDCOLLECTION = 122;
    public static final short TYPECODE_BLOB = 113;
    public static final short TYPECODE_BFILE = 114;
    public static final short TYPECODE_CLOB = 112;
    public static final short TYPECODE_CFILE = 115;
    public static final short TYPECODE_TIME = 185;
    public static final short TYPECODE_TIME_TZ = 186;
    public static final short TYPECODE_TIMESTAMP = 187;
    public static final short TYPECODE_TIMESTAMP_TZ = 188;
    public static final short TYPECODE_TIMESTAMP_LTZ = 232;
    public static final short TYPECODE_INTERVAL_YM = 189;
    public static final short TYPECODE_INTERVAL_DS = 190;
    public static final short TYPECODE_UROWID = 104;
    public static final short TYPECODE_OTMFIRST = 228;
    public static final short TYPECODE_OTMLAST = 320;
    public static final short TYPECODE_SYSFIRST = 228;
    public static final short TYPECODE_SYSLAST = 235;
    public static final short TYPECODE_PLS_INTEGER = 266;
    public static final short TYPECODE_ITABLE = 251;
    public static final short TYPECODE_RECORD = 250;
    public static final short TYPECODE_BOOLEAN = 252;
    public static final short TYPECODE_NCHAR = 286;
    public static final short TYPECODE_NVARCHAR2 = 287;
    public static final short TYPECODE_NCLOB = 288;
    public static final short TYPECODE_NONE = 0;
    public static final short TYPECODE_ERRHP = 283;
    public static final short TYPECODE_JDBC_JOBJECT = 2000;
    public static final short TYPECODE_JDBC_STRUCT = 2002;
    public static final short TYPECODE_JDBC_ARRAY = 2003;
    public static final short TYPECODE_JDBC_JOPAQUE = 2000;
    public static final short TYPECODE_JDBC_REF = 2006;
    public static final short TYPECODE_JDBC_JSTRUCT = 2008;
    public static final short TYPECODE_SQLXML = 2009;
    private static final short TYPECODE_MAXVALUE = 2009;
    SQLName sqlName;
    OracleNamedType pickler;
    transient OracleConnection connection;
    short internalTypeCode;
    boolean isTransient;
    byte[] toid;
    int toidVersion;
    long precision;
    byte scale;
    byte[] transientImage;
    AttributeDescriptor[] attributesDescriptor;
    transient Boolean isInstanciable;
    transient String supertype;
    transient int numLocalAttrs;
    transient String[] subtypes;
    transient String[] attrJavaNames;
    String typeNameByUser;
    private static String[] typeCodeTypeNameMap;
    Object acProxy;
    private static final String CLASS_NAME = TypeDescriptor.class.getName();
    public static boolean DEBUG_SERIALIZATION = false;
    static final byte[] KOTTDEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1};
    static final byte[] KOTTBEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 2};
    static final byte[] KOTADEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 3};
    static final byte[] KOTMDEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 4};
    static final byte[] KOTTBXEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 66};
    static final byte[] KOTADXEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 67};
    static final byte[] KOTTDTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1};
    static final byte[] KOTTBTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2};
    static final byte[] KOTADTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3};
    static final byte[] KOTMDTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5};
    static final byte[] KOTMITOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6};
    static final byte[] KOTEXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7};
    static final byte[] KOTEX1TOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 57};
    static final byte[] KOTTBXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 66};
    static final byte[] KOTADXTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67};
    public static final byte[] RAWTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23};
    public static final byte[] JSONTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71};
    public static final byte[] ANYTYPETOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 16};
    public static final byte[] ANYDATATOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 17};
    public static final byte[] ANYDATASETTOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 18};
    public static final byte[] XMLTYPETOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 0};
    static final short[] OID_TO_TYPECODE = new short[72];

    static {
        OID_TO_TYPECODE[8] = 12;
        OID_TO_TYPECODE[9] = 27;
        OID_TO_TYPECODE[10] = 28;
        OID_TO_TYPECODE[11] = 29;
        OID_TO_TYPECODE[12] = 21;
        OID_TO_TYPECODE[13] = 22;
        OID_TO_TYPECODE[14] = 4;
        OID_TO_TYPECODE[15] = 2;
        OID_TO_TYPECODE[16] = 7;
        OID_TO_TYPECODE[17] = 23;
        OID_TO_TYPECODE[18] = 25;
        OID_TO_TYPECODE[19] = 26;
        OID_TO_TYPECODE[20] = 245;
        OID_TO_TYPECODE[21] = 246;
        OID_TO_TYPECODE[22] = 3;
        OID_TO_TYPECODE[23] = 95;
        OID_TO_TYPECODE[24] = 32;
        OID_TO_TYPECODE[25] = 9;
        OID_TO_TYPECODE[26] = 96;
        OID_TO_TYPECODE[27] = 1;
        OID_TO_TYPECODE[28] = 105;
        OID_TO_TYPECODE[29] = 247;
        OID_TO_TYPECODE[30] = 248;
        OID_TO_TYPECODE[31] = 108;
        OID_TO_TYPECODE[32] = 0;
        OID_TO_TYPECODE[33] = 0;
        OID_TO_TYPECODE[34] = 112;
        OID_TO_TYPECODE[35] = 113;
        OID_TO_TYPECODE[36] = 115;
        OID_TO_TYPECODE[37] = 114;
        OID_TO_TYPECODE[38] = 0;
        OID_TO_TYPECODE[39] = 0;
        OID_TO_TYPECODE[40] = 0;
        OID_TO_TYPECODE[41] = 0;
        OID_TO_TYPECODE[42] = 0;
        OID_TO_TYPECODE[43] = 0;
        OID_TO_TYPECODE[44] = 0;
        OID_TO_TYPECODE[45] = 0;
        OID_TO_TYPECODE[46] = 252;
        OID_TO_TYPECODE[47] = 0;
        OID_TO_TYPECODE[48] = 0;
        OID_TO_TYPECODE[49] = 0;
        OID_TO_TYPECODE[50] = 0;
        OID_TO_TYPECODE[51] = 0;
        OID_TO_TYPECODE[52] = 0;
        OID_TO_TYPECODE[53] = 0;
        OID_TO_TYPECODE[54] = 0;
        OID_TO_TYPECODE[55] = 0;
        OID_TO_TYPECODE[56] = 0;
        OID_TO_TYPECODE[57] = 0;
        OID_TO_TYPECODE[58] = 58;
        OID_TO_TYPECODE[59] = 185;
        OID_TO_TYPECODE[60] = 186;
        OID_TO_TYPECODE[61] = 187;
        OID_TO_TYPECODE[62] = 188;
        OID_TO_TYPECODE[63] = 189;
        OID_TO_TYPECODE[64] = 190;
        OID_TO_TYPECODE[65] = 232;
        OID_TO_TYPECODE[66] = 0;
        OID_TO_TYPECODE[67] = 0;
        OID_TO_TYPECODE[68] = 100;
        OID_TO_TYPECODE[69] = 101;
        OID_TO_TYPECODE[70] = 104;
        typeCodeTypeNameMap = null;
        try {
            getTypeCodeTypeNameMap();
        } catch (Exception e) {
        }
    }

    void copyDescriptor(TypeDescriptor src) {
        if (src.sqlName != null) {
            this.sqlName = src.sqlName;
        }
        this.pickler = src.pickler;
        this.connection = src.connection;
        this.internalTypeCode = src.internalTypeCode;
        this.isTransient = src.isTransient;
        this.toid = src.toid;
        this.toidVersion = src.toidVersion;
        this.precision = src.precision;
        this.scale = src.scale;
        this.transientImage = src.transientImage;
        this.attributesDescriptor = src.attributesDescriptor;
        this.isInstanciable = src.isInstanciable;
        this.supertype = src.supertype;
        this.numLocalAttrs = src.numLocalAttrs;
        this.subtypes = src.subtypes;
        this.attrJavaNames = src.attrJavaNames;
        if (src.typeNameByUser != null) {
            this.typeNameByUser = src.typeNameByUser;
        }
    }

    protected TypeDescriptor(short _typeCode) {
        this.isTransient = false;
        this.toid = null;
        this.toidVersion = 1;
        this.transientImage = null;
        this.attributesDescriptor = null;
        this.isInstanciable = null;
        this.supertype = null;
        this.numLocalAttrs = -1;
        this.subtypes = null;
        this.attrJavaNames = null;
        this.internalTypeCode = _typeCode;
    }

    protected TypeDescriptor(short _typeCode, String name, Connection conn) throws SQLException {
        this.isTransient = false;
        this.toid = null;
        this.toidVersion = 1;
        this.transientImage = null;
        this.attributesDescriptor = null;
        this.isInstanciable = null;
        this.supertype = null;
        this.numLocalAttrs = -1;
        this.subtypes = null;
        this.attrJavaNames = null;
        this.internalTypeCode = _typeCode;
        if (name == null || conn == null) {
            SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 60, "Invalid arguments").fillInStackTrace();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "TypeDescriptor.<init>", "Invalid arguments, 'name' should not be an empty string and conn should not be null. An exception is thrown.", null, sqlException);
            throw sqlException;
        }
        setPhysicalConnectionOf(conn);
        this.typeNameByUser = name;
    }

    protected TypeDescriptor(short _typeCode, SQLName name, Connection conn) throws SQLException {
        this.isTransient = false;
        this.toid = null;
        this.toidVersion = 1;
        this.transientImage = null;
        this.attributesDescriptor = null;
        this.isInstanciable = null;
        this.supertype = null;
        this.numLocalAttrs = -1;
        this.subtypes = null;
        this.attrJavaNames = null;
        this.internalTypeCode = _typeCode;
        if (name == null || conn == null) {
            SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 60, "Invalid arguments").fillInStackTrace();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "TypeDescriptor.<init>", "Invalid arguments, 'name' and 'conn' should not be null. An exception is thrown.", null, sqlException);
            throw sqlException;
        }
        this.sqlName = name;
        this.typeNameByUser = this.sqlName.getName();
        setPhysicalConnectionOf(conn);
    }

    protected TypeDescriptor(short _typeCode, SQLName name, OracleTypeADT type, Connection conn) throws SQLException {
        this.isTransient = false;
        this.toid = null;
        this.toidVersion = 1;
        this.transientImage = null;
        this.attributesDescriptor = null;
        this.isInstanciable = null;
        this.supertype = null;
        this.numLocalAttrs = -1;
        this.subtypes = null;
        this.attrJavaNames = null;
        this.internalTypeCode = _typeCode;
        if (name == null || type == null) {
            SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 60, "Invalid arguments").fillInStackTrace();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "TypeDescriptor.<init>", "Invalid arguments, 'type' and 'conn' should not be null. An exception is thrown.", null, sqlException);
            throw sqlException;
        }
        this.sqlName = name;
        this.typeNameByUser = this.sqlName.getName();
        if (conn != null) {
            setPhysicalConnectionOf(conn);
        }
        this.pickler = type;
        this.pickler.setDescriptor(this);
        this.toid = type.getTOID();
    }

    protected TypeDescriptor(short _typeCode, OracleTypeADT type, Connection conn) throws SQLException {
        this.isTransient = false;
        this.toid = null;
        this.toidVersion = 1;
        this.transientImage = null;
        this.attributesDescriptor = null;
        this.isInstanciable = null;
        this.supertype = null;
        this.numLocalAttrs = -1;
        this.subtypes = null;
        this.attrJavaNames = null;
        this.internalTypeCode = _typeCode;
        if (type == null || conn == null) {
            SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 60, "Invalid arguments").fillInStackTrace();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "TypeDescriptor.<init>", "Invalid arguments, 'type' and 'conn' should not be null. An exception is thrown.", null, sqlException);
            throw sqlException;
        }
        setPhysicalConnectionOf(conn);
        this.sqlName = null;
        this.pickler = type;
        this.pickler.setDescriptor(this);
        this.toid = type.getTOID();
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public OracleTypeMetaData.Kind getKind() {
        return OracleTypeMetaData.Kind.TYPE;
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public String getName() throws SQLException {
        if (this.sqlName == null) {
            Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
            Throwable th = null;
            try {
                initSQLName();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        String ret = null;
        if (this.sqlName != null) {
            ret = this.sqlName.getName();
        }
        return ret;
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public SQLName getSQLName() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.sqlName == null) {
                initSQLName();
            }
            SQLName sQLName = this.sqlName;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return sQLName;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void initSQLName() throws SQLException {
        if (!this.isTransient) {
            if (this.connection == null) {
                SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace();
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initSQLName", "Internal error, connection is null. An exception is thrown.", null, sqlException);
                throw sqlException;
            }
            if (this.pickler != null) {
                this.sqlName = new SQLName(this.pickler.getFullName(), this.connection);
                return;
            }
            if (this.typeNameByUser != null) {
                this.sqlName = new SQLName(this.typeNameByUser, this.connection);
                return;
            }
            if (this.toid != null) {
                String typeName = OracleTypeADT.toid2typename(this.connection, this.toid);
                this.typeNameByUser = typeName;
                this.sqlName = new SQLName(typeName, this.connection);
                String qname = this.sqlName.getName();
                TypeDescriptor descriptorFromCache = (TypeDescriptor) this.connection.doGetDescriptor(qname);
                if (descriptorFromCache != null) {
                    copyDescriptor(descriptorFromCache);
                    return;
                }
                return;
            }
            if (this.internalTypeCode == 108 || this.internalTypeCode == 122) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
            }
        }
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public String getSchemaName() throws SQLException {
        Monitor.CloseableLock lock = getInternalConnection().acquireCloseableLock();
        Throwable th = null;
        try {
            String ret = null;
            if (this.sqlName == null) {
                initSQLName();
            }
            if (this.sqlName != null) {
                ret = this.sqlName.getSchema();
            }
            return ret;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    public String getTypeName() throws SQLException {
        Monitor.CloseableLock lock = getInternalConnection().acquireCloseableLock();
        Throwable th = null;
        try {
            String ret = null;
            if (this.sqlName == null) {
                initSQLName();
            }
            if (this.sqlName != null) {
                ret = this.sqlName.getSimpleName();
            }
            return ret;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    public OracleNamedType getPickler() {
        return this.pickler;
    }

    public OracleConnection getInternalConnection() {
        return this.connection;
    }

    public void setPhysicalConnectionOf(Connection conn) {
        this.connection = ((oracle.jdbc.OracleConnection) conn).physicalConnectionWithin();
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public int getTypeCode() throws SQLException {
        return this.internalTypeCode;
    }

    @Override // oracle.jdbc.OracleTypeMetaData
    public String getTypeCodeName() throws SQLException {
        return getTypeCodeTypeNameMap()[getTypeCode()];
    }

    private static String[] getTypeCodeTypeNameMap() throws SQLException, ClassNotFoundException, SecurityException {
        if (typeCodeTypeNameMap == null) {
            String[] typeCodeTypeNameMap_local = new String[2010];
            try {
                Class typeDescriptorClass = Class.forName("oracle.sql.TypeDescriptor");
                Field[] publicTypeCodeFields = typeDescriptorClass.getFields();
                for (int i = 0; i < publicTypeCodeFields.length; i++) {
                    if (publicTypeCodeFields[i].getName().startsWith("TYPECODE_")) {
                        try {
                            typeCodeTypeNameMap_local[publicTypeCodeFields[i].getInt(null)] = publicTypeCodeFields[i].getName();
                        } catch (Exception ex) {
                            throw ((SQLException) DatabaseError.createSqlException(1, "TypeDescriptor.getTypeCodeName: " + ex.getMessage()).fillInStackTrace());
                        }
                    }
                }
                typeCodeTypeNameMap = typeCodeTypeNameMap_local;
            } catch (ClassNotFoundException ex2) {
                throw ((SQLException) DatabaseError.createSqlException(1, "TypeDescriptor.getTypeCodeName: got a ClassNotFoundException: " + ex2.getMessage()).fillInStackTrace());
            }
        }
        return typeCodeTypeNameMap;
    }

    public short getInternalTypeCode() throws SQLException {
        return this.internalTypeCode;
    }

    public static boolean isV2available(byte[] toid) {
        int i = 0;
        while (toid[i] == 0) {
            i++;
        }
        if (i == 13 && toid[13] == 2 && toid[14] == 16 && toid[15] < 67 && toid[15] >= 0) {
            return true;
        }
        return false;
    }

    public static TypeDescriptor getTypeDescriptor(String name, oracle.jdbc.OracleConnection conn) throws SQLException {
        oracle.jdbc.OracleConnection conn2 = ConcreteProxyUtil.unwrapConnectionProxy(conn);
        OracleConnection physConn = conn2.physicalConnectionWithin();
        Monitor.CloseableLock lock = physConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                SQLName sqlName = new SQLName(name, conn2);
                String qname = sqlName.getName();
                TypeDescriptor descriptor = (TypeDescriptor) physConn.doGetDescriptor(qname);
                if (descriptor == null) {
                    OracleTypeADT otype = new OracleTypeADT(name, conn2);
                    OracleConnection iconn = (OracleConnection) conn2;
                    otype.init(iconn);
                    descriptor = newDescriptorFromADT(sqlName, conn2, otype);
                    conn2.putDescriptor(qname, descriptor);
                }
                return descriptor;
            } catch (SQLRecoverableException sqlrexc) {
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getTypeDescriptor", "Exception caught and thrown : {0}", (String) null, (String) sqlrexc, (Object) sqlrexc.getMessage());
                throw sqlrexc;
            } catch (SQLException sqlException) {
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getTypeDescriptor", "Exception caught and thrown : {0}", (String) null, (String) sqlException, (Object) sqlException.getMessage());
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, sqlException, 60, "Unable to resolve type \"" + name + "\"").fillInStackTrace());
            } catch (Exception e) {
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getTypeDescriptor", "Exception caught and thrown : {0} ", (String) null, (String) e, (Object) e.getMessage());
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, 60, "Unable to resolve type \"" + name + "\"").fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private static TypeDescriptor newDescriptorFromADT(SQLName sqlName, oracle.jdbc.OracleConnection conn, OracleTypeADT otype) throws SQLException {
        TypeDescriptor descriptor;
        OracleNamedType realType = otype.cleanup();
        switch (realType.getTypeCode()) {
            case 2002:
            case 2008:
                descriptor = new StructDescriptor(sqlName, (OracleTypeADT) realType, conn);
                break;
            case 2003:
                descriptor = new ArrayDescriptor(sqlName, (OracleTypeCOLLECTION) realType, conn);
                break;
            case OracleTypes.BLOB /* 2004 */:
            case OracleTypes.CLOB /* 2005 */:
            case 2006:
            default:
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "newDescriptorFromADT", "The real type should be either STRUCT, JAVA_STRUCT, ARRAY or OPAQUE. An exception is thrown.", null, null);
                throw ((SQLException) DatabaseError.createSqlException(1).fillInStackTrace());
            case OracleTypes.OPAQUE /* 2007 */:
                descriptor = new OpaqueDescriptor(sqlName, (OracleTypeOPAQUE) realType, conn);
                break;
        }
        realType.setDescriptor(descriptor);
        return descriptor;
    }

    public static TypeDescriptor getTypeDescriptor(String qualifiedName, oracle.jdbc.OracleConnection conn, byte[] image, long offset) throws SQLException {
        oracle.jdbc.OracleConnection conn2 = ConcreteProxyUtil.unwrapConnectionProxy(conn);
        OracleConnection physConn = conn2.physicalConnectionWithin();
        Monitor.CloseableLock lock = physConn.acquireCloseableLock();
        Throwable th = null;
        try {
            String qname = getSubtypeName(conn2, image, offset);
            if (qname == null) {
                qname = qualifiedName;
            }
            TypeDescriptor descriptor = (TypeDescriptor) physConn.doGetDescriptor(qname);
            if (descriptor == null) {
                SQLName sqlName = new SQLName(qname, conn2);
                OracleTypeADT otype = new OracleTypeADT(qname, conn2);
                otype.init(physConn);
                OracleNamedType realType = otype.cleanup();
                switch (realType.getTypeCode()) {
                    case 2002:
                    case 2008:
                        descriptor = new StructDescriptor(sqlName, (OracleTypeADT) realType, conn2);
                        break;
                    case 2003:
                        descriptor = new ArrayDescriptor(sqlName, (OracleTypeCOLLECTION) realType, conn2);
                        break;
                    case OracleTypes.BLOB /* 2004 */:
                    case OracleTypes.CLOB /* 2005 */:
                    case 2006:
                    default:
                        CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getTypeDescriptor", "The real type should be either STRUCT, JAVA_STRUCT, ARRAY or OPAQUE. An exception is thrown.", null, null);
                        throw ((SQLException) DatabaseError.createSqlException(1).fillInStackTrace());
                    case OracleTypes.OPAQUE /* 2007 */:
                        descriptor = new OpaqueDescriptor(sqlName, (OracleTypeOPAQUE) realType, conn2);
                        break;
                }
                conn2.putDescriptor(qname, descriptor);
            }
            return descriptor;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.ORAData
    public Datum toDatum(Connection c) throws SQLException {
        if ((c instanceof OracleConnectionWrapper) && !(c instanceof oracle.jdbc.driver.OracleConnection)) {
            this.connection = (OracleConnection) ((OracleConnectionWrapper) c).unwrap();
        } else {
            this.connection = (OracleConnection) c;
        }
        OpaqueDescriptor desc = OpaqueDescriptor.createDescriptor("SYS.ANYTYPE", c);
        byte[] image = new byte[getOpaqueImageTypeSize()];
        pickleOpaqueTypeImage(image, 0, false);
        OPAQUE opq = new OPAQUE(desc, this.connection, image);
        opq.setShareBytes(opq.toBytes());
        return opq;
    }

    @Override // oracle.jdbc.OracleData
    public Object toJDBCObject(Connection c) throws SQLException {
        return toDatum(c);
    }

    static TypeDescriptor unpickleOpaqueTypeImage(PickleContext context, Connection connection, short[] rdbmsTypeCodeArr) throws SQLException {
        TypeDescriptor ret;
        int offsetBegin = context.offset();
        byte[] image = context.image();
        context.skipBytes(1);
        short flag = (short) context.readUB2();
        rdbmsTypeCodeArr[0] = (short) context.readUB2();
        Monitor.CloseableLock lock = ((OracleConnection) connection).acquireCloseableLock();
        Throwable th = null;
        try {
            if ((flag & 32) != 0) {
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "unpickleOpaqueTypeImage", "Export/Import of persistent type isn't supported.", null, null);
                throw ((SQLException) DatabaseError.createSqlException(178).fillInStackTrace());
            }
            if ((flag & 1) == 0) {
                if ((flag & 2) == 0 || rdbmsTypeCodeArr[0] == 110 || rdbmsTypeCodeArr[0] == 58) {
                    byte[] toid = context.readDataValue(16);
                    int toidVersion = context.readUB2();
                    String typeName = (String) ((OracleConnection) connection).getDescriptor(toid);
                    ret = (TypeDescriptor) ((OracleConnection) connection).doGetDescriptor(typeName);
                    if (ret == null) {
                        if (rdbmsTypeCodeArr[0] == 122) {
                            ret = new ArrayDescriptor(toid, toidVersion, connection);
                        } else if (rdbmsTypeCodeArr[0] == 108 || rdbmsTypeCodeArr[0] == 110) {
                            ret = new StructDescriptor(toid, toidVersion, connection);
                        } else if (rdbmsTypeCodeArr[0] == 58) {
                            ret = new OpaqueDescriptor(toid, toidVersion, connection);
                        } else {
                            throw ((SQLException) DatabaseError.createSqlException(178).fillInStackTrace());
                        }
                    }
                } else {
                    ret = new TypeDescriptor(rdbmsTypeCodeArr[0]);
                }
                ret.setTransient(false);
            } else {
                int numattrs = (int) context.readUB4();
                if (rdbmsTypeCodeArr[0] == 108) {
                    AttributeDescriptor[] ados = null;
                    if (numattrs > 0) {
                        ados = new AttributeDescriptor[numattrs];
                        for (int i = 0; i < numattrs; i++) {
                            int adeFlag = context.readByte();
                            ados[i] = Kotad.unpickleAttributeImage(adeFlag == 2, context);
                            if (adeFlag != 2) {
                                short[] attrRdbmsTypeCode = new short[1];
                                ados[i].setTypeDescriptor(unpickleOpaqueTypeImage(context, connection, attrRdbmsTypeCode));
                            }
                        }
                    }
                    ret = new StructDescriptor(ados, connection);
                } else if (numattrs == 1) {
                    context.readByte();
                    ret = Kotad.unpickleTypeDescriptorImage(context);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(178).fillInStackTrace());
                }
                ret.setTransient(true);
            }
            if (ret.isTransient()) {
                int offsetEnd = context.offset();
                byte[] transientTypeImage = new byte[offsetEnd - offsetBegin];
                System.arraycopy(image, offsetBegin, transientTypeImage, 0, transientTypeImage.length);
                ret.setTransientImage(transientTypeImage);
            }
            return ret;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    void setTransientImage(byte[] _transientImage) {
        this.transientImage = _transientImage;
    }

    void setTransient(boolean _isTransient) {
        this.isTransient = _isTransient;
    }

    public boolean isTransient() {
        return this.isTransient;
    }

    int getOpaqueImageTypeSize() {
        int ret;
        if (this.isTransient) {
            ret = this.transientImage.length;
        } else {
            ret = 5;
            if (this.toid != null && this.toid.length == 16) {
                ret = 23;
            }
        }
        return ret;
    }

    int pickleOpaqueTypeImage(byte[] buffer, int offset, boolean isREF) {
        int offset2;
        if (this.isTransient) {
            System.arraycopy(this.transientImage, 0, buffer, offset, this.transientImage.length);
            offset2 = offset + this.transientImage.length;
        } else {
            boolean includeToid = false;
            if (this.toid != null && this.toid.length == 16) {
                includeToid = true;
            }
            int offset3 = offset + 1;
            buffer[offset] = 1;
            short rdbmsTypeCode = this.internalTypeCode;
            if (isREF) {
                rdbmsTypeCode = 110;
            }
            int flag = 512;
            if (rdbmsTypeCode != 108 && rdbmsTypeCode != 122) {
                flag = 512 | 2;
            }
            if (includeToid && rdbmsTypeCode != 110) {
                flag |= 4;
            }
            int offset4 = offset3 + 1;
            buffer[offset3] = (byte) (((flag & OracleXAResource.ORAISOLATIONMASK) >> 8) & 255);
            int offset5 = offset4 + 1;
            buffer[offset4] = (byte) (flag & 255);
            int offset6 = offset5 + 1;
            buffer[offset5] = (byte) (((rdbmsTypeCode & 65280) >> 8) & 255);
            offset2 = offset6 + 1;
            buffer[offset6] = (byte) (rdbmsTypeCode & 255);
            if (includeToid) {
                System.arraycopy(this.toid, 0, buffer, offset2, this.toid.length);
                int offset7 = offset2 + this.toid.length;
                int offset8 = offset7 + 1;
                buffer[offset7] = (byte) (((this.toidVersion & OracleXAResource.ORAISOLATIONMASK) >> 8) & 255);
                offset2 = offset8 + 1;
                buffer[offset8] = (byte) (this.toidVersion & 255);
            }
        }
        return offset2;
    }

    public void setPrecision(long _precision) {
        this.precision = _precision;
    }

    public long getPrecision() {
        return this.precision;
    }

    public void setScale(byte _scale) {
        this.scale = _scale;
    }

    public byte getScale() {
        return this.scale;
    }

    public boolean isInHierarchyOf(String checkThisName) throws SQLException {
        return false;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        try {
            if (this.sqlName == null) {
                initSQLName();
            }
            out.writeObject(this.sqlName);
            out.writeObject(this.pickler);
        } catch (SQLException e) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "writeObject", "Exception caught and thrown : {0} ", (String) null, (String) e, (Object) e.getMessage());
            throw new IOException(e.getMessage());
        }
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.sqlName = (SQLName) in.readObject();
        this.pickler = (OracleNamedType) in.readObject();
    }

    public void setConnection(Connection connection) throws SQLException {
        setPhysicalConnectionOf(connection);
        this.pickler.setConnection(getInternalConnection());
    }

    public static String getSubtypeName(oracle.jdbc.OracleConnection conn, byte[] image, long offset) throws SQLException {
        if (image == null || image.length == 0 || conn == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, " 'image' should not be empty and 'conn' should not be null. ").fillInStackTrace());
        }
        String ret = OracleTypeADT.getSubtypeName(conn, image, offset);
        return ret;
    }

    public void initMetadataRecursively() throws SQLException {
        Monitor.CloseableLock lock = getInternalConnection().acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.pickler != null) {
                this.pickler.initMetadataRecursively();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void initNamesRecursively() throws SQLException {
        if (this.pickler != null) {
            this.pickler.initNamesRecursively();
        }
    }

    public void fixupConnection(OracleConnection fixupConn) throws SQLException {
        if (this.connection == null) {
            this.connection = fixupConn;
        }
        if (this.pickler != null) {
            this.pickler.fixupConnection(fixupConn);
        }
    }

    public String toXMLString() throws SQLException {
        return toXMLString(false);
    }

    public String toXMLString(boolean fetchAllMetaDataAsNeeded) throws SQLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        printXMLHeader(pw);
        printXML(pw, 0, fetchAllMetaDataAsNeeded);
        return sw.getBuffer().substring(0);
    }

    public void printXML(PrintStream s) throws SQLException {
        printXML(s, false);
    }

    public void printXML(PrintStream stream, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        PrintWriter pw = new PrintWriter((OutputStream) stream, true);
        printXMLHeader(pw);
        printXML(pw, 0, fetchAllMetaDataAsNeeded);
    }

    void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        String tagname = tagName();
        pw.println("<" + tagname + " sqlName=\"" + getName() + "\" >");
        if (this.pickler != null) {
            this.pickler.printXML(pw, indent + 1, fetchAllMetaDataAsNeeded);
        }
        pw.println("</" + tagname + ">");
    }

    String tagName() {
        return "TypeDescriptor";
    }

    void printXMLHeader(PrintWriter pw) throws SQLException {
        pw.println("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    public static String convToUpperCase(String str) {
        if (str != null && !str.startsWith("\"")) {
            char[] srcChars = str.toCharArray();
            for (int i = 0; i < srcChars.length; i++) {
                srcChars[i] = Character.toUpperCase(srcChars[i]);
            }
            str = String.copyValueOf(srcChars);
        }
        return str;
    }

    public Class getClass(Map map) throws SQLException {
        String qualifiedName = getName();
        Class c = this.connection.getClassForType(qualifiedName, map);
        String schema = getSchemaName();
        String type = getTypeName();
        if (c == null) {
            c = (Class) map.get(type);
        }
        if (c == null) {
            if (this.connection.isProxySession()) {
                schema = convToUpperCase(schema);
            }
            c = (Class) map.get(schema + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + type);
        }
        if (SQLName.s_parseAllFormat) {
            if (c == null && this.connection.getDefaultSchemaNameForNamedTypes().equals(schema)) {
                c = (Class) map.get("\"" + type + "\"");
            }
            if (c == null) {
                c = (Class) map.get("\"" + schema + "\".\"" + type + "\"");
            }
            if (c == null) {
                c = (Class) map.get("\"" + schema + "\"." + type);
            }
            if (c == null) {
                c = (Class) map.get(schema + ".\"" + type + "\"");
            }
        }
        return c;
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.acProxy = w;
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.acProxy;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }
}
