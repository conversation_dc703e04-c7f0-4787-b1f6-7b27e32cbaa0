package oracle.sql;

import java.sql.Connection;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.zone.ZoneRules;
import java.util.Calendar;
import java.util.Locale;
import java.util.SimpleTimeZone;
import java.util.TimeZone;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleTimestampWithTimeZone;

/* loaded from: ojdbc8.jar:oracle/sql/TIMESTAMPTZ.class */
public class TIMESTAMPTZ extends Datum implements OracleTimestampWithTimeZone {
    static final long serialVersionUID = 6708361144588335769L;
    static final Calendar CAL_GMT_US = Calendar.getInstance(TimeZone.getTimeZone("GMT"), Locale.US);
    static final TimeZone TIMEZONE_UTC = TimeZone.getTimeZone("UTC");
    private static int HOUR_MILLISECOND = 3600000;
    private static int MINUTE_MILLISECOND = 60000;
    private static int SECOND_MILLISECOND = 1000;
    private static int HOUR_SECOND = 3600;
    private static int MINUTE_SECOND = 60;
    private static int OFFSET_HOUR = 20;
    private static int OFFSET_MINUTE = 60;
    private static byte REGIONIDBIT = Byte.MIN_VALUE;

    public TIMESTAMPTZ() {
        super(initTimestamptz());
    }

    public TIMESTAMPTZ(byte[] timestamptz) {
        super(timestamptz);
    }

    public TIMESTAMPTZ(Connection conn, Date date) throws SQLException {
        super(toBytes(conn, date));
    }

    public TIMESTAMPTZ(Connection conn, Date date, Calendar cal) throws SQLException {
        super(toBytes(conn, date, cal));
    }

    public TIMESTAMPTZ(Connection conn, Time time) throws SQLException {
        super(toBytes(conn, time));
    }

    public TIMESTAMPTZ(Connection conn, Time time, Calendar cal) throws SQLException {
        super(toBytes(conn, time, cal));
    }

    public TIMESTAMPTZ(Connection conn, Timestamp timestamp) throws SQLException {
        super(toBytes(conn, timestamp));
    }

    public TIMESTAMPTZ(Connection conn, Timestamp timestamp, Calendar cal) throws SQLException {
        super(toBytes(conn, timestamp, cal));
    }

    public TIMESTAMPTZ(Connection conn, Timestamp timestamp, ZoneId tzid) throws SQLException {
        super(toBytes(conn, timestamp, tzid));
    }

    public TIMESTAMPTZ(Connection conn, DATE date) throws SQLException {
        super(toBytes(conn, date));
    }

    public TIMESTAMPTZ(Connection conn, String str) throws SQLException {
        super(toBytes(conn, str));
    }

    public TIMESTAMPTZ(Connection conn, String str, Calendar cal) throws SQLException {
        super(toBytes(conn, str, cal));
    }

    public TIMESTAMPTZ(OffsetDateTime odt) throws SQLException {
        super(toBytes(odt));
    }

    public TIMESTAMPTZ(ZonedDateTime zdt) throws SQLException {
        super(toBytes(zdt));
    }

    public TIMESTAMPTZ(Connection conn, LocalDateTime ldt) throws SQLException {
        super(toBytes(conn, ldt));
    }

    public TIMESTAMPTZ(OffsetTime ot) throws SQLException {
        super(toBytes(ot));
    }

    public static Date toDate(Connection conn, byte[] timestamptz) throws SQLException {
        int[] result = new int[13];
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            for (int i = 0; i < 13; i++) {
                result[i] = timestamptz[i] & 255;
            }
            int year = TIMESTAMP.getJavaYear(result[0], result[1]);
            Calendar cal = Calendar.getInstance();
            cal.set(1, year);
            cal.set(2, result[2] - 1);
            cal.set(5, result[3]);
            cal.set(11, result[4] - 1);
            cal.set(12, result[5] - 1);
            cal.set(13, result[6] - 1);
            int tsmillis = TIMESTAMP.getNanos(timestamptz, 7) / 1000000;
            cal.set(14, tsmillis);
            if ((result[11] & REGIONIDBIT) != 0) {
                int regionID = getHighOrderbits(result[11]) + getLowOrderbits(result[12]);
                TIMEZONETAB tzTab = getTIMEZONETAB(conn);
                if (tzTab.checkID(regionID)) {
                    tzTab.updateTable(conn, regionID);
                }
                int offset = tzTab.getOffset(cal, regionID);
                cal.add(10, offset / HOUR_MILLISECOND);
                cal.add(12, (offset % HOUR_MILLISECOND) / MINUTE_MILLISECOND);
            } else {
                cal.add(10, result[11] - OFFSET_HOUR);
                cal.add(12, result[12] - OFFSET_MINUTE);
            }
            long millis = cal.getTime().getTime();
            Date date = new Date(millis);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return date;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static Date toDate2(Connection conn, byte[] timestamptz) throws SQLException {
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        Calendar cal = (Calendar) CAL_GMT_US.clone();
        cal.set(1, year);
        cal.set(2, result[2] - 1);
        cal.set(5, result[3]);
        cal.set(11, result[4] - 1);
        cal.set(12, result[5] - 1);
        cal.set(13, result[6] - 1);
        int tsmillis = TIMESTAMP.getNanos(timestamptz, 7) / 1000000;
        cal.set(14, tsmillis);
        long millis = cal.getTime().getTime();
        return new Date(millis);
    }

    public static Time toTime(Connection conn, byte[] timestamptz) throws SQLException {
        int hour = timestamptz[4] & 255;
        int minute = timestamptz[5] & 255;
        int second = timestamptz[6] & 255;
        Calendar cal = (Calendar) CAL_GMT_US.clone();
        cal.set(1, 1970);
        cal.set(2, 0);
        cal.set(5, 1);
        cal.set(11, hour - 1);
        cal.set(12, minute - 1);
        cal.set(13, second - 1);
        cal.set(14, 0);
        return new Time(cal.getTimeInMillis());
    }

    public static DATE toDATE(Connection conn, byte[] timestamptz) throws SQLException {
        return new DATE(toTimestampInSessionTimezone(conn, timestamptz));
    }

    public static TIMESTAMP toTIMESTAMP(Connection conn, byte[] timestamptz) throws SQLException {
        return new TIMESTAMP(toTimestampInSessionTimezone(conn, timestamptz));
    }

    public static Timestamp toTimestamp(Connection conn, byte[] timestamptz) throws SQLException {
        long timeInMillis;
        int[] result = new int[13];
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        for (int i = 0; i < 13; i++) {
            try {
                try {
                    result[i] = timestamptz[i] & 255;
                } finally {
                }
            } catch (Throwable th2) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th2;
            }
        }
        Calendar cal = Calendar.getInstance();
        Calendar gcal = (Calendar) CAL_GMT_US.clone();
        Calendar cal1 = Calendar.getInstance();
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        cal.set(1, year);
        cal.set(2, result[2] - 1);
        cal.set(5, result[3]);
        cal.set(11, result[4] - 1);
        cal.set(12, result[5] - 1);
        cal.set(13, result[6] - 1);
        cal.set(14, 0);
        gcal.set(1, year);
        gcal.set(2, result[2] - 1);
        gcal.set(5, result[3]);
        gcal.set(11, result[4] - 1);
        gcal.set(12, result[5] - 1);
        gcal.set(13, result[6] - 1);
        gcal.set(14, 0);
        long timeInMillis2 = cal.getTime().getTime();
        if ((result[11] & REGIONIDBIT) != 0) {
            int regionID = getHighOrderbits(result[11]) + getLowOrderbits(result[12]);
            TIMEZONETAB tzTab = getTIMEZONETAB(conn);
            if (tzTab.checkID(regionID)) {
                tzTab.updateTable(conn, regionID);
            }
            int offset = tzTab.getOffset(gcal, regionID);
            timeInMillis = timeInMillis2 + offset;
            TimeZone calTZ = cal.getTimeZone();
            TimeZone cal1TZ = cal1.getTimeZone();
            if (!calTZ.inDaylightTime(cal.getTime()) && cal1TZ.inDaylightTime(new Timestamp(timeInMillis))) {
                timeInMillis = cal1TZ instanceof SimpleTimeZone ? timeInMillis - ((SimpleTimeZone) cal1TZ).getDSTSavings() : timeInMillis - HOUR_MILLISECOND;
            }
            if (calTZ.inDaylightTime(cal.getTime()) && !cal1TZ.inDaylightTime(new Timestamp(timeInMillis))) {
                if (cal1TZ instanceof SimpleTimeZone) {
                    timeInMillis += ((SimpleTimeZone) calTZ).getDSTSavings();
                } else {
                    timeInMillis += HOUR_MILLISECOND;
                }
            }
        } else {
            cal.add(10, result[11] - OFFSET_HOUR);
            cal.add(12, result[12] - OFFSET_MINUTE);
            timeInMillis = cal.getTime().getTime();
        }
        Timestamp ts = new Timestamp(timeInMillis);
        long milliGMT = gcal.getTime().getTime();
        Calendar tcal = Calendar.getInstance();
        tcal.setTimeInMillis(milliGMT);
        Calendar tcal2 = Calendar.getInstance();
        tcal2.setTime(ts);
        boolean dst1 = tcal.getTimeZone().inDaylightTime(tcal.getTime());
        boolean dst2 = tcal2.getTimeZone().inDaylightTime(tcal2.getTime());
        if (dst1 && !dst2) {
            ts = new Timestamp(timeInMillis - tcal.getTimeZone().getDSTSavings());
        } else if (!dst1 && dst2) {
            ts = new Timestamp(timeInMillis + tcal2.getTimeZone().getDSTSavings());
        }
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        ts.setNanos(nanos);
        Timestamp timestamp = ts;
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th4) {
                    th.addSuppressed(th4);
                }
            } else {
                lock.close();
            }
        }
        return timestamp;
    }

    public static Timestamp toTimestamp2(Connection conn, byte[] timestamptz) throws SQLException {
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        Calendar gmtCal = (Calendar) CAL_GMT_US.clone();
        gmtCal.clear();
        gmtCal.set(1, year);
        gmtCal.set(2, result[2] - 1);
        gmtCal.set(5, result[3]);
        gmtCal.set(11, result[4] - 1);
        gmtCal.set(12, result[5] - 1);
        gmtCal.set(13, result[6] - 1);
        gmtCal.set(14, 0);
        long milliSec = gmtCal.getTime().getTime();
        Timestamp ts = new Timestamp(milliSec);
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        ts.setNanos(nanos);
        return ts;
    }

    static Timestamp toTimestampInSessionTimezone(Connection conn, byte[] timestamptz) throws SQLException {
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        Calendar gmtCal = (Calendar) CAL_GMT_US.clone();
        gmtCal.clear();
        gmtCal.set(1, year);
        gmtCal.set(2, result[2] - 1);
        gmtCal.set(5, result[3]);
        gmtCal.set(11, result[4] - 1);
        gmtCal.set(12, result[5] - 1);
        gmtCal.set(13, result[6] - 1);
        gmtCal.set(14, 0);
        Calendar sesscal = TIMESTAMPLTZ.getSessCalendar(conn);
        TIMESTAMPLTZ.TimeZoneAdjust(conn, gmtCal, sesscal);
        long milliSec = sesscal.getTime().getTime();
        Timestamp ts = new Timestamp(milliSec);
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        ts.setNanos(nanos);
        return ts;
    }

    public static String toString(Connection conn, byte[] timestamptz) throws SQLException {
        String regname;
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            int[] result = new int[13];
            for (int i = 0; i < 13; i++) {
                result[i] = timestamptz[i] & 255;
            }
            Calendar calUTC = (Calendar) CAL_GMT_US.clone();
            calUTC.set(1, TIMESTAMP.getJavaYear(result[0], result[1]));
            calUTC.set(2, result[2] - 1);
            calUTC.set(5, result[3]);
            calUTC.set(11, result[4] - 1);
            calUTC.set(12, result[5] - 1);
            calUTC.set(13, result[6] - 1);
            calUTC.set(14, 0);
            if ((result[11] & REGIONIDBIT) != 0) {
                int regionID = getHighOrderbits(result[11]) + getLowOrderbits(result[12]);
                TIMEZONETAB tzTab = intConn.getTIMEZONETAB();
                if (tzTab.checkID(regionID)) {
                    tzTab.updateTable(intConn, regionID);
                }
                regname = ZONEIDMAP.getRegion(regionID);
                int offset_gmt = tzTab.getOffset(calUTC, regionID);
                calUTC.add(14, offset_gmt);
            } else {
                int off_hour = result[11] - OFFSET_HOUR;
                int off_minute = result[12] - OFFSET_MINUTE;
                String regname2 = off_hour + ":";
                if (off_minute == 0) {
                    regname = regname2 + "00";
                } else {
                    regname = regname2 + "" + off_minute;
                }
                calUTC.add(10, off_hour);
                calUTC.add(12, off_minute);
            }
            int year = calUTC.get(1);
            if (calUTC.get(0) == 0) {
                year = -(year - 1);
            }
            int month = calUTC.get(2) + 1;
            int date = calUTC.get(5);
            int hour = calUTC.get(11);
            int minute = calUTC.get(12);
            int second = calUTC.get(13);
            int nanos = TIMESTAMP.getNanos(timestamptz, 7);
            String string = toString(year, month, date, hour, minute, second, nanos, regname);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return string;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public static OffsetDateTime toOffsetDateTime(Connection conn, byte[] timestamptz) throws SQLException, NullPointerException {
        ZoneOffset zOffset;
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        OffsetDateTime odtUTC = OffsetDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1, nanos, ZoneOffset.UTC);
        if ((result[11] & REGIONIDBIT) != 0) {
            int regionID = getHighOrderbits(result[11]) + getLowOrderbits(result[12]);
            OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
            TIMEZONETAB tzTab = intConn.getTIMEZONETAB();
            if (tzTab.checkID(regionID)) {
                tzTab.updateTable(intConn, regionID);
            }
            int offset_gmt = tzTab.getOffset(odtUTC.toInstant().toEpochMilli(), regionID);
            zOffset = ZoneOffset.ofTotalSeconds(offset_gmt / SECOND_MILLISECOND);
        } else {
            int off_hour = result[11] - OFFSET_HOUR;
            int off_minute = result[12] - OFFSET_MINUTE;
            zOffset = ZoneOffset.ofHoursMinutes(off_hour, off_minute);
        }
        OffsetDateTime retval = odtUTC.withOffsetSameInstant(zOffset);
        return retval;
    }

    private static OffsetDateTime toOffsetDateTime(byte[] timestamptz) throws SQLException {
        ZoneOffset zOffset;
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        OffsetDateTime odtUTC = OffsetDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1, nanos, ZoneOffset.UTC);
        if ((result[11] & REGIONIDBIT) != 0) {
            int regionID = getHighOrderbits(result[11]);
            String tzName = ZONEIDMAP.getRegion(regionID + getLowOrderbits(result[12]));
            ZoneId tzid = ZoneId.of(tzName);
            ZoneRules zRule = tzid.getRules();
            if (zRule.isFixedOffset()) {
                zOffset = zRule.getOffset(odtUTC.toInstant());
            } else {
                throw new SQLException("Timezone not supported: " + tzName);
            }
        } else {
            int off_hour = result[11] - OFFSET_HOUR;
            int off_minute = result[12] - OFFSET_MINUTE;
            zOffset = ZoneOffset.ofHoursMinutes(off_hour, off_minute);
        }
        OffsetDateTime retval = odtUTC.withOffsetSameInstant(zOffset);
        return retval;
    }

    public static final String toString(int year, int month, int day, int hours, int minutes, int seconds, int nanos, String regionName) {
        String stringRep = "" + year + "-" + toStr(month) + "-" + toStr(day) + " " + toStr(hours) + ":" + toStr(minutes) + ":" + toStr(seconds);
        if (nanos >= 0) {
            String nanoString = String.format("%09d", Integer.valueOf(nanos));
            char[] nanoChars = nanoString.toCharArray();
            int i = nanoChars.length;
            while (i > 1 && nanoChars[i - 1] == '0') {
                i--;
            }
            stringRep = stringRep + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + nanoString.substring(0, i);
        }
        if (regionName != null) {
            stringRep = stringRep + " " + regionName;
        }
        return stringRep;
    }

    private static final String toStr(int x) {
        return x < 10 ? "0" + x : Integer.toString(x);
    }

    public Timestamp timestampValue(Connection conn) throws SQLException {
        if (((OracleConnection) conn.unwrap(OracleConnection.class)).getTimestamptzInGmt()) {
            return toTimestamp2(conn, getBytes());
        }
        return toTimestamp(conn, getBytes());
    }

    @Override // oracle.jdbc.internal.OracleTimestampWithTimeZone
    public byte[] toBytes() {
        return getBytes();
    }

    public static byte[] toBytes(Connection conn, Date date) throws SQLException {
        return toBytes(conn, date, (Calendar) null);
    }

    public static byte[] toBytes(Connection conn, Date date, Calendar cal) throws SQLException {
        if (date == null) {
            return null;
        }
        byte[] result = new byte[13];
        Calendar cal1 = getWorkCal(conn, cal);
        cal1.setTime(date);
        if (OracleDriver.getSystemPropertyDateZeroTime()) {
            cal1.set(11, 0);
            cal1.set(12, 0);
            cal1.set(13, 0);
        }
        Calendar cal2 = doCalWork(conn, cal1, result);
        int year = TIMESTAMP.getOracleYear(cal2);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal2.get(2) + 1);
        result[3] = (byte) cal2.get(5);
        result[4] = (byte) (cal2.get(11) + 1);
        result[5] = (byte) (cal2.get(12) + 1);
        result[6] = (byte) (cal2.get(13) + 1);
        result[7] = 0;
        result[8] = 0;
        result[9] = 0;
        result[10] = 0;
        return result;
    }

    public static byte[] toBytes(Connection conn, Time time) throws SQLException {
        return toBytes(conn, time, (Calendar) null);
    }

    public static byte[] toBytes(Connection conn, Time time, Calendar cal) throws SQLException {
        int defaultYear;
        if (time == null) {
            return null;
        }
        byte[] result = new byte[13];
        Calendar cal1 = getWorkCal(conn, cal);
        cal1.setTime(time);
        if (((OracleConnection) conn.unwrap(OracleConnection.class)).getUse1900AsYearForTime()) {
            defaultYear = 1900;
        } else {
            defaultYear = 1970;
        }
        cal1.set(1, defaultYear);
        cal1.set(2, 0);
        cal1.set(5, 1);
        Calendar cal2 = doCalWork(conn, cal1, result);
        int year = TIMESTAMP.getOracleYear(cal2);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal2.get(2) + 1);
        result[3] = (byte) cal2.get(5);
        result[4] = (byte) (cal2.get(11) + 1);
        result[5] = (byte) (cal2.get(12) + 1);
        result[6] = (byte) (cal2.get(13) + 1);
        result[7] = 0;
        result[8] = 0;
        result[9] = 0;
        result[10] = 0;
        return result;
    }

    private static byte[] toBytes(Connection conn, Time time, ZoneId tzid) throws SQLException {
        int defaultYear;
        if (time == null) {
            return null;
        }
        byte[] result = new byte[13];
        Calendar gmtCal = (Calendar) CAL_GMT_US.clone();
        gmtCal.setTime(time);
        if (((OracleConnection) conn.unwrap(OracleConnection.class)).getUse1900AsYearForTime()) {
            defaultYear = 1900;
        } else {
            defaultYear = 1970;
        }
        gmtCal.set(1, defaultYear);
        gmtCal.set(2, 0);
        gmtCal.set(5, 1);
        Calendar cal2 = doCalWork(conn, gmtCal, result);
        int year = TIMESTAMP.getOracleYear(cal2);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal2.get(2) + 1);
        result[3] = (byte) cal2.get(5);
        result[4] = (byte) (cal2.get(11) + 1);
        result[5] = (byte) (cal2.get(12) + 1);
        result[6] = (byte) (cal2.get(13) + 1);
        result[7] = 0;
        result[8] = 0;
        result[9] = 0;
        result[10] = 0;
        String timeZone = tzid.getId();
        int regionId = ZONEIDMAP.getID(timeZone);
        if (!ZONEIDMAP.isValidID(regionId)) {
            ZoneRules zRule = tzid.getRules();
            if (zRule.isFixedOffset()) {
                ZoneOffset zOff = zRule.getOffset(time.toInstant());
                int offset = zOff.getTotalSeconds();
                result[11] = (byte) ((offset / HOUR_SECOND) + OFFSET_HOUR);
                result[12] = (byte) (((offset % HOUR_SECOND) / MINUTE_SECOND) + OFFSET_MINUTE);
            } else {
                throw new SQLException("Timezone not supported: " + timeZone);
            }
        } else {
            result[11] = (byte) setHighOrderbits(regionId);
            result[11] = (byte) (result[11] | REGIONIDBIT);
            result[12] = (byte) setLowOrderbits(regionId);
        }
        return result;
    }

    public static byte[] toBytes(Connection conn, Timestamp timestamp) throws SQLException {
        return toBytes(conn, timestamp, (Calendar) null);
    }

    public static byte[] toBytes(Connection conn, Timestamp timestamp, Calendar cal) throws SQLException {
        if (timestamp == null) {
            return null;
        }
        Calendar cal1 = getWorkCal(conn, cal);
        cal1.setTime(timestamp);
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) (cal2.get(2) + 1), (byte) cal2.get(5), (byte) (cal2.get(11) + 1), (byte) (cal2.get(12) + 1), (byte) (cal2.get(13) + 1), (byte) (nanos >> 24), (byte) ((nanos >> 16) & 255), (byte) ((nanos >> 8) & 255), (byte) (nanos & 255), 0, 0};
        Calendar cal2 = doCalWork(conn, cal1, result);
        int year = TIMESTAMP.getOracleYear(cal2);
        int nanos = timestamp.getNanos();
        return result;
    }

    public static byte[] toBytes(Connection conn, Timestamp timestamp, ZoneId tzid) throws SQLException {
        return toBytes(timestamp, tzid, false);
    }

    private static byte[] toBytes(Timestamp timestamp, ZoneId tzid, boolean useZoneCal) throws SQLException {
        byte[] result = new byte[13];
        Calendar gmtCal = (Calendar) CAL_GMT_US.clone();
        if (useZoneCal) {
            Calendar zoneCal = Calendar.getInstance(TimeZone.getTimeZone(tzid));
            zoneCal.setTime(timestamp);
            gmtCal.setTime(zoneCal.getTime());
        } else {
            gmtCal.setTime(timestamp);
        }
        int year = TIMESTAMP.getOracleYear(gmtCal);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (gmtCal.get(2) + 1);
        result[3] = (byte) gmtCal.get(5);
        result[4] = (byte) (gmtCal.get(11) + 1);
        result[5] = (byte) (gmtCal.get(12) + 1);
        result[6] = (byte) (gmtCal.get(13) + 1);
        result[7] = (byte) (timestamp.getNanos() >> 24);
        result[8] = (byte) ((timestamp.getNanos() >> 16) & 255);
        result[9] = (byte) ((timestamp.getNanos() >> 8) & 255);
        result[10] = (byte) (timestamp.getNanos() & 255);
        String timeZone = tzid.getId();
        int regionId = ZONEIDMAP.getID(timeZone);
        if (!ZONEIDMAP.isValidID(regionId)) {
            ZoneRules zRule = tzid.getRules();
            if (zRule.isFixedOffset()) {
                ZoneOffset zOff = zRule.getOffset(timestamp.toInstant());
                int offset = zOff.getTotalSeconds();
                result[11] = (byte) ((offset / HOUR_SECOND) + OFFSET_HOUR);
                result[12] = (byte) (((offset % HOUR_SECOND) / MINUTE_SECOND) + OFFSET_MINUTE);
            } else {
                throw new SQLException("Timezone not supported: " + timeZone);
            }
        } else {
            result[11] = (byte) setHighOrderbits(regionId);
            result[11] = (byte) (result[11] | REGIONIDBIT);
            result[12] = (byte) setLowOrderbits(regionId);
        }
        return result;
    }

    public static byte[] toBytes(Connection conn, DATE date) throws SQLException {
        if (date == null) {
            return null;
        }
        Calendar cal = getWorkCal(conn, null);
        cal.setTime(DATE.toDate(date.toBytes()));
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) (cal1.get(2) + 1), (byte) cal1.get(5), (byte) (cal1.get(11) + 1), (byte) (cal1.get(12) + 1), (byte) (cal1.get(13) + 1), 0, 0, 0, 0, 0, 0};
        Calendar cal1 = doCalWork(conn, cal, result);
        int year = TIMESTAMP.getOracleYear(cal1);
        return result;
    }

    public static byte[] toBytes(Connection conn, String str) throws SQLException, NumberFormatException {
        byte[] result;
        try {
            result = toBytes(conn, Timestamp.valueOf(str));
        } catch (IllegalArgumentException e) {
            result = parseTimestampTz(conn, str);
        }
        return result;
    }

    public static byte[] toBytes(Connection conn, String str, Calendar cal) throws SQLException, NumberFormatException {
        Calendar cal1;
        Calendar local = (Calendar) CAL_GMT_US.clone();
        Timestamp ts = parseTimestamp(str);
        local.setTime(ts);
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.set(1, local.get(1));
        cal1.set(2, local.get(2));
        cal1.set(5, local.get(5));
        cal1.set(11, local.get(11));
        cal1.set(12, local.get(12));
        cal1.set(13, local.get(13));
        cal1.set(14, local.get(14));
        int nano = ts.getNanos();
        Timestamp ts2 = new Timestamp(cal1.getTime().getTime());
        ts2.setNanos(nano);
        return toBytes(conn, ts2, cal);
    }

    @Override // oracle.sql.Datum
    public String stringValue(Connection conn) throws SQLException {
        return toString(conn, getBytes());
    }

    public static byte[] toBytes(OffsetDateTime odt) throws SQLException {
        if (odt == null) {
            return null;
        }
        OffsetDateTime odtutc = odt.withOffsetSameInstant(ZoneOffset.UTC);
        int year = TIMESTAMP.getOracleYear(odtutc.getYear());
        int month = odtutc.getMonthValue();
        int date = odtutc.getDayOfMonth();
        int hour = odtutc.getHour();
        int minute = odtutc.getMinute();
        int second = odtutc.getSecond();
        int nanos = odtutc.getNano();
        int offset = odt.getOffset().getTotalSeconds();
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) month, (byte) date, (byte) (hour + 1), (byte) (minute + 1), (byte) (second + 1), (byte) (nanos >> 24), (byte) ((nanos >> 16) & 255), (byte) ((nanos >> 8) & 255), (byte) (nanos & 255), (byte) ((offset / HOUR_SECOND) + OFFSET_HOUR), (byte) (((offset % HOUR_SECOND) / MINUTE_SECOND) + OFFSET_MINUTE)};
        return result;
    }

    public static byte[] toBytes(ZonedDateTime zdt) throws SQLException {
        if (zdt == null) {
            return null;
        }
        byte[] result = new byte[13];
        OffsetDateTime odtutc = zdt.toOffsetDateTime().withOffsetSameInstant(ZoneOffset.UTC);
        int year = TIMESTAMP.getOracleYear(odtutc.getYear());
        int month = odtutc.getMonthValue();
        int date = odtutc.getDayOfMonth();
        int hour = odtutc.getHour();
        int minute = odtutc.getMinute();
        int second = odtutc.getSecond();
        int nanos = odtutc.getNano();
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) month;
        result[3] = (byte) date;
        result[4] = (byte) (hour + 1);
        result[5] = (byte) (minute + 1);
        result[6] = (byte) (second + 1);
        result[7] = (byte) (nanos >> 24);
        result[8] = (byte) ((nanos >> 16) & 255);
        result[9] = (byte) ((nanos >> 8) & 255);
        result[10] = (byte) (nanos & 255);
        ZoneId tzid = zdt.getZone();
        String timeZone = tzid.getId();
        int regionId = ZONEIDMAP.getID(timeZone);
        if (!ZONEIDMAP.isValidID(regionId)) {
            ZoneRules zRule = tzid.getRules();
            if (zRule.isFixedOffset()) {
                ZoneOffset zOff = zRule.getOffset(zdt.toInstant());
                int offset = zOff.getTotalSeconds();
                result[11] = (byte) ((offset / HOUR_SECOND) + OFFSET_HOUR);
                result[12] = (byte) (((offset % HOUR_SECOND) / MINUTE_SECOND) + OFFSET_MINUTE);
            } else {
                throw new SQLException("Timezone not supported: " + timeZone);
            }
        } else {
            result[11] = (byte) setHighOrderbits(regionId);
            result[11] = (byte) (result[11] | REGIONIDBIT);
            result[12] = (byte) setLowOrderbits(regionId);
        }
        return result;
    }

    public static byte[] toBytes(Connection conn, LocalDateTime ldt) throws SQLException {
        if (ldt == null) {
            return null;
        }
        ZoneId zId = ((OracleConnection) conn.unwrap(OracleConnection.class)).getSessionZoneId();
        if (zId == null) {
            zId = ZoneId.systemDefault();
        }
        return toBytes(ZonedDateTime.of(ldt, zId));
    }

    public static byte[] toBytes(OffsetTime ot) throws SQLException {
        if (ot == null) {
            return null;
        }
        return toBytes(ot.atDate(LocalDate.of(1970, 1, 1)));
    }

    public OffsetDateTime offsetDateTimeValue() throws SQLException {
        return toOffsetDateTime(getBytes());
    }

    public OffsetDateTime offsetDateTimeValue(Connection conn) throws SQLException {
        return toOffsetDateTime(conn, getBytes());
    }

    public ZonedDateTime zonedDateTimeValue() throws SQLException {
        return toZonedDateTime(getBytes());
    }

    public LocalDateTime localDateTimeValue() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    public Date dateValue(Connection conn) throws SQLException {
        if (((OracleConnection) conn.unwrap(OracleConnection.class)).getTimestamptzInGmt()) {
            return toDate2(conn, getBytes());
        }
        return toDate(conn, getBytes());
    }

    public Time timeValue(Connection conn) throws SQLException {
        return toTime(conn, getBytes());
    }

    public TimeZone getTimeZone() throws SQLException {
        return getTimeZone(shareBytes());
    }

    private static TimeZone getTimeZone(byte[] bits) throws SQLException {
        String regname;
        String tzname;
        if ((bits[11] & REGIONIDBIT) != 0) {
            int regionID = getHighOrderbits(bits[11]);
            tzname = ZONEIDMAP.getRegion(regionID + getLowOrderbits(bits[12]));
        } else {
            int off_hour = bits[11] - OFFSET_HOUR;
            int off_minute = bits[12] - OFFSET_MINUTE;
            String regname2 = off_hour + ":";
            if (off_minute == 0) {
                regname = regname2 + "00";
            } else {
                regname = regname2 + "" + off_minute;
            }
            tzname = "GMT" + (off_hour >= 0 ? "+" : "") + regname;
        }
        return TimeZone.getTimeZone(tzname);
    }

    public static TIMESTAMPTZ of(ZonedDateTime zdt) throws SQLException {
        return new TIMESTAMPTZ(toBytes(zdt));
    }

    public ZonedDateTime toZonedDateTime() throws SQLException {
        return toZonedDateTime(getBytes());
    }

    public LocalDateTime toLocalDateTime() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.time.LocalDateTime] */
    public static LocalDateTime toLocalDateTime(byte[] timestamptz) throws SQLException {
        return toZonedDateTime(timestamptz).toLocalDateTime();
    }

    /* JADX WARN: Type inference failed for: r0v26, types: [java.time.ZonedDateTime] */
    public static ZonedDateTime toZonedDateTime(byte[] timestamptz) throws SQLException {
        ZoneId zId;
        int[] result = new int[13];
        for (int i = 0; i < 13; i++) {
            result[i] = timestamptz[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        int nanos = TIMESTAMP.getNanos(timestamptz, 7);
        ZonedDateTime zdtUTC = ZonedDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1, nanos, ZoneId.of("UTC"));
        if ((result[11] & REGIONIDBIT) != 0) {
            int regionID = getHighOrderbits(result[11]);
            zId = ZoneId.of(ZONEIDMAP.getRegion(regionID + getLowOrderbits(result[12])));
        } else {
            int off_hour = result[11] - OFFSET_HOUR;
            int off_minute = result[12] - OFFSET_MINUTE;
            zId = ZoneId.of(ZoneOffset.ofHoursMinutes(off_hour, off_minute).toString());
        }
        return zdtUTC.withZoneSameInstant(zId);
    }

    public OffsetTime toOffsetTime() throws SQLException {
        return toOffsetDateTime(getBytes()).toOffsetTime();
    }

    public static TIMESTAMPTZ of(OffsetDateTime odt) throws SQLException {
        return new TIMESTAMPTZ(odt);
    }

    public static TIMESTAMPTZ of(Connection conn, LocalDateTime ldt) throws SQLException {
        return new TIMESTAMPTZ(conn, ldt);
    }

    public OffsetDateTime toOffsetDateTime() throws SQLException {
        return toOffsetDateTime(getBytes());
    }

    private static Calendar getWorkCal(Connection conn, Calendar cal) {
        Calendar cal1;
        if (cal == null) {
            String locTimeZone = ((oracle.jdbc.OracleConnection) conn).getSessionTimeZone();
            if (locTimeZone != null) {
                cal1 = Calendar.getInstance(TimeZone.getTimeZone(locTimeZone));
            } else {
                cal1 = Calendar.getInstance();
            }
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        return cal1;
    }

    private static Calendar doCalWork(Connection conn, Calendar cal, byte[] result) throws SQLException {
        int offset;
        OracleConnection intConn = (OracleConnection) conn.unwrap(OracleConnection.class);
        Monitor.CloseableLock lock = intConn.acquireCloseableLock();
        Throwable th = null;
        try {
            if (cal.getTimeZone().getID() == "Custom") {
                offset = cal.getTimeZone().getRawOffset();
                result[11] = (byte) ((offset / HOUR_MILLISECOND) + OFFSET_HOUR);
                result[12] = (byte) (((offset % HOUR_MILLISECOND) / MINUTE_MILLISECOND) + OFFSET_MINUTE);
            } else {
                String timeZone = cal.getTimeZone().getID();
                int regionId = ZONEIDMAP.getID(timeZone);
                if (!ZONEIDMAP.isValidID(regionId)) {
                    if (cal.getTimeZone().useDaylightTime()) {
                        throw new SQLException("Timezone not supported: " + timeZone);
                    }
                    offset = cal.getTimeZone().getRawOffset();
                    result[11] = (byte) ((offset / HOUR_MILLISECOND) + OFFSET_HOUR);
                    result[12] = (byte) (((offset % HOUR_MILLISECOND) / MINUTE_MILLISECOND) + OFFSET_MINUTE);
                } else {
                    TIMEZONETAB tzTab = getTIMEZONETAB(conn);
                    if (tzTab.checkID(regionId)) {
                        tzTab.updateTable(conn, regionId);
                    }
                    OffsetDST trans_data = new OffsetDST();
                    byte olap = tzTab.getLocalOffset(cal, regionId, trans_data);
                    offset = trans_data.getOFFSET();
                    boolean overlap = cal.getTimeZone().inDaylightTime(cal.getTime());
                    if (overlap && olap == 1) {
                        if (trans_data.getDSTFLAG() == 0) {
                            offset += HOUR_MILLISECOND;
                        } else {
                            throw new SQLException();
                        }
                    }
                    result[11] = (byte) setHighOrderbits(regionId);
                    result[11] = (byte) (result[11] | REGIONIDBIT);
                    result[12] = (byte) setLowOrderbits(regionId);
                }
            }
            Calendar gmtCal = (Calendar) CAL_GMT_US.clone();
            gmtCal.set(0, cal.get(0));
            gmtCal.set(1, cal.get(1));
            gmtCal.set(2, cal.get(2));
            gmtCal.set(5, cal.get(5));
            gmtCal.set(11, cal.get(11));
            gmtCal.set(12, cal.get(12));
            gmtCal.set(13, cal.get(13));
            gmtCal.add(14, (-1) * offset);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return gmtCal;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static byte[] initTimestamptz() {
        Calendar cal = Calendar.getInstance();
        String timeZone = cal.getTimeZone().getID();
        byte[] tmp = {119, -86, 1, 1, 1, 1, 1, 0, 0, 0, 0, (byte) setHighOrderbits(ZONEIDMAP.getID(timeZone)), 0};
        tmp[11] = (byte) (tmp[11] | REGIONIDBIT);
        tmp[12] = (byte) setLowOrderbits(ZONEIDMAP.getID(timeZone));
        return tmp;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return this;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        Timestamp[] ts = new Timestamp[arraySize];
        return ts;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> cls) {
        if (cls.getName().compareTo("java.sql.Date") == 0 || cls.getName().compareTo("java.sql.Time") == 0 || cls.getName().compareTo("java.sql.Timestamp") == 0 || cls.getName().compareTo("java.time.LocalDateTime") == 0 || cls.getName().compareTo("java.time.OffsetDateTime") == 0 || cls.getName().compareTo("java.time.OffsetTime") == 0 || cls.getName().compareTo("java.time.ZonedDateTime") == 0 || cls.getName().compareTo("java.lang.String") == 0) {
            return true;
        }
        return false;
    }

    private static Timestamp parseTimestamp(String timeStr) throws SQLException, NumberFormatException {
        int second;
        if (timeStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        String sTemp = timeStr.trim();
        int spacePos = sTemp.indexOf(32);
        if (spacePos == -1) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        String sDate = sTemp.substring(0, spacePos).trim();
        String sTime = sTemp.substring(spacePos + 1).trim();
        if ((sDate == null) | (sTime == null)) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        int posH1 = sDate.indexOf(45);
        int posH2 = sDate.indexOf(45, posH1 + 1);
        if (posH1 < 1 || posH2 < 1 || posH2 == sDate.length()) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        String sYear = sDate.substring(0, posH1);
        String sMonth = sDate.substring(posH1 + 1, posH2);
        String sDay = sDate.substring(posH2 + 1);
        if (sYear.length() != 4 || sMonth.length() != 2 || sDay.length() != 2) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        int year = Integer.parseInt(sYear);
        int month = Integer.parseInt(sMonth) - 1;
        int day = Integer.parseInt(sDay);
        int posC1 = sTime.indexOf(58);
        int posC2 = sTime.indexOf(58, posC1 + 1);
        if (posC1 < 1 || posC2 < 1 || posC2 == sTime.length()) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        String sHour = sTime.substring(0, posC1);
        String sMinute = sTime.substring(posC1 + 1, posC2);
        if (sHour.length() != 2 || sMinute.length() != 2) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        int hour = Integer.parseInt(sHour);
        int minute = Integer.parseInt(sMinute);
        int nanos = 0;
        int posDot = sTime.indexOf(46, posC2 + 1);
        if (posDot == -1) {
            second = Integer.parseInt(sTime.substring(posC2 + 1));
        } else if (posDot > 0 && posDot < sTime.length() - 1) {
            second = Integer.parseInt(sTime.substring(posC2 + 1, posDot));
            String sNanos = sTime.substring(posDot + 1);
            if (sNanos.length() > "000000000".length() || !Character.isDigit(sNanos.charAt(0))) {
                throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
            }
            nanos = Integer.parseInt(sNanos + "000000000".substring(0, "000000000".length() - sNanos.length()));
        } else {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        Calendar cal = (Calendar) CAL_GMT_US.clone();
        cal.set(1, year);
        cal.set(2, month);
        cal.set(5, day);
        cal.set(11, hour);
        cal.set(12, minute);
        cal.set(13, second);
        cal.set(14, 0);
        Timestamp result = new Timestamp(cal.getTime().getTime());
        result.setNanos(nanos);
        return result;
    }

    private static byte[] parseTimestampTz(Connection conn, String timeStr) throws SQLException, NumberFormatException {
        String sTz;
        int second;
        Calendar cal;
        if (timeStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The value is null.").fillInStackTrace());
        }
        String sTemp = timeStr.trim();
        int spacePos = sTemp.indexOf(32);
        if (spacePos == -1) {
            throw ((SQLException) DatabaseError.createSqlException(68, "There is a space expected after date. (yyyy-mm-dd hh:mm:ss[.fffffffff])").fillInStackTrace());
        }
        String sDate = sTemp.substring(0, spacePos).trim();
        String sTime = sTemp.substring(spacePos + 1).trim();
        if ((sDate == null) | (sTime == null)) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The value of date/time is null.").fillInStackTrace());
        }
        boolean nYear = false;
        if (sDate.charAt(0) == '-') {
            nYear = true;
            if (sDate.length() > 1) {
                sDate = sDate.substring(1);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(68, "The date value is invalid.").fillInStackTrace());
            }
        }
        int posH1 = sDate.indexOf(45);
        int posH2 = sDate.indexOf(45, posH1 + 1);
        if (posH1 < 1 || posH2 < 1 || posH2 == sDate.length()) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The date should have two hyphens and the expected format is yyyy-mm-dd.").fillInStackTrace());
        }
        String sYear = sDate.substring(0, posH1);
        String sMonth = sDate.substring(posH1 + 1, posH2);
        String sDay = sDate.substring(posH2 + 1);
        if (sYear.length() != 4 || sMonth.length() != 2 || sDay.length() != 2) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The value of year/month/date is wrong. The format must be yyyy-mm-dd.").fillInStackTrace());
        }
        int year = Integer.parseInt(sYear);
        int month = Integer.parseInt(sMonth) - 1;
        int day = Integer.parseInt(sDay);
        if (nYear) {
            year = -year;
        }
        int posC1 = sTime.indexOf(58);
        int posC2 = sTime.indexOf(58, posC1 + 1);
        int spacePos2 = sTime.indexOf(32);
        if (spacePos2 != -1) {
            sTz = sTime.substring(spacePos2 + 1).trim();
            sTime = sTime.substring(0, spacePos2).trim();
        } else {
            sTz = null;
        }
        if (posC1 < 1 || posC2 < 1 || posC2 == sTime.length()) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The format of time is expected to be hh:mm:ss[.fffffffff]").fillInStackTrace());
        }
        String sHour = sTime.substring(0, posC1);
        String sMinute = sTime.substring(posC1 + 1, posC2);
        if (sHour.length() != 2 || sMinute.length() != 2) {
            throw ((SQLException) DatabaseError.createSqlException(68, "The value of hour/minute is wrong. The format must be hh:mm:ss").fillInStackTrace());
        }
        int hour = Integer.parseInt(sHour);
        int minute = Integer.parseInt(sMinute);
        int nanos = 0;
        int posDot = sTime.indexOf(46, posC2 + 1);
        if (posDot == -1) {
            second = Integer.parseInt(sTime.substring(posC2 + 1));
        } else if (posDot > 0 && posDot < sTime.length() - 1) {
            second = Integer.parseInt(sTime.substring(posC2 + 1, posDot));
            String sNanos = sTime.substring(posDot + 1);
            if (sNanos.length() > "000000000".length() || !Character.isDigit(sNanos.charAt(0))) {
                throw ((SQLException) DatabaseError.createSqlException(68, "The number of digits of nanoseconds is more than 9 or it has non-numeric character.").fillInStackTrace());
            }
            nanos = Integer.parseInt(sNanos + "000000000".substring(0, "000000000".length() - sNanos.length()));
        } else {
            throw ((SQLException) DatabaseError.createSqlException(68, "The number of digits of nanoseconds is zero.").fillInStackTrace());
        }
        Calendar cal2 = null;
        if (sTz != null) {
            char cTz = sTz.charAt(0);
            if (cTz == '-') {
                sTz = "GMT" + sTz;
            } else if (Character.isDigit(cTz)) {
                sTz = "GMT+" + sTz;
            }
            cal2 = Calendar.getInstance(TimeZone.getTimeZone(sTz));
        }
        if (cal2 == null) {
            cal = (Calendar) CAL_GMT_US.clone();
        } else {
            cal = cal2;
        }
        cal.set(1, year);
        cal.set(2, month);
        cal.set(5, day);
        cal.set(11, hour);
        cal.set(12, minute);
        cal.set(13, second);
        cal.set(14, 0);
        Timestamp result = new Timestamp(cal.getTime().getTime());
        result.setNanos(nanos);
        return toBytes(conn, result, cal2);
    }

    private static int setHighOrderbits(int ID) {
        return (ID & 8128) >> 6;
    }

    private static int setLowOrderbits(int ID) {
        return (ID & 63) << 2;
    }

    private static int getHighOrderbits(int hour) {
        return (hour & 127) << 6;
    }

    private static int getLowOrderbits(int minute) {
        return (minute & 252) >> 2;
    }

    static TIMEZONETAB getTIMEZONETAB(Connection conn) throws SQLException {
        OracleConnection oconn = (OracleConnection) conn.unwrap(OracleConnection.class);
        return oconn.getTIMEZONETAB();
    }
}
