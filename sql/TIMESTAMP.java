package oracle.sql;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.TimeZone;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.OracleTimestamp;

/* loaded from: ojdbc8.jar:oracle/sql/TIMESTAMP.class */
public class TIMESTAMP extends Datum implements Serializable, OracleTimestamp {
    static final int CENTURY_DEFAULT = 119;
    static final int DECADE_DEFAULT = 100;
    static final int MONTH_DEFAULT = 1;
    static final int DAY_DEFAULT = 1;
    static final int DECADE_INIT = 170;
    static final int JAVA_YEAR = 1970;
    static final int JAVA_MONTH = 0;
    static final int JAVA_DATE = 1;
    public static final int SIZE_DATE = 7;
    public static final int SIZE_TIMESTAMP = 11;
    public static final int SIZE_TIMESTAMP_NOFRAC = 7;
    static final int SIZE_TIMESTAMPTZ = 13;
    static final int MINYEAR = -4712;
    static final int MAXYEAR = 9999;
    static final int JANMONTH = 1;
    static final int DECMONTH = 12;
    static final int MINDAYS = 1;
    static final int MAXDAYS = 31;
    static final int MINHOURS = 1;
    static final int MAXHOURS = 24;
    static final int MINMINUTES = 1;
    static final int MAXMINUTES = 60;
    static final int MINSECONDS = 1;
    static final int MAXSECONDS = 60;
    static final int[] daysInMonth = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    static final long serialVersionUID = -7964732752952728545L;

    public TIMESTAMP() {
        super(initTimestamp());
    }

    public TIMESTAMP(byte[] timestamp) {
        super(timestamp);
    }

    public TIMESTAMP(Time time) {
        super(toBytes(time));
    }

    public TIMESTAMP(Date date) {
        super(toBytes(date));
    }

    public TIMESTAMP(Timestamp timestamp) {
        super(toBytes(timestamp));
    }

    public TIMESTAMP(Timestamp timestamp, Calendar cal) {
        super(toBytes(timestamp, cal));
    }

    public TIMESTAMP(DATE date) {
        super(toBytes(date));
    }

    public static final int getNanos(byte[] buffer, int off) {
        int nanos = (buffer[off] & 255) << 24;
        return nanos | ((buffer[off + 1] & 255) << 16) | ((buffer[off + 2] & 255) << 8) | (buffer[off + 3] & 255 & 255);
    }

    public TIMESTAMP(String str) {
        super(toBytes(str));
    }

    public TIMESTAMP(OffsetDateTime odt) {
        super(toBytes(odt));
    }

    public TIMESTAMP(ZonedDateTime zdt) {
        super(toBytes(zdt));
    }

    public TIMESTAMP(LocalDateTime ldt) {
        super(toBytes(ldt));
    }

    public TIMESTAMP(LocalTime lt) {
        super(toBytes(lt));
    }

    public TIMESTAMP(LocalDate ld) {
        super(toBytes(ld));
    }

    public TIMESTAMP(OffsetTime ot) {
        super(toBytes(ot));
    }

    public static Date toDate(byte[] timestamp) throws SQLException {
        int[] result;
        int arrlength = timestamp.length;
        if (arrlength == 11) {
            result = new int[11];
        } else {
            result = new int[7];
        }
        for (int i = 0; i < timestamp.length; i++) {
            result[i] = timestamp[i] & 255;
        }
        int year = getJavaYear(result[0], result[1]);
        Calendar cal = Calendar.getInstance();
        cal.set(1, year);
        cal.set(2, result[2] - 1);
        cal.set(5, result[3]);
        cal.set(11, result[4] - 1);
        cal.set(12, result[5] - 1);
        cal.set(13, result[6] - 1);
        int tsmillis = 0;
        if (arrlength == 11) {
            tsmillis = getNanos(timestamp, 7) / 1000000;
        }
        cal.set(14, tsmillis);
        long millis = cal.getTimeInMillis();
        return new Date(millis);
    }

    public static Time toTime(byte[] timestamp) throws SQLException {
        int hour = timestamp[4] & 255;
        int minute = timestamp[5] & 255;
        int second = timestamp[6] & 255;
        Calendar cal = Calendar.getInstance();
        cal.set(1, JAVA_YEAR);
        cal.set(2, 0);
        cal.set(5, 1);
        cal.set(11, hour - 1);
        cal.set(12, minute - 1);
        cal.set(13, second - 1);
        cal.set(14, 0);
        return new Time(cal.getTime().getTime());
    }

    public static Timestamp toTimestamp(byte[] timestamp) throws SQLException {
        return toTimestamp(timestamp, null);
    }

    public static Timestamp toTimestamp(byte[] timestamp, Calendar cal) throws SQLException {
        int[] result;
        Calendar cal1;
        int arrlength = timestamp.length;
        if (arrlength == 0) {
            return null;
        }
        if (arrlength == 11) {
            result = new int[11];
        } else {
            result = new int[7];
        }
        for (int i = 0; i < timestamp.length; i++) {
            result[i] = timestamp[i] & 255;
        }
        int year = getJavaYear(result[0], result[1]);
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.set(1, year);
        cal1.set(2, result[2] - 1);
        cal1.set(5, result[3]);
        cal1.set(11, result[4] - 1);
        cal1.set(12, result[5] - 1);
        cal1.set(13, result[6] - 1);
        cal1.set(14, 0);
        long millis = cal1.getTime().getTime();
        Timestamp ts = new Timestamp(millis);
        int nanos = 0;
        if (arrlength == 11) {
            nanos = getNanos(timestamp, 7);
        }
        ts.setNanos(nanos);
        return ts;
    }

    public static DATE toDATE(byte[] timestamp) throws SQLException {
        byte[] date = new byte[7];
        System.arraycopy(timestamp, 0, date, 0, 7);
        return new DATE(date);
    }

    @Override // oracle.sql.Datum
    public Timestamp timestampValue() throws SQLException {
        return toTimestamp(getBytes());
    }

    @Override // oracle.sql.Datum
    public Timestamp timestampValue(Calendar cal) throws SQLException {
        return toTimestamp(getBytes(), cal);
    }

    public static String toString(byte[] bytes) {
        if (bytes.length == 0) {
            return null;
        }
        int[] result = new int[bytes.length];
        for (int i = 0; i < bytes.length; i++) {
            if (bytes[i] < 0) {
                result[i] = bytes[i] + 256;
            } else {
                result[i] = bytes[i];
            }
        }
        int year = getJavaYear(result[0], result[1]);
        int month = result[2];
        int day = result[3];
        int hours = result[4] - 1;
        int minutes = result[5] - 1;
        int seconds = result[6] - 1;
        int nanos = 0;
        if (bytes.length > 7) {
            nanos = getNanos(bytes, 7);
        }
        return TIMESTAMPTZ.toString(year, month, day, hours, minutes, seconds, nanos, null);
    }

    @Override // oracle.jdbc.internal.OracleTimestamp
    public byte[] toBytes() {
        return getBytes();
    }

    public static byte[] toBytes(Time time) {
        if (time == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        int nanos = cal.get(14) * 1000000;
        byte[] result = new byte[nanos > 0 ? 11 : 7];
        result[0] = 119;
        result[1] = -86;
        result[2] = 1;
        result[3] = 1;
        result[4] = (byte) (cal.get(11) + 1);
        result[5] = (byte) (cal.get(12) + 1);
        result[6] = (byte) (cal.get(13) + 1);
        if (nanos > 0) {
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        }
        return result;
    }

    public static byte[] toBytes(Date date) {
        if (date == null) {
            return null;
        }
        byte[] result = new byte[7];
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = getOracleYear(cal);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal.get(2) + 1);
        result[3] = (byte) cal.get(5);
        if (!OracleDriver.getSystemPropertyDateZeroTime()) {
            result[4] = (byte) (cal.get(11) + 1);
            result[5] = (byte) (cal.get(12) + 1);
            result[6] = (byte) (cal.get(13) + 1);
        } else {
            result[4] = 1;
            result[5] = 1;
            result[6] = 1;
        }
        return result;
    }

    public static byte[] toBytes(Timestamp timestamp) {
        return toBytes(timestamp, null);
    }

    public static byte[] toBytes(Timestamp timestamp, Calendar cal) {
        byte[] result;
        Calendar cal1;
        if (timestamp == null) {
            return null;
        }
        int nanos = timestamp.getNanos();
        if (nanos == 0) {
            result = new byte[7];
        } else {
            result = new byte[11];
        }
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.setTime(timestamp);
        int year = getOracleYear(cal1);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal1.get(2) + 1);
        result[3] = (byte) cal1.get(5);
        result[4] = (byte) (cal1.get(11) + 1);
        result[5] = (byte) (cal1.get(12) + 1);
        result[6] = (byte) (cal1.get(13) + 1);
        if (nanos != 0) {
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        }
        return result;
    }

    public static byte[] toBytes(DATE date) {
        if (date == null) {
            return null;
        }
        byte[] result = new byte[7];
        System.arraycopy(date.getBytes(), 0, result, 0, 7);
        return result;
    }

    public static byte[] toBytes(String str) {
        return toBytes(Timestamp.valueOf(str));
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return timestampValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        Timestamp[] ts = new Timestamp[arraySize];
        return ts;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> cls) {
        if (cls == Date.class || cls == Time.class || cls == Timestamp.class || cls == LocalDateTime.class || cls == LocalDate.class || cls == LocalTime.class || cls == String.class) {
            return true;
        }
        return false;
    }

    public static TIMESTAMP TimeZoneConvert(Connection conn, TIMESTAMP tstamp, TimeZone tz1, TimeZone tz2) throws SQLException {
        int[] result;
        byte[] timestamp = tstamp.getBytes();
        int arrlength = timestamp.length;
        if (arrlength == 11) {
            result = new int[11];
        } else {
            result = new int[7];
        }
        for (int i = 0; i < arrlength; i++) {
            result[i] = timestamp[i] & 255;
        }
        int year = getJavaYear(result[0], result[1]);
        int month = result[2] - 1;
        int day = result[3];
        int hours = result[4] - 1;
        int minutes = result[5] - 1;
        int seconds = result[6] - 1;
        Calendar cal1 = Calendar.getInstance(tz1);
        cal1.set(1, year);
        cal1.set(2, month);
        cal1.set(5, day);
        cal1.set(11, hours);
        cal1.set(12, minutes);
        cal1.set(13, seconds);
        cal1.set(14, 0);
        long millis = cal1.getTimeInMillis();
        Timestamp ts = new Timestamp(millis);
        int nanos = 0;
        if (arrlength == 11) {
            nanos = getNanos(timestamp, 7);
        }
        ts.setNanos(nanos);
        Calendar cal2 = Calendar.getInstance(tz2);
        return new TIMESTAMP(ts, cal2);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        return toString(getBytes());
    }

    public String toString() {
        return stringValue();
    }

    public static int getOracleYear(Calendar cal) {
        int year = cal.get(1);
        if (cal.get(0) == 0) {
            year = -year;
        }
        if (year < MINYEAR || year > MAXYEAR) {
            throw new IllegalArgumentException("Invalid year value");
        }
        return year;
    }

    public static int getOracleYear(int jYear) {
        return jYear <= 0 ? jYear - 1 : jYear;
    }

    @Override // oracle.sql.Datum
    public Date dateValue() throws SQLException {
        return toDate(getBytes());
    }

    @Override // oracle.sql.Datum
    public Time timeValue() throws SQLException {
        return toTime(getBytes());
    }

    public static int getJavaYear(int cent, int decade) {
        int year = ((cent - 100) * 100) + (decade - 100);
        if (year < 0) {
            year++;
        }
        return year;
    }

    public static TIMESTAMP of(LocalDateTime ldt) throws SQLException {
        return new TIMESTAMP(ldt);
    }

    public LocalDateTime toLocalDateTime() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    public LocalDateTime localDateTimeValue() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    public static TIMESTAMP of(OffsetDateTime odt) {
        return new TIMESTAMP(odt);
    }

    public static LocalDateTime toLocalDateTime(byte[] timestamp) throws SQLException {
        int[] result;
        int arrlength = timestamp.length;
        if (arrlength == 11) {
            result = new int[11];
        } else {
            result = new int[7];
        }
        for (int i = 0; i < timestamp.length; i++) {
            result[i] = timestamp[i] & 255;
        }
        int year = getJavaYear(result[0], result[1]);
        int nanos = 0;
        if (arrlength == 11) {
            nanos = getNanos(timestamp, 7);
        }
        LocalDateTime ldt = LocalDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1, nanos);
        return ldt;
    }

    public static LocalTime toLocalTime(byte[] timestamp) throws SQLException {
        return toLocalDateTime(timestamp).toLocalTime();
    }

    public static LocalDate toLocalDate(byte[] timestamp) throws SQLException {
        return toLocalDateTime(timestamp).toLocalDate();
    }

    public static TIMESTAMP of(ZonedDateTime zdt) {
        return new TIMESTAMP(zdt);
    }

    public static TIMESTAMP of(OffsetTime ot) {
        return new TIMESTAMP(ot);
    }

    public static TIMESTAMP of(LocalTime lt) throws SQLException {
        return new TIMESTAMP(lt);
    }

    public LocalTime toLocalTime() throws SQLException {
        return toLocalTime(getBytes());
    }

    public LocalTime localTimeValue() throws SQLException {
        return toLocalTime(getBytes());
    }

    public static TIMESTAMP of(LocalDate ld) throws SQLException {
        return new TIMESTAMP(ld);
    }

    public LocalDate toLocalDate() throws SQLException {
        return toLocalDate(getBytes());
    }

    public LocalDate localDateValue() throws SQLException {
        return toLocalDate(getBytes());
    }

    public static byte[] toBytes(OffsetDateTime odt) {
        if (odt == null) {
            return null;
        }
        return toBytes(odt.toLocalDateTime());
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [java.time.LocalDateTime] */
    public static byte[] toBytes(ZonedDateTime zdt) {
        if (zdt == null) {
            return null;
        }
        return toBytes((LocalDateTime) zdt.toLocalDateTime());
    }

    public static byte[] toBytes(OffsetTime ot) {
        if (ot == null) {
            return null;
        }
        return toBytes(ot.toLocalTime().atDate(LocalDate.of(JAVA_YEAR, 1, 1)));
    }

    public static byte[] toBytes(LocalDate ld) {
        if (ld == null) {
            return null;
        }
        return toBytes(ld.atTime(0, 0, 0));
    }

    public static byte[] toBytes(LocalDateTime ldt) {
        byte[] result;
        if (ldt == null) {
            return null;
        }
        int year = getOracleYear(ldt.getYear());
        int month = ldt.getMonthValue();
        int date = ldt.getDayOfMonth();
        int hour = ldt.getHour();
        int minute = ldt.getMinute();
        int second = ldt.getSecond();
        int nanos = ldt.getNano();
        if (nanos != 0) {
            result = new byte[11];
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        } else {
            result = new byte[7];
        }
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) month;
        result[3] = (byte) date;
        result[4] = (byte) (hour + 1);
        result[5] = (byte) (minute + 1);
        result[6] = (byte) (second + 1);
        return result;
    }

    public static byte[] toBytes(LocalTime lt) {
        if (lt == null) {
            return null;
        }
        return toBytes(lt.atDate(LocalDate.of(JAVA_YEAR, 1, 1)));
    }

    private static byte[] initTimestamp() {
        byte[] tmp = {119, -86, 1, 1, 1, 1, 1, 0, 0, 0, 0};
        return tmp;
    }

    private boolean isLeapYear(int y) {
        return y % 4 == 0 && (y > 1582 ? y % 100 != 0 || y % DatabaseError.TTC_ERR_BASE == 0 : y != MINYEAR);
    }

    private boolean isValid() {
        int year;
        int month;
        int day;
        int hour;
        int minutes;
        int seconds;
        byte[] ts = getBytes();
        if ((ts.length != 11 && ts.length != 7) || (year = (((ts[0] & 255) - 100) * 100) + ((ts[1] & 255) - 100)) < MINYEAR || year > MAXYEAR || year == 0 || (month = ts[2] & 255) < 1 || month > 12 || (day = ts[3] & 255) < 1 || day > 31) {
            return false;
        }
        if (day > daysInMonth[month - 1] && (!isLeapYear(year) || month != 2 || day != 29)) {
            return false;
        }
        if ((year == 1582 && month == 10 && day >= 5 && day < 15) || (hour = ts[4] & 255) < 1 || hour > 24 || (minutes = ts[5] & 255) < 1 || minutes > 60 || (seconds = ts[6] & 255) < 1 || seconds > 60) {
            return false;
        }
        return true;
    }

    private void readObject(ObjectInputStream in) throws ClassNotFoundException, IOException {
        in.defaultReadObject();
        if (!isValid()) {
            throw new IOException("Invalid TIMESTAMP");
        }
    }
}
