package oracle.sql;

import java.io.InputStream;
import java.io.Reader;
import java.io.StringReader;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/sql/CHAR.class */
public class CHAR extends Datum {
    static final long serialVersionUID = 5559010489982176244L;
    private CharacterSet charSet;
    private int oracleId;
    private boolean variableLength;
    public static final CharacterSet DEFAULT_CHARSET = CharacterSet.make(-1);
    private static final byte[] empty = new byte[0];

    protected CHAR() {
    }

    public CHAR(byte[] bytes, CharacterSet charSet) {
        setValue(bytes, charSet);
    }

    public CHAR(byte[] bytes, int offset, int count, CharacterSet charSet) throws SQLException {
        if (bytes == null) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        byte[] data = new byte[count];
        System.arraycopy(bytes, offset, data, 0, count);
        setValue(data, charSet);
    }

    public CHAR(String str, CharacterSet charSet, int len) throws SQLException {
        this(str, charSet);
        byte[] tmpData = shareBytes();
        if (tmpData.length < len) {
            byte[] tmp = new byte[len];
            System.arraycopy(tmpData, 0, tmp, 0, tmpData.length);
            for (int i = tmpData.length; i < len; i++) {
                tmp[i] = 32;
            }
            setShareBytes(tmp);
        }
    }

    public CHAR(String str, CharacterSet charSet) throws SQLException {
        if (str == null) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        charSet = charSet == null ? DEFAULT_CHARSET : charSet;
        setValue(charSet.convertWithReplacement(str), charSet);
    }

    public CHAR(Object obj, CharacterSet charSet) throws SQLException {
        this(obj == null ? (String) null : obj.toString(), charSet);
    }

    public CharacterSet getCharacterSet() {
        if (this.charSet == null) {
            if (this.oracleId == 0) {
                this.oracleId = -1;
            }
            if (DEFAULT_CHARSET != null && (this.oracleId == -1 || this.oracleId == DEFAULT_CHARSET.getOracleId())) {
                this.charSet = DEFAULT_CHARSET;
            } else {
                this.charSet = CharacterSet.make(this.oracleId);
            }
        }
        return this.charSet;
    }

    public int oracleId() {
        return this.oracleId;
    }

    public String getString() throws SQLException {
        return getCharacterSet().toString(shareBytes(), 0, (int) getLength());
    }

    public String getStringWithReplacement() {
        byte[] bytes = shareBytes();
        return getCharacterSet().toStringWithReplacement(bytes, 0, bytes.length);
    }

    public String toString() {
        return getStringWithReplacement();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleRef
    public boolean equals(Object other) {
        boolean result = this == other;
        if (!result && (other instanceof CHAR)) {
            CHAR o = (CHAR) other;
            result = bytesEqual(o) && getCharacterSet().equals(o.getCharacterSet());
        }
        return result;
    }

    void setValue(byte[] bytes, CharacterSet charSet) {
        this.charSet = charSet == null ? DEFAULT_CHARSET : charSet;
        this.oracleId = this.charSet.getOracleId();
        setShareBytes(bytes == null ? empty : bytes);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return stringValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        String class_name = jClass.getName();
        return class_name.compareTo("java.lang.String") == 0 || class_name.compareTo("java.lang.Long") == 0 || class_name.compareTo("java.math.BigDecimal") == 0 || class_name.compareTo("java.io.InputStream") == 0 || class_name.compareTo("java.sql.Date") == 0 || class_name.compareTo("java.sql.Time") == 0 || class_name.compareTo("java.sql.Timestamp") == 0 || class_name.compareTo("java.io.Reader") == 0;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        return toString();
    }

    @Override // oracle.sql.Datum
    public boolean booleanValue() throws SQLException {
        String s = stringValue();
        if (s == null || s.trim().equals("0") || s.trim().compareToIgnoreCase("f") == 0 || s.trim().compareToIgnoreCase("false") == 0 || s.trim().compareToIgnoreCase("n") == 0 || s.trim().compareToIgnoreCase("no") == 0) {
            return false;
        }
        if (s.trim().equals("1") || s.trim().compareToIgnoreCase("t") == 0 || s.trim().compareToIgnoreCase("true") == 0 || s.trim().compareToIgnoreCase("y") == 0 || s.trim().compareToIgnoreCase("yes") == 0) {
            return true;
        }
        throw ((SQLException) DatabaseError.createSqlException(59).fillInStackTrace());
    }

    @Override // oracle.sql.Datum
    public int intValue() throws SQLException {
        long result = longValue();
        if (result > 2147483647L || result < -2147483648L) {
            throw ((SQLException) DatabaseError.createSqlException(26).fillInStackTrace());
        }
        return (int) result;
    }

    @Override // oracle.sql.Datum
    public long longValue() throws SQLException {
        try {
            long ret_val = Long.valueOf(stringValue().trim()).longValue();
            return ret_val;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(59).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum
    public float floatValue() throws SQLException {
        try {
            float ret_val = Float.valueOf(stringValue().trim()).floatValue();
            return ret_val;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(59).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum
    public double doubleValue() throws SQLException {
        try {
            double ret_val = Double.valueOf(stringValue().trim()).doubleValue();
            return ret_val;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(59).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum
    public byte byteValue() throws SQLException {
        long result = longValue();
        if (result > 127 || result < -128) {
            throw ((SQLException) DatabaseError.createSqlException(26).fillInStackTrace());
        }
        return (byte) result;
    }

    @Override // oracle.sql.Datum
    public Date dateValue() throws SQLException {
        return Date.valueOf(stringValue().trim());
    }

    @Override // oracle.sql.Datum
    public Time timeValue() throws SQLException {
        return Time.valueOf(stringValue().trim());
    }

    @Override // oracle.sql.Datum
    public Timestamp timestampValue() throws SQLException {
        return Timestamp.valueOf(stringValue().trim());
    }

    @Override // oracle.sql.Datum
    public BigDecimal bigDecimalValue() throws SQLException {
        try {
            BigDecimal big_dec_val = new BigDecimal(stringValue().trim());
            return big_dec_val;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(12, "bigDecimalValue").fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Reader characterStreamValue() throws SQLException {
        return new StringReader(getString());
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream asciiStreamValue() throws SQLException {
        return getStream();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream binaryStreamValue() throws SQLException {
        return getStream();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return new String[arraySize];
    }

    @Override // oracle.sql.Datum
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    public void setVariableLength(boolean variableLength) {
        this.variableLength = variableLength;
    }

    public boolean isVariableLength() {
        return this.variableLength;
    }
}
