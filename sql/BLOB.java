package oracle.sql;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.util.logging.Level;
import oracle.jdbc.LargeObjectAccessMode;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleBlob;
import oracle.jdbc.internal.OracleConcreteProxy;
import oracle.jdbc.internal.OracleLargeObject;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableBlob;
import oracle.jdbc.replay.driver.TxnReplayableConnection;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/sql/BLOB.class */
public class BLOB extends DatumWithConnection implements OracleBlob, OracleConcreteProxy {
    public static final int MAX_CHUNK_SIZE = 32768;
    public static final int DURATION_INVALID = -1;
    public static final int DURATION_SESSION = 10;
    public static final int DURATION_CALL = 12;
    public static final int OLD_WRONG_DURATION_SESSION = 1;
    public static final int OLD_WRONG_DURATION_CALL = 2;
    public static final int MODE_READONLY = 0;
    public static final int MODE_READWRITE = 1;
    protected oracle.jdbc.driver.OracleBlob target;
    private OracleBlob ojiOracleBlob;

    protected BLOB() {
        this.target = null;
        this.ojiOracleBlob = null;
        oracle.jdbc.driver.OracleBlob b = new oracle.jdbc.driver.OracleBlob();
        setTarget(null, b, null, false);
    }

    public oracle.jdbc.driver.OracleBlob getTarget() {
        return this.target;
    }

    private void setTarget(OracleConnection conn, oracle.jdbc.driver.OracleBlob b, byte[] lob_descriptor, boolean recordConstructor) {
        this.ojiOracleBlob = (OracleBlob) ConcreteProxyUtil.getProxyObject(conn, b, TxnReplayableBlob.class, this);
        this.target = b;
        setShareBytes(this.target.shareBytes());
        this.targetDatumWithConnection = this.target;
        this.targetDatum = this.target;
        if (this.ojiOracleBlob != null) {
            try {
                Object proxyObj = ConcreteProxyUtil.checkAndGetACProxyConnection(conn);
                if (proxyObj != null && recordConstructor) {
                    ((TxnReplayableConnection) proxyObj).BLOBConstructorRecording(lob_descriptor, this);
                }
            } catch (SQLException sqe) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, "oracle/sql/BLOB", "setTarget", sqe.getMessage(), (String) null, (Throwable) null);
            }
            this.ojiOracleDatumWithConnection = this.ojiOracleBlob;
            return;
        }
        this.ojiOracleBlob = this.target;
    }

    public void createAndSetShardingLobProxy(Class proxyClass, Object creator) {
        this.ojiOracleBlob = (OracleBlob) ConcreteProxyUtil.getProxyObject(this.ojiOracleBlob, proxyClass, creator);
        this.ojiOracleDatumWithConnection = this.ojiOracleBlob;
    }

    @Override // oracle.jdbc.internal.OracleConcreteProxy
    public TxnReplayableBase getConcreteProxy() {
        if (this.ojiOracleBlob instanceof TxnReplayableBase) {
            return (TxnReplayableBase) this.ojiOracleBlob;
        }
        return null;
    }

    protected BLOB(oracle.jdbc.driver.OracleBlob b) {
        this.target = null;
        this.ojiOracleBlob = null;
        setTarget(null, b, null, false);
    }

    public BLOB(OracleConnection conn) throws SQLException {
        this(conn, null);
    }

    public BLOB(OracleConnection conn, byte[] lob_descriptor, boolean fromObject) throws SQLException {
        this(conn, lob_descriptor);
        this.target.setFromobject(fromObject);
    }

    public BLOB(OracleConnection conn, byte[] lob_descriptor) throws SQLException {
        this.target = null;
        this.ojiOracleBlob = null;
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate(conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleBlob b = new oracle.jdbc.driver.OracleBlob(conn, lob_descriptor);
        setTarget(conn, b, lob_descriptor, delegateConn != null);
    }

    @Override // java.sql.Blob
    public long length() throws SQLException {
        return this.ojiOracleBlob.length();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final long lengthInternal() throws SQLException {
        return this.ojiOracleBlob.lengthInternal();
    }

    @Override // java.sql.Blob
    public byte[] getBytes(long pos, int length) throws SQLException {
        return this.ojiOracleBlob.getBytes(pos, length);
    }

    @Override // java.sql.Blob
    public InputStream getBinaryStream() throws SQLException {
        return this.ojiOracleBlob.getBinaryStream();
    }

    public InputStream getBinaryStream(boolean isInternal) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.getBinaryStream(isInternal);
    }

    @Override // java.sql.Blob
    public long position(byte[] pattern, long start) throws SQLException {
        return this.ojiOracleBlob.position(pattern, start);
    }

    @Override // java.sql.Blob
    public long position(Blob pattern, long start) throws SQLException {
        return this.ojiOracleBlob.position(pattern, start);
    }

    @Override // oracle.jdbc.OracleBlob
    public int getBytes(long pos, int length, byte[] buf) throws SQLException {
        return this.ojiOracleBlob.getBytes(pos, length, buf);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public int putBytes(long pos, byte[] bytes) throws SQLException {
        return this.ojiOracleBlob.putBytes(pos, bytes);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public int putBytes(long pos, byte[] bytes, int length) throws SQLException {
        return this.ojiOracleBlob.putBytes(pos, bytes, length);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public OutputStream getBinaryOutputStream() throws SQLException {
        return this.ojiOracleBlob.getBinaryOutputStream();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public byte[] getLocator() {
        return this.ojiOracleBlob.getLocator();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public void setLocator(byte[] locator) {
        this.ojiOracleBlob.setLocator(locator);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public int getChunkSize() throws SQLException {
        return this.ojiOracleBlob.getChunkSize();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public int getBufferSize() throws SQLException {
        return this.ojiOracleBlob.getBufferSize();
    }

    public static BLOB empty_lob() throws SQLException {
        return getEmptyBLOB();
    }

    public static BLOB getEmptyBLOB() throws SQLException {
        byte[] locator = new byte[86];
        locator[1] = 84;
        locator[5] = 24;
        BLOB blob = new BLOB();
        blob.setShareBytes(locator);
        oracle.jdbc.driver.OracleBlob target = blob.getTarget();
        target.setShareBytes(locator);
        return blob;
    }

    @Override // oracle.jdbc.OracleBlob
    public boolean isEmptyLob() throws SQLException {
        return this.ojiOracleBlob.isEmptyLob();
    }

    @Override // oracle.jdbc.OracleBlob
    public boolean isSecureFile() throws SQLException {
        return this.ojiOracleBlob.isSecureFile();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public OutputStream getBinaryOutputStream(long pos) throws SQLException {
        return this.ojiOracleBlob.getBinaryOutputStream(pos);
    }

    @Override // oracle.jdbc.OracleBlob
    public InputStream getBinaryStream(long pos) throws SQLException {
        return this.ojiOracleBlob.getBinaryStream(pos);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public void trim(long newlen) throws SQLException {
        this.ojiOracleBlob.trim(newlen);
    }

    public static BLOB createTemporary(Connection conn, boolean cache, int _duration) throws SQLException {
        int duration = _duration;
        if (_duration == 1) {
            duration = 10;
        }
        if (_duration == 2) {
            duration = 12;
        }
        if (conn == null || (duration != 10 && duration != 12)) {
            throw ((SQLException) DatabaseError.createSqlException(68, "'conn' should not be null and 'duration' should either be equal to DURATION_SESSION or to DURATION_CALL").fillInStackTrace());
        }
        oracle.jdbc.internal.OracleConnection physConn = ((OracleConnection) conn).physicalConnectionWithin();
        return getDBAccess(physConn).createTemporaryBlob(physConn, cache, duration);
    }

    public static void freeTemporary(BLOB temp_lob) throws SQLException {
        if (temp_lob == null) {
            return;
        }
        temp_lob.freeTemporary();
    }

    public static boolean isTemporary(BLOB lob) throws SQLException {
        if (lob == null) {
            return false;
        }
        return lob.isTemporary();
    }

    public static short getDuration(BLOB lob) throws SQLException {
        if (lob == null) {
            return (short) -1;
        }
        return lob.getDuration();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public void freeTemporary() throws SQLException {
        this.ojiOracleBlob.freeTemporary();
    }

    @Override // oracle.jdbc.OracleBlob, oracle.jdbc.internal.OracleLargeObject
    public boolean isTemporary() throws SQLException {
        return this.ojiOracleBlob.isTemporary();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public short getDuration() throws SQLException {
        return this.ojiOracleBlob.getDuration();
    }

    @Override // oracle.jdbc.OracleBlob
    public void openLob(LargeObjectAccessMode mode) throws SQLException {
        this.ojiOracleBlob.openLob(mode);
    }

    @Deprecated
    public void open(int mode) throws SQLException {
        openLob(mode);
    }

    public void openLob(int mode) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        this.target.openLob(mode);
    }

    @Override // oracle.jdbc.OracleBlob
    public void closeLob() throws SQLException {
        this.ojiOracleBlob.closeLob();
    }

    @Override // oracle.jdbc.OracleBlob
    public boolean isOpenLob() throws SQLException {
        return this.ojiOracleBlob.isOpenLob();
    }

    @Override // java.sql.Blob
    public int setBytes(long pos, byte[] bytes) throws SQLException {
        return this.ojiOracleBlob.setBytes(pos, bytes);
    }

    @Override // java.sql.Blob
    public int setBytes(long pos, byte[] bytes, int offset, int len) throws SQLException {
        return this.ojiOracleBlob.setBytes(pos, bytes, offset, len);
    }

    @Override // java.sql.Blob
    public OutputStream setBinaryStream(long pos) throws SQLException {
        return this.ojiOracleBlob.setBinaryStream(pos);
    }

    @Override // java.sql.Blob
    public void truncate(long len) throws SQLException {
        this.ojiOracleBlob.truncate(len);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        this.ojiOracleBlob.toJdbc();
        return this;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return this.ojiOracleBlob.isConvertibleTo(jClass);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Reader characterStreamValue() throws SQLException {
        return this.ojiOracleBlob.characterStreamValue();
    }

    public Reader characterStreamValue(boolean isInternal) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.characterStreamValue(isInternal);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream asciiStreamValue() throws SQLException {
        return this.ojiOracleBlob.asciiStreamValue();
    }

    public InputStream asciiStreamValue(boolean isInternal) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.asciiStreamValue(isInternal);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream binaryStreamValue() throws SQLException {
        return this.ojiOracleBlob.binaryStreamValue();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public InputStream binaryStreamValue(boolean isInternal) throws SQLException {
        return this.ojiOracleBlob.binaryStreamValue(isInternal);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return this.ojiOracleBlob.makeJdbcArray(arraySize);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public BlobDBAccess getDBAccess() throws SQLException {
        return this.ojiOracleBlob.getDBAccess();
    }

    public static BlobDBAccess getDBAccess(Connection conn) throws SQLException {
        Monitor.CloseableLock lock = ((OracleConnection) conn).physicalConnectionWithin().acquireCloseableLock();
        Throwable th = null;
        try {
            BlobDBAccess blobDBAccessCreateBlobDBAccess = ((OracleConnection) conn).physicalConnectionWithin().createBlobDBAccess();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return blobDBAccessCreateBlobDBAccess;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.DatumWithConnection, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Connection getJavaSqlConnection() throws SQLException {
        return this.ojiOracleBlob.getJavaSqlConnection();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final void setLength(long _length) {
        this.ojiOracleBlob.setLength(_length);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final void setChunkSize(int _dbChunkSize) {
        this.ojiOracleBlob.setChunkSize(_dbChunkSize);
    }

    public final void setPrefetchedData(byte[] _prefetchData) {
        setPrefetchedData(_prefetchData, _prefetchData == null ? 0 : _prefetchData.length);
    }

    public final void setPrefetchedData(byte[] _prefetchData, int _size) {
        setPrefetchData(_prefetchData == null ? null : OracleLargeObject.PrefetchData.wrapArray(_prefetchData, _size));
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final void setPrefetchData(OracleLargeObject.PrefetchData<byte[]> prefetchData) {
        this.ojiOracleBlob.setPrefetchData(prefetchData);
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final OracleLargeObject.PrefetchData<byte[]> getPrefetchData() {
        return this.ojiOracleBlob.getPrefetchData();
    }

    public final byte[] getPrefetchedData() {
        OracleLargeObject.PrefetchData<byte[]> prefetchData = this.ojiOracleBlob.getPrefetchData();
        if (prefetchData == null) {
            return null;
        }
        return prefetchData.share();
    }

    public final int getPrefetchedDataSize() {
        OracleLargeObject.PrefetchData<byte[]> prefetchData = this.ojiOracleBlob.getPrefetchData();
        if (prefetchData == null) {
            return 0;
        }
        return prefetchData.length();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final void setActivePrefetch(boolean _activePrefetch) {
        this.ojiOracleBlob.setActivePrefetch(_activePrefetch);
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final void clearCachedData() {
        this.ojiOracleBlob.clearCachedData();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public final boolean isActivePrefetch() {
        return this.ojiOracleBlob.isActivePrefetch();
    }

    @Override // oracle.jdbc.internal.OracleBlob
    public boolean canReadBasicLobDataInLocator() throws SQLException {
        return this.ojiOracleBlob.canReadBasicLobDataInLocator();
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public void freeLOB() throws SQLException {
        free();
    }

    @Override // java.sql.Blob
    public void free() throws SQLException {
        this.ojiOracleBlob.free();
    }

    @Override // java.sql.Blob
    public InputStream getBinaryStream(long pos, long length) throws SQLException {
        return this.ojiOracleBlob.getBinaryStream(pos, length);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public void setBytes(byte[] locator) {
        this.ojiOracleBlob.setBytes(locator);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.ojiOracleBlob.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.ojiOracleBlob.getACProxy();
    }

    public OracleBlob getInternal() {
        return this.ojiOracleBlob;
    }

    @Override // oracle.jdbc.OracleBlob
    public SQLXML toSQLXML() throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return toSQLXML(getPhysicalConnection().getDbCsId());
    }

    @Override // oracle.jdbc.OracleBlob
    public SQLXML toSQLXML(int csid) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return new XMLType(getPhysicalConnection(), this, csid);
    }

    @Override // oracle.jdbc.internal.OracleLargeObject
    public final boolean isFree() {
        return this.ojiOracleBlob.isFree();
    }
}
