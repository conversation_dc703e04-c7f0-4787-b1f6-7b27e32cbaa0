package oracle.sql;

import java.sql.Connection;
import java.sql.SQLException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleDatumWithConnection;

/* loaded from: ojdbc8.jar:oracle/sql/DatumWithConnection.class */
public abstract class DatumWithConnection extends Datum implements OracleDatumWithConnection {
    private OracleConnection physicalConnection;
    protected DatumWithConnection targetDatumWithConnection;

    public OracleConnection getPhysicalConnection() {
        if (this.targetDatumWithConnection != null) {
            return this.targetDatumWithConnection.getPhysicalConnection();
        }
        if (this.physicalConnection == null) {
            try {
                this.physicalConnection = (OracleConnection) new OracleDriver().defaultConnection();
            } catch (SQLException e) {
            }
        }
        return this.physicalConnection;
    }

    public DatumWithConnection(byte[] elements) throws SQLException {
        super(elements);
        this.physicalConnection = null;
        this.targetDatumWithConnection = null;
    }

    public DatumWithConnection() {
        this.physicalConnection = null;
        this.targetDatumWithConnection = null;
    }

    public static void assertNotNull(Connection conn) throws SQLException {
        if (conn == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "Connection is null").fillInStackTrace());
        }
    }

    public static void assertNotNull(TypeDescriptor desc) throws SQLException {
        if (desc == null) {
            throw ((SQLException) DatabaseError.createSqlException(61).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.internal.OracleDatumWithConnection
    public void setPhysicalConnectionOf(Connection conn) {
        if (this.ojiOracleDatumWithConnection != null) {
            this.ojiOracleDatumWithConnection.setPhysicalConnectionOf(conn);
        } else if (this.targetDatumWithConnection != null) {
            this.targetDatumWithConnection.setPhysicalConnectionOf(conn);
        } else {
            this.physicalConnection = ((oracle.jdbc.OracleConnection) conn).physicalConnectionWithin();
        }
    }

    public Connection getJavaSqlConnection() throws SQLException {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getJavaSqlConnection();
        }
        return getPhysicalConnection().getWrapper();
    }

    @Override // oracle.jdbc.internal.OracleDatumWithConnection
    public oracle.jdbc.OracleConnection getOracleConnection() throws SQLException {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getOracleConnection();
        }
        return getPhysicalConnection().getWrapper();
    }

    @Override // oracle.jdbc.internal.OracleDatumWithConnection
    public OracleConnection getInternalConnection() throws SQLException {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getInternalConnection();
        }
        return getPhysicalConnection();
    }

    @Override // oracle.jdbc.internal.OracleDatumWithConnection
    public oracle.jdbc.driver.OracleConnection getConnection() throws SQLException {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getConnection();
        }
        if (this.targetDatumWithConnection != null) {
            return this.targetDatumWithConnection.getConnection();
        }
        try {
            oracle.jdbc.driver.OracleConnection ret = (oracle.jdbc.driver.OracleConnection) ((oracle.jdbc.driver.OracleConnection) this.physicalConnection).getWrapper();
            return ret;
        } catch (ClassCastException e) {
            throw ((SQLException) DatabaseError.createSqlException(103).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum
    protected OracleConnection getConnectionDuringExceptionHandling() {
        if (this.targetDatumWithConnection != null) {
            return this.targetDatumWithConnection.getConnectionDuringExceptionHandling();
        }
        return this.physicalConnection;
    }
}
