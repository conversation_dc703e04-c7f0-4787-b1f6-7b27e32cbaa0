package oracle.sql;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.Ref;
import java.sql.SQLData;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.OracleTypeMetaData;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConcreteProxy;
import oracle.jdbc.internal.OracleRef;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableRef;

/* loaded from: ojdbc8.jar:oracle/sql/REF.class */
public class REF extends DatumWithConnection implements OracleRef, Serializable, Cloneable, OracleConcreteProxy {
    protected oracle.jdbc.driver.OracleRef target = null;
    private OracleRef ojiOracleRef = null;

    public oracle.jdbc.driver.OracleRef getTarget() {
        return this.target;
    }

    private void setTarget(OracleConnection conn, oracle.jdbc.driver.OracleRef r) {
        this.ojiOracleRef = (OracleRef) ConcreteProxyUtil.getProxyObject(conn, r, TxnReplayableRef.class, this);
        this.target = r;
        setShareBytes(this.target.shareBytes());
        this.targetDatumWithConnection = this.target;
        this.targetDatum = this.target;
        if (this.ojiOracleRef == null) {
            this.ojiOracleRef = this.target;
        } else {
            this.ojiOracleDatumWithConnection = this.ojiOracleRef;
        }
    }

    public void createAndSetShardingLobProxy(Class proxyClass, Object creator) {
        this.ojiOracleRef = (OracleRef) ConcreteProxyUtil.getProxyObject(this.ojiOracleRef, proxyClass, creator);
        this.ojiOracleDatumWithConnection = this.ojiOracleRef;
    }

    @Override // oracle.jdbc.internal.OracleConcreteProxy
    public TxnReplayableBase getConcreteProxy() {
        if (this.ojiOracleRef instanceof TxnReplayableBase) {
            return (TxnReplayableBase) this.ojiOracleRef;
        }
        return null;
    }

    @Override // java.sql.Ref
    public String getBaseTypeName() throws SQLException {
        return this.ojiOracleRef.getBaseTypeName();
    }

    public REF(String typename, Connection conn, byte[] bytes) throws SQLException {
        Connection conn2 = ConcreteProxyUtil.unwrapConnectionProxy((OracleConnection) conn);
        oracle.jdbc.driver.OracleRef r = new oracle.jdbc.driver.OracleRef(typename, conn2, bytes);
        setTarget((OracleConnection) conn2, r);
    }

    public REF(StructDescriptor desc, Connection conn, byte[] bytes) throws SQLException {
        Connection conn2 = ConcreteProxyUtil.unwrapConnectionProxy((OracleConnection) conn);
        oracle.jdbc.driver.OracleRef r = new oracle.jdbc.driver.OracleRef(desc, conn2, bytes);
        setTarget((OracleConnection) conn2, r);
    }

    @Override // oracle.jdbc.internal.OracleRef
    public Object getValue(Map map) throws SQLException {
        return this.ojiOracleRef.getValue(map);
    }

    @Override // oracle.jdbc.internal.OracleRef
    public Object getValue() throws SQLException {
        return this.ojiOracleRef.getValue();
    }

    @Override // oracle.jdbc.internal.OracleRef
    public STRUCT getSTRUCT() throws SQLException {
        return this.ojiOracleRef.getSTRUCT();
    }

    @Override // oracle.jdbc.internal.OracleRef
    public void setValue(Object value) throws SQLException {
        this.ojiOracleRef.setValue(value);
    }

    @Override // oracle.jdbc.OracleRef
    public OracleTypeMetaData getOracleMetaData() throws SQLException {
        return this.ojiOracleRef.getOracleMetaData();
    }

    @Override // oracle.jdbc.internal.OracleRef
    public StructDescriptor getDescriptor() throws SQLException {
        return this.ojiOracleRef.getDescriptor();
    }

    @Override // oracle.jdbc.internal.OracleRef
    public String getSQLTypeName() throws SQLException {
        return this.ojiOracleRef.getSQLTypeName();
    }

    @Override // java.sql.Ref
    public Object getObject(Map map) throws SQLException {
        return this.ojiOracleRef.getObject(map);
    }

    @Override // java.sql.Ref
    public Object getObject() throws SQLException {
        return this.ojiOracleRef.getObject();
    }

    @Override // java.sql.Ref
    public void setObject(Object value) throws SQLException {
        this.ojiOracleRef.setObject(value);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return this;
    }

    public Object toJdbc(Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Class c;
        Object jdbcObject = this;
        if (map != null && (c = getDescriptor().getClass(map)) != null) {
            jdbcObject = toClass(c, map);
        }
        return jdbcObject;
    }

    public Object toClass(Class clazz, Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Object obj;
        if (clazz == null || clazz == REF.class || clazz == Ref.class || clazz == oracle.jdbc.OracleRef.class || clazz == OracleRef.class) {
            obj = this;
        } else {
            try {
                Object i = clazz.newInstance();
                if (i instanceof SQLData) {
                    obj = this;
                } else if (i instanceof ORADataFactory) {
                    ORADataFactory f = (ORADataFactory) i;
                    obj = f.create(this, 2006);
                } else if (i instanceof OracleDataFactory) {
                    OracleDataFactory f2 = (OracleDataFactory) i;
                    obj = f2.create(this, 2006);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, this.target.getDescriptor().getName()).fillInStackTrace());
                }
            } catch (IllegalAccessException ex) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "IllegalAccessException: " + ex.getMessage()).fillInStackTrace());
            } catch (InstantiationException ex2) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "InstantiationException: " + ex2.getMessage()).fillInStackTrace());
            }
        }
        return obj;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return this.ojiOracleRef.isConvertibleTo(jClass);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return this.ojiOracleRef.makeJdbcArray(arraySize);
    }

    @Override // oracle.jdbc.internal.OracleRef
    public Object clone() throws CloneNotSupportedException {
        return this.target.clone();
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0033  */
    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleRef
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean equals(java.lang.Object r10) {
        /*
            r9 = this;
            r0 = 0
            r11 = r0
            r0 = r9
            oracle.jdbc.driver.OracleRef r0 = r0.target     // Catch: java.lang.Exception -> L38
            r1 = r10
            boolean r0 = r0.equals(r1)     // Catch: java.lang.Exception -> L38
            r11 = r0
            r0 = r11
            if (r0 != 0) goto L35
            r0 = r10
            boolean r0 = r0 instanceof oracle.sql.REF     // Catch: java.lang.Exception -> L38
            if (r0 == 0) goto L33
            r0 = r9
            r1 = r10
            boolean r0 = super.equals(r1)     // Catch: java.lang.Exception -> L38
            if (r0 == 0) goto L33
            r0 = r9
            java.lang.String r0 = r0.getBaseTypeName()     // Catch: java.lang.Exception -> L38
            r1 = r10
            oracle.sql.REF r1 = (oracle.sql.REF) r1     // Catch: java.lang.Exception -> L38
            java.lang.String r1 = r1.getSQLTypeName()     // Catch: java.lang.Exception -> L38
            boolean r0 = r0.equals(r1)     // Catch: java.lang.Exception -> L38
            if (r0 == 0) goto L33
            r0 = 1
            goto L34
        L33:
            r0 = 0
        L34:
            r11 = r0
        L35:
            goto L5a
        L38:
            r12 = move-exception
            oracle.jdbc.diagnostics.Diagnosable r0 = oracle.jdbc.diagnostics.CommonDiagnosable.getInstance()
            java.util.logging.Level r1 = java.util.logging.Level.FINE
            oracle.jdbc.diagnostics.SecurityLabel r2 = oracle.jdbc.diagnostics.SecurityLabel.UNKNOWN
            java.lang.String r3 = "oracle/sql/REF"
            java.lang.String r4 = "equals"
            r5 = r12
            java.lang.String r5 = r5.getMessage()
            r6 = 0
            java.lang.String r6 = (java.lang.String) r6
            r7 = 0
            java.lang.Throwable r7 = (java.lang.Throwable) r7
            java.lang.Throwable r0 = r0.debug(r1, r2, r3, r4, r5, r6, r7)
        L5a:
            r0 = r11
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.sql.REF.equals(java.lang.Object):boolean");
    }

    @Override // oracle.jdbc.internal.OracleRef
    public int hashCode() {
        return this.target.hashCode();
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeObject(shareBytes());
        try {
            out.writeUTF(getBaseTypeName());
        } catch (SQLException e) {
            throw new IOException(e.getMessage());
        }
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        setBytes((byte[]) in.readObject());
        this.target.setTypeName(in.readUTF());
    }

    @Override // oracle.sql.DatumWithConnection, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Connection getJavaSqlConnection() throws SQLException {
        return this.ojiOracleRef.getJavaSqlConnection();
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.ojiOracleRef.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.ojiOracleRef.getACProxy();
    }
}
