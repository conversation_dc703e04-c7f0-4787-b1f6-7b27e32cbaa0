package oracle.sql;

import java.io.PrintWriter;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.sql.Array;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.OracleTypeMetaData;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleArray;
import oracle.jdbc.internal.OracleConcreteProxy;
import oracle.jdbc.proxy.ProxyFactory;
import oracle.jdbc.proxy._Proxy_;
import oracle.jdbc.replay.driver.TxnReplayableArray;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableConnection;

/* loaded from: ojdbc8.jar:oracle/sql/ARRAY.class */
public class ARRAY extends DatumWithConnection implements OracleArray, OracleConcreteProxy {
    public static final int ACCESS_FORWARD = 1;
    public static final int ACCESS_REVERSE = 2;
    public static final int ACCESS_UNKNOWN = 3;
    protected oracle.jdbc.driver.OracleArray target = null;
    private OracleArray ojiOracleArray = null;

    public oracle.jdbc.driver.OracleArray getTarget() {
        return this.target;
    }

    private void setTarget(OracleConnection conn, oracle.jdbc.driver.OracleArray a, String typeName, Object elements, boolean recordConstructor) {
        this.ojiOracleArray = (OracleArray) ConcreteProxyUtil.getProxyObject(conn, a, TxnReplayableArray.class, this);
        this.target = a;
        setShareBytes(this.target.shareBytes());
        this.targetDatumWithConnection = this.target;
        this.targetDatum = this.target;
        if (this.ojiOracleArray != null) {
            try {
                Object proxyObj = ConcreteProxyUtil.checkAndGetACProxyConnection(conn);
                if (proxyObj != null && recordConstructor) {
                    ((TxnReplayableConnection) proxyObj).ARRAYConstructorRecording(typeName, elements, this);
                }
            } catch (SQLException sqe) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, "oracle/sql/ARRAY", "setTarget", sqe.getMessage(), (String) null, (Throwable) null);
            }
            this.ojiOracleDatumWithConnection = this.ojiOracleArray;
            return;
        }
        this.ojiOracleArray = this.target;
    }

    public void createAndSetShardingLobProxy(Class proxyClass, Object creator) {
        this.ojiOracleArray = (OracleArray) ConcreteProxyUtil.getProxyObject(this.ojiOracleArray, proxyClass, creator);
        this.ojiOracleDatumWithConnection = this.ojiOracleArray;
    }

    @Override // oracle.jdbc.internal.OracleConcreteProxy
    public TxnReplayableBase getConcreteProxy() {
        if (this.ojiOracleArray instanceof TxnReplayableBase) {
            return (TxnReplayableBase) this.ojiOracleArray;
        }
        return null;
    }

    public ARRAY(ArrayDescriptor type, Connection conn, Object elements) throws SQLException {
        String typeNameByUser = type != null ? type.typeNameByUser : null;
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate((OracleConnection) conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleArray a = new oracle.jdbc.driver.OracleArray(type, conn, elements);
        setTarget((OracleConnection) conn, a, typeNameByUser, elements, delegateConn != null);
    }

    public ARRAY(ArrayDescriptor type, byte[] bytes, Connection conn) throws SQLException {
        oracle.jdbc.driver.OracleArray a = new oracle.jdbc.driver.OracleArray(type, bytes, conn);
        setTarget((OracleConnection) conn, a, null, null, false);
    }

    public static ARRAY toARRAY(Object obj, OracleConnection conn) throws SQLException {
        ARRAY s = null;
        if (obj != null) {
            if (obj instanceof ARRAY) {
                s = (ARRAY) obj;
            } else if (obj instanceof ORAData) {
                s = (ARRAY) ((ORAData) obj).toDatum(conn);
            } else if (obj instanceof OracleData) {
                Object jdbcObject = ((OracleData) obj).toJDBCObject(conn);
                if (jdbcObject instanceof _Proxy_) {
                    final _Proxy_ proxiedJDBCObject = (_Proxy_) jdbcObject;
                    jdbcObject = AccessController.doPrivileged(new PrivilegedAction<Object>() { // from class: oracle.sql.ARRAY.1
                        @Override // java.security.PrivilegedAction
                        public Object run() {
                            return ProxyFactory.extractDelegate(proxiedJDBCObject);
                        }
                    });
                }
                s = (ARRAY) jdbcObject;
            } else if (obj instanceof CustomDatum) {
                s = (ARRAY) conn.physicalConnectionWithin().toDatum((CustomDatum) obj);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(59, obj).fillInStackTrace());
            }
        }
        return s;
    }

    @Override // java.sql.Array
    public String getBaseTypeName() throws SQLException {
        return this.ojiOracleArray.getBaseTypeName();
    }

    @Override // java.sql.Array
    public int getBaseType() throws SQLException {
        return this.ojiOracleArray.getBaseType();
    }

    @Override // java.sql.Array
    public Object getArray() throws SQLException {
        return this.ojiOracleArray.getArray();
    }

    @Override // oracle.jdbc.OracleArray
    public Map<?, ?> getJavaMap() throws SQLException {
        return this.ojiOracleArray.getJavaMap();
    }

    @Override // java.sql.Array
    public Object getArray(Map map) throws SQLException {
        return this.ojiOracleArray.getArray(map);
    }

    @Override // java.sql.Array
    public Object getArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getArray(index, count);
    }

    @Override // java.sql.Array
    public Object getArray(long index, int count, Map map) throws SQLException {
        return this.ojiOracleArray.getArray(index, count, map);
    }

    @Override // java.sql.Array
    public ResultSet getResultSet() throws SQLException {
        return this.ojiOracleArray.getResultSet();
    }

    @Override // java.sql.Array
    public ResultSet getResultSet(Map map) throws SQLException {
        return this.ojiOracleArray.getResultSet(map);
    }

    @Override // java.sql.Array
    public ResultSet getResultSet(long index, int count) throws SQLException {
        return this.ojiOracleArray.getResultSet(index, count);
    }

    @Override // java.sql.Array
    public ResultSet getResultSet(long index, int count, Map map) throws SQLException {
        return this.ojiOracleArray.getResultSet(index, count, map);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public Datum[] getOracleArray() throws SQLException {
        return this.ojiOracleArray.getOracleArray();
    }

    @Override // oracle.jdbc.OracleArray
    public int length() throws SQLException {
        return this.ojiOracleArray.length();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public Datum[] getOracleArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getOracleArray(index, count);
    }

    @Override // oracle.jdbc.OracleArray
    public String getSQLTypeName() throws SQLException {
        return this.ojiOracleArray.getSQLTypeName();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public Map getMap() throws SQLException {
        return this.ojiOracleArray.getMap();
    }

    @Override // oracle.jdbc.OracleArray
    public OracleTypeMetaData getOracleMetaData() throws SQLException {
        return this.ojiOracleArray.getOracleMetaData();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public ArrayDescriptor getDescriptor() throws SQLException {
        return this.ojiOracleArray.getDescriptor();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public byte[] toBytes() throws SQLException {
        return this.ojiOracleArray.toBytes();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setDatumArray(Datum[] darray) {
        this.ojiOracleArray.setDatumArray(darray);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setObjArray(Object oarray) throws SQLException {
        this.ojiOracleArray.setObjArray(oarray);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setJavaMap(Map<?, ?> map) throws SQLException {
        this.ojiOracleArray.setJavaMap(map);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setLocator(byte[] pseg_bytes) {
        this.ojiOracleArray.setLocator(pseg_bytes);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setPrefixSegment(byte[] pseg_bytes) {
        this.ojiOracleArray.setPrefixSegment(pseg_bytes);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setPrefixFlag(byte psegFlag) {
        this.ojiOracleArray.setPrefixFlag(psegFlag);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public byte[] getLocator() {
        return this.ojiOracleArray.getLocator();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setLength(int len) {
        this.ojiOracleArray.setLength(len);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public boolean hasDataSeg() {
        return this.ojiOracleArray.hasDataSeg();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public boolean isInline() {
        return this.ojiOracleArray.isInline();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        this.target.toJdbc();
        Map map = this.target.getMap();
        return toJdbc(map);
    }

    public Object toJdbc(Map map) throws SQLException, IllegalAccessException, InstantiationException {
        Class c;
        if (this.target.isFreed()) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_FREED_ARRAY);
        }
        Object jdbcObject = this;
        if (map != null && (c = this.target.getDescriptor().getClass(map)) != null) {
            jdbcObject = toClass(c, map);
        }
        return jdbcObject;
    }

    Object toClass(Class clazz, Map map) throws SQLException, IllegalAccessException, InstantiationException {
        Object obj;
        if (this.target.isFreed()) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_FREED_ARRAY);
        }
        if (clazz == null || clazz == ARRAY.class || clazz == Array.class || clazz == oracle.jdbc.OracleArray.class || clazz == OracleArray.class) {
            obj = this;
        } else {
            try {
                Object i = clazz.newInstance();
                if (i instanceof ORADataFactory) {
                    ORADataFactory f = (ORADataFactory) i;
                    obj = f.create(this, 2003);
                } else if (i instanceof OracleDataFactory) {
                    OracleDataFactory f2 = (OracleDataFactory) i;
                    obj = f2.create(this, 2003);
                } else {
                    throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, this.target.getDescriptor().getName());
                }
            } catch (IllegalAccessException ex) {
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "IllegalAccessException: " + ex.getMessage());
            } catch (InstantiationException ex2) {
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "InstantiationException: " + ex2.getMessage());
            }
        }
        return obj;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return this.ojiOracleArray.isConvertibleTo(jClass);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return this.ojiOracleArray.makeJdbcArray(arraySize);
    }

    @Override // oracle.jdbc.OracleArray
    public int[] getIntArray() throws SQLException {
        return this.ojiOracleArray.getIntArray();
    }

    @Override // oracle.jdbc.OracleArray
    public int[] getIntArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getIntArray(index, count);
    }

    @Override // oracle.jdbc.OracleArray
    public double[] getDoubleArray() throws SQLException {
        return this.ojiOracleArray.getDoubleArray();
    }

    @Override // oracle.jdbc.OracleArray
    public double[] getDoubleArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getDoubleArray(index, count);
    }

    @Override // oracle.jdbc.OracleArray
    public short[] getShortArray() throws SQLException {
        return this.ojiOracleArray.getShortArray();
    }

    @Override // oracle.jdbc.OracleArray
    public short[] getShortArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getShortArray(index, count);
    }

    @Override // oracle.jdbc.OracleArray
    public long[] getLongArray() throws SQLException {
        return this.ojiOracleArray.getLongArray();
    }

    @Override // oracle.jdbc.OracleArray
    public long[] getLongArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getLongArray(index, count);
    }

    @Override // oracle.jdbc.OracleArray
    public float[] getFloatArray() throws SQLException {
        return this.ojiOracleArray.getFloatArray();
    }

    @Override // oracle.jdbc.OracleArray
    public float[] getFloatArray(long index, int count) throws SQLException {
        return this.ojiOracleArray.getFloatArray(index, count);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setAutoBuffering(boolean enable) throws SQLException {
        this.ojiOracleArray.setAutoBuffering(enable);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public boolean getAutoBuffering() throws SQLException {
        return this.ojiOracleArray.getAutoBuffering();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setAutoIndexing(boolean enable, int direction) throws SQLException {
        this.ojiOracleArray.setAutoIndexing(enable, direction);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setAutoIndexing(boolean enable) throws SQLException {
        this.ojiOracleArray.setAutoIndexing(enable);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public boolean getAutoIndexing() throws SQLException {
        return this.ojiOracleArray.getAutoIndexing();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public int getAccessDirection() throws SQLException {
        return this.ojiOracleArray.getAccessDirection();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setLastIndexOffset(long index, long offset) throws SQLException {
        this.ojiOracleArray.setLastIndexOffset(index, offset);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setIndexOffset(long index, long offset) throws SQLException {
        this.ojiOracleArray.setIndexOffset(index, offset);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public long getLastIndex() throws SQLException {
        return this.ojiOracleArray.getLastIndex();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public long getLastOffset() throws SQLException {
        return this.ojiOracleArray.getLastOffset();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public long getOffset(long index) throws SQLException {
        return this.ojiOracleArray.getOffset(index);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setImage(byte[] image, long offset, long length) throws SQLException {
        this.ojiOracleArray.setImage(image, offset, length);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setImageLength(long length) throws SQLException {
        this.ojiOracleArray.setImageLength(length);
    }

    @Override // oracle.jdbc.internal.OracleArray
    public long getImageOffset() {
        return this.ojiOracleArray.getImageOffset();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public long getImageLength() {
        return this.ojiOracleArray.getImageLength();
    }

    public String dump() throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        if (this.target.isFreed()) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_FREED_ARRAY);
        }
        return STRUCT.dump(this);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.stringValue();
    }

    static void dump(ARRAY x, PrintWriter pw, int indent) throws SQLException {
        if (indent > 0) {
            pw.println();
        }
        ArrayDescriptor desc = x.getDescriptor();
        for (int i = 0; i < indent; i++) {
            pw.print(' ');
        }
        pw.println("name = " + desc.getName());
        for (int i2 = 0; i2 < indent; i2++) {
            pw.print(' ');
        }
        pw.println("max length = " + desc.getMaxLength());
        Object[] elems = (Object[]) x.getArray();
        for (int i3 = 0; i3 < indent; i3++) {
            pw.print(' ');
        }
        StringBuilder sbAppend = new StringBuilder().append("length = ");
        int length = elems.length;
        pw.println(sbAppend.append(length).toString());
        for (int i4 = 0; i4 < length; i4++) {
            for (int j = 0; j < indent; j++) {
                pw.print(' ');
            }
            pw.print("element[" + i4 + "] = ");
            STRUCT.dump(elems[i4], pw, indent + 4);
        }
    }

    @Override // java.sql.Array
    public void free() throws SQLException {
        this.ojiOracleArray.free();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public int getNumElems() {
        return this.ojiOracleArray.getNumElems();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public Datum[] getDatumArray() {
        return this.ojiOracleArray.getDatumArray();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public Object getObjArray() {
        return this.ojiOracleArray.getObjArray();
    }

    @Override // oracle.jdbc.internal.OracleArray
    public void setNullObjArray() {
        this.ojiOracleArray.setNullObjArray();
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.ojiOracleArray.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.ojiOracleArray.getACProxy();
    }
}
