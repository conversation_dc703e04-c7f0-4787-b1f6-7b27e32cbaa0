package oracle.sql;

import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Calendar;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.OracleDate;

/* loaded from: ojdbc8.jar:oracle/sql/DATE.class */
public class DATE extends Datum implements OracleDate {
    static final long serialVersionUID = 5229717576495161269L;
    public static final int BDA = 1;
    public static final int BDAL = 2;
    public static final int BMO = 4;
    public static final int BMOL = 8;
    public static final int BYR = 16;
    public static final int BYRL = 32;
    public static final int BHR = 64;
    public static final int BHRL = 128;
    public static final int BMN = 256;
    public static final int BMNL = 512;
    public static final int BSC = 1024;
    public static final int BSCL = 2048;
    public static final int MSD = 4096;
    public static final int YR0 = 8192;
    public static final int BDT = 32768;
    public static final int HRZER0 = 65536;
    public static final int MIZERO = 131072;
    public static final int SEZERO = 262144;
    private static final byte LDXTCE = 0;
    private static final byte LDXTYE = 1;
    private static final byte LDXTMO = 2;
    private static final byte LDXTDA = 3;
    private static final byte LDXTHO = 4;
    private static final byte LDXTMI = 5;
    private static final byte LDXTSE = 6;
    private static LdxLib _sldxlib;

    public DATE() {
        super(_initDate());
    }

    public DATE(byte[] date) {
        super(date);
    }

    public DATE(Date date) {
        super(toBytes(date));
    }

    public DATE(Time time) {
        super(toBytes(time));
    }

    public DATE(Timestamp timestamp) {
        super(toBytes(timestamp));
    }

    public DATE(Date date, Calendar cal) {
        super(toBytes(date, cal));
    }

    public DATE(Time time, Calendar cal) {
        super(toBytes(time, cal));
    }

    public DATE(Timestamp timestamp, Calendar cal) {
        super(toBytes(timestamp, cal));
    }

    public DATE(String str) {
        super(toBytes(str));
    }

    public DATE(String str, boolean lenient) throws ParseException {
        super(toBytes(str));
        if (!lenient) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            simpleDateFormat.setLenient(false);
            simpleDateFormat.parse(str);
        }
    }

    public DATE(String str, Calendar cal) {
        super(toBytes(str, cal));
    }

    public DATE(Object obj) throws SQLException {
        if (obj instanceof Date) {
            setShareBytes(toBytes((Date) obj));
            return;
        }
        if (obj instanceof Time) {
            setShareBytes(toBytes((Time) obj));
        } else if (obj instanceof Timestamp) {
            setShareBytes(toBytes((Timestamp) obj));
        } else {
            if (obj instanceof String) {
                setShareBytes(toBytes((String) obj));
                return;
            }
            throw new SQLException("Initialization failed");
        }
    }

    public DATE(Object obj, Calendar cal) throws SQLException {
        if (obj instanceof Date) {
            setShareBytes(toBytes((Date) obj, cal));
            return;
        }
        if (obj instanceof Time) {
            setShareBytes(toBytes((Time) obj, cal));
        } else if (obj instanceof Timestamp) {
            setShareBytes(toBytes((Timestamp) obj, cal));
        } else {
            if (obj instanceof String) {
                setShareBytes(toBytes((String) obj, cal));
                return;
            }
            throw new SQLException("Initialization failed");
        }
    }

    public DATE(LocalDate ld) throws SQLException {
        super(toBytes(ld));
    }

    public DATE(LocalDateTime ldt) throws SQLException {
        super(toBytes(ldt));
    }

    public DATE(LocalTime lt) throws SQLException {
        super(toBytes(lt));
    }

    public DATE(OffsetDateTime odt) throws SQLException {
        super(toBytes(odt));
    }

    public DATE(OffsetTime ot) throws SQLException {
        super(toBytes(ot));
    }

    public DATE(ZonedDateTime zdt) throws SQLException {
        super(toBytes(zdt));
    }

    public static Date toDate(byte[] date) {
        return toDate(date, null);
    }

    public static Time toTime(byte[] date) {
        return toTime(date, null);
    }

    public static Timestamp toTimestamp(byte[] date) {
        return toTimestamp(date, null);
    }

    public static Date toDate(byte[] date, Calendar cal) {
        Calendar cal1;
        int[] result = new int[7];
        for (int i = 0; i < 7; i++) {
            result[i] = date[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.set(1, year);
        cal1.set(2, result[2] - 1);
        cal1.set(5, result[3]);
        if (!OracleDriver.getSystemPropertyDateZeroTimeExtra()) {
            cal1.set(11, result[4] - 1);
            cal1.set(12, result[5] - 1);
            cal1.set(13, result[6] - 1);
        } else {
            cal1.set(11, 0);
            cal1.set(12, 0);
            cal1.set(13, 0);
        }
        cal1.set(14, 0);
        Date newDate = new Date(cal1.getTime().getTime());
        return newDate;
    }

    public static Time toTime(byte[] date, Calendar cal) {
        Calendar cal1;
        int hour = date[4] & 255;
        int minute = date[5] & 255;
        int second = date[6] & 255;
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.set(1, 1970);
        cal1.set(2, 0);
        cal1.set(5, 1);
        cal1.set(11, hour - 1);
        cal1.set(12, minute - 1);
        cal1.set(13, second - 1);
        cal1.set(14, 0);
        return new Time(cal1.getTime().getTime());
    }

    public static Timestamp toTimestamp(byte[] date, Calendar cal) {
        Calendar cal1;
        int[] result = new int[7];
        for (int i = 0; i < 7; i++) {
            result[i] = date[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.set(1, year);
        cal1.set(2, result[2] - 1);
        cal1.set(5, result[3]);
        cal1.set(11, result[4] - 1);
        cal1.set(12, result[5] - 1);
        cal1.set(13, result[6] - 1);
        cal1.set(14, 0);
        Timestamp newTimestamp = new Timestamp(cal1.getTime().getTime());
        return newTimestamp;
    }

    public static String toString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return new String();
        }
        int[] result = new int[7];
        for (int i = 0; i < 7; i++) {
            if (bytes[i] < 0) {
                result[i] = bytes[i] + 256;
            } else {
                result[i] = bytes[i];
            }
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        int month = result[2];
        int day = result[3];
        int hours = result[4] - 1;
        int minutes = result[5] - 1;
        int seconds = result[6] - 1;
        String sResult = TIMESTAMPTZ.toString(year, month, day, hours, minutes, seconds, -1, null);
        return sResult;
    }

    @Override // oracle.jdbc.internal.OracleDate
    public byte[] toBytes() {
        return getBytes();
    }

    public static byte[] toBytes(Date date) {
        return toBytes(date, (Calendar) null);
    }

    public static byte[] toBytes(Time time) {
        return toBytes(time, (Calendar) null);
    }

    public static byte[] toBytes(Timestamp timestamp) {
        return toBytes(timestamp, (Calendar) null);
    }

    public static byte[] toBytes(Date date, Calendar cal) {
        Calendar cal1;
        if (date == null) {
            return null;
        }
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.setTime(date);
        byte[] result = new byte[7];
        int year = TIMESTAMP.getOracleYear(cal1);
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) (cal1.get(2) + 1);
        result[3] = (byte) cal1.get(5);
        if (!OracleDriver.getSystemPropertyDateZeroTime()) {
            result[4] = (byte) (cal1.get(11) + 1);
            result[5] = (byte) (cal1.get(12) + 1);
            result[6] = (byte) (cal1.get(13) + 1);
        } else {
            result[4] = 1;
            result[5] = 1;
            result[6] = 1;
        }
        return result;
    }

    public static byte[] toBytes(Time time, Calendar cal) {
        Calendar cal1;
        if (time == null) {
            return null;
        }
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.setTime(time);
        byte[] result = {119, -86, 1, 1, (byte) (cal1.get(11) + 1), (byte) (cal1.get(12) + 1), (byte) (cal1.get(13) + 1)};
        return result;
    }

    public static byte[] toBytes(Timestamp timestamp, Calendar cal) {
        Calendar cal1;
        if (timestamp == null) {
            return null;
        }
        if (cal == null) {
            cal1 = Calendar.getInstance();
        } else {
            cal1 = Calendar.getInstance(cal.getTimeZone());
        }
        cal1.clear();
        cal1.setTime(timestamp);
        int year = TIMESTAMP.getOracleYear(cal1);
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) (cal1.get(2) + 1), (byte) cal1.get(5), (byte) (cal1.get(11) + 1), (byte) (cal1.get(12) + 1), (byte) (cal1.get(13) + 1)};
        return result;
    }

    public static byte[] toBytes(String str) {
        if (str == null) {
            return null;
        }
        byte[] result = toBytes(Timestamp.valueOf(str));
        return result;
    }

    public static byte[] toBytes(String str, Calendar cal) {
        return toBytes(Timestamp.valueOf(str), cal);
    }

    @Override // oracle.sql.Datum
    public Date dateValue() {
        return toDate(getBytes());
    }

    @Override // oracle.sql.Datum
    public Time timeValue() {
        return toTime(getBytes());
    }

    @Override // oracle.sql.Datum
    public Timestamp timestampValue() {
        return toTimestamp(getBytes());
    }

    public Date dateValue(Calendar cal) {
        return toDate(getBytes(), cal);
    }

    @Override // oracle.sql.Datum
    public Time timeValue(Calendar cal) {
        return toTime(getBytes(), cal);
    }

    @Override // oracle.sql.Datum
    public Timestamp timestampValue(Calendar cal) {
        return toTimestamp(getBytes(), cal);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        return toString(getBytes());
    }

    public String toString() {
        return stringValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() {
        return timestampValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        Timestamp[] ts = new Timestamp[arraySize];
        return ts;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> cls) {
        if (cls == Date.class || cls == Time.class || cls == Timestamp.class || cls == LocalDateTime.class || cls == LocalDate.class || cls == LocalTime.class || cls == String.class) {
            return true;
        }
        return false;
    }

    public DATE addJulianDays(int julianDay, int julianSec) throws SQLException {
        return new DATE(_getLdxLib().ldxads(shareBytes(), julianDay, julianSec));
    }

    public DATE addMonths(int months) throws SQLException {
        return new DATE(_getLdxLib().ldxadm(shareBytes(), months));
    }

    public DATE zeroTime() throws SQLException {
        byte[] tmpDate = toBytes();
        tmpDate[4] = 1;
        tmpDate[5] = 1;
        tmpDate[6] = 1;
        return new DATE(tmpDate);
    }

    public void diffInJulianDays(DATE date, int[] julianDay, int[] julianSec) throws SQLException {
        _getLdxLib().ldxsub(shareBytes(), date.shareBytes(), julianDay, julianSec);
    }

    public NUMBER diffInMonths(DATE date) throws SQLException {
        return new NUMBER(_getLdxLib().ldxsbm(shareBytes(), date.shareBytes()));
    }

    public static DATE getCurrentDate() throws SQLException {
        return new DATE(_getLdxLib().ldxgdt());
    }

    public static int checkValidity(byte[] date) throws SQLException {
        return _getLdxLib().ldxchk(date);
    }

    public static DATE fromJulianDays(int julianDay, int julianSec) throws SQLException {
        return new DATE(_getLdxLib().ldxdfd(julianDay, julianSec));
    }

    public static DATE fromText(String datestr, String fmt, String lang) throws SQLException {
        return new DATE(_getLdxLib().ldxstd(datestr, fmt, lang));
    }

    public DATE lastDayOfMonth() throws SQLException {
        return new DATE(_getLdxLib().ldxldd(shareBytes()));
    }

    public static void numberToJulianDays(NUMBER num, int[] julianDay, int[] julianSec) throws SQLException {
        _getLdxLib().ldxftd(num.toBytes(), julianDay, julianSec);
    }

    public DATE round(String prec) throws SQLException {
        return new DATE(_getLdxLib().ldxrnd(shareBytes(), prec));
    }

    public DATE setDayOfWeek(int day) throws SQLException {
        return new DATE(_getLdxLib().ldxnxd(shareBytes(), day));
    }

    public void toJulianDays(int[] julianDay, int[] julianSec) throws SQLException {
        _getLdxLib().ldxdtd(shareBytes(), julianDay, julianSec);
    }

    public NUMBER toNumber() throws SQLException {
        return new NUMBER(_getLdxLib().ldxdyf(shareBytes()));
    }

    public String toText(String fmt, String lang) throws SQLException {
        return _getLdxLib().ldxdts(shareBytes(), fmt, lang);
    }

    public String toText(byte[] pfmt, String lang) throws SQLException {
        return _getLdxLib().ldxdts(shareBytes(), pfmt, lang);
    }

    public static byte[] parseFormat(String fmt, String lang) throws SQLException {
        return _getLdxLib().ldxsto(fmt, lang);
    }

    public DATE truncate(String prec) throws SQLException {
        return new DATE(_getLdxLib().ldxtrn(shareBytes(), prec));
    }

    public int compareTo(DATE date) {
        return compareBytes(shareBytes(), date.shareBytes());
    }

    private static byte[] _initDate() {
        byte[] tmp = {119, -86, 1, 1, 1, 1, 1};
        return tmp;
    }

    private static LdxLib _getLdxLib() {
        if (_sldxlib == null) {
            _sldxlib = new LdxLibThin();
        }
        return _sldxlib;
    }

    private static void _printBytes(byte[] date) {
        System.out.println(toString(date));
    }

    public static DATE of(LocalDate ld) throws SQLException {
        return new DATE(ld);
    }

    public LocalDate toLocalDate() throws SQLException {
        return toLocalDate(getBytes());
    }

    public LocalDate localDateValue() throws SQLException {
        return toLocalDate(getBytes());
    }

    public static DATE of(LocalDateTime ldt) throws SQLException {
        return new DATE(ldt);
    }

    public LocalDateTime toLocalDateTime() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    public LocalDateTime localDateTimeValue() throws SQLException {
        return toLocalDateTime(getBytes());
    }

    public static DATE of(LocalTime lt) throws SQLException {
        return new DATE(lt);
    }

    public LocalTime toLocalTime() throws SQLException {
        return toLocalTime(getBytes());
    }

    public LocalTime localTimeValue() throws SQLException {
        return toLocalTime(getBytes());
    }

    public static DATE of(OffsetDateTime odt) throws SQLException {
        return new DATE(odt);
    }

    public static DATE of(OffsetTime ot) throws SQLException {
        return new DATE(ot);
    }

    public static DATE of(ZonedDateTime zdt) throws SQLException {
        return new DATE(zdt);
    }

    public static LocalDate toLocalDate(byte[] date) throws SQLException {
        return toLocalDateTime(date).toLocalDate();
    }

    public static LocalTime toLocalTime(byte[] date) throws SQLException {
        return toLocalDateTime(date).toLocalTime();
    }

    public static LocalDateTime toLocalDateTime(byte[] date) throws SQLException {
        int[] result = new int[7];
        for (int i = 0; i < 7; i++) {
            result[i] = date[i] & 255;
        }
        int year = TIMESTAMP.getJavaYear(result[0], result[1]);
        LocalDateTime ldt = LocalDateTime.of(year, result[2], result[3], result[4] - 1, result[5] - 1, result[6] - 1);
        return ldt;
    }

    public static byte[] toBytes(LocalDate ld) {
        if (ld == null) {
            return null;
        }
        return toBytes(ld.atTime(0, 0, 0));
    }

    public static byte[] toBytes(LocalDateTime ldt) {
        if (ldt == null) {
            return null;
        }
        int year = TIMESTAMP.getOracleYear(ldt.getYear());
        int month = ldt.getMonthValue();
        int date = ldt.getDayOfMonth();
        int hour = ldt.getHour();
        int minute = ldt.getMinute();
        int second = ldt.getSecond();
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) month, (byte) date, (byte) (hour + 1), (byte) (minute + 1), (byte) (second + 1)};
        return result;
    }

    public static byte[] toBytes(LocalTime lt) {
        if (lt == null) {
            return null;
        }
        return toBytes(lt.atDate(LocalDate.of(1970, 1, 1)));
    }

    public static byte[] toBytes(OffsetDateTime odt) {
        if (odt == null) {
            return null;
        }
        OffsetDateTime odtutc = odt.withOffsetSameInstant(ZoneOffset.UTC);
        int year = TIMESTAMP.getOracleYear(odtutc.getYear());
        int month = odtutc.getMonthValue();
        int date = odtutc.getDayOfMonth();
        int hour = odtutc.getHour();
        int minute = odtutc.getMinute();
        int second = odtutc.getSecond();
        byte[] result = {(byte) ((year / 100) + 100), (byte) ((year % 100) + 100), (byte) month, (byte) date, (byte) (hour + 1), (byte) (minute + 1), (byte) (second + 1)};
        return result;
    }

    public static byte[] toBytes(OffsetTime ot) {
        if (ot == null) {
            return null;
        }
        return toBytes(ot.atDate(LocalDate.of(1970, 1, 1)));
    }

    public static byte[] toBytes(ZonedDateTime zdt) {
        if (zdt == null) {
            return null;
        }
        return toBytes(zdt.toOffsetDateTime());
    }
}
