package oracle.sql;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.SQLException;
import oracle.core.lmx.CoreException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleNumber;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/sql/NUMBER.class */
public class NUMBER extends Datum implements OracleNumber {
    private static final int CHARACTER_ZERO = 48;
    private static final byte DIGEND = 21;
    private static final byte ODIGEND = 9;
    private static final int HUNDIGMAX = 66;
    private static final int BIGINTARRAYMAX = 54;
    private static final double BIGRATIO = 0.1505149978319906d;
    private static final int BIGLENMAX = 22;
    static final byte LNXM_NUM = 22;
    static final int LNXSGNBT = 128;
    static final byte LNXDIGS = 20;
    static final byte LNXEXPBS = 64;
    static final double ORANUM_FBASE = 100.0d;
    static final int LNXBASE = 100;
    static final byte IEEE_DBL_DIG = 15;
    private static final byte IEEE_FLT_DIG = 6;
    static final int LNXEXPMX = 127;
    static final int LNXEXPMN = 0;
    static final int LNXMXOUT = 40;
    static final int LNXMXFMT = 64;
    private static final byte MAX_LONG_BASE100_DIGITS = 9;
    static final long serialVersionUID = -1656085588913430059L;
    private static final byte[] MAX_LONG = toBytes(Long.MAX_VALUE);
    private static final byte[] MIN_LONG = toBytes(Long.MIN_VALUE);
    private static final byte[] MAX_INT = toBytes(Integer.MAX_VALUE);
    private static final byte[] MIN_INT = toBytes(SQLnetDef.NSPCNCON);
    private static final byte[] MAX_SHORT = toBytes(Short.MAX_VALUE);
    private static final byte[] MIN_SHORT = toBytes(Short.MIN_VALUE);
    private static final byte[] MAX_BYTE = toBytes(Byte.MAX_VALUE);
    private static final byte[] MIN_BYTE = toBytes(Byte.MIN_VALUE);
    private static final BigDecimal BIGDEC_NEGZERO = new BigDecimal("-0");
    private static final BigDecimal BIGDEC_ZERO = BigDecimal.valueOf(0L);
    private static final BigDecimal BIGDEC_ONE = BigDecimal.valueOf(1L);
    private static final BigInteger BIGINT_ZERO = BigInteger.valueOf(0);
    private static final BigInteger BIGINT_HUND = BigInteger.valueOf(100);
    private static final byte[] PI = {-63, 4, 15, 16, 93, 66, 36, 90, 80, 33, 39, 47, 27, 44, 39, 33, 80, 51, 29, 85, 21};
    private static final byte[] E = {-63, 3, 72, 83, 82, 83, 85, 60, 5, 53, 36, 37, 3, 88, 48, 14, 53, 67, 25, 98, 77};
    private static final byte[] LN10 = {-63, 3, 31, 26, 86, 10, 30, 95, 5, 57, 85, 2, 80, 92, 46, 47, 85, 37, 43, 8, 61};
    private static int DBL_MAX = 40;
    private static int INT_MAX = 15;
    private static float FLOAT_MAX_INT = 2.1474836E9f;
    private static float FLOAT_MIN_INT = -2.1474836E9f;
    private static double DOUBLE_MAX_INT = 2.147483647E9d;
    private static double DOUBLE_MIN_INT = -2.147483648E9d;
    private static double DOUBLE_MAX_INT_2 = 2.147483649E9d;
    private static double DOUBLE_MIN_INT_2 = -2.147483649E9d;
    private static String LANGID = "AMERICAN";

    public NUMBER() {
        super(_makeZero());
    }

    public NUMBER(byte[] num) {
        super(num);
    }

    public NUMBER(byte byteNum) {
        super(toBytes(byteNum));
    }

    public NUMBER(int intNum) {
        super(toBytes(intNum));
    }

    public NUMBER(long longNum) {
        super(toBytes(longNum));
    }

    public NUMBER(short shortNum) {
        super(toBytes(shortNum));
    }

    public NUMBER(float floatNum) {
        super(toBytes(floatNum));
    }

    public NUMBER(double doubleNum) throws SQLException {
        super(toBytes(doubleNum));
    }

    public NUMBER(BigDecimal BigDecNum) throws SQLException {
        super(toBytes(BigDecNum));
    }

    public NUMBER(BigInteger BigIntNum) throws SQLException {
        super(toBytes(BigIntNum));
    }

    public NUMBER(String StringNum, int scale) throws SQLException {
        super(toBytes(StringNum, scale));
    }

    public NUMBER(boolean boolNum) {
        super(toBytes(boolNum));
    }

    public NUMBER(Object obj) throws SQLException {
        if (obj instanceof Integer) {
            setShareBytes(toBytes(((Integer) obj).intValue()));
            return;
        }
        if (obj instanceof Long) {
            setShareBytes(toBytes(((Long) obj).longValue()));
            return;
        }
        if (obj instanceof Float) {
            setShareBytes(toBytes(((Float) obj).floatValue()));
            return;
        }
        if (obj instanceof Double) {
            setShareBytes(toBytes(((Double) obj).doubleValue()));
            return;
        }
        if (obj instanceof BigInteger) {
            setShareBytes(toBytes((BigInteger) obj));
            return;
        }
        if (obj instanceof BigDecimal) {
            setShareBytes(toBytes((BigDecimal) obj));
            return;
        }
        if (obj instanceof Boolean) {
            setShareBytes(toBytes(((Boolean) obj).booleanValue()));
            return;
        }
        if (obj instanceof String) {
            setShareBytes(stringToBytes((String) obj));
        } else if (obj instanceof Short) {
            setShareBytes(toBytes(((Short) obj).shortValue()));
        } else {
            if (obj instanceof Byte) {
                setShareBytes(toBytes(((Byte) obj).byteValue()));
                return;
            }
            throw new SQLException("Initialization failed");
        }
    }

    public static double toDouble(byte[] num) {
        if (_isZero(num)) {
            return 0.0d;
        }
        if (_isPosInf(num)) {
            return Double.POSITIVE_INFINITY;
        }
        if (_isNegInf(num)) {
            return Double.NEGATIVE_INFINITY;
        }
        try {
            String dblStr = _getLnxLib().lnxnuc(num, DBL_MAX, null);
            double f = Double.valueOf(dblStr).doubleValue();
            return f;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public static float toFloat(byte[] num) {
        return (float) toDouble(num);
    }

    private static long toLongInternal(byte[] num, byte[] max, byte[] min) throws SQLException {
        if (_isZero(num)) {
            return 0L;
        }
        if (_isInf(num) || compareBytes(num, max) > 0 || compareBytes(num, min) < 0) {
            throw new SQLException(CoreException.getMessage((byte) 3));
        }
        return _getLnxLib().lnxsni(num);
    }

    public static long toLong(byte[] num) throws SQLException {
        return toLongInternal(num, MAX_LONG, MIN_LONG);
    }

    public static int toInt(byte[] num) throws SQLException {
        return (int) toLongInternal(num, MAX_INT, MIN_INT);
    }

    public static short toShort(byte[] num) throws SQLException {
        return (short) toLongInternal(num, MAX_SHORT, MIN_SHORT);
    }

    public static byte toByte(byte[] num) throws SQLException {
        return (byte) toLongInternal(num, MAX_BYTE, MIN_BYTE);
    }

    public static BigInteger toBigInteger(byte[] num) throws SQLException {
        byte oidx;
        int clen;
        int numbytes;
        int arycnt;
        long[] digit = new long[10];
        if (_isZero(num)) {
            return BIGINT_ZERO;
        }
        if (_isInf(num)) {
            throw new SQLException(CoreException.getMessage((byte) 3));
        }
        boolean positive = _isPositive(num);
        byte[] temp = _fromLnxFmt(num);
        if (temp[0] < 0) {
            return BIGINT_ZERO;
        }
        int mantlen = Math.min(temp[0] + 1, temp.length - 1);
        if ((mantlen & 1) == 1) {
            digit[9] = temp[1];
            oidx = (byte) (1 + 1);
            clen = mantlen - 1;
        } else {
            digit[9] = (temp[1] * 100) + temp[1 + 1];
            oidx = (byte) (1 + 2);
            clen = mantlen - 2;
        }
        byte cnt = 9;
        while (clen != 0) {
            long value = (temp[oidx] * 100) + temp[oidx + 1];
            byte b = 9;
            while (true) {
                byte digidx = b;
                if (digidx < cnt) {
                    break;
                }
                long value2 = value + (digit[digidx] * 10000);
                digit[digidx] = value2 & 65535;
                value = value2 >> 16;
                b = (byte) (digidx - 1);
            }
            if (value != 0) {
                cnt = (byte) (cnt - 1);
                digit[cnt] = value;
            }
            oidx = (byte) (oidx + 2);
            clen -= 2;
        }
        if ((digit[cnt] >> 8) != 0) {
            numbytes = (2 * (9 - cnt)) + 2;
        } else {
            numbytes = (2 * (9 - cnt)) + 1;
        }
        byte[] barray = new byte[numbytes];
        if ((numbytes & 1) == 1) {
            barray[0] = (byte) digit[cnt];
            arycnt = 0 + 1;
        } else {
            barray[0] = (byte) (digit[cnt] >> 8);
            int arycnt2 = 0 + 1;
            barray[arycnt2] = (byte) (digit[cnt] & 255);
            arycnt = arycnt2 + 1;
        }
        while (true) {
            cnt = (byte) (cnt + 1);
            if (cnt > 9) {
                break;
            }
            barray[arycnt] = (byte) (digit[cnt] >> 8);
            barray[arycnt + 1] = (byte) (digit[cnt] & 255);
            arycnt += 2;
        }
        BigInteger result = new BigInteger(positive ? 1 : -1, barray);
        int lhd = temp[0] - (mantlen - 1);
        return result.multiply(BIGINT_HUND.pow(lhd));
    }

    public static BigDecimal toBigDecimal(byte[] num) throws SQLException {
        if (_isZero(num)) {
            return BIGDEC_ZERO;
        }
        if (_isInf(num)) {
            throw new SQLException(CoreException.getMessage((byte) 3));
        }
        BigDecimal result = toBigDecimalLong(num);
        return result == null ? toBigDecimalFull(num) : result;
    }

    private static BigDecimal toBigDecimalLong(byte[] num) {
        int mantlen = getOraNumLength(num) - 1;
        if (mantlen > 9) {
            return null;
        }
        int scale = (getScale(num) - mantlen) + 1;
        if (mantlen + scale > 9) {
            return null;
        }
        long mantissa = 0;
        long n = 1;
        for (int i = mantlen; i > 0; i--) {
            try {
                mantissa += Math.multiplyExact(unpackBase100(num, i), n);
                n *= 100;
            } catch (ArithmeticException e) {
                return null;
            }
        }
        int baseTenScale = scale * 2;
        while (baseTenScale > 0) {
            try {
                mantissa = Math.multiplyExact(mantissa, 10L);
                baseTenScale--;
            } catch (ArithmeticException e2) {
                return null;
            }
        }
        while (baseTenScale < 0 && mantissa % 10 == 0) {
            mantissa /= 10;
            baseTenScale++;
        }
        if (!_isPositive(num)) {
            mantissa = -mantissa;
        }
        return BigDecimal.valueOf(mantissa, -baseTenScale);
    }

    private static int getOraNumLength(byte[] num) {
        int n = num.length - 1;
        return (_isPositive(num) || (n == 20 && num[n] != 102)) ? num.length : num.length - 1;
    }

    private static int getScale(byte[] num) {
        if (_isPositive(num)) {
            return (byte) ((num[0] & (-129)) - 65);
        }
        return (byte) (((num[0] ^ (-1)) & (-129)) - 65);
    }

    private static long unpackBase100(byte[] num, int i) {
        return _isPositive(num) ? num[i] - 1 : (byte) (101 - num[i]);
    }

    private static BigDecimal toBigDecimalFull(byte[] num) {
        byte oidx;
        int clen;
        int numbytes;
        int arycnt;
        long[] digit = new long[10];
        boolean positive = _isPositive(num);
        byte[] temp = _fromLnxFmt(num);
        int clen2 = temp.length - 1;
        if ((clen2 & 1) == 1) {
            digit[9] = temp[1];
            oidx = (byte) (1 + 1);
            clen = clen2 - 1;
        } else {
            digit[9] = (temp[1] * 100) + temp[1 + 1];
            oidx = (byte) (1 + 2);
            clen = clen2 - 2;
        }
        byte cnt = 9;
        while (clen != 0) {
            long value = (temp[oidx] * 100) + temp[oidx + 1];
            byte b = 9;
            while (true) {
                byte digidx = b;
                if (digidx < cnt) {
                    break;
                }
                long value2 = value + (digit[digidx] * 10000);
                digit[digidx] = value2 & 65535;
                value = value2 >> 16;
                b = (byte) (digidx - 1);
            }
            if (value != 0) {
                cnt = (byte) (cnt - 1);
                digit[cnt] = value;
            }
            oidx = (byte) (oidx + 2);
            clen -= 2;
        }
        if ((digit[cnt] >> 8) != 0) {
            numbytes = (2 * (9 - cnt)) + 2;
        } else {
            numbytes = (2 * (9 - cnt)) + 1;
        }
        byte[] barray = new byte[numbytes];
        if ((numbytes & 1) == 1) {
            barray[0] = (byte) digit[cnt];
            arycnt = 0 + 1;
        } else {
            barray[0] = (byte) (digit[cnt] >> 8);
            int arycnt2 = 0 + 1;
            barray[arycnt2] = (byte) (digit[cnt] & 255);
            arycnt = arycnt2 + 1;
        }
        while (true) {
            cnt = (byte) (cnt + 1);
            if (cnt > 9) {
                break;
            }
            barray[arycnt] = (byte) (digit[cnt] >> 8);
            barray[arycnt + 1] = (byte) (digit[cnt] & 255);
            arycnt += 2;
        }
        BigInteger bigtemp = new BigInteger(positive ? 1 : -1, barray);
        BigDecimal result = new BigDecimal(bigtemp);
        int scale = (temp[0] - clen2) + 1;
        BigDecimal result2 = result.movePointRight(scale * 2);
        if (scale < 0 && temp[clen2] % 10 == 0) {
            result2 = result2.setScale(-((scale * 2) + 1));
        }
        return result2;
    }

    public static String toString(byte[] num) {
        int char_space;
        int ci;
        int ci2 = 0;
        if (_isZero(num)) {
            return "0";
        }
        if (_isPosInf(num)) {
            return new Double(Double.POSITIVE_INFINITY).toString();
        }
        if (_isNegInf(num)) {
            return new Double(Double.NEGATIVE_INFINITY).toString();
        }
        if (!isValid(num)) {
            throw new IllegalArgumentException(CoreException.getMessage((byte) 11));
        }
        byte[] oranum = _fromLnxFmt(num);
        int exponent = oranum[0];
        int mantlen = oranum.length - 1;
        int lhd = exponent - (mantlen - 1);
        if (lhd >= 0) {
            char_space = (2 * (exponent + 1)) + 1;
        } else if (exponent >= 0) {
            char_space = 2 * (mantlen + 1);
        } else {
            char_space = (2 * (mantlen - exponent)) + 3;
        }
        char[] chars = new char[char_space];
        if (!_isPositive(num)) {
            ci2 = 0 + 1;
            chars[0] = '-';
        }
        if (lhd >= 0) {
            ci = ci2 + _byteToChars(oranum[1], chars, ci2);
            int i = 2;
            while (i <= mantlen) {
                _byteTo2Chars(oranum[i], chars, ci);
                ci += 2;
                i++;
                exponent--;
            }
            if (exponent > 0) {
                while (exponent > 0) {
                    int i2 = ci;
                    int ci3 = ci + 1;
                    chars[i2] = '0';
                    ci = ci3 + 1;
                    chars[ci3] = '0';
                    exponent--;
                }
            }
        } else {
            int point = mantlen + lhd;
            if (point > 0) {
                int ci4 = ci2 + _byteToChars(oranum[1], chars, ci2);
                if (point == 1) {
                    ci4++;
                    chars[ci4] = '.';
                }
                int i3 = 2;
                while (i3 < mantlen) {
                    _byteTo2Chars(oranum[i3], chars, ci4);
                    ci4 += 2;
                    if (point == i3) {
                        ci4++;
                        chars[ci4] = '.';
                    }
                    i3++;
                }
                if (oranum[i3] % 10 == 0) {
                    ci = ci4 + _byteToChars((byte) (oranum[i3] / 10), chars, ci4);
                } else {
                    _byteTo2Chars(oranum[i3], chars, ci4);
                    ci = ci4 + 2;
                }
            } else {
                int i4 = ci2;
                int ci5 = ci2 + 1;
                chars[i4] = '0';
                int ci6 = ci5 + 1;
                chars[ci5] = '.';
                while (point < 0) {
                    int i5 = ci6;
                    int ci7 = ci6 + 1;
                    chars[i5] = '0';
                    ci6 = ci7 + 1;
                    chars[ci7] = '0';
                    point++;
                }
                int i6 = 1;
                while (i6 < mantlen) {
                    _byteTo2Chars(oranum[i6], chars, ci6);
                    ci6 += 2;
                    i6++;
                }
                if (oranum[i6] % 10 == 0) {
                    ci = ci6 + _byteToChars((byte) (oranum[i6] / 10), chars, ci6);
                } else {
                    _byteTo2Chars(oranum[i6], chars, ci6);
                    ci = ci6 + 2;
                }
            }
        }
        return new String(chars, 0, ci);
    }

    public static boolean toBoolean(byte[] num) {
        if (_isZero(num)) {
            return false;
        }
        return true;
    }

    public static byte[] toBytes(double doubleNum) throws SQLException {
        if (Double.isNaN(doubleNum)) {
            throw new IllegalArgumentException(CoreException.getMessage((byte) 11));
        }
        return (doubleNum == 0.0d || doubleNum == -0.0d) ? _makeZero() : doubleNum == Double.POSITIVE_INFINITY ? _makePosInf() : doubleNum == Double.NEGATIVE_INFINITY ? _makeNegInf() : _getLnxLib().lnxren(doubleNum);
    }

    public static byte[] toBytes(float floatNum) {
        if (Float.isNaN(floatNum)) {
            throw new IllegalArgumentException(CoreException.getMessage((byte) 11));
        }
        if (floatNum == 0.0f || floatNum == -0.0f) {
            return _makeZero();
        }
        if (floatNum == Float.POSITIVE_INFINITY) {
            return _makePosInf();
        }
        if (floatNum == Float.NEGATIVE_INFINITY) {
            return _makeNegInf();
        }
        String floatStr = Float.toString(floatNum);
        try {
            return _getLnxLib().lnxcpn(floatStr, false, 0, false, 0, "AMERICAN_AMERICA");
        } catch (Exception e) {
            return null;
        }
    }

    public static byte[] toBytes(long longNum) {
        return _getLnxLib().lnxmin(longNum);
    }

    public static byte[] toBytes(int intNum) {
        return toBytes(intNum);
    }

    public static int toBytes(long longNum, byte[] dst, int offset) {
        return _getLnxLib().lnxmin(longNum, dst, offset);
    }

    public static byte[] toBytes(short shortNum) {
        return toBytes(shortNum);
    }

    public static byte[] toBytes(byte byteNum) {
        return toBytes(byteNum);
    }

    public static byte[] toBytes(BigInteger BigIntNum) throws SQLException {
        byte[] temp;
        int exponent;
        byte bidx;
        int blen;
        int mantlen;
        int oidx;
        if (BigIntNum == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "input should not be null").fillInStackTrace());
        }
        byte[] mantissa = new byte[66];
        long[] bnum = new long[54];
        long[] digit = new long[22];
        byte dstart = 21;
        boolean positive = true;
        if (BigIntNum.signum() == 0) {
            return _makeZero();
        }
        if (BigIntNum.signum() == -1) {
            BigInteger Num = BigIntNum.abs();
            positive = false;
            temp = Num.toByteArray();
            exponent = (int) Math.floor(Num.bitLength() * BIGRATIO);
        } else {
            temp = BigIntNum.toByteArray();
            exponent = (int) Math.floor(BigIntNum.bitLength() * BIGRATIO);
        }
        if (BigIntNum.abs().compareTo(BIGINT_HUND.pow(exponent)) < 0) {
            exponent--;
        }
        if (temp.length > 54) {
            throw new SQLException(CoreException.getMessage((byte) 3));
        }
        for (int i = 0; i < temp.length; i++) {
            if (temp[i] < 0) {
                bnum[i] = temp[i] + 256;
            } else {
                bnum[i] = temp[i];
            }
        }
        int blen2 = temp.length;
        switch (blen2 % 3) {
            case 1:
                digit[21] = bnum[0];
                bidx = (byte) (0 + 1);
                blen = blen2 - 1;
                break;
            case 2:
                digit[21] = (bnum[0] << 8) + bnum[0 + 1];
                bidx = (byte) (0 + 2);
                blen = blen2 - 2;
                break;
            default:
                long value = (bnum[0] << 16) + (bnum[0 + 1] << 8) + bnum[0 + 2];
                digit[21] = value % 1000000;
                digit[21 - 1] = value / 1000000;
                dstart = (byte) (21 - (digit[21 - 1] != 0 ? 1 : 0));
                bidx = (byte) (0 + 3);
                blen = blen2 - 3;
                break;
        }
        while (blen != 0) {
            long value2 = (bnum[bidx] << 4) + (bnum[bidx + 1] >> 4);
            byte b = 21;
            while (true) {
                byte digidx = b;
                if (digidx >= dstart) {
                    long value3 = value2 + (digit[digidx] << 12);
                    digit[digidx] = value3 % 1000000;
                    value2 = value3 / 1000000;
                    b = (byte) (digidx - 1);
                } else {
                    if (value2 != 0) {
                        dstart = (byte) (dstart - 1);
                        digit[dstart] = value2;
                    }
                    long value4 = ((bnum[bidx + 1] & 15) << 8) + bnum[bidx + 2];
                    byte b2 = 21;
                    while (true) {
                        byte digidx2 = b2;
                        if (digidx2 >= dstart) {
                            long value5 = value4 + (digit[digidx2] << 12);
                            digit[digidx2] = value5 % 1000000;
                            value4 = value5 / 1000000;
                            b2 = (byte) (digidx2 - 1);
                        } else {
                            if (value4 != 0) {
                                dstart = (byte) (dstart - 1);
                                digit[dstart] = value4;
                            }
                            bidx = (byte) (bidx + 3);
                            blen -= 3;
                        }
                    }
                }
            }
        }
        byte b3 = (byte) (digit[dstart] / 10000);
        mantissa[0] = b3;
        if (b3 != 0) {
            mantlen = (3 * (21 - dstart)) + 3;
            mantissa[0 + 1] = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0 + 2] = (byte) (digit[dstart] % 100);
            oidx = 0 + 3;
        } else {
            byte b4 = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0] = b4;
            if (b4 != 0) {
                mantlen = (3 * (21 - dstart)) + 2;
                mantissa[0 + 1] = (byte) (digit[dstart] % 100);
                oidx = 0 + 2;
            } else {
                mantissa[0] = (byte) digit[dstart];
                mantlen = (3 * (21 - dstart)) + 1;
                oidx = 0 + 1;
            }
        }
        byte b5 = dstart;
        while (true) {
            byte digidx3 = (byte) (b5 + 1);
            if (digidx3 <= 21) {
                mantissa[oidx] = (byte) (digit[digidx3] / 10000);
                mantissa[oidx + 1] = (byte) ((digit[digidx3] % 10000) / 100);
                mantissa[oidx + 2] = (byte) (digit[digidx3] % 100);
                oidx += 3;
                b5 = digidx3;
            } else {
                for (int i2 = oidx - 1; i2 >= 0 && mantissa[i2] == 0; i2--) {
                    mantlen--;
                }
                if (mantlen > 19) {
                    mantlen = 19;
                    if (mantissa[20] >= 50) {
                        int i3 = 20 - 1;
                        mantissa[i3] = (byte) (mantissa[i3] + 1);
                        while (true) {
                            if (mantissa[i3] == 100) {
                                if (i3 == 0) {
                                    exponent++;
                                    mantissa[i3] = 1;
                                } else {
                                    mantissa[i3] = 0;
                                    i3--;
                                    mantissa[i3] = (byte) (mantissa[i3] + 1);
                                }
                            }
                        }
                        for (int i4 = 19 - 1; i4 >= 0 && mantissa[i4] == 0; i4--) {
                            mantlen--;
                        }
                    }
                }
                if (exponent > 62) {
                    throw new SQLException(CoreException.getMessage((byte) 3));
                }
                byte[] oranum = new byte[mantlen + 1];
                oranum[0] = (byte) exponent;
                System.arraycopy(mantissa, 0, oranum, 1, mantlen);
                return _toLnxFmt(oranum, positive);
            }
        }
    }

    public static byte[] toBytes(BigDecimal BigDecNum) throws SQLException {
        BigDecimal DBTMP;
        int leftdigs;
        byte bidx;
        int blen;
        int mantlen;
        int oidx;
        int exponent;
        BigDecimal DBTMP2;
        if (BigDecNum == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "input should not be null").fillInStackTrace());
        }
        byte[] mantissa = new byte[66];
        long[] bnum = new long[54];
        long[] digit = new long[22];
        byte dstart = 21;
        BigDecimal BDABS = BigDecNum.abs();
        if (BigDecNum.signum() == 0) {
            return _makeZero();
        }
        boolean positive = BigDecNum.signum() != -1;
        int scale = BigDecNum.scale();
        if (scale < 0) {
            BigDecNum.setScale(0);
            scale = 0;
        }
        int rad = BDABS.compareTo(BIGDEC_ONE);
        int moves = 0;
        if (rad == -1) {
            do {
                moves++;
                DBTMP2 = BDABS.movePointRight(moves);
            } while (DBTMP2.compareTo(BIGDEC_ONE) < 0);
            leftdigs = -moves;
        } else {
            do {
                moves++;
                DBTMP = BDABS.movePointLeft(moves);
            } while (DBTMP.compareTo(BIGDEC_ONE) >= 0);
            leftdigs = moves;
        }
        byte[] temp = BDABS.movePointRight(scale).toBigInteger().toByteArray();
        if (temp.length > 54) {
            throw new SQLException(CoreException.getMessage((byte) 3));
        }
        for (int i = 0; i < temp.length; i++) {
            if (temp[i] < 0) {
                bnum[i] = temp[i] + 256;
            } else {
                bnum[i] = temp[i];
            }
        }
        int blen2 = temp.length;
        switch (blen2 % 3) {
            case 1:
                digit[21] = bnum[0];
                bidx = (byte) (0 + 1);
                blen = blen2 - 1;
                break;
            case 2:
                digit[21] = (bnum[0] << 8) + bnum[0 + 1];
                bidx = (byte) (0 + 2);
                blen = blen2 - 2;
                break;
            default:
                long value = (bnum[0] << 16) + (bnum[0 + 1] << 8) + bnum[0 + 2];
                digit[21] = value % 1000000;
                digit[21 - 1] = value / 1000000;
                dstart = (byte) (21 - (digit[21 - 1] != 0 ? 1 : 0));
                bidx = (byte) (0 + 3);
                blen = blen2 - 3;
                break;
        }
        while (blen != 0) {
            long value2 = (bnum[bidx] << 4) + (bnum[bidx + 1] >> 4);
            byte b = 21;
            while (true) {
                byte digidx = b;
                if (digidx >= dstart) {
                    long value3 = value2 + (digit[digidx] << 12);
                    digit[digidx] = value3 % 1000000;
                    value2 = value3 / 1000000;
                    b = (byte) (digidx - 1);
                } else {
                    if (value2 != 0) {
                        dstart = (byte) (dstart - 1);
                        digit[dstart] = value2;
                    }
                    long value4 = ((bnum[bidx + 1] & 15) << 8) + bnum[bidx + 2];
                    byte b2 = 21;
                    while (true) {
                        byte digidx2 = b2;
                        if (digidx2 >= dstart) {
                            long value5 = value4 + (digit[digidx2] << 12);
                            digit[digidx2] = value5 % 1000000;
                            value4 = value5 / 1000000;
                            b2 = (byte) (digidx2 - 1);
                        } else {
                            if (value4 != 0) {
                                dstart = (byte) (dstart - 1);
                                digit[dstart] = value4;
                            }
                            bidx = (byte) (bidx + 3);
                            blen -= 3;
                        }
                    }
                }
            }
        }
        byte b3 = (byte) (digit[dstart] / 10000);
        mantissa[0] = b3;
        if (b3 != 0) {
            mantlen = (3 * (21 - dstart)) + 3;
            mantissa[0 + 1] = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0 + 2] = (byte) (digit[dstart] % 100);
            oidx = 0 + 3;
        } else {
            byte b4 = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0] = b4;
            if (b4 != 0) {
                mantlen = (3 * (21 - dstart)) + 2;
                mantissa[0 + 1] = (byte) (digit[dstart] % 100);
                oidx = 0 + 2;
            } else {
                mantissa[0] = (byte) digit[dstart];
                mantlen = (3 * (21 - dstart)) + 1;
                oidx = 0 + 1;
            }
        }
        byte b5 = dstart;
        while (true) {
            byte digidx3 = (byte) (b5 + 1);
            if (digidx3 <= 21) {
                mantissa[oidx] = (byte) (digit[digidx3] / 10000);
                mantissa[oidx + 1] = (byte) ((digit[digidx3] % 10000) / 100);
                mantissa[oidx + 2] = (byte) (digit[digidx3] % 100);
                oidx += 3;
                b5 = digidx3;
            } else {
                for (int i2 = oidx - 1; i2 >= 0 && mantissa[i2] == 0; i2--) {
                    mantlen--;
                }
                if (scale > 0 && (scale & 1) != 0) {
                    int len = mantlen;
                    byte[] buf = new byte[len + 1];
                    if (mantissa[0] <= 9) {
                        int i3 = 0;
                        while (i3 < len - 1) {
                            buf[i3] = (byte) (((mantissa[i3] % 10) * 10) + (mantissa[i3 + 1] / 10));
                            i3++;
                        }
                        buf[i3] = (byte) ((mantissa[i3] % 10) * 10);
                        if (buf[len - 1] == 0) {
                            mantlen--;
                        }
                    } else {
                        buf[len] = (byte) ((mantissa[len - 1] % 10) * 10);
                        int i4 = len - 1;
                        while (i4 > 0) {
                            buf[i4] = (byte) ((mantissa[i4] / 10) + ((mantissa[i4 - 1] % 10) * 10));
                            i4--;
                        }
                        buf[i4] = (byte) (mantissa[i4] / 10);
                        if (buf[len] > 0) {
                            mantlen++;
                        }
                    }
                    System.arraycopy(buf, 0, mantissa, 0, mantlen);
                }
                if (mantlen > 20) {
                    mantlen = 20;
                    if (mantissa[20] >= 50) {
                        int i5 = 20 - 1;
                        mantissa[i5] = (byte) (mantissa[i5] + 1);
                        while (true) {
                            if (mantissa[i5] == 100) {
                                if (i5 == 0) {
                                    leftdigs++;
                                    mantissa[i5] = 1;
                                } else {
                                    mantissa[i5] = 0;
                                    i5--;
                                    mantissa[i5] = (byte) (mantissa[i5] + 1);
                                }
                            }
                        }
                    }
                    for (int i6 = 20 - 1; i6 >= 0 && mantissa[i6] == 0; i6--) {
                        mantlen--;
                    }
                }
                if (leftdigs <= 0) {
                    if (mantissa[0] < 10) {
                        exponent = ((-(2 - leftdigs)) / 2) + 1;
                    } else {
                        exponent = (-(2 - leftdigs)) / 2;
                    }
                } else {
                    exponent = (leftdigs - 1) / 2;
                }
                if (exponent > 62) {
                    throw new SQLException(CoreException.getMessage((byte) 3));
                }
                if (exponent < -65) {
                    throw new SQLException(CoreException.getMessage((byte) 2));
                }
                byte[] oranum = new byte[mantlen + 1];
                oranum[0] = (byte) exponent;
                System.arraycopy(mantissa, 0, oranum, 1, mantlen);
                return _toLnxFmt(oranum, positive);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0065 A[PHI: r30
      0x0065: PHI (r30v1 'eIndex' int) = (r30v0 'eIndex' int), (r30v2 'eIndex' int) binds: [B:9:0x0054, B:11:0x0062] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] toBytes(java.lang.String r8, int r9) throws java.sql.SQLException, java.lang.NumberFormatException {
        /*
            Method dump skipped, instructions count: 1593
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.sql.NUMBER.toBytes(java.lang.String, int):byte[]");
    }

    public static byte[] toBytes(boolean boolNum) {
        if (boolNum) {
            return toBytes(1L);
        }
        return toBytes(0L);
    }

    @Override // oracle.jdbc.internal.OracleNumber
    public byte[] toBytes() {
        return getBytes();
    }

    @Override // oracle.sql.Datum
    public double doubleValue() {
        return toDouble(shareBytes());
    }

    @Override // oracle.sql.Datum
    public float floatValue() {
        return toFloat(shareBytes());
    }

    @Override // oracle.sql.Datum
    public long longValue() throws SQLException {
        return toLong(shareBytes());
    }

    @Override // oracle.sql.Datum
    public int intValue() throws SQLException {
        return toInt(shareBytes());
    }

    public short shortValue() throws SQLException {
        return toShort(shareBytes());
    }

    @Override // oracle.sql.Datum
    public byte byteValue() throws SQLException {
        return toByte(shareBytes());
    }

    public BigInteger bigIntegerValue() throws SQLException {
        return toBigInteger(shareBytes());
    }

    @Override // oracle.sql.Datum
    public BigDecimal bigDecimalValue() throws SQLException {
        return toBigDecimal(shareBytes());
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        return toString(shareBytes());
    }

    @Override // oracle.sql.Datum
    public boolean booleanValue() {
        return toBoolean(shareBytes());
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        try {
            return bigDecimalValue();
        } catch (SQLException e) {
            return new SQLException(e.getMessage());
        }
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        BigDecimal[] bd = new BigDecimal[arraySize];
        return bd;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> cls) {
        String cls_name = cls.getName();
        if (cls_name.compareTo("java.lang.Integer") == 0 || cls_name.compareTo("java.lang.Long") == 0 || cls_name.compareTo("java.lang.Float") == 0 || cls_name.compareTo("java.lang.Double") == 0 || cls_name.compareTo("java.math.BigInteger") == 0 || cls_name.compareTo("java.math.BigDecimal") == 0 || cls_name.compareTo("java.lang.String") == 0 || cls_name.compareTo("java.lang.Boolean") == 0 || cls_name.compareTo("java.lang.Byte") == 0 || cls_name.compareTo("java.lang.Short") == 0) {
            return true;
        }
        return false;
    }

    public NUMBER abs() throws SQLException {
        return new NUMBER(_getLnxLib().lnxabs(shareBytes()));
    }

    public NUMBER acos() throws SQLException {
        return new NUMBER(_getLnxLib().lnxacos(shareBytes()));
    }

    public NUMBER add(NUMBER n) throws SQLException {
        return new NUMBER(_getLnxLib().lnxadd(shareBytes(), n.shareBytes()));
    }

    public NUMBER asin() throws SQLException {
        return new NUMBER(_getLnxLib().lnxasin(shareBytes()));
    }

    public NUMBER atan() throws SQLException {
        return new NUMBER(_getLnxLib().lnxatan(shareBytes()));
    }

    public NUMBER atan2(NUMBER x) throws SQLException {
        return new NUMBER(_getLnxLib().lnxatan2(shareBytes(), x.shareBytes()));
    }

    public NUMBER ceil() throws SQLException {
        return new NUMBER(_getLnxLib().lnxceil(shareBytes()));
    }

    public NUMBER cos() throws SQLException {
        return new NUMBER(_getLnxLib().lnxcos(shareBytes()));
    }

    public NUMBER cosh() throws SQLException {
        return new NUMBER(_getLnxLib().lnxcsh(shareBytes()));
    }

    public NUMBER decrement() throws SQLException {
        return new NUMBER(_getLnxLib().lnxdec(shareBytes()));
    }

    public NUMBER div(NUMBER n) throws SQLException {
        return new NUMBER(_getLnxLib().lnxdiv(shareBytes(), n.shareBytes()));
    }

    public NUMBER exp() throws SQLException {
        return new NUMBER(_getLnxLib().lnxexp(shareBytes()));
    }

    public NUMBER floatingPointRound(int precision) throws SQLException {
        return new NUMBER(_getLnxLib().lnxfpr(shareBytes(), precision));
    }

    public NUMBER floor() throws SQLException {
        return new NUMBER(_getLnxLib().lnxflo(shareBytes()));
    }

    public NUMBER increment() throws SQLException {
        return new NUMBER(_getLnxLib().lnxinc(shareBytes()));
    }

    public NUMBER ln() throws SQLException {
        return new NUMBER(_getLnxLib().lnxln(shareBytes()));
    }

    public NUMBER log(NUMBER base) throws SQLException {
        return new NUMBER(_getLnxLib().lnxlog(shareBytes(), base.shareBytes()));
    }

    public NUMBER mod(NUMBER n) throws SQLException {
        return new NUMBER(_getLnxLib().lnxmod(shareBytes(), n.shareBytes()));
    }

    public NUMBER mul(NUMBER n) throws SQLException {
        return new NUMBER(_getLnxLib().lnxmul(shareBytes(), n.shareBytes()));
    }

    public NUMBER negate() throws SQLException {
        return new NUMBER(_getLnxLib().lnxneg(shareBytes()));
    }

    public NUMBER pow(NUMBER exp) throws SQLException {
        return new NUMBER(_getLnxLib().lnxbex(shareBytes(), exp.shareBytes()));
    }

    public NUMBER pow(int exp) throws SQLException {
        return new NUMBER(_getLnxLib().lnxpow(shareBytes(), exp));
    }

    public NUMBER round(int decimal_place) throws SQLException {
        return new NUMBER(_getLnxLib().lnxrou(shareBytes(), decimal_place));
    }

    public NUMBER scale(int left, int right, boolean[] big) throws SQLException {
        return new NUMBER(_getLnxLib().lnxsca(shareBytes(), left, right, big));
    }

    public NUMBER shift(int digits) throws SQLException {
        return new NUMBER(_getLnxLib().lnxshift(shareBytes(), digits));
    }

    public NUMBER sin() throws SQLException {
        return new NUMBER(_getLnxLib().lnxsin(shareBytes()));
    }

    public NUMBER sinh() throws SQLException {
        return new NUMBER(_getLnxLib().lnxsnh(shareBytes()));
    }

    public NUMBER sqroot() throws SQLException {
        return new NUMBER(_getLnxLib().lnxsqr(shareBytes()));
    }

    public NUMBER sub(NUMBER n) throws SQLException {
        return new NUMBER(_getLnxLib().lnxsub(shareBytes(), n.shareBytes()));
    }

    public NUMBER tan() throws SQLException {
        return new NUMBER(_getLnxLib().lnxtan(shareBytes()));
    }

    public NUMBER tanh() throws SQLException {
        return new NUMBER(_getLnxLib().lnxtnh(shareBytes()));
    }

    public NUMBER truncate(int decimal_place) throws SQLException {
        return new NUMBER(_getLnxLib().lnxtru(shareBytes(), decimal_place));
    }

    public static NUMBER formattedTextToNumber(String num, String fmt, String lang) throws SQLException {
        return new NUMBER(_getLnxLib().lnxfcn(num, fmt, lang));
    }

    public static NUMBER textToPrecisionNumber(String num, boolean precflag, int preclen, boolean scaleflag, int scalelen, String lang) throws SQLException {
        return new NUMBER(_getLnxLib().lnxcpn(num, precflag, preclen, scaleflag, scalelen, lang));
    }

    public String toFormattedText(String fmt, String lang) throws SQLException {
        return _getLnxLib().lnxnfn(shareBytes(), fmt, lang);
    }

    public String toText(int outStringLength, String lang) throws SQLException {
        return _getLnxLib().lnxnuc(shareBytes(), outStringLength, lang);
    }

    public int compareTo(NUMBER n) {
        return compareBytes(shareBytes(), n.shareBytes());
    }

    public boolean isInf() {
        return _isInf(shareBytes());
    }

    public boolean isNegInf() {
        return _isNegInf(shareBytes());
    }

    public boolean isPosInf() {
        return _isPosInf(shareBytes());
    }

    public boolean isInt() {
        return _isInt(shareBytes());
    }

    public static boolean isValid(byte[] num) {
        int numlen = (byte) num.length;
        if (_isPositive(num)) {
            if (numlen == 1) {
                return _isZero(num);
            }
            if (num[0] == -1 && num[1] == 101) {
                return numlen == 2;
            }
            if (numlen > 21 || num[1] < 2 || num[numlen - 1] < 2) {
                return false;
            }
            for (int i = 1; i < numlen; i++) {
                byte digit = num[i];
                if (digit < 1 || digit > 100) {
                    return false;
                }
            }
            return true;
        }
        if (numlen < 3) {
            return _isNegInf(num);
        }
        if (numlen > 21) {
            return false;
        }
        if (num[numlen - 1] != 102) {
            if (numlen <= 20) {
                return false;
            }
        } else {
            numlen = (byte) (numlen - 1);
        }
        if (num[1] > 100 || num[numlen - 1] > 100) {
            return false;
        }
        for (int i2 = 1; i2 < numlen; i2++) {
            byte digit2 = num[i2];
            if (digit2 < 2 || digit2 > 101) {
                return false;
            }
        }
        return true;
    }

    public boolean isZero() {
        return _isZero(shareBytes());
    }

    public static NUMBER e() {
        return new NUMBER(E);
    }

    public static NUMBER ln10() {
        return new NUMBER(LN10);
    }

    public static NUMBER negInf() {
        return new NUMBER(_makeNegInf());
    }

    public static NUMBER pi() {
        return new NUMBER(PI);
    }

    public static NUMBER posInf() {
        return new NUMBER(_makePosInf());
    }

    public static NUMBER zero() {
        return new NUMBER(_makeZero());
    }

    public int sign() {
        if (_isZero(shareBytes())) {
            return 0;
        }
        return _isPositive(shareBytes()) ? 1 : -1;
    }

    static boolean _isInf(byte[] num) {
        if (num.length == 2 && num[0] == -1 && num[1] == 101) {
            return true;
        }
        if (num[0] == 0 && num.length == 1) {
            return true;
        }
        return false;
    }

    private static boolean _isInt(byte[] num) {
        if (_isZero(num)) {
            return true;
        }
        if (_isInf(num)) {
            return false;
        }
        byte[] oranum = _fromLnxFmt(num);
        byte exponent = oranum[0];
        byte mantlen = (byte) (oranum.length - 1);
        if (mantlen > exponent + 1) {
            return false;
        }
        return true;
    }

    static boolean _isNegInf(byte[] num) {
        if (num[0] == 0 && num.length == 1) {
            return true;
        }
        return false;
    }

    static boolean _isPosInf(byte[] num) {
        if (num.length == 2 && num[0] == -1 && num[1] == 101) {
            return true;
        }
        return false;
    }

    static boolean _isPositive(byte[] num) {
        if ((num[0] & Byte.MIN_VALUE) != 0) {
            return true;
        }
        return false;
    }

    static boolean _isZero(byte[] num) {
        if (num[0] == Byte.MIN_VALUE && num.length == 1) {
            return true;
        }
        return false;
    }

    static byte[] _makePosInf() {
        byte[] num = {-1, 101};
        return num;
    }

    static byte[] _makeNegInf() {
        byte[] num = {0};
        return num;
    }

    static byte[] _makeZero() {
        byte[] num = {Byte.MIN_VALUE};
        return num;
    }

    static byte[] _fromLnxFmt(byte[] num) {
        byte[] tmp;
        int numl = num.length;
        if (_isPositive(num)) {
            tmp = new byte[numl];
            tmp[0] = (byte) ((num[0] & (-129)) - 65);
            for (int i = 1; i < numl; i++) {
                tmp[i] = (byte) (num[i] - 1);
            }
        } else {
            if (numl - 1 == 20 && num[numl - 1] != 102) {
                tmp = new byte[numl];
            } else {
                tmp = new byte[numl - 1];
            }
            tmp[0] = (byte) (((num[0] ^ (-1)) & (-129)) - 65);
            for (int i2 = 1; i2 < tmp.length; i2++) {
                tmp[i2] = (byte) (101 - num[i2]);
            }
        }
        return tmp;
    }

    static byte[] _toLnxFmt(byte[] num, boolean pos) {
        byte[] tmp;
        int numl = num.length;
        if (pos) {
            tmp = new byte[numl];
            tmp[0] = (byte) (num[0] + 128 + 64 + 1);
            for (int i = 1; i < numl; i++) {
                tmp[i] = (byte) (num[i] + 1);
            }
        } else {
            if (numl - 1 < 20) {
                tmp = new byte[numl + 1];
            } else {
                tmp = new byte[numl];
            }
            tmp[0] = (byte) ((((num[0] + 128) + 64) + 1) ^ (-1));
            int i2 = 1;
            while (i2 < numl) {
                tmp[i2] = (byte) (101 - num[i2]);
                i2++;
            }
            if (i2 <= 20) {
                tmp[i2] = 102;
            }
        }
        return tmp;
    }

    /* loaded from: ojdbc8.jar:oracle/sql/NUMBER$LnxLibHolder.class */
    private static class LnxLibHolder {
        static final LnxLibThin _slnxlib = new LnxLibThin();

        private LnxLibHolder() {
        }
    }

    private static LnxLibThin _getLnxLib() {
        return LnxLibHolder._slnxlib;
    }

    private static int _byteToChars(byte inByte, char[] chars, int offset) {
        if (inByte < 0) {
            return 0;
        }
        if (inByte < 10) {
            chars[offset] = (char) (48 + inByte);
            return 1;
        }
        if (inByte < 100) {
            chars[offset] = (char) (48 + (inByte / 10));
            chars[offset + 1] = (char) (48 + (inByte % 10));
            return 2;
        }
        chars[offset] = '1';
        chars[offset + 1] = (char) ((48 + (inByte / 10)) - 10);
        chars[offset + 2] = (char) (48 + (inByte % 10));
        return 3;
    }

    private static void _byteTo2Chars(byte inByte, char[] chars, int offset) {
        if (inByte < 0) {
            chars[offset] = '0';
            chars[offset + 1] = '0';
        } else if (inByte < 10) {
            chars[offset] = '0';
            chars[offset + 1] = (char) (48 + inByte);
        } else if (inByte < 100) {
            chars[offset] = (char) (48 + (inByte / 10));
            chars[offset + 1] = (char) (48 + (inByte % 10));
        } else {
            chars[offset] = '0';
            chars[offset + 1] = '0';
        }
    }

    private static void _printBytes(byte[] num) {
        int numl = num.length;
        System.out.print(numl + ": ");
        for (byte b : num) {
            System.out.print(((int) b) + " ");
        }
        System.out.println();
    }

    private byte[] stringToBytes(String str) throws SQLException {
        int scale = 0;
        String str2 = str.trim();
        if (str2.indexOf(46) >= 0) {
            scale = (str2.length() - 1) - str2.indexOf(46);
        }
        return toBytes(str2, scale);
    }
}
