package oracle.sql;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleRowId;

/* loaded from: ojdbc8.jar:oracle/sql/ROWID.class */
public class ROWID extends Da<PERSON> implements OracleRowId {
    static final long serialVersionUID = 5629736369998199486L;
    Object acProxy;

    public ROWID() {
    }

    public ROWID(byte[] raw_bytes) {
        super(raw_bytes);
    }

    public ROWID(String str) throws SQLException {
        this(toAsciiBytes(str));
    }

    private static byte[] toAsciiBytes(String str) throws SQLException {
        try {
            return str.getBytes("US-ASCII");
        } catch (UnsupportedEncodingException e) {
            throw ((SQLException) DatabaseError.createSqlException(183).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return this;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        String class_name = jClass.getName();
        return class_name.compareTo("java.lang.String") == 0;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        byte[] array = getBytes();
        return new String(array);
    }

    @Override // java.sql.RowId
    public String toString() {
        return stringValue();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return new byte[arraySize];
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.acProxy = w;
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.acProxy;
    }
}
