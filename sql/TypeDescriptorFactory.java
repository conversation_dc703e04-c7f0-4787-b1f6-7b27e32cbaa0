package oracle.sql;

import java.sql.SQLException;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.oracore.PickleContext;

/* compiled from: TypeDescriptor.java */
/* loaded from: ojdbc8.jar:oracle/sql/TypeDescriptorFactory.class */
class TypeDescriptorFactory implements ORADataFactory, OracleDataFactory {
    TypeDescriptorFactory() {
    }

    @Override // oracle.sql.ORADataFactory
    public ORAData create(Datum d, int type) throws SQLException {
        if (d == null) {
            return null;
        }
        if (type == 2007) {
            OPAQUE opq = (OPAQUE) d;
            byte[] image = opq.getBytesValue();
            short[] rdbmsTypeCodeArr = new short[1];
            TypeDescriptor td = TypeDescriptor.unpickleOpaqueTypeImage(new PickleContext(image, 0L), opq.getPhysicalConnection(), rdbmsTypeCodeArr);
            return td;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleDataFactory
    public OracleData create(Object jdbcValue, int type) throws SQLException {
        if (jdbcValue == null) {
            return null;
        }
        if (type == 2007) {
            OPAQUE opq = (OPAQUE) jdbcValue;
            byte[] image = opq.getBytesValue();
            short[] rdbmsTypeCodeArr = new short[1];
            TypeDescriptor td = TypeDescriptor.unpickleOpaqueTypeImage(new PickleContext(image, 0L), opq.getPhysicalConnection(), rdbmsTypeCodeArr);
            return td;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
