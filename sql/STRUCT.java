package oracle.sql;

import java.io.OutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.sql.Connection;
import java.sql.ResultSetMetaData;
import java.sql.SQLData;
import java.sql.SQLException;
import java.sql.SQLOutput;
import java.sql.Struct;
import java.util.Hashtable;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.OracleTypeMetaData;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConcreteProxy;
import oracle.jdbc.internal.OracleStruct;
import oracle.jdbc.proxy.ProxyFactory;
import oracle.jdbc.proxy._Proxy_;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableConnection;
import oracle.jdbc.replay.driver.TxnReplayableStruct;

/* loaded from: ojdbc8.jar:oracle/sql/STRUCT.class */
public class STRUCT extends DatumWithConnection implements OracleStruct, OracleConcreteProxy {
    protected oracle.jdbc.driver.OracleStruct target = null;
    private OracleStruct ojiOracleStruct = null;

    public oracle.jdbc.driver.OracleStruct getTarget() {
        return this.target;
    }

    private void setTarget(OracleConnection conn, oracle.jdbc.driver.OracleStruct s, String typeName, Object[] attributes, boolean recordConstructor) {
        this.ojiOracleStruct = (OracleStruct) ConcreteProxyUtil.getProxyObject(conn, s, TxnReplayableStruct.class, this);
        this.target = s;
        setShareBytes(this.target.shareBytes());
        this.targetDatumWithConnection = this.target;
        this.targetDatum = this.target;
        if (this.ojiOracleStruct != null) {
            try {
                Object proxyObj = ConcreteProxyUtil.checkAndGetACProxyConnection(conn);
                if (proxyObj != null && recordConstructor) {
                    ((TxnReplayableConnection) proxyObj).STRUCTConstructorRecording(typeName, attributes, this);
                }
            } catch (SQLException sqe) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, "oracle/sql/STRUCT", "setTarget", sqe.getMessage(), (String) null, (Throwable) null);
            }
            this.ojiOracleDatumWithConnection = this.ojiOracleStruct;
            return;
        }
        this.ojiOracleStruct = this.target;
    }

    public void createAndSetShardingLobProxy(Class proxyClass, Object creator) {
        this.ojiOracleStruct = (OracleStruct) ConcreteProxyUtil.getProxyObject(this.ojiOracleStruct, proxyClass, creator);
        this.ojiOracleDatumWithConnection = this.ojiOracleStruct;
    }

    @Override // oracle.jdbc.internal.OracleConcreteProxy
    public TxnReplayableBase getConcreteProxy() {
        if (this.ojiOracleStruct instanceof TxnReplayableBase) {
            return (TxnReplayableBase) this.ojiOracleStruct;
        }
        return null;
    }

    public STRUCT(StructDescriptor type, Connection conn, Object[] attributes) throws SQLException {
        String typeNameByUser = type != null ? type.typeNameByUser : null;
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate((OracleConnection) conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleStruct s = new oracle.jdbc.driver.OracleStruct(type, conn, attributes);
        setTarget((OracleConnection) conn, s, typeNameByUser, attributes, delegateConn != null);
    }

    public STRUCT(StructDescriptor type, Connection conn, Map attrList) throws SQLException {
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate((OracleConnection) conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleStruct s = new oracle.jdbc.driver.OracleStruct(type, conn, (Map<?, ?>) attrList);
        setTarget((OracleConnection) conn, s, null, null, delegateConn != null);
    }

    public STRUCT(StructDescriptor type, byte[] elements, Connection conn) throws SQLException {
        OracleConnection delegateConn = ConcreteProxyUtil.getThinDriverReplayableConnectionDelegate((OracleConnection) conn);
        conn = delegateConn != null ? delegateConn : conn;
        oracle.jdbc.driver.OracleStruct s = new oracle.jdbc.driver.OracleStruct(type, elements, conn);
        setTarget((OracleConnection) conn, s, null, null, delegateConn != null);
    }

    @Override // java.sql.Struct
    public String getSQLTypeName() throws SQLException {
        return this.ojiOracleStruct.getSQLTypeName();
    }

    @Override // java.sql.Struct
    public Object[] getAttributes() throws SQLException {
        return this.ojiOracleStruct.getAttributes();
    }

    @Override // java.sql.Struct
    public Object[] getAttributes(Map map) throws SQLException {
        return this.ojiOracleStruct.getAttributes(map);
    }

    @Override // oracle.jdbc.OracleStruct
    public OracleTypeMetaData getOracleMetaData() throws SQLException {
        return this.ojiOracleStruct.getOracleMetaData();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public StructDescriptor getDescriptor() throws SQLException {
        return this.ojiOracleStruct.getDescriptor();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setDescriptor(StructDescriptor desc) {
        this.ojiOracleStruct.setDescriptor(desc);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public Datum[] getOracleAttributes() throws SQLException {
        return this.ojiOracleStruct.getOracleAttributes();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public Map getMap() {
        return this.ojiOracleStruct.getMap();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public byte[] toBytes() throws SQLException {
        return this.ojiOracleStruct.toBytes();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setDatumArray(Datum[] darray) {
        this.ojiOracleStruct.setDatumArray(darray);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public Datum[] getDatumArray() {
        return this.ojiOracleStruct.getDatumArray();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setNullDatumArray() {
        this.ojiOracleStruct.setNullDatumArray();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public Object[] getObjectArray() {
        return this.ojiOracleStruct.getObjectArray();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setNullObjectArray() {
        this.ojiOracleStruct.setNullObjectArray();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setObjArray(Object[] oarray) throws SQLException {
        this.ojiOracleStruct.setObjArray(oarray);
    }

    public static STRUCT toSTRUCT(Object obj, OracleConnection conn) throws SQLException {
        STRUCT s = null;
        if (obj != null) {
            if (obj instanceof STRUCT) {
                s = (STRUCT) obj;
            } else if (obj instanceof ORAData) {
                s = (STRUCT) ((ORAData) obj).toDatum(conn);
            } else if (obj instanceof OracleData) {
                Object jdbcObject = ((OracleData) obj).toJDBCObject(conn);
                if (jdbcObject instanceof _Proxy_) {
                    final _Proxy_ proxiedJDBCObject = (_Proxy_) jdbcObject;
                    jdbcObject = AccessController.doPrivileged(new PrivilegedAction<Object>() { // from class: oracle.sql.STRUCT.1
                        @Override // java.security.PrivilegedAction
                        public Object run() {
                            return ProxyFactory.extractDelegate(proxiedJDBCObject);
                        }
                    });
                }
                s = (STRUCT) jdbcObject;
            } else if (obj instanceof CustomDatum) {
                s = (STRUCT) ((oracle.jdbc.internal.OracleConnection) conn).toDatum((CustomDatum) obj);
            } else if (obj instanceof SQLData) {
                SQLData sqldataObj = (SQLData) obj;
                StructDescriptor desc = StructDescriptor.createDescriptor(sqldataObj.getSQLTypeName(), conn);
                SQLOutput sqlOutput = desc.toJdbc2SQLOutput();
                sqldataObj.writeSQL(sqlOutput);
                s = ((OracleSQLOutput) sqlOutput).getSTRUCT();
            } else {
                throw ((SQLException) DatabaseError.createSqlException(59, obj).fillInStackTrace());
            }
        }
        return s;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        Map map = this.ojiOracleStruct.getMap();
        return toJdbc(map);
    }

    public Object toJdbc(Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Class c;
        Object jdbcObject = this;
        if (map != null && (c = this.ojiOracleStruct.getDescriptor().getClass(map)) != null) {
            jdbcObject = toClass(c, map);
        }
        return jdbcObject;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleStruct
    public Object toClass(Class clazz) throws SQLException {
        return toClass(clazz, this.target.getMap());
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public Object toClass(Class clazz, Map map) throws IllegalAccessException, SQLException, InstantiationException {
        Object obj;
        if (clazz == null || clazz == STRUCT.class || clazz == Struct.class || clazz == oracle.jdbc.OracleStruct.class || clazz == OracleStruct.class) {
            obj = this;
        } else {
            try {
                Object i = clazz.newInstance();
                if (i instanceof SQLData) {
                    ((SQLData) i).readSQL(this.target.getDescriptor().toJdbc2SQLInput(this, this, map), this.target.getDescriptor().getName());
                    obj = i;
                } else if (i instanceof ORADataFactory) {
                    ORADataFactory f = (ORADataFactory) i;
                    obj = f.create(this, 2002);
                } else if (i instanceof OracleDataFactory) {
                    OracleDataFactory f2 = (OracleDataFactory) i;
                    obj = f2.create(this, 2002);
                } else if (i instanceof CustomDatumFactory) {
                    CustomDatumFactory f3 = (CustomDatumFactory) i;
                    obj = f3.create(this, 2002);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, this.target.getDescriptor().getName()).fillInStackTrace());
                }
            } catch (IllegalAccessException ex) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "IllegalAccessException: " + ex.getMessage()).fillInStackTrace());
            } catch (InstantiationException ex2) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "InstantiationException: " + ex2.getMessage()).fillInStackTrace());
            }
        }
        return obj;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        return this.ojiOracleStruct.isConvertibleTo(jClass);
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return this.ojiOracleStruct.makeJdbcArray(arraySize);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setAutoBuffering(boolean enable) throws SQLException {
        this.ojiOracleStruct.setAutoBuffering(enable);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public boolean getAutoBuffering() throws SQLException {
        return this.ojiOracleStruct.getAutoBuffering();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setImage(byte[] image, long offset, long length) throws SQLException {
        this.ojiOracleStruct.setImage(image, offset, length);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public void setImageLength(long length) throws SQLException {
        this.ojiOracleStruct.setImageLength(length);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public long getImageOffset() {
        return this.ojiOracleStruct.getImageOffset();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public long getImageLength() {
        return this.ojiOracleStruct.getImageLength();
    }

    public CustomDatumFactory getFactory(Hashtable map, String classname) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.getFactory(map, classname);
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public ORADataFactory getORADataFactory(Hashtable map, String classname) throws SQLException {
        return this.ojiOracleStruct.getORADataFactory(map, classname);
    }

    public OracleDataFactory getOracleDataFactory(Hashtable map, String classname) throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.getOracleDataFactory(map, classname);
    }

    public String debugString() {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return this.target.debugString();
    }

    @Override // oracle.jdbc.internal.OracleStruct
    public boolean isInHierarchyOf(String checkThisName) throws SQLException {
        return this.ojiOracleStruct.isInHierarchyOf(checkThisName);
    }

    @Override // oracle.sql.DatumWithConnection, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Connection getJavaSqlConnection() throws SQLException {
        return this.ojiOracleStruct.getJavaSqlConnection();
    }

    public String dump() throws SQLException {
        ConcreteProxyUtil.checkAndDisableReplay(this);
        return dump(this);
    }

    public static String dump(Object o) throws SQLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        dump(o, pw);
        return sw.getBuffer().substring(0);
    }

    public static void dump(Object o, PrintStream ps) throws SQLException {
        dump(o, new PrintWriter((OutputStream) ps, true));
    }

    public static void dump(Object o, PrintWriter pw) throws SQLException {
        dump(o, pw, 0);
    }

    static void dump(Object o, PrintWriter pw, int indent) throws SQLException {
        if (o instanceof STRUCT) {
            dump((OracleStruct) o, pw, indent);
            return;
        }
        if (o instanceof ARRAY) {
            ARRAY.dump((ARRAY) o, pw, indent);
        } else if (o == null) {
            pw.println("null");
        } else {
            pw.println(o.toString());
        }
    }

    static void dump(OracleStruct x, PrintWriter pw, int indent) throws SQLException {
        StructDescriptor desc = x.getDescriptor();
        ResultSetMetaData md = desc.getMetaData();
        for (int i = 0; i < indent; i++) {
            pw.print(' ');
        }
        pw.println("name = " + desc.getName());
        for (int i2 = 0; i2 < indent; i2++) {
            pw.print(' ');
        }
        StringBuilder sbAppend = new StringBuilder().append("length = ");
        int length = desc.getLength();
        pw.println(sbAppend.append(length).toString());
        Object[] attr = x.getAttributes();
        for (int i3 = 0; i3 < length; i3++) {
            for (int j = 0; j < indent; j++) {
                pw.print(' ');
            }
            pw.print(md.getColumnName(i3 + 1) + " = ");
            dump(attr[i3], pw, indent + 1);
        }
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public void setACProxy(Object w) {
        this.ojiOracleStruct.setACProxy(w);
    }

    @Override // oracle.jdbc.internal.ACProxyable
    public Object getACProxy() {
        return this.ojiOracleStruct.getACProxy();
    }
}
