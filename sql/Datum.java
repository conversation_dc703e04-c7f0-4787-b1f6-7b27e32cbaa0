package oracle.sql;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.Reader;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleDatumWithConnection;

/* loaded from: ojdbc8.jar:oracle/sql/Datum.class */
public abstract class Datum implements Serializable {
    private byte[] data;
    protected Datum targetDatum = null;
    protected OracleDatumWithConnection ojiOracleDatumWithConnection = null;
    static final long serialVersionUID = 4645732484621936751L;

    public abstract boolean isConvertibleTo(Class<?> cls);

    public abstract Object toJdbc() throws SQLException;

    public abstract Object makeJdbcArray(int i);

    public Datum() {
    }

    public Datum(byte[] newData) {
        this.data = newData;
    }

    public boolean equals(Object obj) {
        if (this.targetDatum != null) {
            if (this.targetDatum.equals(obj) || this == obj) {
                return true;
            }
            if (obj != null && (obj instanceof Datum) && getClass() == obj.getClass()) {
                Datum od = (Datum) obj;
                byte[] tempData = this.targetDatum.shareBytes();
                if (tempData == null && od.data == null) {
                    return true;
                }
                if (tempData == null && od.data != null) {
                    return false;
                }
                if ((tempData != null && od.data == null) || tempData.length != od.data.length) {
                    return false;
                }
                for (int i = 0; i < tempData.length; i++) {
                    if (tempData[i] != od.data[i]) {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (obj != null && (obj instanceof Datum) && getClass() == obj.getClass()) {
            Datum od2 = (Datum) obj;
            if (this.data == null && od2.data == null) {
                return true;
            }
            if (this.data == null && od2.data != null) {
                return false;
            }
            if ((this.data != null && od2.data == null) || this.data.length != od2.data.length) {
                return false;
            }
            for (int i2 = 0; i2 < this.data.length; i2++) {
                if (this.data[i2] != od2.data[i2]) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public int bytesHashCode() {
        if (this.targetDatum != null) {
            return this.targetDatum.bytesHashCode();
        }
        int h = 1;
        if (this.data != null && this.data.length > 0) {
            for (int i = 0; i < this.data.length; i++) {
                h = (31 * h) + this.data[i];
            }
        }
        return h;
    }

    public byte[] shareBytes() {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.shareBytes();
        }
        if (this.targetDatum != null) {
            return this.targetDatum.shareBytes();
        }
        return this.data;
    }

    public long getLength() {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getLength();
        }
        if (this.targetDatum != null) {
            return this.targetDatum.getLength();
        }
        if (null == this.data) {
            return 0L;
        }
        return this.data.length;
    }

    public void setBytes(byte[] array) {
        if (this.ojiOracleDatumWithConnection != null) {
            this.ojiOracleDatumWithConnection.setBytes(array);
        } else {
            if (this.targetDatum != null) {
                this.targetDatum.setBytes(array);
                return;
            }
            int length = array.length;
            this.data = new byte[length];
            System.arraycopy(array, 0, this.data, 0, length);
        }
    }

    public void setShareBytes(byte[] array) {
        if (this.ojiOracleDatumWithConnection != null) {
            this.ojiOracleDatumWithConnection.setShareBytes(array);
        } else if (this.targetDatum != null) {
            this.targetDatum.setShareBytes(array);
        } else {
            this.data = array;
        }
    }

    public byte[] getBytes() {
        if (this.ojiOracleDatumWithConnection != null) {
            return this.ojiOracleDatumWithConnection.getBytes();
        }
        if (this.targetDatum != null) {
            return this.targetDatum.getBytes();
        }
        if (this.data == null) {
            return new byte[0];
        }
        byte[] tmp = new byte[this.data.length];
        System.arraycopy(this.data, 0, tmp, 0, this.data.length);
        return tmp;
    }

    public boolean isNull() {
        if (this.targetDatum != null) {
            return this.targetDatum.isNull();
        }
        return this.data == null || this.data.length == 0;
    }

    public InputStream getStream() {
        if (this.targetDatum != null) {
            return this.targetDatum.getStream();
        }
        return new ByteArrayInputStream(this.data);
    }

    public String stringValue() throws SQLException {
        throw new SQLException("Conversion to String failed");
    }

    public String stringValue(Connection conn) throws SQLException {
        return stringValue();
    }

    public boolean booleanValue() throws SQLException {
        throw new SQLException("Conversion to boolean failed");
    }

    public int intValue() throws SQLException {
        throw new SQLException("Conversion to integer failed");
    }

    public long longValue() throws SQLException {
        throw new SQLException("Conversion to long failed");
    }

    public float floatValue() throws SQLException {
        throw new SQLException("Conversion to float failed");
    }

    public double doubleValue() throws SQLException {
        throw new SQLException("Conversion to double failed");
    }

    public byte byteValue() throws SQLException {
        throw new SQLException("Conversion to byte failed");
    }

    public BigDecimal bigDecimalValue() throws SQLException {
        throw new SQLException("Conversion to BigDecimal failed");
    }

    public Date dateValue() throws SQLException {
        throw new SQLException("Conversion to Date failed");
    }

    public Time timeValue() throws SQLException {
        throw new SQLException("Conversion to Time failed");
    }

    public Time timeValue(Calendar cal) throws SQLException {
        throw new SQLException("Conversion to Time failed");
    }

    public Timestamp timestampValue() throws SQLException {
        throw new SQLException("Conversion to Timestamp failed");
    }

    public Timestamp timestampValue(Calendar cal) throws SQLException {
        throw new SQLException("Conversion to Timestamp failed");
    }

    public Reader characterStreamValue() throws SQLException {
        throw new SQLException("Conversion to character stream failed");
    }

    public InputStream asciiStreamValue() throws SQLException {
        throw new SQLException("Conversion to ascii stream failed");
    }

    public InputStream binaryStreamValue() throws SQLException {
        throw new SQLException("Conversion to binary stream failed");
    }

    public <T> T toClass(Class<T> cls) throws SQLException {
        Object objBinaryStreamValue;
        if (this.targetDatum != null && cls == this.targetDatum.getClass()) {
            return (T) this.targetDatum.toClass(cls);
        }
        if (cls == getClass()) {
            objBinaryStreamValue = this;
        } else if (cls == String.class) {
            objBinaryStreamValue = stringValue();
        } else if (cls == Boolean.class) {
            objBinaryStreamValue = Boolean.valueOf(booleanValue());
        } else if (cls == Byte.class) {
            objBinaryStreamValue = Byte.valueOf(byteValue());
        } else if (cls == Integer.class) {
            objBinaryStreamValue = Integer.valueOf(intValue());
        } else if (cls == Long.class) {
            objBinaryStreamValue = Long.valueOf(longValue());
        } else if (cls == Float.class) {
            objBinaryStreamValue = Float.valueOf(floatValue());
        } else if (cls == Double.class) {
            objBinaryStreamValue = Double.valueOf(doubleValue());
        } else if (cls == BigDecimal.class) {
            objBinaryStreamValue = bigDecimalValue();
        } else if (cls == Date.class) {
            objBinaryStreamValue = dateValue();
        } else if (cls == Time.class) {
            objBinaryStreamValue = timeValue();
        } else if (cls == Timestamp.class) {
            objBinaryStreamValue = timestampValue();
        } else if (cls == Reader.class) {
            objBinaryStreamValue = characterStreamValue();
        } else {
            if (cls != InputStream.class) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, cls.getName()).fillInStackTrace());
            }
            objBinaryStreamValue = binaryStreamValue();
        }
        return cls.cast(objBinaryStreamValue);
    }

    public static int compareBytes(byte[] m, byte[] n) {
        int ml = m.length;
        int nl = n.length;
        int cc = Math.min(ml, nl);
        for (int cl = 0; cl < cc; cl++) {
            int um = m[cl] & 255;
            int un = n[cl] & 255;
            if (um != un) {
                if (um < un) {
                    return -1;
                }
                return 1;
            }
        }
        if (ml == nl) {
            return 0;
        }
        if (ml > nl) {
            return 1;
        }
        return -1;
    }

    protected boolean bytesEqual(Datum other) {
        if (this.targetDatum != null) {
            return this.targetDatum.bytesEqual(other);
        }
        boolean result = this == other;
        if (!result) {
            byte[] odata = other.shareBytes();
            boolean nulldata = this.data == null;
            if (nulldata == (odata == null) && (nulldata || this.data.length == odata.length)) {
                result = true;
                if (!nulldata) {
                    int i = 0;
                    while (true) {
                        if (i >= this.data.length) {
                            break;
                        }
                        if (this.data[i] == odata[i]) {
                            i++;
                        } else {
                            result = false;
                            break;
                        }
                    }
                }
            }
        }
        return result;
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
