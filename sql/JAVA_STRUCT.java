package oracle.sql;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Hashtable;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/sql/JAVA_STRUCT.class */
public class JAVA_STRUCT extends STRUCT {
    static final long serialVersionUID = 2211611973003094149L;

    public JAVA_STRUCT(StructDescriptor type, Connection conn, Object[] attributes) throws SQLException {
        super(type, conn, attributes);
    }

    public JAVA_STRUCT(StructDescriptor type, byte[] elements, Connection conn) throws SQLException {
        super(type, elements, conn);
    }

    @Override // oracle.sql.STRUCT, oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        Map map = getInternalConnection().getJavaObjectTypeMap();
        Class c = null;
        if (map != null) {
            c = getDescriptor().getClass(map);
        } else {
            map = new Hashtable(10);
            getInternalConnection().setJavaObjectTypeMap(map);
        }
        if (c == null) {
            String externalName = StructDescriptor.getJavaObjectClassName(getInternalConnection(), getDescriptor());
            String schemaName = getDescriptor().getSchemaName();
            if (externalName == null || externalName.length() == 0) {
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1);
            }
            try {
                c = getInternalConnection().classForNameAndSchema(externalName, schemaName);
                map.put(getSQLTypeName(), c);
            } catch (ClassNotFoundException e) {
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, "ClassNotFoundException: " + e.getMessage());
            }
        }
        return toClass(c);
    }

    @Override // oracle.sql.STRUCT, oracle.jdbc.internal.OracleStruct
    public Object toJdbc(Map map) throws SQLException {
        return toJdbc();
    }
}
