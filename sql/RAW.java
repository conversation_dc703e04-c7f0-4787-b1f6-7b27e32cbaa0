package oracle.sql;

import java.io.ByteArrayInputStream;
import java.io.CharArrayReader;
import java.io.InputStream;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import oracle.jdbc.driver.DBConversion;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.util.RepConversion;

/* loaded from: ojdbc8.jar:oracle/sql/RAW.class */
public class RAW extends Datum {
    static final long serialVersionUID = -3298750557928937840L;

    static int hexDigit2Nibble(char hex) throws SQLException {
        int result = Character.digit(hex, 16);
        if (result == -1) {
            throw ((SQLException) DatabaseError.createSqlException(59, "Invalid hex digit: " + hex).fillInStackTrace());
        }
        return result;
    }

    public static byte[] hexString2Bytes(String hexString) throws SQLException {
        byte[] bytes;
        int len = hexString.length();
        char[] hexChars = new char[len];
        hexString.getChars(0, len, hexChars, 0);
        int i = 0;
        int j = 0;
        if (len == 0) {
            return new byte[0];
        }
        if (len % 2 > 0) {
            bytes = new byte[(len + 1) / 2];
            i = 0 + 1;
            j = 0 + 1;
            bytes[0] = (byte) hexDigit2Nibble(hexChars[0]);
        } else {
            bytes = new byte[len / 2];
        }
        while (i < bytes.length) {
            int i2 = j;
            int j2 = j + 1;
            j = j2 + 1;
            bytes[i] = (byte) ((hexDigit2Nibble(hexChars[i2]) << 4) | hexDigit2Nibble(hexChars[j2]));
            i++;
        }
        return bytes;
    }

    public static RAW newRAW(Object obj) throws SQLException {
        RAW result = new RAW(obj);
        return result;
    }

    public static RAW oldRAW(Object obj) throws SQLException, UnsupportedEncodingException {
        RAW result;
        if (obj instanceof String) {
            String s = (String) obj;
            try {
                byte[] bytes = s.getBytes("ISO8859_1");
                result = new RAW(bytes);
            } catch (UnsupportedEncodingException e) {
                throw DatabaseError.createSqlException(109, "ISO8859_1 character encoding not found");
            }
        } else {
            result = new RAW(obj);
        }
        return result;
    }

    public RAW() {
    }

    public RAW(byte[] raw_bytes) {
        super(raw_bytes);
    }

    public RAW(Object val) throws SQLException {
        this();
        if (val instanceof byte[]) {
            setShareBytes((byte[]) val);
        } else {
            if (val instanceof String) {
                setShareBytes(hexString2Bytes((String) val));
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, val).fillInStackTrace());
        }
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() throws SQLException {
        return getBytes();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class<?> jClass) {
        String class_name = jClass.getName();
        if (class_name.compareTo("java.lang.String") == 0 || class_name.compareTo("java.io.InputStream") == 0 || class_name.compareTo("java.io.Reader") == 0) {
            return true;
        }
        return false;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public String stringValue() {
        String ret = RepConversion.bArray2String(getBytes());
        return ret;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public Reader characterStreamValue() throws SQLException {
        int nbytes = (int) getLength();
        char[] chars = new char[nbytes * 2];
        byte[] bytes = shareBytes();
        DBConversion.RAWBytesToHexChars(bytes, nbytes, chars);
        Reader ret = new CharArrayReader(chars);
        return ret;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream asciiStreamValue() throws SQLException {
        int nbytes = (int) getLength();
        char[] chars = new char[nbytes * 2];
        byte[] bytes = shareBytes();
        DBConversion.RAWBytesToHexChars(bytes, nbytes, chars);
        byte[] buf = new byte[nbytes * 2];
        DBConversion.javaCharsToAsciiBytes(chars, nbytes * 2, buf);
        InputStream ret = new ByteArrayInputStream(buf);
        return ret;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection, oracle.jdbc.internal.OracleBfile
    public InputStream binaryStreamValue() throws SQLException {
        return getStream();
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int arraySize) {
        return new byte[arraySize];
    }

    @Override // oracle.sql.Datum
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
