# Oracle JDBC 协议解析代码库

## 项目概述

本项目是从Oracle JDBC驱动(ojdbc8.jar)反编译得到的源码，专门用于Oracle数据库网络协议的旁路解析研究。代码库包含了Oracle网络协议栈的完整实现，是理解和实现Oracle流量解析的宝贵资源。

## 协议架构

Oracle数据库使用三层网络协议栈：

```
应用层    │ TTI (Two Task Interface)     │ 数据库函数调用
协议层    │ TTC (Two Task Common)        │ 消息编码解码  
传输层    │ TNS (Transparent Network)    │ 网络包传输
```

## 目录结构

```
sources/
├── oracle/
│   ├── net/                    # TNS协议实现
│   │   ├── ns/                 # 网络服务层
│   │   ├── nt/                 # 网络传输适配器
│   │   ├── resolver/           # 地址解析
│   │   ├── ano/                # 高级网络选项
│   │   └── aso/                # 高级安全选项
│   ├── jdbc/
│   │   ├── driver/             # TTC/TTI协议实现
│   │   ├── oracore/            # Oracle核心数据类型
│   │   └── internal/           # 内部支持类
│   └── sql/                    # SQL数据类型定义
```

## 核心文件说明

### TNS协议层 (129个文件)
- **NIOHeader.java** - TNS包头结构定义
- **SQLnetDef.java** - TNS协议常量和包类型
- **NSProtocol.java** - TNS协议核心实现
- **NIOPacket.java** - 各种TNS包类型实现

### TTC协议层 (50个文件)  
- **T4CTTIMsg.java** - TTC消息基类
- **T4CMAREngine.java** - 数据编码解码引擎
- **T4C8TTIpro.java** - 协议协商实现
- **T4CTTIMsgCodes.java** - TTC消息类型定义

### TTI协议层 (包含在TTC文件中)
- **T4CTTIfun.java** - TTI函数调用基类
- **T4CTTIfunCodes.java** - TTI函数代码定义
- **T4C8Oall.java** - OALL8函数实现(SQL执行)

### 数据类型系统 (53个文件)
- **oracle/sql/** - 基本SQL数据类型(NUMBER, CHAR, DATE等)
- **oracle/jdbc/oracore/** - Oracle核心类型系统
- **PickleContext.java** - 数据序列化机制

## 协议解析要点

### 1. TNS包结构
```
+--------+--------+--------+--------+--------+--------+--------+--------+
| Length (2 bytes) | Checksum (2 bytes) | Type | Flags | Header Checksum |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 2. 主要包类型
- **NSPTCN(1)** - Connect连接请求
- **NSPTAC(2)** - Accept接受连接  
- **NSPTDA(6)** - Data数据包
- **NSPTMK(12)** - Marker标记包

### 3. TTC消息类型
- **TTIPRO(1)** - 协议协商
- **TTIDTY(2)** - 数据类型协商
- **TTIFUN(3)** - 函数调用
- **TTIOER(4)** - 错误消息

### 4. TTI函数代码
- **OALL8(72)** - 通用SQL执行
- **OFETCH(5)** - 获取数据
- **OCOMMIT(14)** - 提交事务
- **OROLLBACK(15)** - 回滚事务

## 数据编码格式

### 基本类型
- **UB1/UB2/UB4/UB8** - 无符号整数(1/2/4/8字节)
- **SB1/SB2/SB4/SB8** - 有符号整数(1/2/4/8字节)

### 字符串(CLR编码)
```
长度 < 253: [长度字节][数据]
长度 >= 253: [0xFE][长度4字节][数据块]...[0x00]
```

### Oracle NUMBER
```
[长度][指数][尾数字节1][尾数字节2]...[尾数字节N]
```

## 典型交互流程

### 连接建立
1. TNS Connect → TNS Accept
2. TTC协议协商(TTIPRO)
3. TTC数据类型协商(TTIDTY)

### SQL执行
1. TTIFUN(OALL8) + SQL语句
2. TTIRXH(结果集头)
3. TTIRXD(数据行)
4. TTIRPA(返回参数)

## 使用建议

### 旁路解析实现
1. **协议识别** - 通过端口(1521)和TNS包头识别
2. **状态机** - 维护连接和会话状态
3. **流式解析** - 避免缓存完整数据包
4. **类型解码** - 正确处理Oracle特有数据类型

### 关键解析点
- TNS包边界和类型识别
- TTC消息解码和数据提取
- TTI函数调用和SQL语句解析
- Oracle数据类型正确转换

## 文档说明

- **Oracle_Protocol_Analysis.md** - 详细的协议分析文档
- **File_Classification.md** - 完整的文件分类清单
- **keep_files_list.txt** - 需要保留的文件列表
- **analyze_files.py** - 文件分析脚本

## 项目价值

这个代码库提供了：
- ✅ **完整的协议实现** - 三层协议栈完整覆盖
- ✅ **详细的编码逻辑** - marshal/unmarshal实现
- ✅ **丰富的数据类型** - 支持所有Oracle数据类型
- ✅ **实际的交互场景** - 连接、查询、事务处理
- ✅ **生产级代码质量** - Oracle官方实现

## 总结

本项目包含239个Java文件，全部与Oracle协议解析相关，没有删除任何文件。这些文件构成了Oracle网络协议的完整实现，是进行Oracle流量旁路解析的宝贵参考资源。

通过研究这些代码，可以深入理解Oracle协议的工作原理，为实现高质量的协议解析器提供坚实的基础。
