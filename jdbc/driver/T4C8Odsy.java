package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Odsy.class */
final class T4C8Odsy extends T4CTTIfun {
    static final byte DSY_REMOTE_OBJ = 1;
    static final byte DSY_TRY_PUBLIC = 2;
    static final byte DSY_TRY_SYNBASE = 4;
    static final byte DSY_SHOW_INVISIBLE_COLUMNS = 8;
    static final short OCI_PTYPE_UNK = 0;
    static final short OCI_PTYPE_TABLE = 1;
    static final short OCI_PTYPE_VIEW = 2;
    static final short OCI_PTYPE_PROC = 3;
    static final short OCI_PTYPE_FUNC = 4;
    static final short OCI_PTYPE_PKG = 5;
    static final short OCI_PTYPE_TYPE = 6;
    static final short OCI_PTYPE_SYN = 7;
    static final short OCI_PTYPE_SEQ = 8;
    static final short OCI_PTYPE_SCHEMA = 17;
    static final short OCI_PTYPE_DATABASE = 18;
    private byte[] objectName;
    short objectType;
    T4C8Kpcds kpcds;

    T4C8Odsy(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.kpcds = null;
        this.kpcds = new T4C8Kpcds(_conn);
        setFunCode((short) 119);
    }

    T4C8Kpcdsc[] doODSYTable(String objectName) throws SQLException, IOException {
        if (isTabular(doODSY(objectName, (short) 0))) {
            return this.kpcds.getT4C8Kpcdscs();
        }
        return null;
    }

    private boolean isTabular(int objectType) {
        return objectType == 7 || objectType == 1 || objectType == 2;
    }

    private int doODSY(String objectName, short objectType) throws SQLException, IOException {
        setDescribedObject(objectName, objectType);
        doRPC();
        return this.kpcds.getObjectType();
    }

    private void setDescribedObject(String name, short type) throws SQLException {
        this.objectName = (name == null || name.length() == 0) ? new byte[0] : this.connection.conversion.StringToCharBytes(name);
        this.objectType = type;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.objectName.length == 0) {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.objectName.length);
        }
        this.meg.marshalUB1(this.objectType);
        this.meg.marshalUB4(0L);
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(14L);
        this.meg.marshalCHR(this.objectName);
        this.objectName = null;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.kpcds.unmarshal();
    }
}
