package oracle.jdbc.driver;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.nio.channels.SocketChannel;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.sql.SQLException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Base64;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.TimeZone;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.logging.Level;
import oracle.jdbc.AccessToken;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.oauth.OpaqueAccessToken;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.OpaquePrivateKey;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.util.RepConversion;
import oracle.net.ano.AnoServices;
import oracle.net.ano.AuthenticationService;
import oracle.net.ns.SessionAtts;
import oracle.net.nt.ConnOption;
import oracle.net.nt.TcpsNTAdapter;
import oracle.security.o3logon.O3LoginClientHelper;
import oracle.security.o5logon.O5Logon;
import oracle.sql.ZONEIDMAP;
import oracle.sql.converter.CharacterSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoauthenticate.class */
final class T4CTTIoauthenticate extends T4CTTIfun {
    private static final int PASSWORD_BUFFER_LENGTH = 2112;
    byte[] terminal;
    byte[] enableTempLobRefCnt;
    byte[] machine;
    byte[] sysUserName;
    byte[] processID;
    byte[] programName;
    byte[] encryptedSK;
    byte[] internalName;
    byte[] externalName;
    byte[] alterSession;
    byte[] aclValue;
    byte[] clientname;
    byte[] editionName;
    byte[] driverName;
    String ressourceManagerId;
    Map<String, Namespace> namespaces;
    boolean bUseO5Logon;
    int verifierType;
    static final int ZTVT_ORCL_7 = 2361;
    static final int ZTVT_SSH1 = 6949;
    static final int ZTVT_NTV = 7809;
    static final int ZTVT_SMD5 = 59694;
    static final int ZTVT_MD5 = 40674;
    static final int ZTVT_SH1 = 45394;
    static final int ZTVT_SHA512 = 18453;
    byte[] salt;
    byte[] encryptedKB;
    boolean isSessionTZ;
    static final int SERVER_VERSION_81 = 8100;
    static final int KPZ_LOGON = 1;
    static final int KPZ_CPW = 2;
    static final int KPZ_SRVAUTH = 4;
    static final int KPZ_ENCRYPTED_PASSWD = 256;
    static final int KPZ_LOGON_MIGRATE = 16;
    static final int KPZ_LOGON_SYSDBA = 32;
    static final int KPZ_LOGON_SYSOPER = 64;
    static final int KPZ_LOGON_PRELIMAUTH = 128;
    static final int KPZ_PASSWD_ENCRYPTED = 256;
    static final int KPZ_LOGON_DBCONC = 512;
    static final int KPZ_PROXY_AUTH = 1024;
    static final int KPZ_SESSION_CACHE = 2048;
    static final int KPZ_PASSWD_IS_VFR = 4096;
    static final int KPZ_LOGON_SYSASM = 4194304;
    static final int KPZ_SESSION_QCACHE = 8388608;
    static final int KPZ_LOGON_SYSBKP = 16777216;
    static final int KPZ_LOGON_SYSDGD = 33554432;
    static final int KPZ_LOGON_SYSKMT = 67108864;
    static final int KPZ_CPW_AUTH = 16;
    static final int KPZ_OPASSWD_ENCRYPTED = 32;
    static final int KPZ_NPASSWD_ENCRYPTED = 64;
    static final int KPZ_NPASSWD_OBFUSCATE = 65536;
    static final String AUTH_TERMINAL = "AUTH_TERMINAL";
    static final String AUTH_PROGRAM_NM = "AUTH_PROGRAM_NM";
    static final String AUTH_MACHINE = "AUTH_MACHINE";
    static final String AUTH_PID = "AUTH_PID";
    static final String AUTH_SID = "AUTH_SID";
    static final String AUTH_SESSKEY = "AUTH_SESSKEY";
    static final String AUTH_VFR_DATA = "AUTH_VFR_DATA";
    static final String AUTH_PASSWORD = "AUTH_PASSWORD";
    static final String AUTH_NEWPASSWORD = "AUTH_NEWPASSWORD";
    static final String AUTH_INTERNALNAME = "AUTH_INTERNALNAME_";
    static final String AUTH_EXTERNALNAME = "AUTH_EXTERNALNAME_";
    static final String AUTH_ACL = "AUTH_ACL";
    static final String AUTH_ALTER_SESSION = "AUTH_ALTER_SESSION";
    static final String AUTH_INITIAL_CLIENT_ROLE = "INITIAL_CLIENT_ROLE";
    static final String AUTH_VERSION_SQL = "AUTH_VERSION_SQL";
    static final String AUTH_VERSION_NO = "AUTH_VERSION_NO";
    static final String AUTH_XACTION_TRAITS = "AUTH_XACTION_TRAITS";
    static final String AUTH_VERSION_STATUS = "AUTH_VERSION_STATUS";
    static final String AUTH_SERIAL_NUM = "AUTH_SERIAL_NUM";
    static final String AUTH_SESSION_ID = "AUTH_SESSION_ID";
    static final String AUTH_CLIENT_CERTIFICATE = "AUTH_CLIENT_CERTIFICATE";
    static final String AUTH_PROXY_CLIENT_NAME = "PROXY_CLIENT_NAME";
    static final String AUTH_CLIENT_DN = "AUTH_CLIENT_DISTINGUISHED_NAME";
    static final String AUTH_INSTANCENAME = "AUTH_INSTANCENAME";
    static final String AUTH_DBNAME = "AUTH_DBNAME";
    static final String AUTH_INSTANCE_NO = "AUTH_INSTANCE_NO";
    static final String AUTH_DB_ID = "AUTH_DB_ID";
    static final String AUTH_DB_MOUNT_ID = "AUTH_DB_MOUNT_ID";
    static final String AUTH_QCACHE_MAXSIZE = "AUTH_QCACHE_MAXSIZE";
    static final String AUTH_QCACHE_CACHELAG = "AUTH_QCACHE_CACHELAG";
    static final String AUTH_GLOBALLY_UNIQUE_DBID = "AUTH_GLOBALLY_UNIQUE_DBID";
    static final String AUTH_QCACHE_CACHEID = "AUTH_QCACHE_CACHEID";
    static final String AUTH_QCACHE_REGID = "AUTH_QCACHE_REGID";
    static final String AUTH_SC_SERVER_HOST = "AUTH_SC_SERVER_HOST";
    static final String AUTH_SC_INSTANCE_NAME = "AUTH_SC_INSTANCE_NAME";
    static final String AUTH_SC_INSTANCE_ID = "AUTH_SC_INSTANCE_ID";
    static final String AUTH_SC_INSTANCE_START_TIME = "AUTH_SC_INSTANCE_START_TIME";
    static final String AUTH_SC_DBUNIQUE_NAME = "AUTH_SC_DBUNIQUE_NAME";
    static final String AUTH_SC_SERVICE_NAME = "AUTH_SC_SERVICE_NAME";
    static final String AUTH_SC_SVC_FLAGS = "AUTH_SC_SVC_FLAGS";
    static final String AUTH_SC_DB_DOMAIN = "AUTH_SC_DB_DOMAIN";
    static final String AUTH_SC_REAL_DBUNIQUE_NAME = "AUTH_SC_REAL_DBUNIQUE_NAME";
    static final String SHARD_NAME = "SHARD_NAME";
    static final String AUTH_SESSION_CLIENT_CSET = "SESSION_CLIENT_CHARSET";
    static final String AUTH_SESSION_CLIENT_LTYPE = "SESSION_CLIENT_LIB_TYPE";
    static final String AUTH_SESSION_CLIENT_DRVNM = "SESSION_CLIENT_DRIVER_NAME";
    static final String AUTH_SESSION_CLIENT_VSN = "SESSION_CLIENT_VERSION";
    static final String AUTH_NLS_LXLAN = "AUTH_NLS_LXLAN";
    static final String AUTH_NLS_LXCTERRITORY = "AUTH_NLS_LXCTERRITORY";
    static final String AUTH_NLS_LXCCURRENCY = "AUTH_NLS_LXCCURRENCY";
    static final String AUTH_NLS_LXCISOCURR = "AUTH_NLS_LXCISOCURR";
    static final String AUTH_NLS_LXCNUMERICS = "AUTH_NLS_LXCNUMERICS";
    static final String AUTH_NLS_LXCDATEFM = "AUTH_NLS_LXCDATEFM";
    static final String AUTH_NLS_LXCDATELANG = "AUTH_NLS_LXCDATELANG";
    static final String AUTH_NLS_LXCSORT = "AUTH_NLS_LXCSORT";
    static final String AUTH_NLS_LXNLSCOMP = "AUTH_NLS_LXNLSCOMP";
    static final String AUTH_NLS_LXCCALENDAR = "AUTH_NLS_LXCCALENDAR";
    static final String AUTH_NLS_LXCUNIONCUR = "AUTH_NLS_LXCUNIONCUR";
    static final String AUTH_NLS_LXCTIMEFM = "AUTH_NLS_LXCTIMEFM";
    static final String AUTH_NLS_LXCSTMPFM = "AUTH_NLS_LXCSTMPFM";
    static final String AUTH_NLS_LXCTTZNFM = "AUTH_NLS_LXCTTZNFM";
    static final String AUTH_NLS_LXCSTZNFM = "AUTH_NLS_LXCSTZNFM";
    static final String SESSION_CLIENT_LOBATTR = "SESSION_CLIENT_LOBATTR";
    static final String AUTH_KPPL_CONN_CLASS = "AUTH_KPPL_CONN_CLASS";
    static final String AUTH_KPPL_PURITY = "AUTH_KPPL_PURITY";
    static final String AUTH_KPPL_TAG = "AUTH_KPPL_TAG";
    static final String AUTH_KPPL_IS_MULTIPROP_TAG = "AUTH_KPPL_IS_MULTIPROP_TAG";
    static final String AUTH_KPPL_FIXUP_CB = "AUTH_KPPL_FIXUP_CB";
    static final String AUTH_KPPL_WAIT = "AUTH_KPPL_WAIT";
    static final String AUTH_PBKDF2_SPEEDY_KEY = "AUTH_PBKDF2_SPEEDY_KEY";
    static final String AUTH_CLIENT_PREFETCH_ROWS = "AUTH_CLIENT_PREFETCH_ROWS";
    private static final String AUTH_TOKEN = "AUTH_TOKEN";
    private static final String AUTH_HEADER = "AUTH_HEADER";
    private static final String AUTH_SIGNATURE = "AUTH_SIGNATURE";
    static final String AUTH_MAX_OPEN_CURSORS = "AUTH_MAX_OPEN_CURSORS";
    static final String AUTH_APPCTX_NSPACE = "AUTH_APPCTX_NSPACE��";
    static final String AUTH_APPCTX_ATTR = "AUTH_APPCTX_ATTR��";
    static final String AUTH_APPCTX_VALUE = "AUTH_APPCTX_VALUE��";
    static final String AUTH_SERVER_TYPE = "AUTH_SERVER_TYPE";
    static final String OCI_SERVER_TYPE_NONE = "0";
    static final String OCI_SERVER_TYPE_DEDICATED = "1";
    static final String OCI_SERVER_TYPE_SHARED = "2";
    static final String OCI_SERVER_TYPE_PSEUDO = "3";
    static final String OCI_SERVER_TYPE_POOLED = "4";
    static final String KPPL_PURITY_DEFAULT = "0";
    static final String KPPL_PURITY_NEW = "1";
    static final String KPPL_PURITY_SELF = "2";
    static final String SESS_PURITY_DEFAULT = "DEFAULT";
    static final String SESS_PURITY_SELF = "SELF";
    static final String AUTH_CONNECT_STRING = "AUTH_CONNECT_STRING";
    static final String AUTH_ORA_DEBUG_JDWP = "AUTH_ORA_DEBUG_JDWP";
    static final String DRIVER_NAME_DEFAULT = "jdbcthin";
    static final int KPU_LIB_UNKN = 0;
    static final int KPU_LIB_DEF = 1;
    static final int KPU_LIB_EI = 2;
    static final int KPU_LIB_XE = 3;
    static final int KPU_LIB_ICUS = 4;
    static final int KPU_LIB_OCI = 5;
    static final int KPU_LIB_THIN = 10;
    static final String AUTH_ORA_EDITION = "AUTH_ORA_EDITION";
    static final String AUTH_AUDIT_BANNER = "AUTH_AUDIT_BANNER";
    static final String AUTH_COPYRIGHT = "AUTH_COPYRIGHT";
    static final String COPYRIGHT_STR = "\"Oracle\nEverybody follows\nSpeedy bits exchange\nStars await to glow\"\nThe preceding key is copyrighted by Oracle Corporation.\nDuplication of this key is not allowed without permission\nfrom Oracle Corporation. Copyright 2003 Oracle Corporation.";
    static final String SESSION_TIME_ZONE = "SESSION_TIME_ZONE";
    static final String SESSION_NLS_LXCCHARSET = "SESSION_NLS_LXCCHARSET";
    static final String SESSION_NLS_LXCNLSLENSEM = "SESSION_NLS_LXCNLSLENSEM";
    static final String SESSION_NLS_LXCNCHAREXCP = "SESSION_NLS_LXCNCHAREXCP";
    static final String SESSION_NLS_LXCNCHARIMP = "SESSION_NLS_LXCNCHARIMP";
    public static final int AUTH_FLAG_O5LOGON = 0;
    public static final int AUTH_FLAG_NONO5LOGON = 1;
    String sessionTimeZone;
    private boolean isRenegotiating;
    private T4CKvaldfList keyValList;
    private byte[] user;
    private long logonMode;
    private byte[][] outKeys;
    private byte[][] outValues;
    private int[] outFlags;
    private int outNbPairs;
    private byte[] PBKDF2Salt;
    private int PBKDF2VgenCount;
    private int PBKDF2SderCount;
    O5Logon o5logonHelper;
    private static final String CLASS_NAME = T4CTTIoauthenticate.class.getName();
    static final String SESS_PURITY_NEW = "NEW";
    static final String[][] KPPL_PURITY = {new String[]{"DEFAULT", "0"}, new String[]{SESS_PURITY_NEW, "1"}, new String[]{"SELF", "2"}};
    private static final DateTimeFormatter AUTH_HEADER_DATE_FORMATTER = DateTimeFormatter.ofPattern("E, dd MMM uuuu HH:mm:ss 'GMT'", Locale.US);

    T4CTTIoauthenticate(T4CConnection _conn, String _ressourceManagerId) throws SQLException {
        super(_conn, (byte) 3);
        this.editionName = null;
        this.isSessionTZ = true;
        this.sessionTimeZone = null;
        this.isRenegotiating = false;
        this.keyValList = null;
        this.user = null;
        this.outKeys = (byte[][]) null;
        this.outValues = (byte[][]) null;
        this.outFlags = new int[0];
        this.outNbPairs = 0;
        this.o5logonHelper = null;
        this.ressourceManagerId = _ressourceManagerId;
        setSessionFields(_conn);
        this.isSessionTZ = true;
        this.bUseO5Logon = false;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.user != null && this.user.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSB4(this.user.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSB4(0);
        }
        this.meg.marshalUB4(this.logonMode);
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.keyValList.size());
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.user != null && this.user.length > 0) {
            this.meg.marshalCHR(this.user);
        }
        this.meg.marshalKEYVAL(this.keyValList.getKeys(), this.keyValList.getValues(), this.keyValList.getFlags(), this.keyValList.size());
        if (getFunCode() == 115) {
            end(Metrics.ConnectionEvent.SEND_OAUTH);
            begin(Metrics.ConnectionEvent.RECEIVE_OAUTH);
        } else if (getFunCode() == 118) {
            end(Metrics.ConnectionEvent.SEND_OSESS);
            begin(Metrics.ConnectionEvent.RECEIVE_OSESS);
        }
    }

    public void setNamespaces(Map<String, Namespace> namespaces) {
        this.namespaces = namespaces;
    }

    private void doOAUTH(byte[] luser, @Blind byte[] lpassword, @Blind byte[] lnewPassword, long llogonMode, String authenticationAdaptor, boolean proxyAuthentication, byte[] proxyDN, byte[] proxyCertificate, byte[][] proxyRoles, int gl_session_id, int gl_serial_nb, byte[] speedyKey) throws SQLException, IOException {
        do {
            this.isRenegotiating = false;
            prepareForOAUTH(luser, lpassword, lnewPassword, llogonMode, authenticationAdaptor, proxyAuthentication, proxyDN, proxyCertificate, proxyRoles, gl_session_id, gl_serial_nb, speedyKey);
            doRPC();
        } while (this.isRenegotiating);
    }

    private CompletionStage<Void> doOAUTHAsync(byte[] luser, @Blind byte[] lpassword, @Blind byte[] lnewPassword, long llogonMode, String authenticationAdaptor, boolean proxyAuthentication, byte[] proxyDN, byte[] proxyCertificate, byte[][] proxyRoles, int gl_session_id, int gl_serial_nb, byte[] speedyKey) {
        try {
            prepareForOAUTH(luser, lpassword, lnewPassword, llogonMode, authenticationAdaptor, proxyAuthentication, proxyDN, proxyCertificate, proxyRoles, gl_session_id, gl_serial_nb, speedyKey);
            return doRPCAsync();
        } catch (SQLException preRPCFailure) {
            return CompletionStageUtil.failedStage(preRPCFailure);
        }
    }

    private void prepareForOAUTH(byte[] luser, @Blind byte[] lpassword, @Blind byte[] lnewPassword, long llogonMode, String authenticationAdaptor, boolean proxyAuthentication, byte[] proxyDN, byte[] proxyCertificate, byte[][] proxyRoles, int gl_session_id, int gl_serial_nb, byte[] speedyKey) throws SQLException {
        begin(Metrics.ConnectionEvent.SEND_OAUTH);
        setFunCode((short) 115);
        this.user = luser;
        this.keyValList = new T4CKvaldfList(this.meg.conv);
        initializeLogonModeForOAUTH(luser, llogonMode, lpassword, authenticationAdaptor, proxyAuthentication);
        if (lnewPassword != null) {
            initializeForOAUTHWithNewPassword(lnewPassword);
        }
        setPasswordKeyValsForOAUTH(lpassword, speedyKey);
        setProxyLogonKeyValsForOAUTH(proxyDN, proxyCertificate, proxyRoles);
        setVSessionKeyValsForOAUTH();
        setInternalNameKeyValsForOAUTH();
        setAlterSessionKeyValsForOAUTH();
        setDriverIdentityKeyValsForOAUTH();
        setSessionIdentityKeyValsForOAUTH(gl_session_id, gl_serial_nb);
        setDRCPKeyValsForOAUTH();
        setResultSetCacheKeyValsForOAUTH();
        setJDWPValForOAuth();
        setClientContextForOAUTH();
        setMiscellaneousKeyValsForOAUTH();
        resetStateBeforeCall();
    }

    private final void initializeLogonModeForOAUTH(byte[] luser, long llogonMode, @Blind byte[] lpassword, String authenticationAdaptor, boolean proxyAuthentication) {
        this.logonMode = llogonMode | 1;
        if (this.connection.isResultSetCacheEnabled) {
            this.logonMode |= 8388608;
        }
        if (proxyAuthentication) {
            this.logonMode |= 1024;
        }
        if (luser != null && luser.length != 0 && lpassword != null && authenticationAdaptor != AnoServices.AUTHENTICATION_RADIUS) {
            this.logonMode |= 256;
        }
    }

    private final void initializeForOAUTHWithNewPassword(@Blind byte[] lnewPassword) throws SQLException {
        this.logonMode |= 18;
        this.logonMode ^= 1;
        this.keyValList.add(AUTH_NEWPASSWORD, lnewPassword);
    }

    private final void setPasswordKeyValsForOAUTH(@Blind byte[] lpassword, byte[] speedyKey) throws SQLException {
        if (lpassword != null) {
            this.keyValList.add(AUTH_PASSWORD, lpassword);
        }
        if (speedyKey != null) {
            this.keyValList.add(AUTH_PBKDF2_SPEEDY_KEY, speedyKey);
        }
        if (this.bUseO5Logon && this.encryptedKB != null) {
            this.keyValList.add(AUTH_SESSKEY, this.encryptedKB, (byte) 1);
        }
    }

    private final void setProxyLogonKeyValsForOAUTH(byte[] proxyDN, byte[] proxyCertificate, byte[][] proxyRoles) throws SQLException {
        if (proxyRoles != null) {
            for (byte[] bArr : proxyRoles) {
                this.keyValList.add(AUTH_INITIAL_CLIENT_ROLE, bArr);
            }
        }
        if (proxyDN != null) {
            this.keyValList.add(AUTH_CLIENT_DN, proxyDN);
        }
        if (proxyCertificate != null) {
            this.keyValList.add(AUTH_CLIENT_CERTIFICATE, proxyCertificate);
        }
    }

    private final void setVSessionKeyValsForOAUTH() throws SQLException {
        this.keyValList.add(AUTH_TERMINAL, this.terminal);
        if (this.programName != null) {
            this.keyValList.add(AUTH_PROGRAM_NM, this.programName);
        }
        if (this.clientname != null) {
            this.keyValList.add(AUTH_PROXY_CLIENT_NAME, this.clientname);
        }
        this.keyValList.add(AUTH_MACHINE, this.machine);
        this.keyValList.add(AUTH_PID, this.processID);
    }

    private final void setInternalNameKeyValsForOAUTH() throws SQLException {
        if (!this.ressourceManagerId.equals(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_RESOURCE_MANAGER_ID_DEFAULT)) {
            byte[] key = this.meg.conv.StringToCharBytes(AUTH_INTERNALNAME);
            key[key.length - 1] = 0;
            this.keyValList.add(key, this.internalName);
            byte[] key2 = this.meg.conv.StringToCharBytes(AUTH_EXTERNALNAME);
            key2[key2.length - 1] = 0;
            this.keyValList.add(key2, this.externalName);
        }
    }

    private final void setDriverIdentityKeyValsForOAUTH() throws SQLException {
        if (this.editionName != null) {
            this.keyValList.add(AUTH_ORA_EDITION, this.editionName);
        }
        this.keyValList.add(AUTH_SESSION_CLIENT_DRVNM, this.driverName);
        this.keyValList.add(AUTH_SESSION_CLIENT_VSN, this.meg.conv.StringToCharBytes(Integer.toString(versionStringToInt(this.connection.doGetMetaData().getDriverVersion()), 10)));
    }

    private final void setJDWPValForOAuth() throws SQLException {
        if (this.connection.thinDebugJDWP != null && !this.connection.thinDebugJDWP.isEmpty()) {
            if (this.o5logonHelper == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_JDWP_EXTERNAL_AUTH_NOT_SUPPORTED).fillInStackTrace());
            }
            byte[] jdwpVal = this.o5logonHelper.encryptJDWPValue(this.meg.conv.StringToCharBytes(this.connection.thinDebugJDWP));
            if (jdwpVal != null) {
                this.keyValList.add(AUTH_ORA_DEBUG_JDWP, jdwpVal);
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_JDWP_FAIL_TO_ENCRYPT_VALUE).fillInStackTrace());
        }
    }

    private final void setMiscellaneousKeyValsForOAUTH() throws SQLException {
        this.keyValList.add(SESSION_CLIENT_LOBATTR, this.enableTempLobRefCnt);
        this.keyValList.add(AUTH_CONNECT_STRING, this.meg.conv.StringToCharBytes(this.connection.net().getConnectionString()));
        this.keyValList.add(AUTH_COPYRIGHT, this.meg.conv.StringToCharBytes(COPYRIGHT_STR));
        this.keyValList.add(AUTH_ACL, this.aclValue);
    }

    private final void setClientContextForOAUTH() throws SQLException {
        if (this.namespaces != null) {
            for (String namespace : this.namespaces.keySet()) {
                Namespace namespaceObj = this.namespaces.get(namespace);
                for (int i = 0; i < namespaceObj.nbPairs; i++) {
                    this.keyValList.add(AUTH_APPCTX_NSPACE, this.meg.conv.StringToCharBytes(namespaceObj.name));
                    this.keyValList.add(AUTH_APPCTX_ATTR, this.meg.conv.StringToCharBytes(namespaceObj.keys[i]));
                    this.keyValList.add(AUTH_APPCTX_VALUE, this.meg.conv.StringToCharBytes(namespaceObj.values[i]));
                }
            }
        }
    }

    private final void setAlterSessionKeyValsForOAUTH() throws SQLException {
        this.keyValList.add(AUTH_ALTER_SESSION, this.alterSession, (byte) 1);
    }

    private final void setSessionIdentityKeyValsForOAUTH(int gl_session_id, int gl_serial_nb) throws SQLException {
        if (gl_session_id != -1) {
            this.keyValList.add(AUTH_SESSION_ID, this.meg.conv.StringToCharBytes(Integer.toString(gl_session_id)));
        }
        if (gl_serial_nb != -1) {
            this.keyValList.add(AUTH_SERIAL_NUM, this.meg.conv.StringToCharBytes(Integer.toString(gl_serial_nb)));
        }
    }

    private final void setDRCPKeyValsForOAUTH() throws SQLException {
        if (this.connection.drcpEnabled) {
            if (this.connection.drcpConnectionClass != null && this.connection.drcpConnectionClass != "") {
                this.keyValList.add(AUTH_KPPL_CONN_CLASS, this.meg.conv.StringToCharBytes(this.connection.drcpConnectionClass));
            }
            this.keyValList.add(AUTH_KPPL_PURITY, this.meg.conv.StringToCharBytes(getKPPLPurity()));
            if (this.connection.drcpTagName != null) {
                this.keyValList.add(AUTH_KPPL_TAG, this.meg.conv.StringToCharBytes(this.connection.drcpTagName));
                if (this.connection.useDRCPMultipletag) {
                    this.keyValList.add(AUTH_KPPL_IS_MULTIPROP_TAG, this.meg.conv.StringToCharBytes("TRUE"));
                }
            }
            if (this.connection.drcpPLSQLCallback != null && this.connection.drcpPLSQLCallback.length() > 0 && this.connection.getTTCVersion() >= 8) {
                this.keyValList.add(AUTH_KPPL_FIXUP_CB, this.meg.conv.StringToCharBytes(this.connection.drcpPLSQLCallback));
            }
        }
    }

    private final void setResultSetCacheKeyValsForOAUTH() throws SQLException {
        if (this.connection.isResultSetCacheActive()) {
            this.keyValList.add(AUTH_QCACHE_CACHEID, this.connection.getResultSetCacheIdAsNibbles());
            this.keyValList.add(AUTH_QCACHE_REGID, this.meg.conv.StringToCharBytes(Long.toString(this.connection.getResultSetCacheRegistrationId())));
        }
    }

    private final void resetStateBeforeCall() {
        this.outNbPairs = 0;
        this.outKeys = (byte[][]) null;
        this.outValues = (byte[][]) null;
        this.outFlags = new int[0];
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    protected boolean processRENEG() {
        this.isRenegotiating = true;
        return false;
    }

    void doOSESSKEY(String userName, long llogonMode) throws SQLException, IOException {
        do {
            this.isRenegotiating = false;
            initializeForOSESSKEY(userName, llogonMode);
            begin(Metrics.ConnectionEvent.SEND_OSESS);
            doRPC();
        } while (this.isRenegotiating);
    }

    final CompletionStage<Void> doOSESSKEYAsync(String userName, long llogonMode) {
        try {
            initializeForOSESSKEY(userName, llogonMode);
            return doRPCAsync();
        } catch (SQLException preRPCFailure) {
            return CompletionStageUtil.failedStage(preRPCFailure);
        }
    }

    private final void initializeForOSESSKEY(String userName, long llogonMode) throws SQLException {
        setFunCode((short) 118);
        this.user = this.meg.conv.StringToCharBytes(userName);
        this.logonMode = llogonMode | 1;
        this.keyValList = new T4CKvaldfList(this.meg.conv);
        this.keyValList.add(AUTH_TERMINAL, this.terminal);
        if (this.programName != null) {
            this.keyValList.add(AUTH_PROGRAM_NM, this.programName);
        }
        this.keyValList.add(AUTH_MACHINE, this.machine);
        this.keyValList.add(AUTH_PID, this.processID);
        this.keyValList.add(AUTH_SID, this.sysUserName);
        resetStateBeforeCall();
    }

    /* JADX WARN: Type inference failed for: r1v5, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v8, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.outNbPairs = this.meg.unmarshalUB2();
        this.outKeys = new byte[this.outNbPairs];
        this.outValues = new byte[this.outNbPairs];
        this.outFlags = this.meg.unmarshalKEYVAL(this.outKeys, this.outValues, this.outNbPairs);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        if (getFunCode() == 118) {
            if (this.connection.getT4CTTIoer().getRetCode() != 28035 || this.connection.net().getAuthenticationAdaptorName() != AnoServices.AUTHENTICATION_RADIUS) {
                this.connection.getT4CTTIoer().processError();
                return;
            }
            return;
        }
        super.processError();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    protected void processRPA() throws SQLException {
        if (getFunCode() == 115) {
            Properties connectionValues = new Properties();
            for (int i = 0; i < this.outNbPairs; i++) {
                String keyStr = this.meg.conv.CharBytesToString(this.outKeys[i], this.outKeys[i].length).trim();
                String valueStr = "";
                if (this.outValues[i] != null) {
                    valueStr = this.meg.conv.CharBytesToString(this.outValues[i], this.outValues[i].length).trim();
                }
                if (!this.connection.isUsingCustomHostnameResolver() || !"AUTH_ONS_CONFIG".equals(keyStr)) {
                    connectionValues.setProperty(keyStr, valueStr);
                }
            }
            String versionNoStr = connectionValues.getProperty(AUTH_VERSION_NO);
            if (versionNoStr != null) {
                try {
                    int serverVersionInt = new Integer(versionNoStr).intValue();
                    debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "receive", "Server version at connection:{0}", (String) null, (Throwable) null, () -> {
                        try {
                            return new Object[]{versionIntToString(serverVersionInt)};
                        } catch (SQLException e) {
                            return new Object[]{e.getMessage()};
                        }
                    });
                } catch (NumberFormatException e) {
                }
            }
            String authDBMountID = connectionValues.getProperty(AUTH_DB_MOUNT_ID);
            if (Objects.nonNull(authDBMountID) && Long.parseLong(authDBMountID) == 0 && this.connection.databaseStateRequirement.equals("MOUNT")) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0229).fillInStackTrace());
            }
            connectionValues.setProperty("SERVER_HOST", connectionValues.getProperty(AUTH_SC_SERVER_HOST, ""));
            connectionValues.setProperty("INSTANCE_NAME", connectionValues.getProperty(AUTH_SC_INSTANCE_NAME, ""));
            connectionValues.setProperty("DATABASE_NAME", connectionValues.getProperty(AUTH_SC_DBUNIQUE_NAME, ""));
            connectionValues.setProperty("SERVICE_NAME", connectionValues.getProperty(AUTH_SC_SERVICE_NAME, ""));
            connectionValues.setProperty(SESSION_TIME_ZONE, this.sessionTimeZone);
            this.connection.updateSessionProperties(connectionValues);
            return;
        }
        if (getFunCode() == 118) {
            Properties connectionValues2 = new Properties();
            for (int i2 = 0; i2 < this.outNbPairs; i2++) {
                String keyStr2 = this.meg.conv.CharBytesToString(this.outKeys[i2], this.outKeys[i2].length).trim();
                String valueStr2 = "";
                if (this.outValues[i2] != null) {
                    valueStr2 = this.meg.conv.CharBytesToString(this.outValues[i2], this.outValues[i2].length).trim();
                }
                if (keyStr2.compareTo(AUTH_GLOBALLY_UNIQUE_DBID) == 0 || keyStr2.compareTo(AUTH_QCACHE_MAXSIZE) == 0 || keyStr2.compareTo(AUTH_QCACHE_CACHELAG) == 0) {
                    connectionValues2.setProperty(keyStr2, valueStr2);
                }
            }
            this.connection.updateSessionProperties(connectionValues2);
            if (this.connection.net().getAuthenticationAdaptorName() != AnoServices.AUTHENTICATION_RADIUS) {
                if (this.outKeys == null || this.outKeys.length < 1) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0210).fillInStackTrace());
                }
                int indexOfSessKey = -1;
                int indexOfVfrDat = -1;
                int indexOfPBKDF2Salt = -1;
                int indexOfPBKDF2VgenCount = -1;
                int indexOfPBKDF2SderCount = -1;
                for (int i3 = 0; i3 < this.outKeys.length; i3++) {
                    try {
                        String sKey = new String(this.outKeys[i3], "US-ASCII");
                        if (sKey.equals(AUTH_SESSKEY)) {
                            indexOfSessKey = i3;
                        } else if (sKey.equals(AUTH_VFR_DATA)) {
                            indexOfVfrDat = i3;
                        } else if (sKey.equals("AUTH_PBKDF2_CSK_SALT")) {
                            indexOfPBKDF2Salt = i3;
                        } else if (sKey.equals("AUTH_PBKDF2_VGEN_COUNT")) {
                            indexOfPBKDF2VgenCount = i3;
                        } else if (sKey.equals("AUTH_PBKDF2_SDER_COUNT")) {
                            indexOfPBKDF2SderCount = i3;
                        }
                        if (indexOfVfrDat != -1 && indexOfSessKey != -1 && indexOfPBKDF2Salt != -1 && indexOfPBKDF2VgenCount != -1 && indexOfPBKDF2SderCount != -1) {
                            break;
                        }
                    } catch (UnsupportedEncodingException e2) {
                    }
                }
                if (indexOfSessKey == -1) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0210).fillInStackTrace());
                }
                this.encryptedSK = this.outValues[indexOfSessKey];
                if (indexOfVfrDat != -1) {
                    this.bUseO5Logon = true;
                    this.salt = this.outValues[indexOfVfrDat];
                    this.verifierType = this.outFlags[indexOfVfrDat];
                    if (this.connection.allowedLogonVersion.equals("12a") && this.verifierType != ZTVT_SHA512) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NO_VALID_LOGON_METHOD).fillInStackTrace());
                    }
                    if (this.connection.allowedLogonVersion.equals("12") && this.verifierType != ZTVT_SHA512 && (this.verifierType != ZTVT_SSH1 || !this.connection.hasServerCompileTimeCapability(4, 2))) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NO_VALID_LOGON_METHOD).fillInStackTrace());
                    }
                }
                if (indexOfPBKDF2Salt != -1) {
                    this.PBKDF2Salt = this.outValues[indexOfPBKDF2Salt];
                }
                if (indexOfPBKDF2VgenCount != -1) {
                    try {
                        this.PBKDF2VgenCount = Integer.parseInt(new String(this.outValues[indexOfPBKDF2VgenCount], "US-ASCII"));
                        if (this.PBKDF2VgenCount < 4096) {
                            this.PBKDF2VgenCount = 4096;
                        }
                    } catch (Exception e3) {
                    }
                }
                if (indexOfPBKDF2SderCount != -1) {
                    try {
                        this.PBKDF2SderCount = Integer.parseInt(new String(this.outValues[indexOfPBKDF2SderCount], "US-ASCII"));
                        if (this.PBKDF2SderCount < 3) {
                            this.PBKDF2SderCount = 3;
                        }
                    } catch (Exception e4) {
                    }
                }
                if (!this.bUseO5Logon) {
                    if (this.encryptedSK == null || this.encryptedSK.length != 16) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0210).fillInStackTrace());
                    }
                }
            }
        }
    }

    void doOAUTH(String userStr, @Blind String passwordStr, @Blind String newPasswordStr, long logonMode) throws SQLException, IOException {
        doOAUTH(userStr, passwordStr, newPasswordStr, logonMode, -1, -1, (byte[][]) null);
    }

    final CompletionStage<Void> doOAUTHAsync(String userStr, @Blind String passwordStr, @Blind String newPasswordStr, long logonMode) {
        return doOAUTHAsync(userStr, passwordStr, newPasswordStr, logonMode, -1, -1, (byte[][]) null);
    }

    void doOAUTH(String userStr, @Blind String passwordStr, @Blind String newPasswordStr, long logonMode, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOAUTH", "userStr={0}, logonMode={1}, gl_session_id={2}, gl_serial_nb={3}, proxyRoles={4}", (String) null, (Throwable) null, userStr, Long.valueOf(logonMode), Integer.valueOf(gl_session_id), Integer.valueOf(gl_serial_nb), proxyRoles);
        String authenticationAdaptor = this.connection.net().getAuthenticationAdaptorName();
        boolean isKerberosAuthentication = AnoServices.AUTHENTICATION_KERBEROS5.equals(authenticationAdaptor);
        if (!isKerberosAuthentication && userStr != null && userStr.length() != 0) {
            byte[] user = this.meg.conv.StringToCharBytes(userStr);
            boolean isRadiusAuthentication = AnoServices.AUTHENTICATION_RADIUS.equals(authenticationAdaptor);
            validateKeySizeForOAUTH(isRadiusAuthentication);
            boolean shouldIgnorePassword = passwordStr == null || (logonMode == 32 && "BEQ".equalsIgnoreCase(authenticationAdaptor));
            if (!shouldIgnorePassword) {
                String sanitizedPassword = sanitizeInputCredential(passwordStr);
                byte[] passwordNet = this.meg.conv.StringToCharBytes(sanitizedPassword);
                if (isRadiusAuthentication) {
                    doOAUTHWithRadiusAuthentication(passwordNet, user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                    return;
                }
                String sanitizedUser = sanitizeInputCredential(userStr);
                if (this.bUseO5Logon) {
                    doOAUTHWithO5Logon(sanitizedUser, sanitizedPassword, passwordNet, newPasswordStr, user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                    return;
                } else {
                    doOAUTHWithO3Logon(sanitizedUser, sanitizedPassword, passwordNet, user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                    return;
                }
            }
            doOAUTHWithoutPassword(user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
            return;
        }
        doOAUTHWithoutUser(logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
        if (AnoServices.AUTHENTICATION_KERBEROS5.equals(authenticationAdaptor)) {
            this.connection.net().doKeyFoldinForExternalAuth();
        }
    }

    final CompletionStage<Void> doOAUTHAsync(String userStr, @Blind String passwordStr, @Blind String newPasswordStr, long logonMode, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) {
        String authenticationAdaptor = this.connection.net().getAuthenticationAdaptorName();
        if (userStr != null && userStr.length() != 0) {
            try {
                byte[] user = this.meg.conv.StringToCharBytes(userStr);
                boolean isRadiusAuthentication = AnoServices.AUTHENTICATION_RADIUS.equals(authenticationAdaptor);
                try {
                    validateKeySizeForOAUTH(isRadiusAuthentication);
                    if (passwordStr != null) {
                        String sanitizedPassword = sanitizeInputCredential(passwordStr);
                        try {
                            byte[] passwordNet = this.meg.conv.StringToCharBytes(sanitizedPassword);
                            if (isRadiusAuthentication) {
                                return doOAUTHWithRadiusAuthenticationAsync(passwordNet, user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                            }
                            String sanitizedUser = sanitizeInputCredential(userStr);
                            if (this.bUseO5Logon) {
                                return doOAUTHWithO5LogonAsync(sanitizedUser, sanitizedPassword, passwordNet, newPasswordStr, user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                            }
                            return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connection is not supported with databases  older than version 12"));
                        } catch (SQLException encodingException) {
                            return CompletionStageUtil.failedStage(encodingException);
                        }
                    }
                    return doOAUTHWithoutPasswordAsync(user, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
                } catch (SQLException validationException) {
                    return CompletionStageUtil.failedStage(validationException);
                }
            } catch (SQLException encodingException2) {
                return CompletionStageUtil.failedStage(encodingException2);
            }
        }
        CompletionStage<Void> completionStage = doOAUTHWithoutUserAsync(logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
        if (AnoServices.AUTHENTICATION_KERBEROS5.equals(authenticationAdaptor)) {
            completionStage = completionStage.thenRun(() -> {
                this.connection.net().doKeyFoldinForExternalAuth();
            });
        }
        return completionStage;
    }

    private final void validateKeySizeForOAUTH(boolean isRadiusAuthentication) throws SQLException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "validateKeySizeForOAUTH", "isRadiusAuthentication={0}", (String) null, (String) null, (Object) Boolean.valueOf(isRadiusAuthentication));
        if (!isRadiusAuthentication && ((this.encryptedSK == null || this.encryptedSK.length > 16) && !this.bUseO5Logon)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0106).fillInStackTrace());
        }
        if (this.bUseO5Logon) {
            if (this.encryptedSK == null || (this.encryptedSK.length != 64 && this.encryptedSK.length != 96)) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0106).fillInStackTrace());
            }
        }
    }

    @Blind
    private final String sanitizeInputCredential(@Blind String credential) {
        String trimCredential = credential.trim();
        if (trimCredential.startsWith("\"") && trimCredential.endsWith("\"")) {
            return removeQuotes(trimCredential);
        }
        return trimCredential;
    }

    private final void doOAUTHWithO5Logon(String sanitizedUser, @Blind String sanitizedPassword, @Blind byte[] passwordNet, @Blind String newPasswordStr, byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        String sanitizedNewPassword;
        byte[] newPasswordNet;
        int[] encryptedNewPasswordLength;
        byte[] o5logonnewpassword;
        byte[] newPassword;
        byte[] encryptedPk;
        lazyLoadO5LogonHelper();
        validateO5VerifierType();
        int[] encryptedPasswordLength = new int[1];
        byte[] o5logonpassword = new byte[PASSWORD_BUFFER_LENGTH];
        for (int k = 0; k < PASSWORD_BUFFER_LENGTH; k++) {
            o5logonpassword[k] = 0;
        }
        boolean isNewPasswordSpecified = newPasswordStr != null;
        if (isNewPasswordSpecified) {
            sanitizedNewPassword = sanitizeInputCredential(newPasswordStr);
            newPasswordNet = this.meg.conv.StringToCharBytes(sanitizedNewPassword);
            encryptedNewPasswordLength = new int[1];
            o5logonnewpassword = new byte[PASSWORD_BUFFER_LENGTH];
            for (int k2 = 0; k2 < PASSWORD_BUFFER_LENGTH; k2++) {
                o5logonnewpassword[k2] = 0;
            }
        } else {
            sanitizedNewPassword = null;
            newPasswordNet = null;
            encryptedNewPasswordLength = null;
            o5logonnewpassword = null;
        }
        int[] encryptedPkLength = new int[1];
        byte[] encryptedPkTemp = new byte[PASSWORD_BUFFER_LENGTH];
        this.encryptedKB = new byte[this.encryptedSK.length];
        for (int k3 = 0; k3 < this.encryptedKB.length; k3++) {
            this.encryptedKB[k3] = 1;
        }
        try {
            this.o5logonHelper.generateOAuthResponse(this.verifierType, this.salt, sanitizedUser, sanitizedPassword, sanitizedNewPassword, passwordNet, newPasswordNet, this.encryptedSK, this.encryptedKB, o5logonpassword, o5logonnewpassword, encryptedPasswordLength, encryptedNewPasswordLength, this.meg.conv.isServerCSMultiByte, this.connection.getServerCompileTimeCapability(4), this.PBKDF2Salt, this.PBKDF2VgenCount, this.PBKDF2SderCount, encryptedPkTemp, encryptedPkLength);
        } catch (Exception e) {
        }
        byte[] password = new byte[encryptedPasswordLength[0]];
        System.arraycopy(o5logonpassword, 0, password, 0, encryptedPasswordLength[0]);
        if (isNewPasswordSpecified) {
            newPassword = new byte[encryptedNewPasswordLength[0]];
            System.arraycopy(o5logonnewpassword, 0, newPassword, 0, encryptedNewPasswordLength[0]);
        } else {
            newPassword = null;
        }
        if (this.verifierType == ZTVT_SHA512 && this.connection.hasServerCompileTimeCapability(4, 32)) {
            encryptedPk = new byte[encryptedPkLength[0]];
            System.arraycopy(encryptedPkTemp, 0, encryptedPk, 0, encryptedPkLength[0]);
        } else {
            encryptedPk = null;
        }
        doOAUTH(user, password, newPassword, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, encryptedPk);
        validateO5ServerResponse();
    }

    private final CompletionStage<Void> doOAUTHWithO5LogonAsync(String sanitizedUser, @Blind String sanitizedPassword, @Blind byte[] passwordNet, @Blind String newPasswordStr, byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) {
        String sanitizedNewPassword;
        byte[] newPasswordNet;
        int[] encryptedNewPasswordLength;
        byte[] o5logonnewpassword;
        byte[] newPassword;
        byte[] encryptedPk;
        lazyLoadO5LogonHelper();
        try {
            validateO5VerifierType();
            int[] encryptedPasswordLength = new int[1];
            byte[] o5logonpassword = new byte[256];
            for (int k = 0; k < 256; k++) {
                o5logonpassword[k] = 0;
            }
            boolean isNewPasswordSpecified = newPasswordStr != null;
            if (isNewPasswordSpecified) {
                sanitizedNewPassword = sanitizeInputCredential(newPasswordStr);
                try {
                    newPasswordNet = this.meg.conv.StringToCharBytes(sanitizedNewPassword);
                    encryptedNewPasswordLength = new int[1];
                    o5logonnewpassword = new byte[256];
                    for (int k2 = 0; k2 < 256; k2++) {
                        o5logonnewpassword[k2] = 0;
                    }
                } catch (SQLException encodingFailure) {
                    return CompletionStageUtil.failedStage(encodingFailure);
                }
            } else {
                sanitizedNewPassword = null;
                newPasswordNet = null;
                encryptedNewPasswordLength = null;
                o5logonnewpassword = null;
            }
            int[] encryptedPkLength = new int[1];
            byte[] encryptedPkTemp = new byte[256];
            this.encryptedKB = new byte[this.encryptedSK.length];
            for (int k3 = 0; k3 < this.encryptedKB.length; k3++) {
                this.encryptedKB[k3] = 1;
            }
            try {
                this.o5logonHelper.generateOAuthResponse(this.verifierType, this.salt, sanitizedUser, sanitizedPassword, sanitizedNewPassword, passwordNet, newPasswordNet, this.encryptedSK, this.encryptedKB, o5logonpassword, o5logonnewpassword, encryptedPasswordLength, encryptedNewPasswordLength, this.meg.conv.isServerCSMultiByte, this.connection.getServerCompileTimeCapability(4), this.PBKDF2Salt, this.PBKDF2VgenCount, this.PBKDF2SderCount, encryptedPkTemp, encryptedPkLength);
            } catch (Exception e) {
            }
            byte[] password = new byte[encryptedPasswordLength[0]];
            System.arraycopy(o5logonpassword, 0, password, 0, encryptedPasswordLength[0]);
            if (isNewPasswordSpecified) {
                newPassword = new byte[encryptedNewPasswordLength[0]];
                System.arraycopy(o5logonnewpassword, 0, newPassword, 0, encryptedNewPasswordLength[0]);
            } else {
                newPassword = null;
            }
            if (this.verifierType == ZTVT_SHA512 && this.connection.hasServerCompileTimeCapability(4, 32)) {
                encryptedPk = new byte[encryptedPkLength[0]];
                System.arraycopy(encryptedPkTemp, 0, encryptedPk, 0, encryptedPkLength[0]);
            } else {
                encryptedPk = null;
            }
            return doOAUTHAsync(user, password, newPassword, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, encryptedPk).thenRun(() -> {
                try {
                    validateO5ServerResponse();
                } catch (SQLException validationFailure) {
                    throw new CompletionException(validationFailure);
                }
            });
        } catch (SQLException validationFailure) {
            return CompletionStageUtil.failedStage(validationFailure);
        }
    }

    private final void lazyLoadO5LogonHelper() {
        if (this.o5logonHelper == null) {
            this.o5logonHelper = new O5Logon(this.connection, this.connection.isO7L_MRExposed, this.connection.thinUseJCEAPI, getDiagnosable());
        }
    }

    private final void validateO5VerifierType() throws SQLException {
        if (this.verifierType != ZTVT_ORCL_7 && this.verifierType != ZTVT_MD5 && this.verifierType != ZTVT_SMD5 && this.verifierType != ZTVT_SH1 && this.verifierType != ZTVT_SSH1 && this.verifierType != ZTVT_SHA512) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "validateO5VerifierType", "verifierType = {0}", (String) null, (String) null, (Object) Integer.valueOf(this.verifierType));
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0224).fillInStackTrace());
        }
    }

    private final void validateO5ServerResponse() throws SQLException {
        String b64SvrResponse = this.connection.sessionProperties.getProperty("AUTH_SVR_RESPONSE");
        try {
            lazyLoadO5LogonHelper();
            if (this.o5logonHelper.validateServerIdentity(b64SvrResponse)) {
                this.connection.net().setAuthSessionKey(this.o5logonHelper.getO5LogonKey());
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0225).fillInStackTrace());
        } catch (Exception e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0225).fillInStackTrace());
        }
    }

    private final void doOAUTHWithO3Logon(String sanitizedUser, @Blind String sanitizedPassword, @Blind byte[] passwordNet, byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        byte pwdPadLen;
        O3LoginClientHelper loginHelper = new O3LoginClientHelper(this.meg.conv.isServerCSMultiByte, this.connection.thinUseJCEAPI);
        byte[] sessionKey = loginHelper.getSessionKey(sanitizedUser, sanitizedPassword, this.encryptedSK);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doOAUTHWithO3Logon", "sessionKey = {0}", (String) null, (String) null, secure(T4CMAREngine.toHex(sessionKey)));
        if (passwordNet.length % 8 > 0) {
            pwdPadLen = (byte) (8 - (passwordNet.length % 8));
        } else {
            pwdPadLen = 0;
        }
        byte[] paddedPwd = new byte[passwordNet.length + pwdPadLen];
        System.arraycopy(passwordNet, 0, paddedPwd, 0, passwordNet.length);
        byte[] ePwdOnSessKey = loginHelper.getEPasswd(sessionKey, paddedPwd);
        byte[] password = new byte[(2 * paddedPwd.length) + 1];
        if (password.length < 2 * ePwdOnSessKey.length) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0106).fillInStackTrace());
        }
        RepConversion.bArray2Nibbles(ePwdOnSessKey, password);
        password[password.length - 1] = RepConversion.nibbleToHex(pwdPadLen);
        doOAUTH(user, password, null, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, null);
        this.connection.net().setAuthSessionKey(sessionKey);
    }

    private final void doOAUTHWithRadiusAuthentication(@Blind byte[] passwordNet, byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        byte pwdPadLen;
        byte[] password;
        int i;
        int i2;
        boolean isTLSConnection = this.connection.net().getSessionAttributes().getNTAdapter() instanceof TcpsNTAdapter;
        if (isTLSConnection) {
            password = passwordNet;
        } else {
            if ((passwordNet.length + 1) % 8 > 0) {
                pwdPadLen = (byte) (8 - ((passwordNet.length + 1) % 8));
            } else {
                pwdPadLen = 0;
            }
            byte[] paddedPwd = new byte[passwordNet.length + 1 + pwdPadLen];
            System.arraycopy(passwordNet, 0, paddedPwd, 0, passwordNet.length);
            byte[] opwd = AuthenticationService.obfuscatePasswordForRadius(paddedPwd);
            password = new byte[opwd.length * 2];
            for (int i3 = 0; i3 < opwd.length; i3++) {
                byte b1 = (byte) ((opwd[i3] & 240) >> 4);
                byte b2 = (byte) (opwd[i3] & 15);
                password[i3 * 2] = (byte) (b1 < 10 ? b1 + 48 : (b1 - 10) + 97);
                int i4 = (i3 * 2) + 1;
                if (b2 < 10) {
                    i = b2;
                    i2 = 48;
                } else {
                    i = b2 - 10;
                    i2 = 97;
                }
                password[i4] = (byte) (i + i2);
            }
        }
        doOAUTH(this.connection.net().getSessionAttributes().isTwoFactorAuthenticationDone() ? null : user, this.connection.net().getSessionAttributes().isTwoFactorAuthenticationDone() ? null : password, null, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, null);
        doKeyFoldinForRadius(passwordNet);
    }

    private void doKeyFoldinForRadius(byte[] pwd) {
        try {
            if (!this.connection.net().getSessionAttributes().profile.useWeakCrypto() && (this.connection.net().getSessionAttributes().isEncryptionActive || this.connection.net().getSessionAttributes().isChecksumActive)) {
                this.connection.net().setAuthSessionKey(MessageDigest.getInstance(AnoServices.CHECKSUM_MD5).digest(pwd));
            }
        } catch (Exception e) {
            throw new RuntimeException("Unable to get foldin key for RADIUS");
        }
    }

    private final CompletionStage<Void> doOAUTHWithRadiusAuthenticationAsync(@Blind byte[] passwordNet, byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) {
        byte pwdPadLen;
        byte[] password;
        int i;
        int i2;
        boolean isTLSConnection = this.connection.net().getSessionAttributes().getNTAdapter() instanceof TcpsNTAdapter;
        if (isTLSConnection) {
            password = passwordNet;
        } else {
            if ((passwordNet.length + 1) % 8 > 0) {
                pwdPadLen = (byte) (8 - ((passwordNet.length + 1) % 8));
            } else {
                pwdPadLen = 0;
            }
            byte[] paddedPwd = new byte[passwordNet.length + 1 + pwdPadLen];
            System.arraycopy(passwordNet, 0, paddedPwd, 0, passwordNet.length);
            byte[] opwd = AuthenticationService.obfuscatePasswordForRadius(paddedPwd);
            password = new byte[opwd.length * 2];
            for (int i3 = 0; i3 < opwd.length; i3++) {
                byte b1 = (byte) ((opwd[i3] & 240) >> 4);
                byte b2 = (byte) (opwd[i3] & 15);
                password[i3 * 2] = (byte) (b1 < 10 ? b1 + 48 : (b1 - 10) + 97);
                int i4 = (i3 * 2) + 1;
                if (b2 < 10) {
                    i = b2;
                    i2 = 48;
                } else {
                    i = b2 - 10;
                    i2 = 97;
                }
                password[i4] = (byte) (i + i2);
            }
        }
        return doOAUTHAsync(this.connection.net().getSessionAttributes().isTwoFactorAuthenticationDone() ? null : user, this.connection.net().getSessionAttributes().isTwoFactorAuthenticationDone() ? null : password, null, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, null).thenRun(() -> {
            doKeyFoldinForRadius(passwordNet);
        });
    }

    private final void doOAUTHWithoutUser(long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        doOAUTHWithoutPassword(null, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
    }

    private final CompletionStage<Void> doOAUTHWithoutUserAsync(long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) {
        return doOAUTHWithoutPasswordAsync(null, logonMode, authenticationAdaptor, gl_session_id, gl_serial_nb, proxyRoles);
    }

    private final void doOAUTHWithoutPassword(byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) throws SQLException, IOException {
        doOAUTH(user, null, null, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, null);
    }

    private final CompletionStage<Void> doOAUTHWithoutPasswordAsync(byte[] user, long logonMode, String authenticationAdaptor, int gl_session_id, int gl_serial_nb, byte[][] proxyRoles) {
        return doOAUTHAsync(user, null, null, logonMode, authenticationAdaptor, (gl_session_id == -1 || gl_serial_nb == -1) ? false : true, null, null, proxyRoles, gl_session_id, gl_serial_nb, null);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v60, types: [byte[]] */
    void doOAUTH(int type, @Blind(PropertiesBlinder.class) Properties prop, int gl_session_id, int gl_serial_nb) throws SQLException, IOException {
        byte[] dn = null;
        byte[] certificate = null;
        byte[][] roles = (byte[][]) null;
        byte[] proxyClientUser = null;
        if (prop.containsKey(oracle.jdbc.OracleConnection.PROXY_ROLES)) {
            String[] strRoles = (String[]) prop.get(oracle.jdbc.OracleConnection.PROXY_ROLES);
            roles = new byte[strRoles.length];
            for (int i = 0; i < strRoles.length; i++) {
                roles[i] = this.meg.conv.StringToCharBytes(strRoles[i]);
            }
        }
        if (type == 1) {
            String _userStr = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME);
            String _passwd = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_PASSWORD);
            if (_passwd != null && _passwd.length() != 0) {
                doOAUTH(_userStr, _passwd, null, 0L, gl_session_id, gl_serial_nb, roles);
                return;
            }
            proxyClientUser = this.meg.conv.StringToCharBytes(_userStr);
        } else if (type == 2) {
            String _dnStr = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_DISTINGUISHED_NAME);
            dn = this.meg.conv.StringToCharBytes(_dnStr);
        } else {
            try {
                byte[] certificate2 = (byte[]) prop.get(oracle.jdbc.OracleConnection.PROXY_CERTIFICATE);
                StringBuffer sb = new StringBuffer();
                for (byte b : certificate2) {
                    String str = Integer.toHexString(255 & b);
                    int length = str.length();
                    if (length == 0) {
                        sb.append("00");
                    } else if (length == 1) {
                        sb.append('0');
                        sb.append(str);
                    } else {
                        sb.append(str);
                    }
                }
                certificate = this.meg.conv.StringToCharBytes(sb.toString());
            } catch (Exception e) {
            }
        }
        doOAUTH(proxyClientUser, null, null, 0L, null, true, dn, certificate, roles, gl_session_id, gl_serial_nb, null);
    }

    final void doOAUTH(@Blind AccessToken accessToken, long logonMode) throws SQLException, IOException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOAUTH", "Using token-based authentication with logonMode={0}", (String) null, (String) null, (Object) Long.valueOf(logonMode));
        if (logonMode != 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "internal_logon and prelim_auth are not supported with token-based authentication").fillInStackTrace());
        }
        if (null == this.connection.getSecurityInformation().getTLSCipherSuite()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "TLS (ie: TCPS) must be enabled for token-based authentication").fillInStackTrace());
        }
        if (SecurityInformation.DNMatchStatus.NOT_VERIFIED == this.connection.getSecurityInformation().getDNMatchStatus()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Distinguished Name (DN) matching must be enabled for token-based authentication").fillInStackTrace());
        }
        if (!(accessToken instanceof OpaqueAccessToken)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Unsupported AccessToken type: " + accessToken.getClass() + ". Only instances created by oracle.jdbc.AccessToken are supported").fillInStackTrace());
        }
        OpaqueAccessToken opaqueAccessToken = (OpaqueAccessToken) accessToken;
        if (opaqueAccessToken.expiration().isBefore(OffsetDateTime.now())) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 25708, opaqueAccessToken.expiration()).fillInStackTrace());
        }
        this.connection.getDiagnosable().suspendLogging();
        do {
            try {
                prepareForOAUTH(null, null, null, logonMode, null, false, null, null, (byte[][]) null, -1, -1, null);
                setTokenKeyValsForOAUTH(opaqueAccessToken);
                try {
                    this.isRenegotiating = false;
                    doRPC();
                    this.keyValList = null;
                    this.meg.clearWriteBuffer();
                    if (this.isRenegotiating) {
                        try {
                            this.connection.getDiagnosable().resumeLogging();
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOAUTH", "Resending OAUTH for token-based authentication", (String) null, (Throwable) null);
                            this.connection.getDiagnosable().suspendLogging();
                        } finally {
                        }
                    }
                } catch (Throwable th) {
                    this.keyValList = null;
                    this.meg.clearWriteBuffer();
                    if (this.isRenegotiating) {
                        try {
                            this.connection.getDiagnosable().resumeLogging();
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOAUTH", "Resending OAUTH for token-based authentication", (String) null, (Throwable) null);
                            this.connection.getDiagnosable().suspendLogging();
                        } finally {
                        }
                    }
                    throw th;
                }
            } finally {
                this.connection.getDiagnosable().resumeLogging();
            }
        } while (this.isRenegotiating);
    }

    private void setTokenKeyValsForOAUTH(OpaqueAccessToken accessToken) throws SQLException {
        char[] token = accessToken.token().getChars();
        try {
            this.keyValList.add(AUTH_TOKEN, this.meg.conv.javaCharsToCHARBytes(token));
            OpaquePrivateKey signingKey = accessToken.privateKey();
            if (signingKey != null) {
                try {
                    byte[] header = this.meg.conv.StringToCharBytes(generateTokenHeader());
                    byte[] signedHeader = (byte[]) signingKey.map(key -> {
                        Signature signature = Signature.getInstance("SHA256withRSA");
                        signature.initSign(key);
                        signature.update(header);
                        return signature.sign();
                    });
                    try {
                        this.keyValList.add(AUTH_HEADER, header);
                        this.keyValList.add(AUTH_SIGNATURE, this.meg.conv.StringToCharBytes(Base64.getEncoder().encodeToString(signedHeader)));
                        Arrays.fill(signedHeader, (byte) 0);
                    } catch (Throwable th) {
                        Arrays.fill(signedHeader, (byte) 0);
                        throw th;
                    }
                } catch (GeneralSecurityException signingFailure) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Failed to generate a signature using private key.", signingFailure).fillInStackTrace());
                }
            }
        } finally {
            Arrays.fill(token, (char) 0);
        }
    }

    @Blind
    private String generateTokenHeader() throws SQLException, IOException {
        SessionAtts sessionAtts = this.meg.net.getSessionAttributes();
        ConnOption connOption = sessionAtts.getcOption();
        if (connOption == null) {
            throw tokenHeaderFailure();
        }
        String serviceName = connOption.service_name != null ? connOption.service_name : connOption.getOriginalConnOption().service_name;
        if (serviceName == null) {
            throw tokenHeaderFailure();
        }
        SocketChannel socketChannel = sessionAtts.getNTAdapter().getSocketChannel();
        if (socketChannel == null) {
            throw tokenHeaderFailure();
        }
        try {
            SocketAddress socketAddress = socketChannel.getRemoteAddress();
            if (!(socketAddress instanceof InetSocketAddress)) {
                throw tokenHeaderFailure();
            }
            InetSocketAddress inetSocketAddress = (InetSocketAddress) socketAddress;
            InetAddress inetAddress = inetSocketAddress.getAddress();
            if (inetAddress == null) {
                throw tokenHeaderFailure();
            }
            return String.format("date: %s\n(request-target): %s\nhost: %s:%d", ZonedDateTime.now(ZoneOffset.UTC).format(AUTH_HEADER_DATE_FORMATTER), serviceName, inetAddress.getHostAddress(), Integer.valueOf(inetSocketAddress.getPort()));
        } catch (IOException e) {
            throw tokenHeaderFailure();
        }
    }

    private SQLException tokenHeaderFailure() {
        return (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Failed to generate a header message for proof of possesion").fillInStackTrace();
    }

    private void setSessionFields(T4CConnection conn) throws SQLException {
        byte[] bArrStringToCharBytes;
        String terminalStr = this.connection.thinVsessionTerminal;
        String machineStr = this.connection.thinVsessionMachine;
        String osuserStr = this.connection.thinVsessionOsuser;
        String programStr = this.connection.thinVsessionProgram;
        String processStr = this.connection.thinVsessionProcess;
        String internalNameStr = this.connection.thinVsessionIname;
        String externalNameStr = this.connection.thinVsessionEname;
        String clientNameStr = this.connection.proxyClientName;
        String driverNameStr = this.connection.driverNameAttribute;
        String editionStr = this.connection.editionName;
        if (this.connection.enableTempLobRefCnt) {
            bArrStringToCharBytes = this.meg.conv.StringToCharBytes(Integer.toString(1));
        } else {
            bArrStringToCharBytes = this.meg.conv.StringToCharBytes(Integer.toString(0));
        }
        this.enableTempLobRefCnt = bArrStringToCharBytes;
        if (machineStr == null) {
            try {
                machineStr = InetAddress.getLocalHost().getHostName();
            } catch (Exception e) {
                machineStr = "jdbcclient";
            }
        }
        if (externalNameStr == null) {
            externalNameStr = "jdbc_" + this.ressourceManagerId;
        }
        if (driverNameStr == null) {
            driverNameStr = "jdbcthin : " + BuildInfo.getDriverVersion();
        }
        this.terminal = this.meg.conv.StringToCharBytes(terminalStr);
        this.machine = this.meg.conv.StringToCharBytes(machineStr);
        this.sysUserName = this.meg.conv.StringToCharBytes(osuserStr);
        this.programName = this.meg.conv.StringToCharBytes(programStr);
        this.processID = this.meg.conv.StringToCharBytes(processStr);
        this.internalName = this.meg.conv.StringToCharBytes(internalNameStr);
        this.externalName = this.meg.conv.StringToCharBytes(externalNameStr);
        if (clientNameStr != null) {
            this.clientname = this.meg.conv.StringToCharBytes(clientNameStr);
        }
        if (editionStr != null) {
            this.editionName = this.meg.conv.StringToCharBytes(editionStr);
        }
        this.driverName = this.meg.conv.StringToCharBytes(driverNameStr);
        TimeZone tz = TimeZone.getDefault();
        String defaultTimeZone = tz.getID();
        if (!ZONEIDMAP.isValidRegion(defaultTimeZone) || !conn.timezoneAsRegion) {
            int tzOffset = tz.getOffset(System.currentTimeMillis());
            int hr = tzOffset / 3600000;
            int mi = Math.abs((tzOffset / 60000) % 60);
            defaultTimeZone = (hr < 0 ? "" + hr : "+" + hr) + (mi < 10 ? ":0" + mi : ":" + mi);
        }
        this.sessionTimeZone = defaultTimeZone;
        conn.sessionTimeZone = defaultTimeZone;
        String nlslanguage = CharacterSetMetaData.getNLSLanguage(Locale.getDefault(Locale.Category.FORMAT));
        String alterNLSLanguage = null;
        if (nlslanguage != null) {
            alterNLSLanguage = " NLS_LANGUAGE='" + nlslanguage + "' ";
        }
        String nlsterritory = CharacterSetMetaData.getNLSTerritory(Locale.getDefault(Locale.Category.FORMAT));
        String alterNLSTerritory = null;
        if (nlsterritory != null) {
            alterNLSTerritory = " NLS_TERRITORY='" + nlsterritory + "' ";
        }
        if (alterNLSLanguage != null || alterNLSTerritory != null || this.isSessionTZ) {
            String doAlter = "ALTER SESSION SET " + (this.isSessionTZ ? "TIME_ZONE='" + this.sessionTimeZone + "'" : "") + (alterNLSLanguage != null ? alterNLSLanguage : "") + (alterNLSTerritory != null ? alterNLSTerritory : "");
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "setSessionFields", "alter session = {0}", (String) null, (String) null, (Object) doAlter);
            this.alterSession = this.meg.conv.StringToCharBytes(doAlter);
            this.alterSession[this.alterSession.length - 1] = 0;
        }
        this.aclValue = this.meg.conv.StringToCharBytes("4400");
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "setSessionFields", "terminalStr = {0}; machineStr = {1}; osuserStr = {2}; programStr = {3}; processStr = {4}; PrxyClientStr = {5}", (String) null, (Throwable) null, terminalStr, machineStr, osuserStr, programStr, processStr, clientNameStr);
    }

    String removeQuotes(@Blind String str) {
        int first = 0;
        int last = str.length() - 1;
        int i = 0;
        while (true) {
            if (i >= str.length()) {
                break;
            }
            if (str.charAt(i) == '\"') {
                i++;
            } else {
                first = i;
                break;
            }
        }
        int i2 = str.length() - 1;
        while (true) {
            if (i2 < 0) {
                break;
            }
            if (str.charAt(i2) == '\"') {
                i2--;
            } else {
                last = i2;
                break;
            }
        }
        String result = str.substring(first, last + 1);
        return result;
    }

    private int versionStringToInt(String driverVersion) throws SQLException {
        int numericVersion;
        String[] versionElements = driverVersion.split("\\.");
        int versionNumber = Integer.parseInt(versionElements[0].replaceAll("\\D", "")) & 255;
        int releaseUpdate = Integer.parseInt(versionElements[1].replaceAll("\\D", "")) & 255;
        int releaseUpdateRevision = Integer.parseInt(versionElements[2].replaceAll("\\D", "")) & 15;
        int increment = Integer.parseInt(versionElements[3].replaceAll("\\D", "")) & 255;
        int extension = Integer.parseInt(versionElements[4].replaceAll("\\D", "")) & 15;
        if (this.connection.getTTCVersion() >= 10) {
            numericVersion = (versionNumber << 24) | (releaseUpdate << 16) | (releaseUpdateRevision << 12) | (increment << 4) | extension;
        } else {
            numericVersion = (versionNumber << 24) | (releaseUpdate << 20) | (releaseUpdateRevision << 12) | (increment << 4) | extension;
        }
        return numericVersion;
    }

    private String versionIntToString(int versionInt) throws SQLException {
        String version;
        if (this.connection.getTTCVersion() >= 10) {
            int versionNumber = ((versionInt & (-16777216)) >> 24) & 255;
            int releaseUpdate = ((versionInt & 16711680) >> 16) & 255;
            int releaseUpdateRevision = ((versionInt & 61440) >> 12) & 15;
            int increment = ((versionInt & 4080) >> 4) & 255;
            int extension = versionInt & 15;
            version = "" + versionNumber + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + releaseUpdate + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + releaseUpdateRevision + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + increment + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + extension;
        } else {
            int versionNumber2 = ((versionInt & (-16777216)) >> 24) & 255;
            int releaseNumber = ((versionInt & 15728640) >> 20) & 15;
            int updateNumber = ((versionInt & 1044480) >> 12) & 255;
            int portingReleaseNumber = ((versionInt & 3840) >> 8) & 15;
            int portingUpdateNumber = versionInt & 255;
            version = "" + versionNumber2 + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + releaseNumber + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + updateNumber + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + portingReleaseNumber + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + portingUpdateNumber;
        }
        return version;
    }

    private String getKPPLPurity() throws SQLException {
        for (String[] purity : KPPL_PURITY) {
            if (this.connection.drcpConnectionPurity.equalsIgnoreCase(purity[0])) {
                return purity[1];
            }
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_SESSION_PURITY).fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    public byte[] getDerivedKeyJdbc(byte[] dhKey, int mode) throws SQLException, InvalidKeySpecException, NoSuchAlgorithmException {
        int mode2;
        if (this.verifierType == ZTVT_ORCL_7) {
            mode2 = mode | 1;
        } else {
            mode2 = mode | 0;
        }
        if (this.o5logonHelper == null) {
            this.o5logonHelper = new O5Logon(this.connection, this.connection.isO7L_MRExposed, this.connection.thinUseJCEAPI, getDiagnosable());
        }
        return this.o5logonHelper.getDerivedKey(dhKey, mode2);
    }
}
