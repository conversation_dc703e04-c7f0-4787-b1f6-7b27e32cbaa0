package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIqcinv.class */
class T4CTTIqcinv extends T4CTTIMsg {
    long kpdqcqid;
    long kpdqcopflg;
    T4CTTIkscn kpdqcscn;

    T4CTTIqcinv(T4CConnection _conn) {
        super(_conn, (byte) 0);
        this.kpdqcscn = null;
    }

    void unmarshal() throws SQLException, IOException {
        this.kpdqcqid = this.meg.unmarshalSB8();
        this.kpdqcopflg = this.meg.unmarshalUB4();
        this.kpdqcscn = this.connection.kscnForByteLength();
        this.kpdqcscn.unmarshal();
    }
}
