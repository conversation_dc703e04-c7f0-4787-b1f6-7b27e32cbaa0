package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.sql.SQLWarning;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIspfp.class */
final class T4CTTIspfp extends T4CTTIfun {
    T4CTTIspfp(T4CConnection _connection) {
        super(_connection, (byte) 3);
        setFunCode((short) 138);
    }

    void doOSPFPPUT() throws SQLException, IOException {
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalPTR();
        this.meg.marshalSWORD(100);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalSWORD(0);
        this.meg.marshalNULLPTR();
        this.meg.marshalSWORD(0);
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int length = this.meg.unmarshalUB2();
        byte[] warning = this.meg.unmarshalNBytes(length);
        if (length > 1) {
            String warningStr = this.meg.conv.CharBytesToString(warning, length, true);
            SQLWarning newsqlwarning = new SQLWarning(warningStr);
            SQLWarning sqlwarning = this.connection.getWarnings();
            if (sqlwarning == null) {
                this.connection.setWarnings(newsqlwarning);
            } else {
                sqlwarning.setNextWarning(newsqlwarning);
            }
        }
        this.meg.unmarshalUB2();
        this.meg.unmarshalUB2();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
