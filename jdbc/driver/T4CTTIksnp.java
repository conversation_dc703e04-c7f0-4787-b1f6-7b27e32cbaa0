package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIksnp.class */
class T4CTTIksnp {
    byte[] snapshot = null;
    int[] intArr = new int[1];

    T4CTTIksnp() {
    }

    void init() {
        this.snapshot = null;
    }

    void marshal(T4CMAREngine meg) throws IOException {
        if (this.snapshot == null) {
            meg.marshalUB4(0L);
        } else {
            meg.marshalUB4(this.snapshot.length);
            meg.marshalCLR(this.snapshot, 0, this.snapshot.length);
        }
    }

    void unmarshal(T4CMAREngine meg) throws SQLException, IOException {
        int snapshotLength = (int) meg.unmarshalUB4();
        this.snapshot = new byte[snapshotLength];
        if (snapshotLength > 0) {
            meg.unmarshalCLR(this.snapshot, 0, this.intArr, snapshotLength);
        }
    }
}
