package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Odscrarr.class */
final class T4C8Odscrarr extends T4CTTIfun {
    private static final byte OPERATIONFLAGS = 7;
    private static final long SQLPARSEVERSION = 2;
    byte[] sqltext;
    T4CTTIdcb dcb;
    int cursor_id;
    int numuds;
    private static final boolean UDSARRAYO2U = true;
    private static final boolean NUMUDSO2U = true;
    OracleStatement statement;
    private Accessor[] accessors;

    T4C8Odscrarr(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.cursor_id = 0;
        this.numuds = 0;
        this.statement = null;
        setFunCode((short) 98);
        this.dcb = new T4CTTIdcb(_conn);
    }

    void doODNY(OracleStatement stmt, int _offset, Accessor[] _acc, byte[] _sql) throws SQLException, IOException {
        this.numuds = 0;
        this.cursor_id = stmt.getCursorId();
        this.statement = stmt;
        if (_sql != null && _sql.length > 0) {
            this.sqltext = _sql;
        } else {
            this.sqltext = PhysicalConnection.EMPTY_BYTE_ARRAY;
        }
        this.dcb.init(stmt, _offset);
        this.accessors = _acc;
        this.numuds = 0;
        try {
            doRPC();
        } catch (SQLException se) {
            this.statement.setCursorId(this.connection.getT4CTTIoer().currCursorID);
            throw se;
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB1((short) 7);
        this.meg.marshalSWORD(this.cursor_id);
        if (this.sqltext.length == 0) {
            this.meg.marshalNULLPTR();
        } else {
            this.meg.marshalPTR();
        }
        this.meg.marshalSB4(this.sqltext.length);
        this.meg.marshalUB4(SQLPARSEVERSION);
        this.meg.marshalO2U(true);
        this.meg.marshalO2U(true);
        this.meg.marshalCHR(this.sqltext);
        this.sqltext = PhysicalConnection.EMPTY_BYTE_ARRAY;
    }

    Accessor[] getAccessors() {
        return this.accessors;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.accessors = this.dcb.receiveCommon(this.accessors, true);
        this.numuds = this.dcb.numuds;
    }
}
