package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTILob.class */
abstract class T4C8TTILob extends T4CTTIfun {
    private static final String CLASS_NAME = T4C8TTILob.class.getName();
    static final int KPLOB_GET_LEN = 1;
    static final int KPLOB_READ = 2;
    static final int KPLOB_TRIM = 32;
    static final int KPLOB_WRITE = 64;
    static final int KPLOB_FILE_OPEN = 256;
    static final int KPLOB_FILE_CLOSE = 512;
    static final int KPLOB_FILE_ISOPEN = 1024;
    static final int KPLOB_FILE_EXISTS = 2048;
    static final int KPLOB_TMP_CREATE = 272;
    static final int KPLOB_TMP_FREE = 273;
    static final int KPLOB_TMP_CBK = 17;
    static final int KPLOB_WRITE_APPEND = 8192;
    static final int KPLOB_PAGE_SIZE = 16384;
    static final int KPLOB_OPEN = 32768;
    static final int KPLOB_CLOSE = 65536;
    static final int KPLOB_ISOPEN = 69632;
    static final int KPLOB_ARRAY_OPERATION = 524288;
    static final int KPLOB_ARRAY_TMPFR = 524561;
    static final int KOKL_ORDONLY = 1;
    static final int KOKL_ORDWR = 2;
    static final int KOLF_ORDONLY = 11;
    static final int DTYCLOB = 112;
    static final int DTYBLOB = 113;
    byte[] sourceLobLocator;
    byte[] destinationLobLocator;
    int destinationLength;
    long sourceOffset;
    long destinationOffset;
    short characterSet;
    long lobamt;
    boolean lobnull;
    long lobops;
    int[] lobscn;
    int lobscnl;
    boolean nullO2U;
    boolean sendLobamt;
    byte[] inBuffer;
    long inBufferOffset;
    long inBufferNumBytes;
    byte[] outBuffer;
    int offsetInOutBuffer;
    long rowsProcessed;
    long lobBytesRead;
    boolean littleEndianClob;
    T4C8TTILobd lobd;

    abstract Datum createTemporaryLob(Connection connection, boolean z, int i) throws SQLException, IOException;

    abstract boolean openLob(byte[] bArr, int i) throws SQLException, IOException;

    abstract boolean closeLob(byte[] bArr) throws SQLException, IOException;

    abstract boolean isOpenLob(byte[] bArr) throws SQLException, IOException;

    T4C8TTILob(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.sourceLobLocator = null;
        this.destinationLobLocator = null;
        this.destinationLength = 0;
        this.sourceOffset = 0L;
        this.destinationOffset = 0L;
        this.characterSet = (short) 0;
        this.lobamt = 0L;
        this.lobnull = false;
        this.lobops = 0L;
        this.lobscn = null;
        this.lobscnl = 0;
        this.nullO2U = false;
        this.sendLobamt = false;
        this.inBuffer = null;
        this.outBuffer = null;
        this.offsetInOutBuffer = 0;
        this.rowsProcessed = 0L;
        this.lobBytesRead = 0L;
        this.littleEndianClob = false;
        this.lobd = null;
        setFunCode((short) 96);
        this.lobd = new T4C8TTILobd(_conn);
    }

    long read(byte[] lobLocator, long offset, long _numBytes, byte[] outBuffer, int _offsetInOutBuffer) throws SQLException, IOException {
        initializeLobdef();
        this.lobops = 2L;
        this.sourceLobLocator = lobLocator;
        this.sourceOffset = offset;
        this.lobamt = _numBytes;
        this.sendLobamt = true;
        this.outBuffer = outBuffer;
        this.offsetInOutBuffer = _offsetInOutBuffer;
        doRPC();
        return this.lobBytesRead;
    }

    long write(byte[] lobLocator, long offset, byte[] _inBuffer, long _inBufferOffset, long _numBytes) throws SQLException, IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "write", "offset={0}, _inBufferOffset={1}, _numBytes={2}", (String) null, (Throwable) null, Long.valueOf(offset), Long.valueOf(_inBufferOffset), Long.valueOf(_numBytes));
        validateLobOperation(lobLocator, 64, "write()");
        initializeLobdef();
        this.lobops = 64L;
        this.sourceLobLocator = lobLocator;
        this.sourceOffset = offset;
        this.lobamt = _numBytes;
        this.sendLobamt = true;
        this.inBuffer = _inBuffer;
        this.inBufferOffset = _inBufferOffset;
        this.inBufferNumBytes = _numBytes;
        doRPC();
        long bytesWritten = this.lobamt;
        return bytesWritten;
    }

    long getLength(byte[] lobLocator) throws SQLException, IOException {
        prepareForGetLengthRPC(lobLocator);
        doRPC();
        return this.lobamt;
    }

    private void prepareForGetLengthRPC(byte[] lobLocator) {
        initializeLobdef();
        this.lobops = 1L;
        this.sourceLobLocator = lobLocator;
        this.sendLobamt = true;
    }

    long getChunkSize(byte[] lobLocator) throws SQLException, IOException {
        initializeLobdef();
        this.lobops = 16384L;
        this.sourceLobLocator = lobLocator;
        this.sendLobamt = true;
        doRPC();
        return this.lobamt;
    }

    long trim(byte[] lobLocator, long newLength) throws SQLException, IOException {
        validateLobOperation(lobLocator, 32, "trim()");
        initializeLobdef();
        this.lobops = 32L;
        this.sourceLobLocator = lobLocator;
        this.lobamt = newLength;
        this.sendLobamt = true;
        doRPC();
        long newLengthfromServer = this.lobamt;
        return newLengthfromServer;
    }

    void doFreeLobPiggyback() throws SQLException, IOException {
        if (this.connection.tempLobFreeOffset > 0) {
            initializeLobdef();
            this.lobops = 524561L;
            this.sourceLobLocator = new byte[this.connection.tempLobFreeOffset];
            System.arraycopy(this.connection.tempLobsToFree, 0, this.sourceLobLocator, 0, this.connection.tempLobFreeOffset);
            setTTCCode((byte) 17);
            doPigRPC();
            resetLobPiggyback();
        }
    }

    void resetLobPiggyback() {
        this.connection.tempLobFreeOffset = 0;
        this.connection.tempLobFreeCount = 0;
    }

    void freeTemporaryLob(byte[] lobLocator) throws SQLException, IOException {
        if (PhysicalConnection.isQuasiLocator(lobLocator) || !PhysicalConnection.isTemporary(lobLocator)) {
            return;
        }
        if (this.connection.getTTCVersion() >= 4) {
            if ((lobLocator[5] & 8) == 0 || (!isAbstractLocator(lobLocator) && !isTemporaryLocator(lobLocator))) {
                throw new SQLException("ORA-22275: invalid LOB locator specified\n", "22275", 22275);
            }
            copyTemporaryLobToFree(lobLocator);
            lobLocator[4] = (byte) (lobLocator[4] & (-65));
            lobLocator[5] = (byte) (lobLocator[5] & (-9));
            lobLocator[7] = (byte) (lobLocator[7] & (-2));
            return;
        }
        initializeLobdef();
        this.lobops = 273L;
        this.sourceLobLocator = lobLocator;
        doRPC();
    }

    void copyTemporaryLobToFree(byte[] lobLocators) {
        if (this.connection.tempLobFreeOffset + lobLocators.length > this.connection.tempLobsToFree.length) {
            byte[] tempBuf = new byte[this.connection.tempLobsToFree.length * 2];
            System.arraycopy(this.connection.tempLobsToFree, 0, tempBuf, 0, this.connection.tempLobsToFree.length);
            this.connection.tempLobsToFree = tempBuf;
        }
        System.arraycopy(lobLocators, 0, this.connection.tempLobsToFree, this.connection.tempLobFreeOffset, lobLocators.length);
        this.connection.tempLobFreeOffset += lobLocators.length;
    }

    boolean openLob(byte[] lobLocator, int mode, int lobops) throws SQLException, IOException {
        boolean didOpen = false;
        if (PhysicalConnection.isQuasiLocator(lobLocator)) {
            return false;
        }
        if (isTemporaryLocator(lobLocator) || isAbstractLocator(lobLocator)) {
            if (isOpenLocator(lobLocator)) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0218).fillInStackTrace());
            }
            lobLocator[7] = (byte) (lobLocator[7] | 8);
            if (mode == 2) {
                lobLocator[7] = (byte) (lobLocator[7] | 16);
            }
            didOpen = true;
        } else {
            initializeLobdef();
            this.sourceLobLocator = lobLocator;
            this.lobops = lobops;
            this.lobamt = mode;
            this.sendLobamt = true;
            doRPC();
            if (this.lobamt != 0) {
                didOpen = true;
            }
        }
        return didOpen;
    }

    boolean closeLob(byte[] lobLocator, int lobops) throws SQLException, IOException {
        if (PhysicalConnection.isQuasiLocator(lobLocator)) {
            return true;
        }
        if (isTemporaryLocator(lobLocator) || isAbstractLocator(lobLocator)) {
            if (!isOpenLocator(lobLocator)) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0219).fillInStackTrace());
            }
            lobLocator[7] = (byte) (lobLocator[7] & (-25));
        } else {
            initializeLobdef();
            this.sourceLobLocator = lobLocator;
            this.lobops = lobops;
            doRPC();
        }
        return true;
    }

    private boolean isTemporaryLocator(byte[] lobLocator) {
        return (lobLocator[7] & 1) == 1;
    }

    private boolean isAbstractLocator(byte[] lobLocator) {
        return (lobLocator[4] & 64) == 64;
    }

    private boolean isOpenLocator(byte[] lobLocator) {
        return (lobLocator[7] & 8) == 8;
    }

    boolean isOpenLob(byte[] lobLocator, int lobops) throws SQLException, IOException {
        Boolean isOpenLocal = isOpenLobLocal(lobLocator);
        if (isOpenLocal != null) {
            return isOpenLocal.booleanValue();
        }
        initializeLobdef();
        this.sourceLobLocator = lobLocator;
        this.lobops = lobops;
        this.nullO2U = true;
        doRPC();
        return this.lobnull;
    }

    private Boolean isOpenLobLocal(byte[] lobLocator) {
        if (PhysicalConnection.isQuasiLocator(lobLocator)) {
            return false;
        }
        if (isTemporaryLocator(lobLocator) || isAbstractLocator(lobLocator)) {
            return Boolean.valueOf(isOpenLocator(lobLocator));
        }
        return null;
    }

    void initializeLobdef() {
        setTTCCode((byte) 3);
        this.sourceLobLocator = null;
        this.destinationLobLocator = null;
        this.sourceOffset = 0L;
        this.destinationOffset = 0L;
        this.destinationLength = 0;
        this.characterSet = (short) 0;
        this.lobamt = 0L;
        this.lobnull = false;
        this.lobops = 0L;
        this.lobscn = null;
        this.lobscnl = 0;
        this.inBuffer = null;
        this.outBuffer = null;
        this.nullO2U = false;
        this.sendLobamt = false;
        this.littleEndianClob = false;
        this.lobBytesRead = 0L;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        int slength = 0;
        if (this.sourceLobLocator != null) {
            slength = this.sourceLobLocator.length;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalSB4(slength);
        if (this.destinationLobLocator != null) {
            this.destinationLength = this.destinationLobLocator.length;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalSB4(this.destinationLength);
        if (this.connection.getTTCVersion() >= 3) {
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalUB4(this.sourceOffset);
        }
        if (this.connection.getTTCVersion() >= 3) {
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalUB4(this.destinationOffset);
        }
        if (this.characterSet != 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.sendLobamt && this.connection.getTTCVersion() < 3) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.nullO2U) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalUB4(this.lobops);
        if (this.lobscnl != 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalSB4(this.lobscnl);
        if (this.connection.getTTCVersion() >= 3) {
            this.meg.marshalSB8(this.sourceOffset);
            this.meg.marshalSB8(this.destinationOffset);
            if (this.sendLobamt) {
                this.meg.marshalPTR();
            } else {
                this.meg.marshalNULLPTR();
            }
            if (this.connection.getTTCVersion() >= 4) {
                this.meg.marshalNULLPTR();
                this.meg.marshalSWORD(0);
                this.meg.marshalNULLPTR();
                this.meg.marshalSWORD(0);
                this.meg.marshalNULLPTR();
                this.meg.marshalSWORD(0);
            }
        }
        if (this.sourceLobLocator != null) {
            this.meg.marshalB1Array(this.sourceLobLocator);
        }
        if (this.destinationLobLocator != null) {
            this.meg.marshalB1Array(this.destinationLobLocator);
        }
        if (this.characterSet != 0) {
            this.meg.marshalUB2(this.characterSet);
        }
        if (this.sendLobamt && this.connection.getTTCVersion() < 3) {
            this.meg.marshalUB4(this.lobamt);
        }
        if (this.lobscnl != 0) {
            for (int i = 0; i < this.lobscnl; i++) {
                this.meg.marshalUB4(this.lobscn[i]);
            }
        }
        if (this.sendLobamt && this.connection.getTTCVersion() >= 3) {
            this.meg.marshalSB8(this.lobamt);
        }
        if (this.lobops == 64) {
            marshalData();
        }
    }

    void marshalData() throws IOException {
        boolean useZeroCopyIO = this.connection.isZeroCopyIOEnabled() & ((this.sourceLobLocator[7] & Byte.MIN_VALUE) != 0);
        boolean varWidthChar = false;
        if ((this.sourceLobLocator[6] & 128) == 128) {
            varWidthChar = true;
        }
        if (this.connection.versionNumber < 10101 && varWidthChar) {
            this.lobd.marshalClobUB2_For9iDB(this.inBuffer, this.inBufferOffset, this.inBufferNumBytes);
        } else {
            this.lobd.marshalLobData(this.inBuffer, this.inBufferOffset, this.inBufferNumBytes, useZeroCopyIO);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readLOBD() throws SQLException, IOException {
        boolean useZeroCopyIO = this.connection.isZeroCopyIOEnabled() & ((this.sourceLobLocator[7] & Byte.MIN_VALUE) != 0);
        boolean varWidthChar = false;
        if ((this.sourceLobLocator[6] & 128) == 128) {
            varWidthChar = true;
        }
        if (this.connection.versionNumber < 10101 && varWidthChar) {
            this.lobBytesRead = this.lobd.unmarshalClobUB2_For9iDB(this.outBuffer, this.offsetInOutBuffer);
        } else {
            this.lobBytesRead = this.lobd.unmarshalLobData(this.outBuffer, this.offsetInOutBuffer, useZeroCopyIO);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        this.rowsProcessed = this.connection.getT4CTTIoer().getCurRowNumber();
        if (this.connection.getT4CTTIoer().getRetCode() != 1403) {
            this.connection.getT4CTTIoer().processError();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        if (this.sourceLobLocator != null) {
            if (this.lobops == 272) {
                this.meg.getNBytes(this.sourceLobLocator, 0, 2);
                byte b0 = this.sourceLobLocator[0];
                byte b1 = this.sourceLobLocator[1];
                short locatorLen = (short) ((b0 << 8) | b1);
                short extraUnusedLocatorBytes = 0;
                if (locatorLen + 2 != this.sourceLobLocator.length) {
                    if (this.sourceLobLocator.length > locatorLen + 2) {
                        extraUnusedLocatorBytes = (short) (this.sourceLobLocator.length - (locatorLen + 2));
                    }
                    this.sourceLobLocator = new byte[locatorLen + 2];
                    this.sourceLobLocator[0] = b0;
                    this.sourceLobLocator[1] = b1;
                }
                this.meg.getNBytes(this.sourceLobLocator, 2, locatorLen);
                if (extraUnusedLocatorBytes > 0) {
                    byte[] temp = new byte[extraUnusedLocatorBytes];
                    this.meg.getNBytes(temp, 0, extraUnusedLocatorBytes);
                }
            } else {
                int length = this.sourceLobLocator.length;
                this.meg.getNBytes(this.sourceLobLocator, 0, length);
            }
        }
        if (this.destinationLobLocator != null) {
            short length2 = this.meg.unmarshalSB2();
            this.destinationLobLocator = this.meg.unmarshalNBytes(length2);
        }
        if (this.characterSet != 0) {
            this.characterSet = this.meg.unmarshalSB2();
        }
        if (this.sendLobamt) {
            if (this.connection.getTTCVersion() >= 3) {
                this.lobamt = this.meg.unmarshalSB8();
            } else {
                this.lobamt = this.meg.unmarshalUB4();
            }
        }
        if (this.nullO2U) {
            short isNull = this.meg.unmarshalSB1();
            if (isNull != 0) {
                this.lobnull = true;
            }
        }
    }

    int getTemporaryLobSize() {
        try {
            if (this.connection.getVersionNumber() >= 19000) {
                return 108;
            }
            return 40;
        } catch (SQLException e) {
            return 40;
        }
    }

    void validateLobOperation(byte[] lobLocator, int lobOperation, String caller) throws SQLException {
        switch (lobOperation) {
            case 32:
            case 64:
                if (PhysicalConnection.isValueBasedLocator(lobLocator)) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0227, caller).fillInStackTrace());
                }
                if (PhysicalConnection.isReadOnly(lobLocator)) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0228, caller).fillInStackTrace());
                }
                return;
            default:
                return;
        }
    }

    static String bytesToHex(byte[] bytes) {
        char[] hexArray = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = hexArray[v & 15];
        }
        return new String(hexChars);
    }
}
