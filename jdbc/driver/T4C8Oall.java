package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayDeque;
import java.util.Arrays;
import java.util.Properties;
import java.util.Vector;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.T4CConnection;
import oracle.jdbc.internal.KeywordValue;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.xa.OracleXAResource;
import oracle.net.ns.SQLnetDef;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Oall.class */
final class T4C8Oall extends T4CTTIfun {
    private static final String CLASS_NAME;
    Vector<IOException> nonFatalIOExceptions;
    static final byte[] EMPTY_BYTES;
    static final int UOPF_PRS = 1;
    static final int UOPF_BND = 8;
    static final int UOPF_EXE = 32;
    static final int UOPF_FEX = 512;
    static final int UOPF_FCH = 64;
    static final int UOPF_CAN = 128;
    static final int UOPF_COM = 256;
    static final int UOPF_DSY = 131072;
    static final int UOPF_SIO = 1024;
    static final int UOPF_NPL = 32768;
    static final int UOPF_DFN = 16;
    static final int UOPF_NCF = 262144;
    static final int UOPF_BER = 524288;
    static final int EXE_COMMIT_ON_SUCCESS = 1;
    static final int EXE_LEAVE_CUR_MAPPED = 2;
    static final int EXE_BATCH_DML_ERRORS = 4;
    static final int EXE_SCROL_READ_ONLY = 8;
    static final int EXE_DISABLE_ROWSHIP = 16;
    static final int EXE_RESULT_CACHE = 32;
    static final int EXE_NO_RESULT_CACHE = 64;
    static final int EXE_ACET = 128;
    static final int EXE_FROM_OPIOSQ = 256;
    static final int EXE_REQ_SESSSTATE_STABLE = 512;
    static final int EXE_NODESC_METADATA = 1024;
    static final int KPUCXEXE = 32;
    static final int KPUCXFCH = 64;
    static final int KPUCXCAN = 128;
    static final int KPUCXCOM = 256;
    static final int KPUCXFEX = 512;
    static final int KPUCXNCF = 262144;
    static final int AL8EX_GET_PIDMLRC = 16384;
    static final int AL8EX_RSET_REPOSITION = 262144;
    static final int AL8EX_REQ_SESSSTATE_STABLE = 524288;
    static final int AL8KW_MAXLANG = 63;
    static final int AL8KW_TIMEZONE = 163;
    static final int AL8KW_ERR_OVLAP = 164;
    static final int AL8KW_SESSION_ID = 165;
    static final int AL8KW_SERIAL_NUM = 166;
    static final int AL8KW_TAG_FOUND = 167;
    static final int AL8KW_SCHEMA_NAME = 168;
    static final int AL8KW_SCHEMA_ID = 169;
    static final int AL8KW_ENABLED_ROLES = 170;
    static final int AL8KW_AUX_SESSSTATE = 171;
    static final int AL8KW_EDITION = 172;
    static final int AL8KW_SQL_TXLP = 173;
    static final int AL8KW_FSQL_SNTX = 174;
    static final String AL8KW_ERR_OVLAP_STR = "AL8KW_ERR_OVLAP";
    static final String AL8KW_SCHEMA_NAME_STR = "AL8KW_SCHEMA_NAME";
    static final String AL8KW_ENABLED_ROLES_STR = "AL8KW_ENABLED_ROLES";
    static final String AL8KW_EDITION_STR = "AL8KW_EDITION";
    static final String AL8KW_AUX_SESSSTATE_STR = "AL8KW_AUX_SESSSTATE";
    static final String AL8KW_SQL_TXLP_STR = "AL8KW_SQL_TXLP";
    static final String AL8KW_FSQL_SNTX_STR = "AL8KW_FSQL_SNTX";
    static final int AL8KW_OPENCURSORS = 175;
    static final String AL8KW_OPENCURSORS_STR = "AL8KW_OPENCURSORS";
    static final int AL8KW_PDBUID = 176;
    static final int AL8KW_DBID = 177;
    static final int AL8KW_GUDBID = 178;
    static final int AL8KW_DBNAME = 179;
    static final int AL8KW_SERVICE_NAME = 183;
    static final int AL8KW_MODULE = 184;
    static final String AL8KW_MODULE_STR = "AL8KW_MODULE";
    static final int AL8KW_ACTION = 185;
    static final String AL8KW_ACTION_STR = "AL8KW_ACTION";
    static final int AL8KW_CLIENT_INFO = 186;
    static final String AL8KW_CLIENT_INFO_STR = "AL8KW_CLIENT_INFO";
    static final int AL8KW_ROW_ARCHIVAL = 187;
    static final String AL8KW_ROW_ARCHIVAL_STR = "AL8KW_ROW_ARCHIVAL";
    static final int AL8KW_CONTAINER_NAME = 197;
    static final String AL8KW_CONTAINER_NAME_STR = "CONTAINER_NAME";
    static final int AL8KW_CLIENT_ID = 198;
    static final String AL8KW_CLIENT_ID_STR = "AL8KW_CLIENT_ID";
    static final int AL8KW_ENABLED_ROLE_NAMES = 199;
    static final String AL8KW_ENABLED_ROLE_NAMES_STR = "AL8KW_ENABLED_ROLE_NAMES";
    static final int AL8KW_PREFETCH_ROWS = 200;
    static final String AL8KW_PREFETCH_ROWS_STR = "AL8KW_PREFETCH_ROWS";
    static final int AL8KW_GTRID = 201;
    static final byte KPUTX_GTRID_SYNC_MODE = -64;
    static final byte KPUTX_GTRID_SYNC_SET = 64;
    static final byte KPUTX_GTRID_SYNC_UNSET = Byte.MIN_VALUE;
    static final byte KPUTX_GTRID_SYNC_REASON = 63;
    static final byte KPUTX_GTRID_SYNC_SERVER = 1;
    static final byte KPUTX_GTRID_SYNC_CLIENT = 2;
    static final byte KPUTX_GTRID_SYNC_TXEND_XA = 3;
    static final String[] NLS_KEYS;
    static final int LDIREGIDFLAG = 120;
    static final int LDIREGIDSET = 181;
    static final int LDIMAXTIMEFIELD = 60;
    static final int AL8EX_IMPL_RESULTS_CLIENT = 32768;
    long rowsProcessed;
    int numberOfDefinePositions;
    long options;
    int cursor;
    byte[] sqlStmt;
    final long[] al8i4;
    boolean plsql;
    Accessor[] definesAccessors;
    int definesLength;
    Accessor[] outBindAccessors;
    int numberOfBindPositions;
    InputStream[][] parameterStream;
    short[] bindIndicators;
    byte[] bindBytes;
    char[] bindChars;
    ByteArray bindData;
    boolean bindUseDBA;
    long[] bindDataOffsets;
    int[] bindDataLengths;
    int bindDataIndex;
    int bindIndicatorSubRange;
    byte[] tmpBindsByteArray;
    DBConversion conversion;
    byte[] ibtBindBytes;
    char[] ibtBindChars;
    short[] ibtBindIndicators;
    boolean sendBindsDefinition;
    OracleStatement oracleStatement;
    short dbCharSet;
    short NCharSet;
    T4CTTIrxd rxd;
    T4C8TTIrxh rxh;
    T4CTTIdcb dcb;
    T4CTTIimplres implres;
    OracleStatement.SqlKind typeOfStatement;
    int defCols;
    int rowsToFetch;
    boolean aFetchWasDone;
    T4CTTIoac[] oacdefBindsSent;
    T4CTTIoac[] oacdefDefines;
    int[] definedColumnSize;
    int[] definedColumnType;
    int[] definedColumnFormOfUse;
    NTFDCNRegistration registration;
    static final int AL8TXCUR = 1;
    static final int AL8TXDON = 2;
    static final int AL8TXRON = 4;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4C8Oall.class.desiredAssertionStatus();
        CLASS_NAME = T4C8Oall.class.getName();
        EMPTY_BYTES = new byte[0];
        NLS_KEYS = new String[]{"AUTH_NLS_LXCCURRENCY", "AUTH_NLS_LXCISOCURR", "AUTH_NLS_LXCNUMERICS", null, null, null, null, "AUTH_NLS_LXCDATEFM", "AUTH_NLS_LXCDATELANG", "AUTH_NLS_LXCTERRITORY", "SESSION_NLS_LXCCHARSET", "AUTH_NLS_LXCSORT", "AUTH_NLS_LXCCALENDAR", null, null, null, "AUTH_NLS_LXLAN", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "AUTH_NLS_LXNLSCOMP", null, "AUTH_NLS_LXCUNIONCUR", null, null, null, null, "AUTH_NLS_LXCTIMEFM", "AUTH_NLS_LXCSTMPFM", "AUTH_NLS_LXCTTZNFM", "AUTH_NLS_LXCSTZNFM", "SESSION_NLS_LXCNLSLENSEM", "SESSION_NLS_LXCNCHAREXCP", "SESSION_NLS_LXCNCHARIMP"};
    }

    T4C8Oall(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.nonFatalIOExceptions = null;
        this.sqlStmt = new byte[0];
        this.al8i4 = new long[13];
        this.plsql = false;
        this.bindUseDBA = false;
        this.bindDataOffsets = null;
        this.bindDataLengths = null;
        this.sendBindsDefinition = false;
        this.defCols = 0;
        this.aFetchWasDone = false;
        this.registration = null;
        setFunCode((short) 94);
        this.rxh = new T4C8TTIrxh(_connection);
        this.rxd = new T4CTTIrxd(_connection);
        this.dcb = new T4CTTIdcb(_connection);
        this.implres = new T4CTTIimplres(_connection);
    }

    void doOALL(boolean doParse, boolean doExecute, boolean doFetch, boolean doDescribe, boolean doDefine) throws Exception {
        prepareForCall(doParse, doExecute, doFetch, doDescribe, doDefine);
        try {
            doRPC();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "doOALL", "OALL8 options={0}, cursor={1}. ", null, null, Long.valueOf(this.options), Integer.valueOf(this.cursor));
            clearStateAfterCall();
            if (this.nonFatalIOExceptions != null) {
                throw handleNonFatalIOExceptionsAfterCall();
            }
        } catch (IOException | SQLException exception) {
            if (this.nonFatalIOExceptions != null) {
                exception.addSuppressed(handleNonFatalIOExceptionsAfterCall());
            }
            throw exception;
        }
    }

    private void prepareForCall(boolean doParse, boolean doExecute, boolean doFetch, boolean doDescribe, boolean doDefine) throws SQLException, IOException {
        initializeCharacterSetsBeforeCall();
        int number_of_bound_rows = getNumberOfBoundRowsBeforeCall();
        initializeSQLBeforeCall(doParse);
        requireValidBatchCommamnd(number_of_bound_rows);
        resetStateBeforeCall();
        if (doDefine && this.typeOfStatement.isSELECT()) {
            initDefinesDefinition();
        }
        initializeBindOACsBeforeCall();
        if (doParse) {
            this.sendBindsDefinition = true;
        }
        boolean isBatchExecution = number_of_bound_rows > 1;
        this.options = setOptions(doParse, doExecute, doFetch, doDefine, isBatchExecution);
        initializeI4OptionsBeforeCall(doDescribe, doFetch, number_of_bound_rows);
        initializeFunctionCodeBeforeCall();
        setExecutingSQLBeforeCall();
    }

    private final void initializeCharacterSetsBeforeCall() {
        this.dbCharSet = this.conversion.getServerCharSetId();
        this.NCharSet = this.conversion.getNCharSetId();
    }

    private final int getNumberOfBoundRowsBeforeCall() {
        int number_of_bound_rows = 0;
        if (this.bindIndicators != null) {
            number_of_bound_rows = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
        }
        return number_of_bound_rows;
    }

    private void initializeSQLBeforeCall(boolean doParse) throws SQLException {
        if (!doParse) {
            this.sqlStmt = EMPTY_BYTES;
        }
        if (this.sqlStmt == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0203).fillInStackTrace());
        }
    }

    private void requireValidBatchCommamnd(int numberOfBoundRows) throws SQLException {
        if (!this.typeOfStatement.isDML() && numberOfBoundRows > 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 80).fillInStackTrace());
        }
    }

    private void resetStateBeforeCall() {
        this.rowsProcessed = 0L;
        this.options = 0L;
        this.plsql = this.typeOfStatement.isPlsqlOrCall();
        this.sendBindsDefinition = false;
        this.aFetchWasDone = false;
        this.nonFatalIOExceptions = null;
        if (this.receiveState != 0) {
            this.receiveState = 0;
        }
        this.rxh.init();
        this.rxd.init();
        this.rxd.setNumberOfColumns(this.definesLength);
        this.connection.getT4CTTIoer().init();
    }

    private void initializeBindOACsBeforeCall() throws SQLException, IOException {
        if (this.numberOfBindPositions > 0 && this.bindIndicators != null) {
            if (this.oacdefBindsSent == null) {
                this.oacdefBindsSent = new T4CTTIoac[this.numberOfBindPositions];
            } else if (this.oacdefBindsSent.length != this.numberOfBindPositions) {
                this.oacdefBindsSent = new T4CTTIoac[this.numberOfBindPositions];
            }
            this.sendBindsDefinition = initBindsDefinition(this.oacdefBindsSent);
            return;
        }
        if (this.numberOfBindPositions == 0 && this.oacdefBindsSent != null) {
            this.oacdefBindsSent = null;
        }
    }

    private void initializeI4OptionsBeforeCall(boolean doDescribe, boolean doFetch, int numberOfBoundRows) {
        if ((this.options & 1) > 0) {
            this.al8i4[0] = 1;
        } else {
            this.al8i4[0] = 0;
        }
        if (this.plsql || this.typeOfStatement.isOTHER()) {
            this.al8i4[1] = 1;
        } else if (doDescribe) {
            if (doFetch && this.oracleStatement.isFetchStreams) {
                this.al8i4[1] = this.rowsToFetch;
            } else {
                this.al8i4[1] = 0;
            }
        } else if (this.typeOfStatement.isDML()) {
            this.al8i4[1] = numberOfBoundRows == 0 ? 1L : numberOfBoundRows;
        } else if (doFetch && !doDescribe) {
            this.al8i4[1] = this.rowsToFetch;
        } else {
            this.al8i4[1] = 0;
        }
        if (this.typeOfStatement.isSELECT()) {
            this.al8i4[7] = 1;
        } else {
            this.al8i4[7] = 0;
        }
        long scn = this.oracleStatement.inScn;
        int leastSignificantHalfScn = (int) scn;
        int mostSignificantHalfScn = (int) (scn >> 32);
        this.al8i4[5] = leastSignificantHalfScn;
        this.al8i4[6] = mostSignificantHalfScn;
        if (this.typeOfStatement.isDML()) {
            long[] jArr = this.al8i4;
            jArr[9] = jArr[9] | 16384;
            long[] jArr2 = this.al8i4;
            jArr2[9] = jArr2[9] & (-262145);
            this.al8i4[11] = 0;
        } else {
            long[] jArr3 = this.al8i4;
            jArr3[9] = jArr3[9] & (-16385);
            if (this.typeOfStatement.isSELECT() && this.oracleStatement.isSSSCursor && this.connection.replayModes.contains(T4CConnection.ReplayMode.REPLAYING)) {
                long[] jArr4 = this.al8i4;
                jArr4[9] = jArr4[9] | 262144;
                this.al8i4[11] = this.oracleStatement.sssCursorPosition;
            } else {
                long[] jArr5 = this.al8i4;
                jArr5[9] = jArr5[9] & (-262145);
                this.al8i4[11] = 0;
            }
        }
        try {
            if (this.connection.isSSSHybrid()) {
                long[] jArr6 = this.al8i4;
                jArr6[9] = jArr6[9] | 524288;
            }
        } catch (SQLException e) {
        }
        if ((this.options & 32) != 0) {
            long[] jArr7 = this.al8i4;
            jArr7[9] = jArr7[9] | 32768;
        } else {
            long[] jArr8 = this.al8i4;
            jArr8[9] = jArr8[9] & (-32769);
        }
    }

    private void initializeFunctionCodeBeforeCall() {
        if ((this.options & 64) != 0 && (this.options & 32) == 0 && (this.options & 1) == 0 && (this.options & 8) == 0 && (this.options & 16) == 0 && !this.oracleStatement.needToSendOalToFetch) {
            setFunCode((short) 5);
            return;
        }
        if (this.oracleStatement.inScn == 0 && (this.options & 32) != 0 && (this.options & 1) == 0 && (this.options & 16) == 0 && (this.options & 8) == 0 && (this.options & 32768) != 0 && (this.options & 64) != 0) {
            setFunCode((short) 78);
        } else {
            setFunCode((short) 94);
        }
    }

    private void setExecutingSQLBeforeCall() {
        if (getFunCode() == 94 || getFunCode() == 78) {
            ((T4CConnection) this.oracleStatement.connection).setExecutingRPCSQL(this.oracleStatement.sqlObject.actualSql);
        }
    }

    private void clearStateAfterCall() {
        if ((this.options & 32) != 0) {
            this.oracleStatement.inScn = 0L;
        }
        this.ibtBindIndicators = null;
        this.ibtBindChars = null;
        this.ibtBindBytes = null;
        this.tmpBindsByteArray = null;
        this.outBindAccessors = null;
        this.bindBytes = null;
        this.bindChars = null;
        this.bindIndicators = null;
        this.oracleStatement = null;
        this.parameterStream = (InputStream[][]) null;
    }

    private SQLException handleNonFatalIOExceptionsAfterCall() {
        IOException firstIOex = this.nonFatalIOExceptions.get(0);
        SQLException sqlex = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 266).fillInStackTrace();
        sqlex.initCause(firstIOex);
        return sqlex;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readBVC() throws SQLException, IOException {
        int nbOfColumnSent = this.meg.unmarshalUB2();
        this.rxd.unmarshalBVC(nbOfColumnSent);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readIOV() throws SQLException, IOException {
        T4CTTIiov iov = new T4CTTIiov(this.connection, this.rxh, this.rxd);
        iov.init();
        iov.unmarshalV10(this.numberOfBindPositions);
        if (!this.oracleStatement.isDmlReturning && !iov.isIOVectorEmpty()) {
            byte[] ioVector = iov.getIOVector();
            this.outBindAccessors = iov.processRXD(this.outBindAccessors, this.numberOfBindPositions, this.bindBytes, this.bindChars, this.bindIndicators, this.bindIndicatorSubRange, this.conversion, this.tmpBindsByteArray, ioVector, this.parameterStream, this.oracleStatement, null, null, null);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRXH() throws SQLException, IOException {
        this.rxh.init();
        this.rxh.unmarshalV10(this.rxd);
        if (this.rxh.uacBufLength > 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0004).fillInStackTrace());
        }
        if ((this.rxh.rxhflg & 8) == 8) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0222).fillInStackTrace());
        }
        if ((this.rxh.rxhflg & 16) == 16) {
            for (int i = 0; i < this.definesAccessors.length; i++) {
                if (this.definesAccessors[i].udskpos >= 0 && this.definesAccessors[i].udskpos != i) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0223).fillInStackTrace());
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processSLG() throws SQLException, IOException {
        readRXH();
        int[] oacmxlArr = new int[this.numberOfBindPositions];
        for (int i = 0; i < this.numberOfBindPositions; i++) {
            oacmxlArr[i] = this.oacdefBindsSent[i].oacmxl;
        }
        this.nonFatalIOExceptions = marshalBinds(oacmxlArr, true);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processOCSHRDKEY(byte[] buf) throws SQLException {
        this.oracleStatement.setShardingKeyRpnTokens(buf);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    boolean readRXD() throws SQLException, IOException {
        this.aFetchWasDone = true;
        if (this.oracleStatement.isDmlReturning && this.numberOfBindPositions > 0) {
            for (int col = 0; col < this.oracleStatement.numberOfBindPositions; col++) {
                try {
                    Accessor acc = this.oracleStatement.accessors[col];
                    if (acc != null) {
                        int nbOfRowsSent = (int) this.meg.unmarshalUB4();
                        this.oracleStatement.increaseCapacity(nbOfRowsSent);
                        this.oracleStatement.rowsDmlReturned += nbOfRowsSent;
                        for (int row = 0; row < nbOfRowsSent; row++) {
                            acc.unmarshalOneRow();
                            this.oracleStatement.storedRowCount++;
                        }
                    }
                } catch (IOException | SQLException e) {
                }
            }
            this.oracleStatement.returnParamsFetched = true;
            return false;
        }
        if (this.iovProcessed || (this.outBindAccessors != null && this.definesAccessors == null)) {
            if (this.rxd.unmarshal(this.outBindAccessors, this.numberOfBindPositions)) {
                return true;
            }
            return false;
        }
        if (this.rxd.unmarshal(this.definesAccessors, this.definesLength)) {
            return true;
        }
        return false;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        String qcOption;
        int al8o4l = this.meg.unmarshalUB2();
        int[] al8o4 = new int[al8o4l];
        for (int i = 0; i < al8o4l; i++) {
            al8o4[i] = (int) this.meg.unmarshalUB4();
        }
        int leastSignificantHalfScn = al8o4[0];
        int mostSignificantHalfScn = al8o4[1] & (32768 ^ (-1));
        long scn = (leastSignificantHalfScn & SQLnetDef.NSPDDLSLMAX) | ((mostSignificantHalfScn & SQLnetDef.NSPDDLSLMAX) << 32);
        if (scn != 0) {
            this.oracleStatement.connection.outScn = scn;
        }
        this.cursor = al8o4[2];
        int al8txl = this.meg.unmarshalUB2();
        if (al8txl > 0) {
            this.meg.unmarshalNBytes(al8txl);
        }
        int al8kvl = this.meg.unmarshalUB2();
        KeywordValue[] al8kv = new KeywordValue[al8kvl];
        for (int i2 = 0; i2 < al8kvl; i2++) {
            al8kv[i2] = KeywordValueI.unmarshal(this.meg);
        }
        this.connection.updateSessionProperties(al8kv);
        this.oracleStatement.dcnQueryId = -1L;
        this.oracleStatement.dcnTableName = null;
        if (this.connection.getTTCVersion() >= 4) {
            int registrationFeedbackLength = (int) this.meg.unmarshalUB4();
            byte[] registrationFeedback = this.meg.unmarshalNBytes(registrationFeedbackLength);
            if (registrationFeedbackLength > 0 && this.registration != null) {
                boolean isQC = false;
                Properties regOptions = this.registration.getRegistrationOptions();
                if (regOptions != null && (qcOption = regOptions.getProperty(oracle.jdbc.OracleConnection.DCN_QUERY_CHANGE_NOTIFICATION)) != null && qcOption.compareToIgnoreCase("true") == 0) {
                    isQC = true;
                }
                int tablesDescriptionLength = registrationFeedbackLength;
                if (isQC) {
                    tablesDescriptionLength = registrationFeedbackLength - 8;
                }
                String tablesAll = new String(registrationFeedback, 0, tablesDescriptionLength, StandardCharsets.US_ASCII);
                char[] delimitor = {0};
                String[] tables = tablesAll.split(new String(delimitor));
                this.registration.addTablesName(tables, tables.length);
                this.oracleStatement.dcnTableName = tables;
                if (isQC) {
                    int queryId2 = (registrationFeedback[registrationFeedbackLength - 1] & 255) | ((registrationFeedback[registrationFeedbackLength - 2] & 255) << 8) | ((registrationFeedback[registrationFeedbackLength - 3] & 255) << 16) | ((registrationFeedback[registrationFeedbackLength - 4] & 255) << 24);
                    int queryId1 = (registrationFeedback[registrationFeedbackLength - 5] & 255) | ((registrationFeedback[registrationFeedbackLength - 6] & 255) << 8) | ((registrationFeedback[registrationFeedbackLength - 7] & 255) << 16) | ((registrationFeedback[registrationFeedbackLength - 8] & 255) << 24);
                    long queryId = (queryId1 & SQLnetDef.NSPDDLSLMAX) | ((queryId2 & SQLnetDef.NSPDDLSLMAX) << 32);
                    this.oracleStatement.dcnQueryId = queryId;
                }
            }
        }
        if (this.connection.getTTCVersion() >= 7 && this.typeOfStatement.isDML()) {
            int al8pidmlrcl = (int) this.meg.unmarshalUB4();
            long[] al8pidmlrc = new long[al8pidmlrcl];
            for (int i3 = 0; i3 < al8pidmlrcl; i3++) {
                al8pidmlrc[i3] = this.meg.unmarshalSB8();
            }
            this.oracleStatement.batchRowsUpdatedArray = al8pidmlrc;
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readDCB() throws SQLException, IOException {
        this.dcb.init(this.oracleStatement, 0);
        this.definesAccessors = this.dcb.receive(this.definesAccessors);
        this.numberOfDefinePositions = this.dcb.numuds;
        this.definesLength = this.numberOfDefinePositions;
        this.rxd.setNumberOfColumns(this.numberOfDefinePositions);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRSH() throws SQLException, IOException {
        long queryId = this.meg.unmarshalSB8();
        this.connection.kpdqidcscn.unmarshal();
        this.connection.kpdqidcscn.getSCN();
        this.oracleStatement.setQueryId(queryId);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readIMPLRES() throws SQLException, IOException {
        this.implres.init(this.oracleStatement);
        int rsCount = (int) this.meg.unmarshalUB4();
        this.oracleStatement.implicitResultSetStatements = new ArrayDeque<>(rsCount);
        while (rsCount != 0) {
            this.implres.readImplicitResultSet();
            rsCount--;
        }
        this.oracleStatement.implicitResultSetIterator = this.oracleStatement.implicitResultSetStatements.iterator();
    }

    private boolean isORA1403Ignored() {
        if (this.typeOfStatement.isSELECT()) {
            return true;
        }
        return false;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void unmarshalError() throws SQLException, IOException {
        if (isORA1403Ignored()) {
            this.connection.getT4CTTIoer().unmarshal(true);
        } else {
            super.unmarshalError();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        T4CTTIoer11 oer = this.connection.getT4CTTIoer();
        this.cursor = oer.currCursorID;
        this.rowsProcessed = oer.getCurRowNumber();
        this.oracleStatement.isAllFetched |= oer.retCode == 1403;
        if (this.typeOfStatement.isSELECT() && oer.retCode == 1403) {
            this.aFetchWasDone = true;
        }
        if (oer.retCode != 1403 || (oer.retCode == 1403 && !isORA1403Ignored())) {
            if (this.oracleStatement.connection.checksumMode.needToCalculateFetchChecksum() && oer.retCode != 0) {
                long localCheckSum = oer.updateChecksum(this.oracleStatement.checkSum);
                this.oracleStatement.checkSum = localCheckSum;
            }
            oer.processError(this.oracleStatement);
            if (this.oracleStatement.connection.enableSSSCursor) {
                this.oracleStatement.sssCursorChecksum = ((T4CTTIoer) oer).oerchksm;
            }
        }
    }

    int getCursorId() {
        return this.cursor;
    }

    void continueReadRow(int start, OracleStatement s) throws SQLException, IOException {
        try {
            this.oracleStatement = s;
            this.receiveState = 2;
            if (this.rxd.unmarshal(this.definesAccessors, start, this.definesLength)) {
                this.receiveState = 3;
            } else {
                resumeReceive();
            }
        } finally {
            this.oracleStatement = null;
        }
    }

    long getNumRows() {
        long rows = 0;
        if (this.typeOfStatement == null) {
            return 0L;
        }
        if (this.receiveState == 3) {
            rows = -2;
        } else {
            switch (this.typeOfStatement) {
                case DELETE:
                case INSERT:
                case MERGE:
                case UPDATE:
                case ALTER_SESSION:
                case OTHER:
                case PLSQL_BLOCK:
                case CALL_BLOCK:
                    rows = this.rowsProcessed;
                    break;
                case SELECT_FOR_UPDATE:
                case SELECT:
                    if (!$assertionsDisabled && this.definesAccessors != null && this.definesAccessors[0] == null) {
                        throw new AssertionError("definesAccessors is not null but definesAccessors[0] is null");
                    }
                    rows = (this.definesAccessors == null || this.definesLength <= 0 || this.definesAccessors[0] == null) ? 0L : this.definesAccessors[0].lastRowProcessed;
                    break;
                    break;
            }
        }
        return rows;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (getFunCode() == 5) {
            this.meg.marshalSWORD(this.cursor);
            this.meg.marshalSWORD((int) this.al8i4[1]);
        } else if (getFunCode() == 78) {
            this.meg.marshalSWORD(this.cursor);
            this.meg.marshalSWORD((int) this.al8i4[1]);
            this.meg.marshalSWORD(96);
            int exeflg = 0;
            if ((this.options & 256) == 256) {
                exeflg = 0 | 1;
            }
            try {
                if (this.connection.isSSSHybrid()) {
                    exeflg |= 512;
                }
            } catch (SQLException e) {
            }
            this.meg.marshalSWORD(exeflg);
            int[] oacmxlArr = new int[this.numberOfBindPositions];
            for (int i = 0; i < this.numberOfBindPositions; i++) {
                oacmxlArr[i] = this.oacdefBindsSent[i].oacmxl;
            }
            if (this.numberOfBindPositions > 0 && this.bindIndicators != null) {
                marshalBinds(oacmxlArr, false);
            }
        } else {
            if (this.oracleStatement.needToSendOalToFetch) {
                this.oracleStatement.needToSendOalToFetch = false;
            }
            marshalPisdef();
            this.meg.marshalCHR(this.sqlStmt);
            this.meg.marshalUB4Array(this.al8i4);
            int[] oacmxlArr2 = new int[this.numberOfBindPositions];
            for (int i2 = 0; i2 < this.numberOfBindPositions; i2++) {
                oacmxlArr2[i2] = this.oacdefBindsSent[i2].oacmxl;
            }
            if ((this.options & 8) != 0 && this.numberOfBindPositions > 0 && this.bindIndicators != null && this.sendBindsDefinition) {
                marshalBindsTypes(this.oacdefBindsSent);
            }
            if (this.connection.getTTCVersion() >= 2 && (this.options & 16) != 0) {
                for (int i3 = 0; i3 < this.defCols; i3++) {
                    this.oacdefDefines[i3].marshal();
                }
            }
            if ((this.options & 32) != 0 && this.numberOfBindPositions > 0 && this.bindIndicators != null) {
                this.nonFatalIOExceptions = marshalBinds(oacmxlArr2, false);
            }
        }
        debugp(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "cursor={0}, sendBindsDefinition={1}, bind positions={2}, al8i4={3}. ", (String) null, (Throwable) null, () -> {
            return new Object[]{Integer.valueOf(this.cursor), Boolean.valueOf(this.sendBindsDefinition), Integer.valueOf(this.numberOfBindPositions), Arrays.toString(this.al8i4)};
        });
    }

    void marshalPisdef() throws IOException {
        this.meg.marshalUB4(this.options);
        this.meg.marshalSWORD(this.cursor);
        if (this.sqlStmt.length == 0) {
            this.meg.marshalNULLPTR();
        } else {
            this.meg.marshalPTR();
        }
        this.meg.marshalSWORD(this.sqlStmt.length);
        if (this.al8i4.length == 0) {
            this.meg.marshalNULLPTR();
        } else {
            this.meg.marshalPTR();
        }
        this.meg.marshalSWORD(this.al8i4.length);
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        if ((this.options & 64) == 0 && (this.options & 32) != 0 && (this.options & 1) != 0 && this.typeOfStatement.isSELECT()) {
            this.meg.marshalUB4(Long.MAX_VALUE);
            this.meg.marshalUB4(this.rowsToFetch);
        } else {
            this.meg.marshalUB4(0L);
            this.meg.marshalUB4(0L);
        }
        if (!this.typeOfStatement.isPlsqlOrCall()) {
            this.meg.marshalUB4(2147483647L);
        } else {
            this.meg.marshalUB4(32760L);
        }
        if ((this.options & 8) != 0 && this.numberOfBindPositions > 0 && this.sendBindsDefinition) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.numberOfBindPositions);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        if (this.connection.getTTCVersion() >= 2) {
            if (this.defCols > 0 && (this.options & 16) != 0) {
                this.meg.marshalPTR();
                this.meg.marshalSWORD(this.defCols);
            } else {
                this.meg.marshalNULLPTR();
                this.meg.marshalSWORD(0);
            }
        }
        if (this.connection.getTTCVersion() >= 4) {
            int regid_lb = 0;
            int regid_mb = 0;
            if (this.registration != null) {
                long regid = this.registration.getRegId();
                regid_lb = (int) (regid & (-1));
                regid_mb = (int) ((regid & (-4294967296L)) >> 32);
            }
            this.meg.marshalUB4(regid_lb);
            this.meg.marshalNULLPTR();
            this.meg.marshalPTR();
            if (this.connection.getTTCVersion() >= 5) {
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
                this.meg.marshalUB4(regid_mb);
                if (this.connection.getTTCVersion() >= 7) {
                    if (this.typeOfStatement.isDML()) {
                        this.meg.marshalPTR();
                        this.meg.marshalUB4(this.oracleStatement.currentRank);
                        this.meg.marshalPTR();
                    } else {
                        this.meg.marshalNULLPTR();
                        this.meg.marshalUB4(0L);
                        this.meg.marshalNULLPTR();
                    }
                    if (this.connection.getTTCVersion() >= 8) {
                        this.meg.marshalNULLPTR();
                        this.meg.marshalUB4(0L);
                        this.meg.marshalNULLPTR();
                        this.meg.marshalUB4(0L);
                        this.meg.marshalNULLPTR();
                        if (this.connection.getTTCVersion() >= 9) {
                            this.meg.marshalNULLPTR();
                            this.meg.marshalUB4(0L);
                        }
                    }
                }
            }
        }
    }

    boolean initBindsDefinition(T4CTTIoac[] oacdefArr) throws SQLException, IOException {
        short sqlType;
        int oacmal;
        boolean needToUpdateDefinition = false;
        short[] l_bindIndicators = this.bindIndicators;
        int _oacmxl = 0;
        int nbOfIbt = 0;
        for (int P = 0; P < this.numberOfBindPositions; P++) {
            int subRangeOffset = this.bindIndicatorSubRange + 5 + (10 * P);
            short formOfUse = l_bindIndicators[subRangeOffset + 9];
            int _oactype = l_bindIndicators[subRangeOffset + 0] & 65535;
            T4CTTIoac oac = oacdefArr[P];
            boolean isReusableType = false;
            if (oac == null) {
                oac = new T4CTTIoac(this.connection);
                oacdefArr[P] = oac;
            } else if (oac.getRequestedType() == _oactype) {
                isReusableType = true;
            }
            switch (_oactype) {
                case 8:
                case 24:
                    if (this.plsql) {
                        _oacmxl = 32760;
                    } else {
                        _oacmxl = Integer.MAX_VALUE;
                    }
                    if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setFormOfUse(formOfUse);
                        oac.setCharset(formOfUse == 2 ? this.NCharSet : this.dbCharSet);
                        break;
                    }
                    break;
                case 109:
                case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                    OracleTypeADT adt = null;
                    if (this.outBindAccessors != null && this.outBindAccessors[P] != null) {
                        if (this.outBindAccessors[P].internalOtype != null) {
                            _oacmxl = _oactype == 109 ? 11 : 4000;
                            adt = (OracleTypeADT) ((TypeAccessor) this.outBindAccessors[P]).internalOtype;
                        }
                    } else if ((this.oracleStatement instanceof OraclePreparedStatement) && ((OraclePreparedStatement) this.oracleStatement).binders != null && ((OraclePreparedStatement) this.oracleStatement).binders[this.oracleStatement.firstRowInBatch] != null) {
                        _oacmxl = _oactype == 109 ? 11 : 4000;
                        Binder binder = ((OraclePreparedStatement) this.oracleStatement).binders[this.oracleStatement.firstRowInBatch][P];
                        if (binder == null && ((OraclePreparedStatement) this.oracleStatement).lastBinders != null) {
                            binder = ((OraclePreparedStatement) this.oracleStatement).lastBinders[P];
                        }
                        adt = ((OraclePreparedStatement) this.oracleStatement).getOtype(binder);
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 89, "Binding NAMED_TYPE but no type defined").fillInStackTrace());
                    }
                    if (!isReusableType || adt == null || !Arrays.equals(oacdefArr[P].oactoid, adt.getTOID()) || oacdefArr[P].oacmxl < _oacmxl) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setADT(adt);
                        break;
                    }
                    break;
                case 180:
                    if (this.bindUseDBA) {
                        _oacmxl = 11;
                    } else {
                        _oacmxl = l_bindIndicators[subRangeOffset + 1] & 65535;
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initBindsDefinition", "TIMESTAMP case _oacmxl={0}. ", (String) null, (String) null, (Object) Integer.valueOf(_oacmxl));
                    short scale = -1;
                    Binder binder2 = ((OraclePreparedStatement) this.oracleStatement).currentRowBinders[P];
                    if (binder2 == null) {
                        binder2 = ((OraclePreparedStatement) this.oracleStatement).binders[this.oracleStatement.firstRowInBatch][P];
                        if (binder2 == null && ((OraclePreparedStatement) this.oracleStatement).lastBinders != null) {
                            binder2 = ((OraclePreparedStatement) this.oracleStatement).lastBinders[P];
                        }
                    }
                    if (binder2 != null) {
                        scale = ((OraclePreparedStatement) this.oracleStatement).getScale(binder2);
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initBindsDefinition", "TIMESTAMP case scale={0}. ", (String) null, (String) null, (Object) Short.valueOf(scale));
                    if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || scale == -1 || oacdefArr[P].oacscl != scale) {
                        boolean wasScaleSet = true;
                        if (scale == -1) {
                            wasScaleSet = false;
                            scale = 9;
                        }
                        int number_of_bound_rows = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
                        if (number_of_bound_rows == 1) {
                            int valueLengthOffset = ((l_bindIndicators[subRangeOffset + 7] & 65535) << 16) + (l_bindIndicators[subRangeOffset + 8] & 65535);
                            short s = l_bindIndicators[valueLengthOffset];
                            int nullOffset = ((this.bindIndicators[subRangeOffset + 5] & 65535) << 16) + (this.bindIndicators[subRangeOffset + 6] & 65535);
                            boolean isValueNull = l_bindIndicators[nullOffset] == -1;
                            if ((isValueNull || s == 7) && (!wasScaleSet || scale == 0)) {
                                scale = 0;
                            } else if (s == 11) {
                                scale = 9;
                            }
                        }
                        if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oacscl < scale) {
                            needToUpdateDefinition = true;
                            oac.init((short) _oactype, _oacmxl);
                            oac.addFlg2(134217728L);
                            oac.setTimestampFractionalSecondsPrecision(scale);
                            break;
                        }
                    }
                    break;
                case 182:
                    _oacmxl = l_bindIndicators[subRangeOffset + 1] & 65535;
                    if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setFormOfUse(formOfUse);
                        oac.setCharset(formOfUse == 2 ? this.NCharSet : this.dbCharSet);
                        oac.setPrecision((short) 9);
                        oac.addFlg2(134217728L);
                        break;
                    }
                    break;
                case 183:
                    _oacmxl = l_bindIndicators[subRangeOffset + 1] & 65535;
                    if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setFormOfUse(formOfUse);
                        oac.setCharset(formOfUse == 2 ? this.NCharSet : this.dbCharSet);
                        oac.setPrecision((short) 9);
                        oac.addFlg2(134217728L);
                        oac.setScale((short) 9);
                        break;
                    }
                    break;
                case 994:
                    int[] returnParamMetaLocal = this.oracleStatement.returnParamMeta;
                    _oactype = returnParamMetaLocal[3 + (P * 4) + 0];
                    _oacmxl = returnParamMetaLocal[3 + (P * 4) + 2];
                    short formOfUse2 = (short) returnParamMetaLocal[3 + (P * 4) + 3];
                    if (_oactype == 109 || _oactype == 111) {
                        TypeAccessor typeAccessor = (TypeAccessor) this.oracleStatement.accessors[P];
                        _oacmxl = _oactype == 109 ? 11 : 4000;
                        OracleTypeADT adt2 = (OracleTypeADT) typeAccessor.internalOtype;
                        if (!isReusableType || !Arrays.equals(oacdefArr[P].oactoid, adt2.getTOID()) || oacdefArr[P].oacmxl < _oacmxl) {
                            needToUpdateDefinition = true;
                            oac.init((short) _oactype, _oacmxl);
                            oac.setADT(adt2);
                            break;
                        }
                    } else if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse2) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setFormOfUse(formOfUse2);
                        oac.setCharset(formOfUse2 == 2 ? this.NCharSet : this.dbCharSet);
                        if (_oactype == 119) {
                            oac.setMxlc(OracleXAResource.TMSUSPEND);
                            oac.addFlg2(34393292800L);
                            break;
                        } else if (_oactype == 127) {
                            oac.setMxlc(524308);
                            oac.addFlg2(34393292800L);
                            break;
                        }
                    }
                    break;
                case CharacterSet.JA16TSTSET_CHARSET /* 998 */:
                    if (this.outBindAccessors != null && this.outBindAccessors[P] != null) {
                        PlsqlIbtBindInfo ibtInfo = this.outBindAccessors[P].plsqlIndexTableBindInfo();
                        sqlType = (short) ibtInfo.element_internal_type;
                        _oacmxl = ibtInfo.elemMaxLen;
                        oacmal = ibtInfo.maxLen;
                    } else if (this.ibtBindIndicators[6 + (nbOfIbt * 8)] != 0) {
                        sqlType = this.ibtBindIndicators[6 + (nbOfIbt * 8)];
                        oacmal = ((this.ibtBindIndicators[(6 + (nbOfIbt * 8)) + 2] & 65535) << 16) | (this.ibtBindIndicators[6 + (nbOfIbt * 8) + 3] & 65535);
                        _oacmxl = this.ibtBindIndicators[6 + (nbOfIbt * 8) + 1] * this.conversion.sMaxCharSize;
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 89, "Binding PLSQL index-by table but no type defined").fillInStackTrace());
                    }
                    nbOfIbt++;
                    if (oacdefArr[P].requestedtype != sqlType || oacdefArr[P].oacmxl != _oacmxl || oacdefArr[P].oacmal < oacmal) {
                        needToUpdateDefinition = true;
                        oac.init(sqlType, _oacmxl);
                        oac.setMal(oacmal);
                        oac.addFlg((short) 64);
                        break;
                    }
                    break;
                default:
                    _oacmxl = l_bindIndicators[subRangeOffset + 1] & 65535;
                    if (_oacmxl == 0) {
                        _oacmxl = l_bindIndicators[subRangeOffset + 2] & 65535;
                        if (_oactype == 996) {
                            _oacmxl *= 2;
                        } else if (_oacmxl > 1) {
                            _oacmxl--;
                        }
                        if (formOfUse == 2) {
                            _oacmxl *= this.conversion.maxNCharSize;
                        }
                        if (this.typeOfStatement == OracleStatement.SqlKind.PLSQL_BLOCK || (this.connection.versionNumber >= 10200 && this.typeOfStatement == OracleStatement.SqlKind.CALL_BLOCK)) {
                            if (_oacmxl == 0) {
                                _oacmxl = this.connection.maxVcsBytesPlsql;
                            } else {
                                _oacmxl *= this.conversion.sMaxCharSize;
                            }
                        } else if (this.typeOfStatement == OracleStatement.SqlKind.CALL_BLOCK) {
                            if (_oacmxl < 4001) {
                                _oacmxl = 4001;
                            }
                        } else if (formOfUse != 2) {
                            int t4MaxLength = this.connection.maxVarcharLength;
                            if (((T4CConnection) this.oracleStatement.connection).retainV9BindBehavior && _oacmxl <= t4MaxLength) {
                                _oacmxl = Math.min(_oacmxl * this.conversion.sMaxCharSize, t4MaxLength);
                            } else {
                                _oacmxl *= this.conversion.sMaxCharSize;
                            }
                        }
                        if (_oacmxl == 0 && this.bindUseDBA && this.bindDataLengths[P] > 0) {
                            _oacmxl = this.bindDataLengths[P];
                        }
                        if (_oacmxl == 0) {
                            _oacmxl = 32;
                        }
                    } else if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse) {
                        needToUpdateDefinition = true;
                    }
                    if (!isReusableType || oacdefArr[P].oacmxl < _oacmxl || oacdefArr[P].oaccsfrm != formOfUse) {
                        needToUpdateDefinition = true;
                        oac.init((short) _oactype, _oacmxl);
                        oac.setFormOfUse(formOfUse);
                        oac.setCharset(formOfUse == 2 ? this.NCharSet : this.dbCharSet);
                        break;
                    }
                    break;
            }
            if (this.oracleStatement.parameterMaxLength != null && this.oracleStatement.parameterMaxLength[P] > _oacmxl) {
                oac.init((short) _oactype, this.oracleStatement.parameterMaxLength[P]);
                needToUpdateDefinition = true;
            }
        }
        if (this.oracleStatement.parameterMaxLength != null) {
            this.oracleStatement.parameterMaxLength = null;
        }
        if (needToUpdateDefinition) {
            this.oracleStatement.nbPostPonedColumns[0] = 0;
        }
        return needToUpdateDefinition;
    }

    void initDefinesDefinition() throws SQLException {
        this.defCols = 0;
        for (int i = 0; i < this.definedColumnType.length && this.definedColumnType[i] != 0; i++) {
            this.defCols++;
        }
        this.oacdefDefines = new T4CTTIoac[this.defCols];
        for (int i2 = 0; i2 < this.oacdefDefines.length; i2++) {
            this.oacdefDefines[i2] = new T4CTTIoac(this.connection);
            short internalType = (short) this.oracleStatement.getInternalType(this.definedColumnType[i2]);
            int maxLength = Integer.MAX_VALUE;
            long flg2 = 0;
            int oacmxlc = 0;
            short formOfUse = 1;
            if (this.definedColumnFormOfUse != null && this.definedColumnFormOfUse.length > i2 && this.definedColumnFormOfUse[i2] == 2) {
                formOfUse = 2;
            }
            if (internalType == 8) {
                internalType = 1;
            } else if (internalType == 24) {
                internalType = 23;
            } else if (internalType == 1 || internalType == 96) {
                internalType = 1;
                maxLength = this.connection.maxVarcharLength * this.conversion.sMaxCharSize;
                if (this.definedColumnSize != null && this.definedColumnSize.length > i2 && this.definedColumnSize[i2] > 0) {
                    maxLength = this.definedColumnSize[i2] * this.conversion.sMaxCharSize;
                }
            } else if (this.connection.useLobPrefetch && (internalType == 113 || internalType == 112 || internalType == 114 || internalType == 119 || internalType == 127)) {
                maxLength = 4000;
                if (this.definedColumnSize != null && this.definedColumnSize.length > i2 && this.definedColumnSize[i2] >= 0) {
                    flg2 = 33554432;
                    oacmxlc = this.definedColumnSize[i2];
                }
                if (this.connection.sendAllDataForValueLobs || internalType == 127) {
                    flg2 |= 34359738368L;
                }
            } else if (internalType == 23) {
                maxLength = this.connection.maxRawLength;
            }
            this.oacdefDefines[i2].init(internalType, maxLength);
            this.oacdefDefines[i2].addFlg2(flg2);
            this.oacdefDefines[i2].setMxlc(oacmxlc);
            this.oacdefDefines[i2].setFormOfUse(formOfUse);
            this.oacdefDefines[i2].setCharset(formOfUse == 2 ? this.NCharSet : this.dbCharSet);
        }
    }

    void marshalBindsTypes(T4CTTIoac[] oac) throws IOException {
        if (oac == null) {
            return;
        }
        for (T4CTTIoac t4CTTIoac : oac) {
            t4CTTIoac.marshal();
        }
    }

    private Vector<IOException> marshalBinds(int[] oacmxlArr, boolean processingSLG) throws IOException {
        int rowId;
        boolean sendFirstPostPonedColumnOnly;
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalBinds", "processingSLG={0}. ", (String) null, (String) null, (Object) Boolean.valueOf(processingSLG));
        Vector<IOException> ioExceptionsFromUserStream = null;
        int number_of_bound_rows = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
        if (processingSLG) {
            rowId = this.rxh.iterNum;
            sendFirstPostPonedColumnOnly = true;
        } else {
            rowId = 0;
            sendFirstPostPonedColumnOnly = false;
        }
        while (rowId < number_of_bound_rows) {
            if (!convertBindRow(rowId)) {
                return ioExceptionsFromUserStream;
            }
            int parameterIndex = this.oracleStatement.firstRowInBatch + rowId;
            InputStream[] streamParameters = null;
            if (this.parameterStream != null) {
                streamParameters = this.parameterStream[parameterIndex];
            }
            Vector<IOException> ioExceptionsFromUserStreamForThisRow = this.rxd.marshal(this.bindBytes, this.bindChars, this.bindIndicators, this.bindIndicatorSubRange, this.tmpBindsByteArray, this.conversion, streamParameters, this.ibtBindBytes, this.ibtBindChars, this.ibtBindIndicators, null, 0, oacmxlArr, this.plsql, this.oracleStatement.returnParamMeta, this.oracleStatement.nbPostPonedColumns, this.oracleStatement.indexOfPostPonedColumn, sendFirstPostPonedColumnOnly, this.bindData, this.bindDataOffsets, this.bindDataLengths, this.bindUseDBA);
            sendFirstPostPonedColumnOnly = false;
            if (ioExceptionsFromUserStreamForThisRow != null) {
                if (ioExceptionsFromUserStream == null) {
                    ioExceptionsFromUserStream = new Vector<>();
                }
                ioExceptionsFromUserStream.addAll(ioExceptionsFromUserStreamForThisRow);
            }
            rowId++;
        }
        return ioExceptionsFromUserStream;
    }

    private boolean convertBindRow(int rowId) throws IOException {
        try {
            this.oracleStatement.convertBindRow(rowId);
            return true;
        } catch (SQLException sqlException) {
            try {
                this.connection.cancelOperationOnServer(false);
                setMarshallingException(sqlException);
                return false;
            } catch (SQLException cancelException) {
                IOException ioException = new IOException("Failed to cancel operation after a bind conversion failure", cancelException);
                ioException.addSuppressed(sqlException);
                throw ioException;
            }
        }
    }

    long setOptions(boolean doParse, boolean doExecute, boolean doFetch, boolean doDefine, boolean isBatchExecution) throws SQLException {
        long options;
        long options2 = 0;
        if (doParse && !doExecute && !doFetch) {
            options = 0 | 1;
        } else if (doParse && doExecute && !doFetch) {
            options = 32801;
            if ((this.typeOfStatement == OracleStatement.SqlKind.SELECT || this.typeOfStatement == OracleStatement.SqlKind.SELECT_FOR_UPDATE) && this.oracleStatement.connection.autocommit && this.oracleStatement.connection.commitSelectOnAutocommit) {
                options = 32801 | 256;
            }
        } else if (doExecute && doFetch) {
            if (doParse) {
                options2 = 0 | 1;
            }
            switch (this.typeOfStatement) {
                case DELETE:
                case INSERT:
                case MERGE:
                case UPDATE:
                case ALTER_SESSION:
                case OTHER:
                    if (this.oracleStatement.isDmlReturning) {
                        options = options2 | 1056 | (this.oracleStatement.connection.autocommit ? 256 : 0);
                        break;
                    } else {
                        options = options2 | 32800 | (this.oracleStatement.connection.autocommit ? 256 : 0);
                        break;
                    }
                case PLSQL_BLOCK:
                case CALL_BLOCK:
                    if (this.numberOfBindPositions > 0) {
                        options = options2 | 1056 | (this.oracleStatement.connection.autocommit ? 256 : 0);
                        if (this.sendBindsDefinition) {
                            options |= 8;
                            break;
                        }
                    } else {
                        options = options2 | 32 | (this.oracleStatement.connection.autocommit ? 256 : 0);
                        break;
                    }
                    break;
                case SELECT_FOR_UPDATE:
                case SELECT:
                    options = options2 | 32864;
                    break;
                default:
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0204).fillInStackTrace());
            }
        } else if (!doParse && !doExecute && doFetch) {
            options = 32832;
        } else {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0204).fillInStackTrace());
        }
        if (!this.typeOfStatement.isPlsqlOrCall()) {
            if ((doParse || doExecute || !doFetch) && this.numberOfBindPositions > 0 && this.sendBindsDefinition) {
                options |= 8;
            }
            if (this.connection.versionNumber >= 9000 && doDefine && this.typeOfStatement.isSELECT()) {
                options |= 16;
            }
        }
        if (isBatchExecution && this.connection.continueBatchOnError) {
            options |= 524288;
        }
        return options & (-1);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    protected OracleStatement getStatement() {
        return this.oracleStatement;
    }
}
