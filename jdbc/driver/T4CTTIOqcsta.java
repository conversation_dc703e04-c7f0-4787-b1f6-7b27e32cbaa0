package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIOqcsta.class */
class T4CTTIOqcsta extends T4CTTIfun {
    final long kpdqcstaregid;
    long kpdqcstabksz;
    long kpdqcstamxbks;
    long kpdqcstabks;
    long kpdqcstahbks;
    long kpdqcstasrs;
    long kpdqcstafrs;
    long kpdqcstaqm;
    long kpdqcstainvs;
    long kpdqcstapinv;
    long kpdqcstapval;

    T4CTTIOqcsta(T4CConnection _conn, long regid) {
        super(_conn, (byte) 17);
        setFunCode((short) 167);
        this.kpdqcstaregid = regid;
    }

    void doOQCSTA(T4CConnection conn, T4CMAREngine mare, long blockSize, long noBlocks, long blocksAllocated, long hashBuckets, long resultSetCreated, long invalidatedBeforeEOF, long cacheMatches, long invalidations, long invalidPurgeCount, long validPurgeCount) throws IOException {
        this.connection = conn;
        this.meg = mare;
        this.kpdqcstabksz = blockSize;
        this.kpdqcstamxbks = noBlocks;
        this.kpdqcstabks = blocksAllocated;
        this.kpdqcstahbks = hashBuckets;
        this.kpdqcstasrs = resultSetCreated;
        this.kpdqcstafrs = invalidatedBeforeEOF;
        this.kpdqcstaqm = cacheMatches;
        this.kpdqcstainvs = invalidations;
        this.kpdqcstapinv = invalidPurgeCount;
        this.kpdqcstapval = validPurgeCount;
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalSB8(this.kpdqcstaregid);
        this.meg.marshalSB8(this.kpdqcstabksz);
        this.meg.marshalSB8(this.kpdqcstamxbks);
        this.meg.marshalSB8(this.kpdqcstabks);
        this.meg.marshalSB8(this.kpdqcstahbks);
        this.meg.marshalSB8(this.kpdqcstasrs);
        this.meg.marshalSB8(this.kpdqcstafrs);
        this.meg.marshalSB8(this.kpdqcstaqm);
        this.meg.marshalSB8(this.kpdqcstainvs);
        this.meg.marshalSB8(this.kpdqcstapinv);
        this.meg.marshalSB8(this.kpdqcstapval);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
