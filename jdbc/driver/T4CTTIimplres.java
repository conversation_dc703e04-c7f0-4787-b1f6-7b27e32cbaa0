package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIimplres.class */
class T4CTTIimplres extends T4CTTIMsg {
    OracleStatement statement;

    T4CTTIimplres(T4CConnection _conn) {
        super(_conn, (byte) 27);
    }

    void init(OracleStatement oracleStatement) {
        this.statement = oracleStatement;
    }

    void readImplicitResultSet() throws SQLException, IOException {
        OracleStatement parent = this.statement;
        OracleStatement newstmt = this.connection.createImplicitResultSetStatement(parent);
        T4CTTIdcb dcb = new T4CTTIdcb((T4CConnection) newstmt.connection);
        dcb.init(newstmt, 0);
        newstmt.accessors = dcb.receive(newstmt.accessors);
        newstmt.numberOfDefinePositions = dcb.numuds;
        newstmt.needToSendOalToFetch = true;
        int cursorId = (int) this.meg.unmarshalUB4();
        newstmt.setCursorId(cursorId);
        this.statement = parent;
    }
}
