package oracle.jdbc.driver;

import java.io.IOException;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.logging.Level;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIpro.class */
class T4C8TTIpro extends T4CTTIMsg {
    private static final String CLASS_NAME;
    short svrCharSet;
    short svrCharSetElem;
    byte svrFlags;
    byte[] svrPortDescription;
    short proSvrVer;
    short oVersion;
    boolean svrInfoAvailable;
    byte[] proCliVerTTC8;
    byte[] proCliStrTTC8;
    short NCHAR_CHARSET;
    byte[] runtimeCapabilities;
    byte[] compileTimeCapabilities;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4C8TTIpro.class.desiredAssertionStatus();
        CLASS_NAME = T4C8TTIpro.class.getName();
    }

    T4C8TTIpro(T4CConnection _conn) throws SQLException, IOException {
        super(_conn, (byte) 1);
        this.oVersion = (short) -1;
        this.svrInfoAvailable = false;
        this.proCliVerTTC8 = new byte[]{6, 5, 4, 3, 2, 1, 0};
        this.proCliStrTTC8 = new byte[]{74, 97, 118, 97, 95, 84, 84, 67, 45, 56, 46, 50, 46, 48, 0};
        this.NCHAR_CHARSET = (short) 0;
        this.runtimeCapabilities = null;
        this.compileTimeCapabilities = null;
    }

    public byte[] getSvrPortDescription() {
        return this.svrPortDescription;
    }

    public void setProtocolAndOracleVersions(short protocolVersion) throws SQLException {
        switch (protocolVersion) {
            case 4:
                this.oVersion = (short) 7230;
                break;
            case 5:
                this.oVersion = (short) 8030;
                break;
            case 6:
                this.oVersion = (short) 8100;
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0217).fillInStackTrace());
        }
        this.proSvrVer = protocolVersion;
    }

    byte[] receivePacket() throws SQLException, IOException {
        if (this.meg.unmarshalUB1() != 1) {
            this.connection.net().getSessionAttributes().ano.checkForAnoNegotiationFailure();
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
        return receivePayload();
    }

    byte[] receivePayload() throws SQLException, IOException {
        this.meg.proSvrVer = this.meg.unmarshalUB1();
        setProtocolAndOracleVersions(this.meg.proSvrVer);
        this.meg.unmarshalUB1();
        this.svrPortDescription = this.meg.unmarshalTEXT(50);
        this.svrCharSet = (short) this.meg.unmarshalNativeUB2(true);
        this.svrFlags = (byte) this.meg.unmarshalUB1();
        short sUnmarshalNativeUB2 = (short) this.meg.unmarshalNativeUB2(true);
        this.svrCharSetElem = sUnmarshalNativeUB2;
        if (sUnmarshalNativeUB2 > 0) {
            this.meg.unmarshalNBytes(this.svrCharSetElem * 5);
        }
        this.svrInfoAvailable = true;
        if (this.proSvrVer < 5) {
            return null;
        }
        int fdoLength = this.meg.unmarshalNativeUB2(false);
        byte[] fdo = this.meg.unmarshalNBytes(fdoLength);
        int i = 6 + (fdo[5] & 255) + (fdo[6] & 255);
        this.NCHAR_CHARSET = (short) ((fdo[i + 3] & 255) << 8);
        this.NCHAR_CHARSET = (short) (this.NCHAR_CHARSET | ((short) (fdo[i + 4] & 255)));
        if (this.proSvrVer < 6) {
            return null;
        }
        int len = this.meg.unmarshalUB1();
        this.compileTimeCapabilities = new byte[len];
        for (int j = 0; j < len; j++) {
            this.compileTimeCapabilities[j] = (byte) this.meg.unmarshalUB1();
        }
        int len2 = this.meg.unmarshalUB1();
        if (len2 > 0) {
            this.runtimeCapabilities = new byte[len2];
            for (int j2 = 0; j2 < len2; j2++) {
                this.runtimeCapabilities[j2] = (byte) this.meg.unmarshalUB1();
            }
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "receive", "negotiated ttc oVersion={0}, server charset={1}", (String) null, (Throwable) null, Short.valueOf(this.oVersion), Short.valueOf(this.svrCharSet));
        return this.compileTimeCapabilities;
    }

    public void initFrom(T4CTTICookie cookie) throws SQLException {
        if (!$assertionsDisabled && cookie == null) {
            throw new AssertionError("cookie cannot be null");
        }
        setProtocolAndOracleVersions(cookie.getConnectionProtocolVersion());
        this.svrPortDescription = cookie.getDatabasePortage();
        this.svrCharSet = cookie.getDatabaseCharSet();
        this.svrFlags = cookie.getDatabaseCharSetFlag();
        setNCharCharacterSet(cookie.getDatabaseNCharSet());
        this.runtimeCapabilities = cookie.getDatabaseRuntimeCapabilities();
        this.compileTimeCapabilities = cookie.getDatabaseCompileTimeCapabilities();
    }

    void setNCharCharacterSet(short nCharSet) {
        this.NCHAR_CHARSET = nCharSet;
    }

    final CompletionStage<byte[]> receiveAsync(Executor executor) {
        CompletableFuture<byte[]> receiveFuture = new CompletableFuture<>();
        this.meg.net.onWriteReady(writeError -> {
            executor.execute(() -> {
                if (writeError != null) {
                    receiveFuture.completeExceptionally(writeError);
                    return;
                }
                try {
                    this.meg.flush();
                    this.meg.net.onReadReady(readError -> {
                        executor.execute(() -> {
                            if (readError != null) {
                                receiveFuture.completeExceptionally(readError);
                                return;
                            }
                            try {
                                byte[] compileTimeCapabilities = receivePacket();
                                receiveFuture.complete(compileTimeCapabilities);
                            } catch (Throwable throwable) {
                                receiveFuture.completeExceptionally(throwable);
                            }
                        });
                    });
                } catch (Throwable throwable) {
                    receiveFuture.completeExceptionally(throwable);
                }
            });
        });
        return receiveFuture;
    }

    short getOracleVersion() {
        return this.oVersion;
    }

    byte[] getServerRuntimeCapabilities() {
        return this.runtimeCapabilities;
    }

    byte[] getServerCompileTimeCapabilities() {
        return this.compileTimeCapabilities;
    }

    short getCharacterSet() {
        return this.svrCharSet;
    }

    short getncharCHARSET() {
        return this.NCHAR_CHARSET;
    }

    byte getFlags() {
        return this.svrFlags;
    }

    void marshal() throws IOException {
        marshalTTCcode();
        this.meg.marshalB1Array(this.proCliVerTTC8);
        this.meg.marshalB1Array(this.proCliStrTTC8);
    }

    short getProtocolVersion() {
        return this.proSvrVer;
    }

    void doRPC() throws SQLException, IOException {
        TraceEventListener.TraceContext ctx = SimpleTraceContext.builder().connectionId(this.connection.getNetConnectionId()).function(DatabaseFunction.TTC_PRO_ROUNDTRIP).build();
        this.connection.getTraceEventListener().roundTrip(TraceEventListener.Sequence.BEFORE, ctx, null);
        marshal();
        receivePacket();
        this.connection.getTraceEventListener().roundTrip(TraceEventListener.Sequence.AFTER, ctx, null);
    }

    void printServerInfo() {
        if (this.svrInfoAvailable) {
            int i = 0;
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "printServerInfo", "Protocol version={0} oVersion ={1}", (String) null, (Throwable) null, Short.valueOf(this.proSvrVer), Short.valueOf(this.oVersion));
            StringWriter s = new StringWriter();
            s.write("Protocol string  =");
            while (i < this.svrPortDescription.length) {
                int i2 = i;
                i++;
                s.write((char) this.svrPortDescription[i2]);
            }
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "printServerInfo", "{0}\nCharacter Set ID  = {1}\nRemote flags      = {2}\nNumber of Elements in Server's Character Set Graph = {3}", (String) null, (Throwable) null, s.toString(), Short.valueOf(this.svrCharSet), Byte.valueOf(this.svrFlags), Short.valueOf(this.svrCharSetElem));
            return;
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "printServerInfo", "Server info Not Available", (String) null, (Throwable) null);
    }
}
