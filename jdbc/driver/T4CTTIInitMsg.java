package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIInitMsg.class */
class T4CTTIInitMsg extends T4CTTIMsg {
    private static final byte VERSION = 1;
    private static final byte TTIINIT_CLNT_IGNORES_DBCST = 1;
    static final byte[] ASSUMED_SRV_CT_CAPS = {6, 1, 1, 1, -17, 15, 1, 23, 1, 1, 1, 1, 1, 1, 1, Byte.MAX_VALUE, -1, 3, 16, 3, 3, 1, 1, -1, 1, -1, -1, 1, 12, 1, 1, -1, 1, 6, 12, -10, 1, Byte.MAX_VALUE, 5, 15, -1, 13, 11, 0, 39, 0, 0, 0, 0, 0, 0};
    static final byte[] ASSUMED_SRV_RT_CAPS = {2, 1, 0, 1, 24, 0, Byte.MAX_VALUE, 1, 0, 0, 0, 0};
    private T4C8TTIpro protocolMessage;
    private T4CConnection connection;
    private short assumedDBServerCharset;
    private short assumedDBServernCharset;

    private T4CTTIInitMsg(Builder builder) {
        super(builder.connection, (byte) 34);
        this.protocolMessage = null;
        this.connection = null;
        this.assumedDBServerCharset = (short) 0;
        this.assumedDBServernCharset = (short) 0;
        this.protocolMessage = builder.protocolMessage;
        this.connection = builder.connection;
        this.assumedDBServerCharset = builder.assumedDBServerCharset;
        this.assumedDBServernCharset = builder.assumedDBServernCharset;
    }

    public static Builder builder() {
        return new Builder();
    }

    void marshal() throws IOException {
        marshalTTCcode();
        this.meg.marshalUB1((short) 1);
        this.meg.marshalUB1((this.assumedDBServerCharset == 0 && this.assumedDBServernCharset == 0) ? (short) 1 : (short) 0);
        this.meg.marshalUB1((short) 0);
        this.protocolMessage.marshal();
        this.meg.marshalUB2(this.assumedDBServerCharset);
        this.meg.marshalUB1(this.assumedDBServerCharset == 0 ? (short) 0 : this.meg.types.getFlags());
        this.meg.marshalUB2(this.assumedDBServernCharset);
        this.meg.marshalUB1((short) 24);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIInitMsg$Builder.class */
    public static final class Builder {
        private T4C8TTIpro protocolMessage;
        private T4CConnection connection;
        private short assumedDBServerCharset;
        private short assumedDBServernCharset;

        private Builder() {
            this.assumedDBServerCharset = (short) 0;
            this.assumedDBServernCharset = (short) 0;
        }

        public Builder protocolMessage(T4C8TTIpro protocolMessage) {
            this.protocolMessage = protocolMessage;
            return this;
        }

        public Builder connection(T4CConnection connection) {
            this.connection = connection;
            return this;
        }

        public Builder assumedDBServerCharset(short assumedDBServerCharset) {
            this.assumedDBServerCharset = assumedDBServerCharset;
            return this;
        }

        public Builder assumedDBServernCharset(short assumedDBServernCharset) {
            this.assumedDBServernCharset = assumedDBServernCharset;
            return this;
        }

        public T4CTTIInitMsg build() {
            if (this.connection == null) {
                throw new IllegalArgumentException("connection must be set");
            }
            if (this.protocolMessage == null) {
                throw new IllegalArgumentException("TTIpro message must be set");
            }
            return new T4CTTIInitMsg(this);
        }
    }
}
