package oracle.jdbc.driver;

import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTICookie.class */
class T4CTTICookie {
    private static final byte VERSION = 1;
    private short connectionProtocolVersion;
    private short databaseCharSet;
    private byte databaseCharSetFlag;
    private short databaseNCharSet;
    private byte[] databaseRuntimeCapabilities;
    private byte[] databaseCompileTimeCapabilities;
    private byte[] databasePortage;

    private T4CTTICookie(Builder builder) {
        this.databaseRuntimeCapabilities = null;
        this.databaseCompileTimeCapabilities = null;
        this.databasePortage = null;
        this.connectionProtocolVersion = builder.connectionProtocolVersion;
        this.databaseCharSet = builder.databaseCharSet;
        this.databaseCharSetFlag = builder.databaseCharSetFlag;
        this.databaseNCharSet = builder.databaseNCharSet;
        this.databaseRuntimeCapabilities = builder.databaseRuntimeCapabilities;
        this.databaseCompileTimeCapabilities = builder.databaseCompileTimeCapabilities;
        this.databasePortage = builder.databasePortage;
    }

    static Builder builder() {
        return new Builder();
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        T4CTTICookie ttiCookie = (T4CTTICookie) o;
        return this.connectionProtocolVersion == ttiCookie.connectionProtocolVersion && this.databaseCharSet == ttiCookie.databaseCharSet && this.databaseCharSetFlag == ttiCookie.databaseCharSetFlag && this.databaseNCharSet == ttiCookie.databaseNCharSet && Arrays.equals(this.databaseRuntimeCapabilities, ttiCookie.databaseRuntimeCapabilities) && Arrays.equals(this.databaseCompileTimeCapabilities, ttiCookie.databaseCompileTimeCapabilities) && Arrays.equals(this.databasePortage, ttiCookie.databasePortage);
    }

    public int hashCode() {
        int result = Objects.hash((byte) 1, Short.valueOf(this.connectionProtocolVersion), Short.valueOf(this.databaseCharSet), Byte.valueOf(this.databaseCharSetFlag), Short.valueOf(this.databaseNCharSet));
        return (31 * ((31 * ((31 * result) + Arrays.hashCode(this.databaseRuntimeCapabilities))) + Arrays.hashCode(this.databaseCompileTimeCapabilities))) + Arrays.hashCode(this.databasePortage);
    }

    public String toString() {
        return "T4CTTICookie{version=1, connectionProtocolVersion=" + ((int) this.connectionProtocolVersion) + ", databaseCharSet=" + ((int) this.databaseCharSet) + ", databaseCharSetFlag=" + ((int) this.databaseCharSetFlag) + ", databaseNCharSet=" + ((int) this.databaseNCharSet) + ", databaseRuntimeCapabilities=" + Arrays.toString(this.databaseRuntimeCapabilities) + ", databaseCompileTimeCapabilities=" + Arrays.toString(this.databaseCompileTimeCapabilities) + ", databasePortage=" + Arrays.toString(this.databasePortage) + '}';
    }

    byte getVersion() {
        return (byte) 1;
    }

    short getConnectionProtocolVersion() {
        return this.connectionProtocolVersion;
    }

    short getDatabaseCharSet() {
        return this.databaseCharSet;
    }

    byte getDatabaseCharSetFlag() {
        return this.databaseCharSetFlag;
    }

    short getDatabaseNCharSet() {
        return this.databaseNCharSet;
    }

    byte[] getDatabaseRuntimeCapabilities() {
        return (byte[]) this.databaseRuntimeCapabilities.clone();
    }

    byte[] getDatabaseCompileTimeCapabilities() {
        return (byte[]) this.databaseCompileTimeCapabilities.clone();
    }

    byte[] getDatabasePortage() {
        return (byte[]) this.databasePortage.clone();
    }

    void marshal(T4CMAREngine meg) throws IOException {
        meg.marshalSB1((byte) 1);
        meg.marshalUB1(this.connectionProtocolVersion);
        meg.marshalNativeUB2(this.databaseCharSet, true);
        meg.marshalUB1(this.databaseCharSetFlag);
        meg.marshalNativeUB2(this.databaseNCharSet, true);
        meg.marshalUB1((short) (this.databasePortage.length + 1));
        meg.marshalB1Array(this.databasePortage);
        meg.marshalSB1((byte) 0);
        meg.marshalUB1((short) this.databaseCompileTimeCapabilities.length);
        meg.marshalB1Array(this.databaseCompileTimeCapabilities);
        meg.marshalUB1((short) this.databaseRuntimeCapabilities.length);
        meg.marshalB1Array(this.databaseRuntimeCapabilities);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTICookie$Builder.class */
    static final class Builder {
        private short connectionProtocolVersion;
        private short databaseCharSet;
        private byte databaseCharSetFlag;
        private short databaseNCharSet;
        private byte[] databaseRuntimeCapabilities;
        private byte[] databaseCompileTimeCapabilities;
        private byte[] databasePortage;

        private Builder() {
        }

        Builder connectionProtocolVersion(short connectionProtocolVersion) {
            this.connectionProtocolVersion = connectionProtocolVersion;
            return this;
        }

        Builder databaseCharSet(short databaseCharSet) {
            this.databaseCharSet = databaseCharSet;
            return this;
        }

        Builder databaseCharSetFlag(byte databaseCharSetFlag) {
            this.databaseCharSetFlag = databaseCharSetFlag;
            return this;
        }

        Builder databaseNCharSet(short databaseNCharSet) {
            this.databaseNCharSet = databaseNCharSet;
            return this;
        }

        Builder databaseRuntimeCapabilities(byte[] databaseRuntimeCapabilities) {
            this.databaseRuntimeCapabilities = databaseRuntimeCapabilities;
            return this;
        }

        Builder databaseCompileTimeCapabilities(byte[] databaseCompileTimeCapabilities) {
            this.databaseCompileTimeCapabilities = databaseCompileTimeCapabilities;
            return this;
        }

        Builder databasePortage(byte[] databasePortage) {
            this.databasePortage = databasePortage;
            return this;
        }

        T4CTTICookie build() {
            if (this.databaseCompileTimeCapabilities == null) {
                throw new IllegalArgumentException("Cannot build a cookie with compile time capabilities not set");
            }
            if (this.databasePortage == null) {
                throw new IllegalArgumentException("Cannot build a cookie with database platform not set");
            }
            if (this.connectionProtocolVersion <= 0) {
                throw new IllegalArgumentException("Cannot build a cookie with database protocoal version not correctly set");
            }
            return new T4CTTICookie(this);
        }
    }
}
