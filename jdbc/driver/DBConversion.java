package oracle.jdbc.driver;

import java.io.InputStream;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.function.Consumer;
import java.util.function.IntFunction;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.util.RepConversion;
import oracle.sql.CharacterSet;
import oracle.sql.converter.CharacterSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/DBConversion.class */
public class DBConversion implements Diagnosable {
    private static final String CLASS_NAME = DBConversion.class.getName();
    public static final boolean DO_CONVERSION_WITH_REPLACEMENT = true;
    public static final short ORACLE8_PROD_VERSION = 8030;
    protected short serverNCharSetId;
    protected short serverCharSetId;
    protected short clientCharSetId;
    protected CharacterSet serverCharSet;
    protected CharacterSet serverNCharSet;
    protected CharacterSet clientCharSet;
    protected CharacterSet asciiCharSet;
    protected boolean isServerCharSetFixedWidth;
    protected boolean isServerNCharSetFixedWidth;
    protected int c2sNlsRatio;
    protected int s2cNlsRatio;
    protected int sMaxCharSize;
    protected int cMaxCharSize;
    protected int maxNCharSize;
    protected boolean isServerCSMultiByte;
    private boolean isStrictASCIIConversion;
    private boolean isQuickASCIIConversion;
    final IntFunction<byte[]> byteArrayAllocator;
    final Consumer<byte[]> byteArrayCache;
    final IntFunction<char[]> charArrayAllocator;
    final Consumer<char[]> charArrayCache;
    public static final short DBCS_CHARSET = -1;
    public static final short UCS2_CHARSET = -5;
    public static final short ASCII_CHARSET = 1;
    public static final short ISO_LATIN_1_CHARSET = 31;
    public static final short WE8ISO8859P15_CHARSET = 46;
    public static final short AL24UTFFSS_CHARSET = 870;
    public static final short UTF8_CHARSET = 871;
    public static final short AL32UTF8_CHARSET = 873;
    public static final short AL16UTF16_CHARSET = 2000;

    public DBConversion(short svrCharSet, short drvrCharSet, short svrNCharSet, boolean strictConversion, boolean quickASCIIConversion) throws SQLException {
        this(svrCharSet, drvrCharSet, svrNCharSet, strictConversion, quickASCIIConversion, x$0 -> {
            return new byte[x$0];
        }, byteArray -> {
        }, x$02 -> {
            return new char[x$02];
        }, charArray -> {
        });
    }

    public DBConversion(short svrCharSet, short drvrCharSet, short svrNCharSet, boolean strictConversion, boolean quickASCIIConversion, IntFunction<byte[]> byteArrayAllocator, Consumer<byte[]> byteArrayCache, IntFunction<char[]> charArrayAllocator, Consumer<char[]> charArrayCache) throws SQLException {
        this.isStrictASCIIConversion = false;
        this.isQuickASCIIConversion = false;
        this.isStrictASCIIConversion = strictConversion;
        this.isQuickASCIIConversion = quickASCIIConversion;
        this.byteArrayAllocator = byteArrayAllocator;
        this.byteArrayCache = byteArrayCache;
        this.charArrayAllocator = charArrayAllocator;
        this.charArrayCache = charArrayCache;
        if (drvrCharSet != -1) {
            init(svrCharSet, drvrCharSet, svrNCharSet);
        }
    }

    public DBConversion(short svrCharSet, short drvrCharSet, short svrNCharSet) throws SQLException {
        this(svrCharSet, drvrCharSet, svrNCharSet, false, false);
    }

    void init(short svrCharSet, short drvrCharSet, short svrNCharSet) throws SQLException {
        switch (drvrCharSet) {
            case -5:
            case 1:
            case 2:
            case 31:
            case 46:
            case 178:
            case 870:
            case 871:
            case 873:
                break;
            default:
                unexpectedCharset(drvrCharSet);
                break;
        }
        this.serverCharSetId = svrCharSet;
        this.clientCharSetId = drvrCharSet;
        this.serverCharSet = CharacterSet.make(this.serverCharSetId);
        this.serverNCharSetId = svrNCharSet;
        this.serverNCharSet = CharacterSet.make(this.serverNCharSetId);
        this.clientCharSet = CharacterSet.make(this.clientCharSetId);
        this.c2sNlsRatio = CharacterSetMetaData.getRatio(svrCharSet, drvrCharSet);
        this.s2cNlsRatio = CharacterSetMetaData.getRatio(drvrCharSet, svrCharSet);
        this.sMaxCharSize = CharacterSetMetaData.getRatio(svrCharSet, 1);
        this.cMaxCharSize = CharacterSetMetaData.getRatio(drvrCharSet, 1);
        this.maxNCharSize = CharacterSetMetaData.getRatio(svrNCharSet, 1);
        findFixedWidthInfo();
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "init", "c2sNlsRatio={0}, s2cNlsRatio={1}. ", (String) null, (Throwable) null, Integer.valueOf(this.c2sNlsRatio), Integer.valueOf(this.s2cNlsRatio));
    }

    void findFixedWidthInfo() throws SQLException {
        this.isServerCharSetFixedWidth = CharacterSetMetaData.isFixedWidth(this.serverCharSetId);
        this.isServerNCharSetFixedWidth = CharacterSetMetaData.isFixedWidth(this.serverNCharSetId);
        this.isServerCSMultiByte = this.sMaxCharSize > 1;
    }

    public short getServerCharSetId() {
        return this.serverCharSetId;
    }

    public short getNCharSetId() {
        return this.serverNCharSetId;
    }

    public boolean IsNCharFixedWith() {
        return this.serverNCharSetId == 2000;
    }

    public short getClientCharSet() {
        if (this.clientCharSetId == -1) {
            return this.serverCharSetId;
        }
        return this.clientCharSetId;
    }

    public CharacterSet getDbCharSetObj() {
        return this.serverCharSet;
    }

    public CharacterSet getDriverCharSetObj() {
        return this.clientCharSet;
    }

    public CharacterSet getDriverNCharSetObj() {
        return this.serverNCharSet;
    }

    CharacterSet getCharacterSet(short formOfUse) {
        if (formOfUse == 2) {
            return getDriverNCharSetObj();
        }
        return getDriverCharSetObj();
    }

    public static final short findDriverCharSet(short svrCharSet, short oraVersion) {
        short driver_charset;
        switch (svrCharSet) {
            case 1:
            case 2:
            case 31:
            case 46:
            case 178:
            case 873:
                driver_charset = svrCharSet;
                break;
            default:
                driver_charset = oraVersion >= 8030 ? (short) 871 : (short) 870;
                break;
        }
        return driver_charset;
    }

    public static final byte[] stringToDriverCharBytes(String str, short charset) throws SQLException {
        if (str == null) {
            return null;
        }
        byte[] ret_bytes = null;
        switch (charset) {
            case -5:
            case 2000:
                ret_bytes = CharacterSet.stringToAL16UTF16Bytes(str);
                break;
            case -1:
            default:
                unexpectedCharset(charset);
                break;
            case 1:
            case 2:
                ret_bytes = CharacterSet.stringToASCII(str);
                break;
            case 870:
            case 871:
                ret_bytes = CharacterSet.stringToUTF(str);
                break;
            case 873:
                ret_bytes = CharacterSet.stringToAL32UTF8(str);
                break;
        }
        return ret_bytes;
    }

    public byte[] StringToCharBytes(String str) throws SQLException {
        if (str.length() == 0) {
            return null;
        }
        switch (this.clientCharSetId) {
            case -1:
                return this.serverCharSet.convertWithReplacement(str);
            case 1:
                if (this.isQuickASCIIConversion) {
                    byte[] bytearr = new byte[str.length()];
                    CharacterSet.convertJavaCharsToASCIIBytes(str.toCharArray(), 0, bytearr, 0, str.length(), false);
                    return bytearr;
                }
                break;
            case 2:
            case 31:
            case 46:
            case 178:
                return this.clientCharSet.convertWithReplacement(str);
        }
        return stringToDriverCharBytes(str, this.clientCharSetId);
    }

    public String CharBytesToString(byte[] bytes, int nbytes) throws SQLException {
        return CharBytesToString(bytes, nbytes, true);
    }

    public String CharBytesToString(byte[] bytes, int nbytes, boolean useReplacementChar) throws SQLException {
        String ret_str = null;
        if (bytes.length == 0) {
            return null;
        }
        switch (this.clientCharSetId) {
            case -5:
                ret_str = CharacterSet.AL16UTF16BytesToString(bytes, nbytes);
                break;
            case -1:
                ret_str = this.serverCharSet.toStringWithReplacement(bytes, 0, nbytes);
                break;
            case 1:
                ret_str = new String(bytes, 0, nbytes, StandardCharsets.US_ASCII);
                break;
            case 2:
            case 31:
            case 46:
            case 178:
                if (useReplacementChar) {
                    ret_str = this.clientCharSet.toStringWithReplacement(bytes, 0, nbytes);
                    break;
                } else {
                    ret_str = this.clientCharSet.toString(bytes, 0, nbytes);
                    break;
                }
            case 870:
            case 871:
                ret_str = CharacterSet.UTFToString(bytes, 0, nbytes, useReplacementChar);
                break;
            case 873:
                ret_str = CharacterSet.AL32UTF8ToString(bytes, 0, nbytes, useReplacementChar);
                break;
            default:
                unexpectedCharset(this.clientCharSetId);
                break;
        }
        return ret_str;
    }

    public String NCharBytesToString(byte[] bytes, int nbytes) throws SQLException {
        String ret_str = null;
        if (this.clientCharSetId == -1) {
            ret_str = this.serverNCharSet.toStringWithReplacement(bytes, 0, nbytes);
        } else {
            switch (this.serverNCharSetId) {
                case -5:
                case 2000:
                    ret_str = CharacterSet.AL16UTF16BytesToString(bytes, nbytes);
                    break;
                case -1:
                    ret_str = this.serverCharSet.toStringWithReplacement(bytes, 0, nbytes);
                    break;
                case 1:
                case 2:
                    ret_str = new String(bytes, 0, nbytes, StandardCharsets.US_ASCII);
                    break;
                case 31:
                case 46:
                case 178:
                    ret_str = this.serverNCharSet.toStringWithReplacement(bytes, 0, nbytes);
                    break;
                case 870:
                case 871:
                    ret_str = CharacterSet.UTFToString(bytes, 0, nbytes);
                    break;
                case 873:
                    ret_str = CharacterSet.AL32UTF8ToString(bytes, 0, nbytes);
                    break;
                default:
                    unexpectedCharset(this.clientCharSetId);
                    break;
            }
        }
        return ret_str;
    }

    public int javaCharsToCHARBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        return javaCharsToCHARBytes(chars, nchars, bytes, this.clientCharSetId);
    }

    public int javaCharsToCHARBytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, int nchars) throws SQLException {
        return javaCharsToCHARBytes(chars, charOffset, bytes, byteOffset, this.clientCharSetId, nchars);
    }

    public byte[] javaCharsToCHARBytes(char[] chars) throws SQLException {
        byte[] buffer = this.byteArrayAllocator.apply(chars.length * getMaxCharbyteSize());
        try {
            byte[] bArrCopyOf = Arrays.copyOf(buffer, javaCharsToCHARBytes(chars, chars.length, buffer));
            this.byteArrayCache.accept(buffer);
            return bArrCopyOf;
        } catch (Throwable th) {
            this.byteArrayCache.accept(buffer);
            throw th;
        }
    }

    public int javaCharsToNCHARBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        return javaCharsToCHARBytes(chars, nchars, bytes, this.serverNCharSetId);
    }

    public int javaCharsToNCHARBytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, int nchars) throws SQLException {
        return javaCharsToCHARBytes(chars, charOffset, bytes, byteOffset, this.serverNCharSetId, nchars);
    }

    protected int javaCharsToCHARBytes(char[] chars, int nchars, byte[] bytes, short cs) throws SQLException {
        return javaCharsToCHARBytes(chars, 0, bytes, 0, cs, nchars);
    }

    protected int javaCharsToCHARBytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, short cs, int nchars) throws SQLException {
        return javaCharsToCHARBytes(chars, charOffset, bytes, byteOffset, cs, nchars, null);
    }

    protected int javaCharsToCHARBytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, short cs, int nchars, int[] codePointCount) throws SQLException {
        int ret = 0;
        switch (cs) {
            case -5:
            case 2000:
                ret = CharacterSet.convertJavaCharsToAL16UTF16Bytes(chars, charOffset, bytes, byteOffset, nchars);
                if (codePointCount != null && codePointCount.length > 0) {
                    codePointCount[0] = -1;
                    break;
                }
                break;
            case -1:
                ret = javaCharsToDbCsBytes(chars, charOffset, bytes, byteOffset, nchars);
                if (codePointCount != null && codePointCount.length > 0) {
                    codePointCount[0] = -1;
                    break;
                }
                break;
            case 1:
                ret = CharacterSet.convertJavaCharsToASCIIBytes(chars, charOffset, bytes, byteOffset, nchars, this.isStrictASCIIConversion);
                if (codePointCount != null && codePointCount.length > 0) {
                    codePointCount[0] = ret;
                    break;
                }
                break;
            case 2:
            case 46:
            case 178:
                int[] ncharsRet = {nchars};
                this.clientCharSet.convertWithReplacement(chars, charOffset, bytes, byteOffset, ncharsRet);
                ret = ncharsRet[0];
                if (codePointCount != null && codePointCount.length > 0) {
                    codePointCount[0] = ret;
                    break;
                }
                break;
            case 31:
                ret = CharacterSet.convertJavaCharsToISOLATIN1Bytes(chars, charOffset, bytes, byteOffset, nchars);
                if (codePointCount != null && codePointCount.length > 0) {
                    codePointCount[0] = ret;
                    break;
                }
                break;
            case 870:
            case 871:
                ret = CharacterSet.convertJavaCharsToUTFBytes(chars, charOffset, bytes, byteOffset, nchars, codePointCount);
                break;
            case 873:
                ret = CharacterSet.convertJavaCharsToAL32UTF8Bytes(chars, charOffset, bytes, byteOffset, nchars, codePointCount);
                break;
            default:
                unexpectedCharset(this.clientCharSetId);
                break;
        }
        return ret;
    }

    public int CHARBytesToJavaChars(byte[] bytes, int byteOffset, char[] chars, int charOffset, int[] nbytes, int charSize, boolean isNchar) throws SQLException {
        if (isNchar) {
            return NCHARBytesToJavaChars(bytes, byteOffset, chars, charOffset, nbytes, charSize);
        }
        return CHARBytesToJavaChars(bytes, byteOffset, chars, charOffset, nbytes, charSize);
    }

    public int CHARBytesToJavaChars(byte[] bytes, int byteOffset, char[] chars, int charOffset, int[] nbytes, int charSize) throws SQLException {
        return _CHARBytesToJavaChars(bytes, byteOffset, chars, charOffset, this.clientCharSetId, nbytes, charSize, this.serverCharSet, this.serverNCharSet, this.clientCharSet, false);
    }

    public int NCHARBytesToJavaChars(byte[] bytes, int byteOffset, char[] chars, int charOffset, int[] nbytes, int charSize) throws SQLException {
        return _CHARBytesToJavaChars(bytes, byteOffset, chars, charOffset, this.serverNCharSetId, nbytes, charSize, this.serverCharSet, this.serverNCharSet, this.clientCharSet, true);
    }

    static final int _CHARBytesToJavaChars(byte[] bytes, int byteOffset, char[] chars, int charOffset, short cs, int[] nbytes, int charSize, CharacterSet _m_databaseCs, CharacterSet _m_databaseNCs, CharacterSet _m_driverCs, boolean isNCharData) throws SQLException {
        int count = 0;
        switch (cs) {
            case -5:
            case 2000:
                int nbBytes = nbytes[0] - (nbytes[0] % 2);
                if (charSize > chars.length - charOffset) {
                    charSize = chars.length - charOffset;
                }
                if (charSize * 2 < nbBytes) {
                    nbBytes = charSize * 2;
                }
                count = CharacterSet.convertAL16UTF16BytesToJavaChars(bytes, byteOffset, chars, charOffset, nbBytes, true);
                nbytes[0] = nbytes[0] - nbBytes;
                break;
            case -1:
                unexpectedCharset((short) -1);
                break;
            case 1:
                int nbBytes2 = nbytes[0];
                if (charSize > chars.length - charOffset) {
                    charSize = chars.length - charOffset;
                }
                if (charSize < nbBytes2) {
                    nbBytes2 = charSize;
                }
                count = CharacterSet.convertASCIIBytesToJavaChars(bytes, byteOffset, chars, charOffset, nbBytes2);
                nbytes[0] = nbytes[0] - nbBytes2;
                break;
            case 31:
            case 46:
            case 178:
                count = _m_databaseCs.toCharWithReplacement(bytes, byteOffset, chars, charOffset, nbytes[0]);
                nbytes[0] = nbytes[0] - count;
                break;
            case 870:
            case 871:
                if (charSize > chars.length - charOffset) {
                    charSize = chars.length - charOffset;
                }
                count = CharacterSet.convertUTFBytesToJavaChars(bytes, byteOffset, chars, charOffset, nbytes, true, charSize);
                break;
            case 873:
                if (charSize > chars.length - charOffset) {
                    charSize = chars.length - charOffset;
                }
                count = CharacterSet.convertAL32UTF8BytesToJavaChars(bytes, byteOffset, chars, charOffset, nbytes, true, charSize);
                break;
            default:
                CharacterSet chSet = _m_driverCs;
                if (isNCharData) {
                    chSet = _m_databaseNCs;
                }
                String converted = chSet.toStringWithReplacement(bytes, byteOffset, nbytes[0]);
                char[] convertedChars = converted.toCharArray();
                int nbCharsToCopy = convertedChars.length;
                if (nbCharsToCopy > charSize) {
                    nbCharsToCopy = charSize;
                }
                count = nbCharsToCopy;
                nbytes[0] = nbytes[0] - nbCharsToCopy;
                System.arraycopy(convertedChars, 0, chars, charOffset, nbCharsToCopy);
                break;
        }
        return count;
    }

    public byte[] asciiBytesToCHARBytes(byte[] bytes) {
        byte[] retbytes = null;
        switch (this.clientCharSetId) {
            case -5:
                retbytes = new byte[bytes.length * 2];
                int retbyte_i = 0;
                for (byte b : bytes) {
                    int i = retbyte_i;
                    int retbyte_i2 = retbyte_i + 1;
                    retbytes[i] = 0;
                    retbyte_i = retbyte_i2 + 1;
                    retbytes[retbyte_i2] = b;
                }
                break;
            case -1:
                if (this.asciiCharSet == null) {
                    this.asciiCharSet = CharacterSet.make(1);
                }
                try {
                    retbytes = this.serverCharSet.convert(this.asciiCharSet, bytes, 0, bytes.length);
                    break;
                } catch (SQLException e) {
                    break;
                }
            default:
                retbytes = bytes;
                break;
        }
        return retbytes;
    }

    public int javaCharsToDbCsBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        int num_conv_bytes = javaCharsToDbCsBytes(chars, 0, bytes, 0, nchars);
        return num_conv_bytes;
    }

    public int javaCharsToDbCsBytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, int nchars) throws SQLException {
        int num_conv_bytes = 0;
        catchCharsLen(chars, charOffset, nchars);
        String str = new String(chars, charOffset, nchars);
        byte[] dbcs_bytes = this.serverCharSet.convertWithReplacement(str);
        if (dbcs_bytes != null) {
            num_conv_bytes = dbcs_bytes.length;
            catchBytesLen(bytes, byteOffset, num_conv_bytes);
            System.arraycopy(dbcs_bytes, 0, bytes, byteOffset, num_conv_bytes);
        }
        return num_conv_bytes;
    }

    public static final int javaCharsToUcs2Bytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        int byte_i = javaCharsToUcs2Bytes(chars, 0, bytes, 0, nchars);
        return byte_i;
    }

    public static final int javaCharsToUcs2Bytes(char[] chars, int charOffset, byte[] bytes, int byteOffset, int nchars) throws SQLException {
        catchCharsLen(chars, charOffset, nchars);
        catchBytesLen(bytes, byteOffset, nchars * 2);
        int lastChar = nchars + charOffset;
        int byte_i = byteOffset;
        for (int char_i = charOffset; char_i < lastChar; char_i++) {
            int i = byte_i;
            int byte_i2 = byte_i + 1;
            bytes[i] = (byte) ((chars[char_i] >> '\b') & 255);
            byte_i = byte_i2 + 1;
            bytes[byte_i2] = (byte) (chars[char_i] & 255);
        }
        return byte_i - byteOffset;
    }

    public static final int ucs2BytesToJavaChars(byte[] bytes, int nbytes, char[] chars) throws SQLException {
        return CharacterSet.AL16UTF16BytesToJavaChars(bytes, nbytes, chars);
    }

    public static final byte[] stringToAsciiBytes(String str) {
        return CharacterSet.stringToASCII(str);
    }

    public static final int asciiBytesToJavaChars(byte[] bytes, int nbytes, char[] chars) throws SQLException {
        return CharacterSet.convertASCIIBytesToJavaChars(bytes, 0, chars, 0, nbytes);
    }

    public static final int javaCharsToAsciiBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        return CharacterSet.convertJavaCharsToASCIIBytes(chars, 0, bytes, 0, nchars);
    }

    static final int asciiBytesToUTF16Bytes(byte[] asciiBytes, int nbytes, byte[] utf16Bytes) {
        if (nbytes < 0) {
            throw new IllegalArgumentException("Number of bytes to convert is negative:" + nbytes);
        }
        int writeLimit = Math.min(nbytes * 2, utf16Bytes.length & (-2));
        for (int i = 0; i < writeLimit; i += 2) {
            utf16Bytes[i] = 0;
            utf16Bytes[i + 1] = asciiBytes[i >>> 1];
        }
        return writeLimit;
    }

    public static final boolean isCharSetMultibyte(short charSet) {
        switch (charSet) {
            case -5:
            case -1:
            case 870:
            case 871:
            case 873:
                return true;
            case 1:
            case 31:
            case 46:
                return false;
            default:
                return false;
        }
    }

    public int getMaxCharbyteSize() {
        return _getMaxCharbyteSize(this.clientCharSetId);
    }

    public int getMaxNCharbyteSize() {
        return _getMaxCharbyteSize(this.serverNCharSetId);
    }

    public int _getMaxCharbyteSize(short cs) {
        switch (cs) {
            case -5:
            case 2000:
                break;
            case -1:
                break;
            case 1:
                break;
            case 31:
            case 46:
                break;
            case 870:
            case 871:
                break;
            case 873:
                break;
        }
        return 1;
    }

    public boolean isUcs2CharSet() {
        return this.clientCharSetId == -5;
    }

    public static final int RAWBytesToHexChars(byte[] bytes, int nbytes, char[] chars) {
        int char_i = 0;
        for (int byte_i = 0; byte_i < nbytes; byte_i++) {
            int i = char_i;
            int char_i2 = char_i + 1;
            chars[i] = (char) RepConversion.nibbleToHex((byte) ((bytes[byte_i] >> 4) & 15));
            char_i = char_i2 + 1;
            chars[char_i2] = (char) RepConversion.nibbleToHex((byte) (bytes[byte_i] & 15));
        }
        return char_i;
    }

    public final int hexDigit2Nibble(char hex) throws SQLException {
        int result = Character.digit(hex, 16);
        if (result == -1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, "Invalid hex digit: " + hex).fillInStackTrace());
        }
        return result;
    }

    public final byte[] hexString2Bytes(String hexString) throws SQLException {
        int len = hexString.length();
        char[] hexChars = new char[len];
        hexString.getChars(0, len, hexChars, 0);
        return hexChars2Bytes(hexChars, 0, len);
    }

    public final byte[] hexChars2Bytes(char[] hexChars, int offset, int len) throws SQLException {
        byte[] bytes;
        int i = 0;
        int j = offset;
        if (len == 0) {
            return new byte[0];
        }
        if (len % 2 > 0) {
            bytes = new byte[(len + 1) / 2];
            i = 0 + 1;
            j++;
            bytes[0] = (byte) hexDigit2Nibble(hexChars[j]);
        } else {
            bytes = new byte[len / 2];
        }
        while (i < bytes.length) {
            int i2 = j;
            int j2 = j + 1;
            j = j2 + 1;
            bytes[i] = (byte) ((hexDigit2Nibble(hexChars[i2]) << 4) | hexDigit2Nibble(hexChars[j2]));
            i++;
        }
        return bytes;
    }

    public InputStream ConvertStream(InputStream stream, int conversion, Monitor monitor) {
        return new OracleConversionInputStream(this, stream, conversion, monitor);
    }

    public InputStream ConvertStream(InputStream stream, int conversion, int max_bytes, Monitor monitor) {
        return new OracleConversionInputStream(this, stream, conversion, max_bytes, monitor);
    }

    public InputStream ConvertStreamInternal(InputStream stream, int conversion, int max_bytes) {
        return new OracleConversionInputStreamInternal(this, stream, conversion, max_bytes);
    }

    public InputStream ConvertStream(Reader stream, int conversion, int max_chars, short form_of_use, Monitor monitor) {
        OracleConversionInputStream ocis = new OracleConversionInputStream(this, stream, conversion, max_chars, form_of_use, monitor);
        return ocis;
    }

    public InputStream ConvertStreamInternal(Reader stream, int conversion, int max_chars, short form_of_use) {
        OracleConversionInputStream ocis = new OracleConversionInputStreamInternal(this, stream, conversion, max_chars, form_of_use);
        return ocis;
    }

    public Reader ConvertCharacterStream(InputStream stream, int conversion, Monitor monitor) throws SQLException {
        return new OracleConversionReader(this, stream, conversion, monitor);
    }

    public Reader ConvertCharacterStream(InputStream stream, int conversion, short form_of_use, Monitor monitor) throws SQLException {
        OracleConversionReader ocr = new OracleConversionReader(this, stream, conversion, monitor);
        ocr.setFormOfUse(form_of_use);
        return ocr;
    }

    public InputStream CharsToStream(char[] javachars, int offset, int len, int conversion, Monitor monitor) throws SQLException {
        if (conversion == 10) {
            return new AsciiStream(javachars, offset, len, monitor);
        }
        if (conversion == 11) {
            return new UnicodeStream(javachars, offset, len, monitor);
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 39, "unknownConversion").fillInStackTrace());
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/DBConversion$AsciiStream.class */
    class AsciiStream extends OracleBufferedStream {
        AsciiStream(char[] javachars, int offset, int len, Monitor monitor) {
            super(len, DBConversion.this.byteArrayAllocator, DBConversion.this.byteArrayCache, monitor);
            this.currentBufferSize = this.initialBufferSize;
            byte[] buffer = getBuffer(this.currentBufferSize);
            if (DBConversion.this.serverCharSetId == 1 || !DBConversion.this.isStrictASCIIConversion) {
                int cind = offset;
                for (int bind = 0; bind < len; bind++) {
                    int i = cind;
                    cind++;
                    buffer[bind] = (byte) javachars[i];
                }
            } else {
                if (DBConversion.this.asciiCharSet == null) {
                    DBConversion.this.asciiCharSet = CharacterSet.make(1);
                }
                DBConversion.this.asciiCharSet.convertWithReplacement(javachars, offset, buffer, 0, new int[]{len});
            }
            this.count = len;
        }

        @Override // oracle.jdbc.driver.OracleBufferedStream
        public boolean needBytes() {
            return !this.closed && this.pos < this.count;
        }

        @Override // oracle.jdbc.driver.OracleBufferedStream
        public boolean needBytes(int ignore) {
            return !this.closed && this.pos < this.count;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/DBConversion$UnicodeStream.class */
    class UnicodeStream extends OracleBufferedStream {
        UnicodeStream(char[] javachars, int offset, int len, Monitor monitor) {
            super(len, DBConversion.this.byteArrayAllocator, DBConversion.this.byteArrayCache, monitor);
            this.currentBufferSize = this.initialBufferSize;
            byte[] buffer = getBuffer(this.currentBufferSize);
            int cind = offset;
            int bind = 0;
            while (bind < len) {
                int i = cind;
                cind++;
                char c = javachars[i];
                int i2 = bind;
                int bind2 = bind + 1;
                buffer[i2] = (byte) ((c >> '\b') & 255);
                bind = bind2 + 1;
                buffer[bind2] = (byte) (c & 255);
            }
            this.count = len;
        }

        @Override // oracle.jdbc.driver.OracleBufferedStream
        public boolean needBytes() {
            return !this.closed && this.pos < this.count;
        }

        @Override // oracle.jdbc.driver.OracleBufferedStream
        public boolean needBytes(int ignore) {
            return !this.closed && this.pos < this.count;
        }
    }

    static final void unexpectedCharset(short charSet) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(35, "DBConversion").fillInStackTrace());
    }

    protected static final void catchBytesLen(byte[] bytes, int offset, int nbytes) throws SQLException {
        if (offset + nbytes > bytes.length) {
            throw ((SQLException) DatabaseError.createSqlException(39, "catchBytesLen").fillInStackTrace());
        }
    }

    protected static final void catchCharsLen(char[] chars, int offset, int nchars) throws SQLException {
        if (offset + nchars > chars.length) {
            throw ((SQLException) DatabaseError.createSqlException(39, "catchCharsLen").fillInStackTrace());
        }
    }

    public static final int getUtfLen(char c) {
        int utf_len;
        if ((c & 65408) == 0) {
            utf_len = 1;
        } else if ((c & 63488) == 0) {
            utf_len = 2;
        } else {
            utf_len = 3;
        }
        return utf_len;
    }

    int encodedByteLength(String s, boolean isNChar) throws SQLException {
        int len = 0;
        if (s != null) {
            len = s.length();
            if (len != 0) {
                if (isNChar) {
                    len = this.isServerNCharSetFixedWidth ? len * this.maxNCharSize : this.serverNCharSet.encodedByteLength(s);
                } else {
                    len = this.isServerCharSetFixedWidth ? len * this.sMaxCharSize : this.serverCharSet.encodedByteLength(s);
                }
            }
        }
        return len;
    }

    int encodedByteLength(char[] c, boolean isNChar) throws SQLException {
        int len = 0;
        if (c != null) {
            len = c.length;
            if (len != 0) {
                if (isNChar) {
                    len = this.isServerNCharSetFixedWidth ? len * this.maxNCharSize : this.serverNCharSet.encodedByteLength(c);
                } else {
                    len = this.isServerCharSetFixedWidth ? len * this.sMaxCharSize : this.serverCharSet.encodedByteLength(c);
                }
            }
        }
        return len;
    }

    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }
}
