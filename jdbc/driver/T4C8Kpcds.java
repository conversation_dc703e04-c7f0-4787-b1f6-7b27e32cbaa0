package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;
import oracle.jdbc.internal.KeywordValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Kpcds.class */
class T4C8Kpcds extends T4CTTIMsg {
    private final short INITOBJECTYPE = -1;
    private ArrayList<T4C8Kpcdsc> t4c8kpcdscs;
    private short descObjectType;

    T4C8Kpcds(T4CConnection _connection) {
        super(_connection, (byte) 0);
        this.INITOBJECTYPE = (short) -1;
        this.t4c8kpcdscs = new ArrayList<>();
        this.descObjectType = (short) -1;
    }

    void unmarshal() throws SQLException, IOException {
        this.t4c8kpcdscs.clear();
        this.descObjectType = (short) -1;
        this.meg.unmarshalUB2();
        unmarshalDTYDSYR();
    }

    void unmarshalDTYDSYR() throws SQLException, IOException {
        long headerl_kpcds = this.meg.unmarshalUB4();
        if (headerl_kpcds > 0) {
            unmarshalDTYDSYH();
        }
        long envl_kpcds = this.meg.unmarshalUB4();
        if (envl_kpcds > 0) {
            this.meg.unmarshalDALC();
        }
        this.meg.unmarshalUB1();
        long listl_kpcds = this.meg.unmarshalUB4();
        if (listl_kpcds > 0) {
            unmarshalDTYDSYL();
        }
        long tablel_kpcds = this.meg.unmarshalUB4();
        if (tablel_kpcds > 0) {
            unmarshalDTYDSYT();
        }
        long viewl_kpcds = this.meg.unmarshalUB4();
        if (viewl_kpcds > 0) {
            unmarshalDTYDSYV();
        }
        long procl_kpcds = this.meg.unmarshalUB4();
        if (procl_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYP");
        }
        long funcl_kpcds = this.meg.unmarshalUB4();
        if (funcl_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYF");
        }
        long pkgl_kpcds = this.meg.unmarshalUB4();
        if (pkgl_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYK");
        }
        long synl_kpcds = this.meg.unmarshalUB4();
        if (synl_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYY");
        }
        long coll_kpcds = this.meg.unmarshalUB4();
        if (coll_kpcds > 0) {
            unmarshalDTYDSYC();
        }
        long argl_kpcds = this.meg.unmarshalUB4();
        if (argl_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYA");
        }
        long seql_kpcds = this.meg.unmarshalUB4();
        if (seql_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYQ");
        }
        long typel_kpcds = this.meg.unmarshalUB4();
        if (typel_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYTY");
        }
        long schemal_kpcds = this.meg.unmarshalUB4();
        if (schemal_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYS");
        }
        long databasel_kpcds = this.meg.unmarshalUB4();
        if (databasel_kpcds > 0) {
            unmarshalNotImplement("unmarshalDTYDSYD");
        }
        if (this.connection.getTTCVersion() >= 3) {
            long rulel_kpcds = this.meg.unmarshalUB4();
            if (rulel_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsru");
            }
            long rsl_kpcds = this.meg.unmarshalUB4();
            if (rsl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsrs");
            }
            long ecl_kpcds = this.meg.unmarshalUB4();
            if (ecl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsec");
            }
            long tal_kpcds = this.meg.unmarshalUB4();
            if (tal_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedstl");
            }
            long vtl_kpcds = this.meg.unmarshalUB4();
            if (vtl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsvt");
            }
            long nvl_kpcds = this.meg.unmarshalUB4();
            if (nvl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsnv");
            }
        }
        if (this.connection.getTTCVersion() >= 8) {
            long hierl_kpcds = this.meg.unmarshalUB4();
            if (hierl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsyhr");
            }
            long hrl_kpcds = this.meg.unmarshalUB4();
            if (hrl_kpcds > 0) {
                unmarshalNotImplement("unmarshalPiedsyhc");
            }
        }
    }

    void unmarshalDTYDSYR_R() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        unmarshalDTYDSYR();
    }

    int getObjectType() {
        return this.descObjectType;
    }

    void unmarshalDTYDSYH() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        short type_kpcdsh = this.meg.unmarshalUB1();
        if (this.descObjectType == -1) {
            this.descObjectType = type_kpcdsh;
        }
        byte[] time_kpcdsh = this.meg.unmarshalDALC();
        int length = time_kpcdsh.length;
        this.meg.unmarshalUB4();
        byte[] schema_kpcdsh = this.meg.unmarshalDALC();
        int length2 = schema_kpcdsh.length;
        byte[] name_kpcdsh = this.meg.unmarshalDALC();
        int length3 = name_kpcdsh.length;
        this.meg.unmarshalUB4();
    }

    void unmarshalDTYDSYV() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB4();
        long refl_kpcdsv = this.meg.unmarshalUB4();
        int[] ret = new int[1];
        if (refl_kpcdsv > 0) {
            this.meg.unmarshalCLR((int) refl_kpcdsv, ret);
        }
        long collstl_kpcdsv = this.meg.unmarshalUB4();
        if (collstl_kpcdsv > 0) {
            unmarshalDTYDSYR_R();
        }
        this.meg.unmarshalUB2();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalUB1();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        if (this.connection.getTTCVersion() >= 5) {
            this.meg.unmarshalUB1();
        }
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.unmarshalUB4();
        }
        if (this.connection.getTTCVersion() >= 20) {
            unmarshallAnnotations();
        }
    }

    void unmarshalDTYDSYT() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB4();
        long refl_kpcdst = this.meg.unmarshalUB4();
        int[] ret = new int[1];
        if (refl_kpcdst > 0) {
            this.meg.unmarshalCLR((int) refl_kpcdst, ret);
        }
        long collstl_kpcdst = this.meg.unmarshalUB4();
        if (collstl_kpcdst > 0) {
            unmarshalDTYDSYR_R();
        }
        this.meg.unmarshalUB2();
        this.meg.unmarshalSWORD();
        this.meg.unmarshalUB4();
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB2();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalUB1();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        if (this.connection.getTTCVersion() >= 3) {
            this.meg.unmarshalUB1();
            this.meg.unmarshalUB4();
        }
        if (this.connection.getTTCVersion() >= 4) {
            long kk_kpcdst = this.meg.unmarshalUB4();
            if (kk_kpcdst > 0) {
                unmarshalDTYEN();
            }
        }
        if (this.connection.getTTCVersion() >= 5) {
            this.meg.unmarshalUB1();
        }
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.unmarshalUB4();
        }
        if (this.connection.getTTCVersion() >= 20) {
            unmarshallAnnotations();
        }
    }

    void unmarshalDTYEN() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB2();
        this.meg.unmarshalUB4();
        this.meg.unmarshalDALC();
        this.meg.unmarshalUB4();
        this.meg.unmarshalDALC();
    }

    void unmarshalDTYDSYC() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        int size_kpcdsc = this.meg.unmarshalUB2();
        int dty_kpcdsc = this.meg.unmarshalUB2();
        int[] ret = {0};
        byte[] name_kpcdsc = this.meg.unmarshalDALC(ret);
        int namel_kpcdsc = ret[0];
        short precision_kpcdsc = this.meg.unmarshalUB1();
        short scale_kpcdsc = this.meg.unmarshalSB1();
        short isnull_kpcdsc = this.meg.unmarshalUB1();
        this.meg.unmarshalDALC();
        byte[] typnm_kpcdsc = this.meg.unmarshalDALC(ret);
        int typnml_kpcdsc = ret[0];
        long refl_kpcdsc = this.meg.unmarshalUB4();
        ret[0] = 0;
        if (refl_kpcdsc > 0) {
            this.meg.unmarshalCLR((int) refl_kpcdsc, ret);
        }
        this.meg.unmarshalUB2();
        short charsetform_kpcdsc = this.meg.unmarshalUB1();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        this.meg.unmarshalDALC();
        if (this.connection.getTTCVersion() >= 3) {
            this.meg.unmarshalUB2();
        }
        if (this.connection.getTTCVersion() >= 5) {
            this.meg.unmarshalUB1();
        }
        if (this.connection.getTTCVersion() >= 8) {
            this.meg.unmarshalDALC();
            this.meg.unmarshalDALC();
            this.meg.unmarshalDALC();
        }
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.unmarshalUB4();
        }
        String domainName = null;
        String domainSchema = null;
        if (this.connection.getTTCVersion() >= 17) {
            ret[0] = 0;
            byte[] domname_kpcdsc = this.meg.unmarshalDALC(ret);
            int domnamel_kpcdsc = ret[0];
            domainName = domnamel_kpcdsc == 0 ? null : this.meg.conv.CharBytesToString(domname_kpcdsc, domnamel_kpcdsc);
            ret[0] = 0;
            byte[] domsch_kpcdsc = this.meg.unmarshalDALC(ret);
            int domschl_kpcdsc = ret[0];
            domainSchema = domschl_kpcdsc == 0 ? null : this.meg.conv.CharBytesToString(domsch_kpcdsc, domschl_kpcdsc);
        }
        Map<String, String> annotations = null;
        if (this.connection.getTTCVersion() >= 20) {
            annotations = unmarshallAnnotations();
        }
        if (this.connection.getTTCVersion() >= 24) {
            this.meg.unmarshalUB4();
            this.meg.unmarshalUB1();
            this.meg.unmarshalUB1();
        }
        String columnName = namel_kpcdsc == 0 ? null : this.meg.conv.CharBytesToString(name_kpcdsc, namel_kpcdsc);
        String typeName = typnml_kpcdsc == 0 ? null : this.meg.conv.CharBytesToString(typnm_kpcdsc, typnml_kpcdsc);
        T4C8Kpcdsc newCol = new T4C8Kpcdsc(size_kpcdsc, dty_kpcdsc, columnName, precision_kpcdsc, scale_kpcdsc, isnull_kpcdsc, charsetform_kpcdsc, typeName, domainName, domainSchema, annotations);
        this.t4c8kpcdscs.add(newCol);
    }

    private Map<String, String> unmarshallAnnotations() throws SQLException, IOException {
        int kvArrlen = (int) this.meg.unmarshalUB4();
        if (kvArrlen <= 0) {
            return null;
        }
        this.meg.unmarshalUB1();
        T4CTTIkvarr t4CTTIkvarr = new T4CTTIkvarr(this.connection);
        t4CTTIkvarr.unmarshal();
        if (t4CTTIkvarr.kpdkvarrptr == null) {
            return null;
        }
        Map<String, String> annotations = new LinkedHashMap<>();
        for (KeywordValue kv : t4CTTIkvarr.kpdkvarrptr) {
            byte[] binValue = kv.getBinaryValue();
            annotations.put(kv.getTextValue(), binValue == null ? "" : this.meg.conv.CharBytesToString(binValue, binValue.length));
        }
        return annotations;
    }

    T4C8Kpcdsc[] getT4C8Kpcdscs() {
        T4C8Kpcdsc[] a = new T4C8Kpcdsc[this.t4c8kpcdscs.size()];
        this.t4c8kpcdscs.toArray(a);
        this.t4c8kpcdscs.clear();
        return a;
    }

    void unmarshalDTYDSYL() throws SQLException, IOException {
        this.meg.unmarshalUB1();
        this.meg.unmarshalUB1();
        int num_kpcdsl = this.meg.unmarshalUB2();
        this.meg.unmarshalUB1();
        for (int i = 0; i < num_kpcdsl; i++) {
            unmarshalDTYDSYR();
        }
    }

    void unmarshalNotImplement(String methodName) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException(methodName).fillInStackTrace());
    }
}
