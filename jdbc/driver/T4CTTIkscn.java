package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkscn.class */
class T4CTTIkscn extends T4CTTIMsg {
    long kscnbas;
    int kscnwrp;

    T4CTTIkscn(T4CConnection _conn) {
        super(_conn, (byte) 0);
    }

    T4CTTIkscn(T4CConnection _conn, byte _ttccode) {
        super(_conn, _ttccode);
    }

    public void unmarshal() throws SQLException, IOException {
        this.kscnbas = this.meg.unmarshalUB4();
        this.kscnwrp = this.meg.unmarshalUB2();
    }

    public void marshal() throws SQLException, IOException {
        this.meg.marshalUB4(this.kscnbas);
        this.meg.marshalUB2(this.kscnwrp);
    }

    public long getSCN() {
        long scn = (this.kscnbas & SQLnetDef.NSPDDLSLMAX) | ((this.kscnwrp & SQLnetDef.NSPDDLSLMAX) << 32);
        return scn;
    }

    public void setSCN(long scn) {
        this.kscnbas = scn & SQLnetDef.NSPDDLSLMAX;
        this.kscnwrp = (int) (scn >> 32);
    }

    static boolean isLessThanUnsigned(long n1, long n2) {
        return (n1 < n2) ^ (((n1 > 0L ? 1 : (n1 == 0L ? 0 : -1)) < 0) != ((n2 > 0L ? 1 : (n2 == 0L ? 0 : -1)) < 0));
    }
}
