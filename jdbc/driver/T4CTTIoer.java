package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoer.class */
class T4CTTIoer extends T4CTTIoer19 {
    private static final String CLASS_NAME = T4CTTIoer.class.getName();
    long oertyp2;
    long oerchksm;

    T4CTTIoer(T4CConnection conn) {
        super(conn);
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    void init() {
        super.init();
        this.oertyp2 = 0L;
        this.oerchksm = 0L;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    int unmarshal() throws SQLException, IOException {
        return unmarshal(false);
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    int unmarshal(boolean ignoreORA1403) throws SQLException, IOException {
        unmarshalAttributes();
        if (this.oerrcd2 != 0) {
            if (this.oerrcd2 == 1403 && ignoreORA1403) {
                unmarshalErrorMessageAndIgnore();
            } else {
                unmarshalErrorMessage();
            }
        }
        return this.currCursorID;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    void unmarshalAttributes() throws SQLException, IOException {
        super.unmarshalAttributes();
        if (this.connection.getTTCVersion() >= 14) {
            this.oertyp2 = this.meg.unmarshalUB4();
            this.oerchksm = this.meg.unmarshalUB4();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    void print() throws SQLException {
        super.print();
    }

    @Override // oracle.jdbc.driver.T4CTTIoer19, oracle.jdbc.driver.T4CTTIoer11
    long updateChecksum(long localCheckSum) throws SQLException {
        return CRC64.updateChecksum(CRC64.updateChecksum(super.updateChecksum(localCheckSum), this.oertyp2), this.oerchksm);
    }
}
