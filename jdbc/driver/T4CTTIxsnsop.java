package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.XSNamespace;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIxsnsop.class */
class T4CTTIxsnsop extends T4CTTIfun {
    private OracleConnection.XSOperationCode operationCode;
    private byte[] sessionId;
    private XSNamespace[] namespaces;
    private XSNamespace[] outNamespaces;

    T4CTTIxsnsop(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 172);
    }

    void doOXSNS(OracleConnection.XSOperationCode _operationCode, byte[] _sessionId, XSNamespace[] _namespaces, boolean roundTripRPC) throws SQLException, IOException {
        if (roundTripRPC) {
            setTTCCode((byte) 3);
        } else {
            setTTCCode((byte) 17);
        }
        this.operationCode = _operationCode;
        this.sessionId = _sessionId;
        this.namespaces = _namespaces;
        if (this.namespaces != null) {
            for (int i = 0; i < this.namespaces.length; i++) {
                ((XSNamespaceI) this.namespaces[i]).doCharConversion(this.meg.conv);
            }
        }
        if (roundTripRPC) {
            doRPC();
        } else {
            doPigRPC();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.operationCode.getCode());
        boolean sendSessionId = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSessionId = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendNamespaces = false;
        this.meg.marshalPTR();
        if (this.namespaces != null && this.namespaces.length > 0) {
            sendNamespaces = true;
            this.meg.marshalUB4(this.namespaces.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalPTR();
        if (sendSessionId) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (sendNamespaces) {
            for (int i = 0; i < this.namespaces.length; i++) {
                ((XSNamespaceI) this.namespaces[i]).marshal(this.meg);
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.outNamespaces = null;
        int namespacesLength = (int) this.meg.unmarshalUB4();
        if (namespacesLength > 0) {
            this.outNamespaces = new XSNamespace[namespacesLength];
            for (int i = 0; i < namespacesLength; i++) {
                this.outNamespaces[i] = XSNamespaceI.unmarshal(this.meg);
            }
        }
    }

    XSNamespace[] getNamespaces() throws SQLException {
        return this.outNamespaces;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
