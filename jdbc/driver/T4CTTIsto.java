package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIsto.class */
final class T4CTTIsto extends T4CTTIfun {
    static final int STOMFDBA = 1;
    static final int STOMFACA = 2;
    static final int STOMFALO = 4;
    static final int STOMFSHU = 8;
    static final int STOMFFRC = 16;
    static final int STOMFPOL = 32;
    static final int STOMFABO = 64;
    static final int STOMFATX = 128;
    static final int STOMFLTX = 256;
    static final int STOSDONE = 1;
    static final int STOSINPR = 2;
    static final int STOSERR = 3;
    private int inmode;
    private int outmode;

    T4CTTIsto(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.inmode = 0;
        this.outmode = 0;
    }

    void doOV6STRT(int mode) throws SQLException, IOException {
        setFunCode((short) 48);
        this.inmode = mode;
        this.outmode = 0;
        doRPC();
    }

    void doOV6STOP(int mode) throws SQLException, IOException {
        setFunCode((short) 49);
        this.inmode = mode;
        this.outmode = 0;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalSWORD(this.inmode);
        this.meg.marshalPTR();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.outmode = (int) this.meg.unmarshalUB4();
        if (this.outmode == 3) {
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
