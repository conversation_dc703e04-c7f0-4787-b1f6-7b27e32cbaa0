package oracle.jdbc.driver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIfunCodes.class */
public class T4CTTIfunCodes {
    public static final short OOPEN = 2;
    public static final short OFETCH = 5;
    public static final short OCLOSE = 8;
    public static final short OLOGOFF = 9;
    public static final short OCOMON = 12;
    public static final short OCOMOFF = 13;
    public static final short OCOMMIT = 14;
    public static final short OROLLBACK = 15;
    public static final short OCANCEL = 20;
    public static final short ODSCRARR = 43;
    public static final short OVERSION = 59;
    public static final short OV6STRT = 48;
    public static final short OV6STOP = 49;
    public static final short OK2RPC = 67;
    public static final short OALL7 = 71;
    public static final short OSQL7 = 74;
    public static final short OEXFEN = 78;
    public static final short O3LOGON = 81;
    public static final short O3LOGA = 82;
    public static final short OKOD = 92;
    public static final short OALL8 = 94;
    public static final short OLOBOPS = 96;
    public static final short ODNY = 98;
    public static final short OTXSE = 103;
    public static final short OTXEN = 104;
    public static final short OCCA = 105;
    public static final short O80SES = 107;
    public static final short ODSY = 119;
    public static final short OAUTH = 115;
    public static final short OSESSKEY = 118;
    public static final short OCANA = 120;
    public static final short OKPN = 125;
    public static final short OOTCM = 127;
    public static final short OSCID = 135;
    public static final short OSPFPPUT = 138;
    public static final short OKPFC = 139;
    public static final short OPING = 147;
    public static final short OKEYVAL = 154;
    public static final short OXSSCS = 155;
    public static final short OXSSRO = 156;
    public static final short OXSSPO = 157;
    public static final short OAQEQ = 121;
    public static final short OAQDQ = 122;
    public static final short OAQGPS = 132;
    public static final short OAQLS = 126;
    public static final short OAQXQ = 145;
    public static final short OSESSGET = 162;
    public static final short OSESSRLS = 163;
    public static final short OSSTEMPLATE = 164;
    public static final short OQCSTA = 167;
    public static final short OQCID = 168;
    public static final short OXSNSO = 172;
    public static final short OXSNS = 178;
    public static final short OXSSYNC = 176;
    public static final short OXSATT = 180;
    public static final short OXSCRE = 179;
    public static final short OXSDET = 181;
    public static final short OXSDES = 182;
    public static final short OXSSET = 183;
    public static final short OSESSSTATE = 176;
    public static final short OAPPCONTREPLAY = 177;
    public static final short OAQENQ = 184;
    public static final short OAQDEQ = 185;
    public static final short OAQEMNDEQ = 186;
    public static final short OAQNFY = 187;
    public static final short OCHUNKINFO = 190;
    public static final short OCLFEATURES = 191;
    public static final short OSAGA = 195;
    public static final short ODPP = 128;
    public static final short ODPMOP = 130;
    public static final short ODPLS = 129;
    public static final short OPLBGN = 199;
    public static final short OPLEND = 200;
    public static final short OPLOPN = 203;
    static final byte OERFSPND = 1;
    static final byte OERFATAL = 2;
    static final byte OERFPLSW = 4;
    static final byte OERFUPD = 8;
    static final byte OERFEXIT = 16;
    static final byte OERFNCF = 32;
    static final byte OERFRDONLY = 64;
    static final short OERFSBRK = 128;
    static final byte OERwANY = 1;
    static final byte OERwTRUN = 2;
    static final byte OERwLICM = 2;
    static final byte OERwNVIC = 4;
    static final byte OERwITCE = 8;
    static final byte OERwUDnW = 16;
    static final byte OERwCPER = 32;
    static final byte OERwPLEX = 64;
    static final short ORACLE8_PROD_VERSION = 8030;
    static final short ORACLE81_PROD_VERSION = 8100;
    static final short MIN_OVERSION_SUPPORTED = 7230;
    static final short MIN_TTCVER_SUPPORTED = 4;
    static final short V8_TTCVER_SUPPORTED = 5;
    static final short MAX_TTCVER_SUPPORTED = 6;
    static final int REFCURSOR_SIZE = 5;
    static final byte OCQCINV = 1;
    static final byte OCOSPID = 2;
    static final byte OCTRCEVT = 3;
    static final byte OCSESSRET = 4;
    static final byte OCSSYNC = 5;
    static final byte OCXSSS = 6;
    static final byte OCLTXID = 7;
    static final byte OCAPPCONTCTL = 8;
    static final byte OCXSSS2 = 9;
    static final byte OSESSSIGN = 10;
    static final byte OCSHRDKEY = 11;
    static final byte MAX_OCFN = 11;
    static final int TTIEOCFRO = 1;
    static final int TTIEOCCUR = 2;
    static final int TTIEOCDON = 4;
    static final int TTIEOCECT = 8;
    static final int TTIEOCFSE = 16;
    static final int TTIEOCFPR = 32;
    static final int TTIEOCFSW = 64;
    static final int TTIEOCFMF = 128;
    static final int TTIEOCETS = 256;
    static final int TTIEOCFCP = 512;
    static final int TTIEOCFTI = 1024;
    static final int TTIEOCFIV = Integer.MIN_VALUE;
    static final int TTIEOCF_DROP_WHEN_RETURNED = 2048;
    static final int TTIEOCREL = 32768;
}
