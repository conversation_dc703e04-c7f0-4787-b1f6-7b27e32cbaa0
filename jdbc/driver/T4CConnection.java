package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStream;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.Duration;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.TimeZone;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.zip.CRC32;
import oracle.jdbc.AccessToken;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.LogicalTransactionIdEventListener;
import oracle.jdbc.NotificationRegistration;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessage;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.clio.annotations.Debug;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DirectPathBufferMarshaler;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.driver.ResultSetCache;
import oracle.jdbc.driver.oauth.AccessTokenBuilder;
import oracle.jdbc.driver.resource.ResourceType;
import oracle.jdbc.driver.utils.StringUtils;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSMessage;
import oracle.jdbc.internal.JMSMessageProperties;
import oracle.jdbc.internal.JMSNotificationRegistration;
import oracle.jdbc.internal.KeywordValue;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleLargeObject;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.internal.XSEventListener;
import oracle.jdbc.internal.XSKeyval;
import oracle.jdbc.internal.XSNamespace;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSecureId;
import oracle.jdbc.internal.XSSessionParameters;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.pool.OraclePooledConnection;
import oracle.jdbc.pool.OracleShardingKeyImpl;
import oracle.jdbc.xa.OracleXAResource;
import oracle.net.ano.AnoServices;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.Communication;
import oracle.net.ns.NSProtocol;
import oracle.net.ns.NSProtocolNIO;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.nt.AsyncOutboundTimeoutHandler;
import oracle.net.nt.ConnectDescription;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.TcpNTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;
import oracle.net.resolver.TimeUnitSuffixUtility;
import oracle.sql.BLOB;
import oracle.sql.BfileDBAccess;
import oracle.sql.BlobDBAccess;
import oracle.sql.CLOB;
import oracle.sql.ClobDBAccess;
import oracle.sql.Datum;
import oracle.sql.LobPlsqlUtil;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TypeDescriptor;
import oracle.sql.ZONEIDMAP;
import oracle.sql.converter.CharacterSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CConnection.class */
class T4CConnection extends PhysicalConnection implements BfileDBAccess, BlobDBAccess, ClobDBAccess {
    static final short MIN_TTCVER_SUPPORTED = 4;
    static final short V8_TTCVER_SUPPORTED = 5;
    static final short MAX_TTCVER_SUPPORTED = 6;
    static final long RESULTSET_CACHE_BLOCK_SIZE_FOR_STAT = 512;
    private static final String CLASS_NAME;
    static final int DEFAULT_LONG_PREFETCH_SIZE = 4080;
    static final String DEFAULT_CONNECT_STRING = "localhost:1521:orcl";
    static final String DEFAULT_CACHE_LOCATION = "net8://?PR=0";
    static final int REFCURSOR_SIZE = 5;
    static final byte[] EMPTY_BYTE;
    boolean isTTIRENEGReceived;
    boolean isLoggedOn;
    ConnectionPath currentConnectionPath;
    private boolean useZeroCopyIO;
    boolean useLobPrefetch;
    private boolean useDynamicVectorIO;
    private boolean svrSupportsRequests;
    private boolean svrSupportsExplicitRequestBit;
    private OpaqueString password;
    private Communication net;
    int eocs;
    private NTFEventListener[] ltxidListeners;
    private final Monitor ltxidListenersMonitor;
    private NTFEventListener[] xsListeners;
    private final Monitor xsListenersMonitor;
    boolean readAsNonStream;
    T4CTTIoer11 oer;
    T4CMAREngine mare;
    T4C8TTIpro pro;
    T4C8TTIdty dty;
    T4CTTIrxd rxd;
    T4CTTIsto sto;
    T4CTTIspfp spfp;
    T4CTTIoauthenticate auth;
    T4C8Odscrarr describe;
    T4C8Odsy describeTbl;
    T4C8Oall all8;
    T4C8Oclose close8;
    T4CTTIoclfeatures oclFeatures;
    T4C7Ocommoncall commoncall;
    T4Caqe aqe;
    T4Caqdq aqdq;
    T4CTTIoaqenq oaqenq;
    T4CTTIoaqdeq oaqdeq;
    T4CTTIkpdnrdeq kpdnrdeq;
    T4CTTCaqa aqa;
    T4C8TTIBfile bfileMsg;
    T4C8TTIBlob blobMsg;
    T4C8TTIClob clobMsg;
    T4C8TTIJson jsonMsg;
    T4C8TTIBlob lobMsg;
    T4CTTIoses oses;
    T4CTTIoping oping;
    T4CTTIokpn okpn;
    T4CTTIOtxen otxen;
    T4CTTIOtxse otxse;
    T4CTTIOtxse pigOtxse;
    T4CTTIk2rpc k2rpc;
    T4CTTIoscid oscid;
    T4CTTIokeyval okeyval;
    T4CTTIoxsscs oxsscs;
    T4CTTIoxssro oxssro;
    T4CTTIoxsspo oxsspo;
    T4CTTIxsnsop xsnsop;
    T4CTTIosesstate osesstate;
    T4CTTIoappcontreplay oappcontreplay;
    T4CTTIosesstemplate osesstemplate;
    T4CTTIoxsns xsnsop2;
    T4CTTIoxscre oxscre;
    T4CTTIoxsdes oxsdes;
    T4CTTIoxsdet oxsdet;
    T4CTTIoxsatt oxsatt;
    T4CTTIoxsset oxsset;
    T4CTTIosessrls osessrls;
    T4CTTIosessget osessget;
    T4CTTIocsessret ocsessret;
    T4CTTIoqcid oqcid;
    T4CTTIkscn kpdqidcscn;
    T4CTTIOqcsta oqcsta;
    T4CTTIochunkinfo ochunkinfo;
    T4CTTIochunkinfo piggyBackOchunkinfo;
    T4CTTIodpp odpp;
    T4CTTIodpmop odpmop;
    T4CTTIodpls odpls;
    T4CTTIosaga osaga;
    int[] cursorToClose;
    int cursorToCloseOffset;
    int lastCursorToCloseOffset;
    int[] queryToClose;
    int queryToCloseOffset;
    byte[] tempLobsToFree;
    int tempLobFreeOffset;
    int tempLobFreeCount;
    int[] lusFunctionId2;
    byte[][] lusSessionId2;
    KeywordValueLong[][] lusInKeyVal2;
    int[] lusInFlags2;
    int lusOffset2;
    private long osessstateFlags;
    boolean shardingDriverMode;
    boolean tcDriverMode;
    static final int DIRECTIVE_REPLAY_ENABLED = 4;
    EnumSet<ReplayMode> replayModes;
    OracleConnection.EndReplayCallback endReplayCallback;
    oracle.jdbc.internal.ReplayContext[] oappcontreplayContextsArr;
    int oappcontreplayOffset;
    ReplayContext nonRequestDisableReplayCxt;
    DatabaseSessionState sessionState;
    DatabaseSessionState sessionStateOut;
    int sessionId;
    int serialNumber;
    private boolean switchFromProxySession;
    private boolean disableCommitOptimizationOnPDBChange;
    byte negotiatedTTCversion;
    byte[] serverRuntimeCapabilities;
    private byte[] serverCompileTimeCapabilities;
    Hashtable<String, Namespace> namespaces;
    byte[] internalName;
    byte[] externalName;
    static final int FREE = -1;
    static final int SEND = 1;
    static final int RECEIVE = 2;
    int pipeState;
    boolean sentCancel;
    int maxNonStreamBindByteSize;
    boolean statementCancel;
    String databaseUniqueIdentifier;
    String versionNumberString;
    int majorNumber;
    int minorNumber;
    protected short lastExecutedFunCode;
    static final Map<String, String[]> cachedVersionTable;
    private static final T4CTTICookieCache DATABASE_NEGOTIATED_INFORMATION;
    byte currentTTCSeqNumber;
    byte lastPiggyBackCursorCloseSeqNumber;
    short[] ttiList;
    final byte[] tmpBytes128;
    boolean isO7L_MRExposed;
    private short executingRPCFunctionCode;
    String shardingKey;
    String superShardingKey;
    String chunkName;
    private boolean isDatabaseShutdown;
    private boolean isProxySessionLogoff;
    private boolean isFeatureTrackingSupported;
    int releasedSessID;
    int releasedSerial;
    boolean writeBufferIsDirty;
    T4CDirectPathPreparedStatement dppstmt;
    boolean isHybrid;
    private Pipeline pipeline;
    private int asyncCursorCount;
    private int maximumCursorCount;
    private int computedAsyncCursorLimit;
    T4CSessionlessTransaction sessionlessTxn;
    private boolean enableResetState;
    static final String RESET_STATE_PROPERTY = "AUTH_RESET_STATE";
    static final int RESET_STATE_NONE = 0;
    static final int RESET_STATE_LEVEL1 = 1;
    static final int RESET_STATE_LEVEL2 = 2;
    static final int RESET_STATE_AUTO = 3;
    private Properties sessionPropertiesAtLogon;
    private boolean needToResetSessionProperties;
    private boolean autocommitAtLogon;
    private boolean readOnlyAtLogon;
    private int txnLevelAtLogon;
    private int defaultRowPrefetchAtLogon;
    private int defaultExecuteBatchAtLogon;
    private boolean reportRemarksAtLogon;
    private boolean includeSynonymsAtLogon;
    private String[] endToEndValuesAtLogon;
    private boolean endToEndAnyChangedAtLogon;
    private Properties clientInfoAtLogon;
    private boolean restrictGettablesAtLogon;
    private boolean fixedStringAtLogon;
    private boolean defaultncharAtLogon;
    private TimeZone defaultTimeZoneAtLogon;
    private String sessionTimeZoneAtLogon;
    private String databaseTimeZoneAtLogon;
    private ZoneId databaseZoneIdAtLogon;
    private ZoneId sessionZoneIdAtLogon;
    private Calendar dbTzCalendarAtLogon;
    private String currentSchemaAtLogon;
    private DatabaseSessionState sessionStateAtLogon;
    private String executingRPCSQL;
    private static final String UCP_THREAD_NAME_PREFIX = "UCP-worker-thread-";
    static final String OCSID_MODULE = "OCSID.MODULE";
    static final String OCSID_ACTION = "OCSID.ACTION";
    static final String OCSID_CLIENT_ID = "OCSID.CLIENTID";
    static final String OCSID_CLIENT_INFO = "OCSID.CLIENT_INFO";
    static final String E2E_CONTEXT_MODULE = "E2E_CONTEXT.MODULE";
    static final String E2E_CONTEXT_ACTION = "E2E_CONTEXT.ACTION";
    static final String E2E_CONTEXT_CLIENT_ID = "E2E_CONTEXT.CLIENT_IDENTIFIER";
    static final String E2E_CONTEXT_CLIENT_INFO = "E2E_CONTEXT.CLIENT_INFO";
    private final CRC32 checksumEngine;
    private final Hashtable<Long, Integer> tempLobRefCount;
    private boolean needsToBeClosed;
    static final int MAX_SIZE_VSESSION_OSUSER = 128;
    static final int MAX_SIZE_VSESSION_PROCESS = 24;
    static final int MAX_SIZE_VSESSION_MACHINE = 64;
    static final int MAX_SIZE_VSESSION_TERMINAL = 30;
    static final int MAX_SIZE_VSESSION_PROGRAM = 84;
    private ResultSetCache resultSetCache;
    private ArrayList<Long> resultSetCacheLocalInvalidations;
    boolean isServerBigSCN;
    static final /* synthetic */ boolean $assertionsDisabled;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CConnection$ConnectionPath.class */
    enum ConnectionPath {
        NORMAL,
        COOKIE,
        TTIINIT
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CConnection$ReplayMode.class */
    enum ReplayMode {
        RUNTIME_REPLAY_ENABLED,
        RUNTIME_OR_REPLAYING_STATIC,
        NONREQUEST,
        REPLAYING
    }

    static {
        int size;
        $assertionsDisabled = !T4CConnection.class.desiredAssertionStatus();
        CLASS_NAME = T4CConnection.class.getName();
        EMPTY_BYTE = new byte[0];
        cachedVersionTable = new Hashtable();
        try {
            size = Integer.parseInt(getSystemPropertyTTCCookieCacheSize());
        } catch (Exception e) {
            size = Integer.parseInt("50");
        }
        DATABASE_NEGOTIATED_INFORMATION = new T4CTTICookieCache(size);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public short getExecutingRPCFunctionCode() {
        return this.executingRPCFunctionCode;
    }

    void setExecutingRPCFunctionCode(short executingRPCFunctionCode) {
        this.executingRPCFunctionCode = executingRPCFunctionCode;
    }

    void setExecutingRPCSQL(String executingRPCSQL) {
        this.executingRPCSQL = executingRPCSQL;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public String getExecutingRPCSQL() {
        return this.executingRPCSQL;
    }

    /* JADX WARN: Type inference failed for: r1v75, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v77, types: [oracle.jdbc.internal.KeywordValueLong[], oracle.jdbc.internal.KeywordValueLong[][]] */
    T4CConnection(String url, @Blind(PropertiesBlinder.class) Properties info, OracleDriverExtension ext) throws SQLException {
        super(url, info, ext);
        this.currentConnectionPath = ConnectionPath.NORMAL;
        this.ltxidListeners = new NTFEventListener[0];
        this.ltxidListenersMonitor = Monitor.newInstance();
        this.xsListeners = new NTFEventListener[0];
        this.xsListenersMonitor = Monitor.newInstance();
        this.sessionState = null;
        this.sessionStateOut = null;
        this.disableCommitOptimizationOnPDBChange = false;
        this.pipeState = -1;
        this.sentCancel = false;
        this.statementCancel = true;
        this.versionNumberString = null;
        this.majorNumber = 0;
        this.minorNumber = 0;
        this.lastExecutedFunCode = (short) 0;
        this.currentTTCSeqNumber = (byte) 0;
        this.lastPiggyBackCursorCloseSeqNumber = (byte) 0;
        this.tmpBytes128 = new byte[128];
        this.isO7L_MRExposed = true;
        this.executingRPCFunctionCode = (short) 0;
        this.shardingKey = null;
        this.superShardingKey = null;
        this.chunkName = null;
        this.isDatabaseShutdown = false;
        this.isProxySessionLogoff = false;
        this.isFeatureTrackingSupported = false;
        this.dppstmt = null;
        this.isHybrid = false;
        this.enableResetState = false;
        this.sessionPropertiesAtLogon = null;
        this.needToResetSessionProperties = false;
        this.autocommitAtLogon = this.defaultautocommit;
        this.readOnlyAtLogon = false;
        this.txnLevelAtLogon = 2;
        this.defaultRowPrefetchAtLogon = 10;
        this.defaultExecuteBatchAtLogon = 1;
        this.reportRemarksAtLogon = false;
        this.includeSynonymsAtLogon = false;
        this.endToEndValuesAtLogon = this.arrayOfNullStrings;
        this.endToEndAnyChangedAtLogon = false;
        this.clientInfoAtLogon = null;
        this.restrictGettablesAtLogon = false;
        this.fixedStringAtLogon = false;
        this.defaultncharAtLogon = false;
        this.defaultTimeZoneAtLogon = null;
        this.sessionTimeZoneAtLogon = null;
        this.databaseTimeZoneAtLogon = null;
        this.databaseZoneIdAtLogon = null;
        this.sessionZoneIdAtLogon = null;
        this.dbTzCalendarAtLogon = null;
        this.currentSchemaAtLogon = null;
        this.sessionStateAtLogon = null;
        this.executingRPCSQL = null;
        this.checksumEngine = new CRC32();
        this.tempLobRefCount = new Hashtable<>();
        this.isServerBigSCN = false;
        this.cursorToClose = new int[4];
        this.cursorToCloseOffset = 0;
        this.queryToClose = new int[10];
        this.queryToCloseOffset = 0;
        this.tempLobsToFree = new byte[2048];
        this.tempLobFreeOffset = 0;
        this.tempLobFreeCount = 0;
        this.lusFunctionId2 = new int[10];
        this.lusSessionId2 = new byte[10];
        this.lusInKeyVal2 = new KeywordValueLong[10];
        this.lusInFlags2 = new int[10];
        this.lusOffset2 = 0;
        this.replayModes = EnumSet.noneOf(ReplayMode.class);
        this.osessstateFlags = -1L;
        this.endReplayCallback = null;
        this.oappcontreplayOffset = 0;
        this.oappcontreplayContextsArr = null;
        this.minVcsBindSize = 0;
        this.namespaces = new Hashtable<>(5);
        this.currentSchema = null;
        this.ttiList = new short[128];
        this.sessionlessTxn = new T4CSessionlessTransaction(this);
        if (info != null) {
            String tempval = info.getProperty("InternalShardingDriverMode");
            this.shardingDriverMode = tempval != null && tempval.equalsIgnoreCase("true");
            String tempval2 = info.getProperty("InternalTrueCacheDriverMode");
            this.tcDriverMode = tempval2 != null && tempval2.equals("true");
        }
        loadApplicationContextFromProperty();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    final void initializePassword(OpaqueString p) {
        this.password = p;
    }

    private T4CTTICookie getTTCCookie() {
        if (!$assertionsDisabled && this.net.getSessionAttributes().getDatabaseUUID().length() <= 0) {
            throw new AssertionError("Should not be called without a databse uuid");
        }
        Optional<T4CTTICookie> cachedCookie = DATABASE_NEGOTIATED_INFORMATION.get(this.net.getSessionAttributes().getDatabaseUUID());
        return cachedCookie.orElse(null);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void flushRemoteDatabaseTTCCookieCache() {
        DATABASE_NEGOTIATED_INFORMATION.flush();
    }

    private void negotiateTTC(boolean isCookieOptimizationEnabled, T4CTTICookie cookie, boolean ttiInitOptimizationEnabled) throws SQLException, IOException, NumberFormatException {
        if (isCookieOptimizationEnabled && cookie != null) {
            this.currentConnectionPath = ConnectionPath.COOKIE;
            begin(Metrics.ConnectionEvent.TTC_TTICOOKIE_OPTIMIZATION);
            cookieBasedNegotiateSession(cookie);
        } else {
            if (ttiInitOptimizationEnabled) {
                this.currentConnectionPath = ConnectionPath.TTIINIT;
                begin(Metrics.ConnectionEvent.TTC_TTIINIT_OPTIMIZATION);
                ttiInitBasedNegotiateSession();
                return;
            }
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "negotiateTTC", "do normal connect to database", null, null);
            T4CTTICookie newCookie = negotiateSession();
            if (isCookieOptimizationEnabled) {
                if (isLoggable(Level.FINER)) {
                    debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "negotiateTTC", "new cookie for server {0}: {1} ", null, null, StringUtils.byteArrayToHexUnicode(this.net.getSessionAttributes().getDatabaseUUID().getBytes()), newCookie);
                }
                try {
                    DATABASE_NEGOTIATED_INFORMATION.post(this.net.getSessionAttributes().getDatabaseUUID(), newCookie);
                } catch (IllegalStateException e) {
                }
            }
        }
    }

    private final void checkConnectionProperties() {
        if (this.net.getSessionAttributes().getNTAdapter().getNetworkAdapterType() == NTAdapter.NetworkAdapterType.BEQ) {
            if (this.fanEnabled) {
                debug(Level.FINE, SecurityLabel.INTERNAL, CLASS_NAME, "logon", "Disabling {0} as this is not supported when using BEQ", (String) null, (String) null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_FAN_ENABLED);
                this.fanEnabled = false;
            }
            if (this.inbandNotification) {
                debug(Level.FINE, SecurityLabel.INTERNAL, CLASS_NAME, "logon", "Disabling {0} as this is not supported when using BEQ", (String) null, (String) null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_IN_BAND_NOTIFICATION);
                this.inbandNotification = false;
            }
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // oracle.jdbc.driver.PhysicalConnection
    final void logon(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        TimerTask timeoutTask = startLogonTimeout();
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    if (this.isLoggedOn) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0200).fillInStackTrace());
                    }
                    if (this.database == null) {
                        this.database = DEFAULT_CONNECT_STRING;
                    }
                    this.net = new NSProtocolNIO(this.database, createNSProperties(builder), builder == null ? null : builder.getSSLContext(), builder == null ? null : builder.getHostnameResolver(), this.nsDirectBuffer, getDiagnosable(), getTraceEventListener());
                    this.net.setDriverResources(getDriverResources());
                    AccessToken accessToken = getAccessToken(builder, this.net.getConnectDescriptions());
                    connectNetworkSessionProtocol(builder);
                    checkConnectionProperties();
                    if (accessToken == null && this.net.getConnectDescriptions().size() > 1) {
                        accessToken = configureAccessTokenBuilder(builder, this.net.getConnectedDescription()).build();
                    }
                    boolean isCookieOptimizationAvailable = false;
                    T4CTTICookie cachedCookie = null;
                    if (getSecurityInformation().getAuthenticationAdaptor() != SecurityInformation.AuthenticationAdaptorType.RADIUS && this.ttcCookieOptimizationEnabled && this.net.getSessionAttributes().isTTCCookieEnabled() && !this.forceAL32UTF8ForCombinedRoundtrips) {
                        isCookieOptimizationAvailable = true;
                        cachedCookie = getTTCCookie();
                    }
                    debug(Level.FINE, SecurityLabel.INTERNAL, CLASS_NAME, "logon", "cookie optimization enabled? {0}, TTIINIT optimization enabled? {1}", null, null, Boolean.valueOf(this.ttcCookieOptimizationEnabled), Boolean.valueOf(this.combinedInitializationRoundtrips));
                    if (isLoggable(Level.FINER)) {
                        debug(Level.FINER, SecurityLabel.INTERNAL, CLASS_NAME, "logon", "session attributes {0}", (String) null, (String) null, this.net.getSessionAttributes());
                        debug(Level.FINER, SecurityLabel.INTERNAL, CLASS_NAME, "logon", "cookie found? {0}", (String) null, (String) null, cachedCookie != null ? cachedCookie : "no");
                    }
                    if (isDRCPEnabled() && !isClientInitiatedNTFConnection() && net().isTLSEnabled()) {
                        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "logon", "Initiating the TLS renegotiation with pooled auth server before performing authentication.", (String) null, (Throwable) null);
                        net().getSessionAttributes().initTLSRenegotiation();
                    }
                    negotiateTTC(isCookieOptimizationAvailable, cachedCookie, this.combinedInitializationRoundtrips && this.net.getSessionAttributes().isFastAuthenticationOptimizationEnabled());
                    initializeTTC();
                    System.currentTimeMillis();
                    authenticateUserForLogon(accessToken);
                    if (isDRCPEnabled() && !isClientInitiatedNTFConnection() && net().isTLSEnabled()) {
                        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "logon", "Initializing TLS renegotiation with pooled server after successful logon.", (String) null, null);
                        net().getSessionAttributes().initTLSRenegotiation();
                    }
                    initializeSessionInfoAfterLogon();
                    if (this.versionNumberString == null) {
                        initializeDatabaseVersionInfo();
                    }
                    initializeAfterLogon();
                    if (isCookieOptimizationAvailable && getTTCCookie() == null && this.combinedInitializationRoundtrips && this.net.getSessionAttributes().isFastAuthenticationOptimizationEnabled()) {
                        try {
                            DATABASE_NEGOTIATED_INFORMATION.post(this.net.getSessionAttributes().getDatabaseUUID(), T4CTTICookie.builder().databaseRuntimeCapabilities(this.pro.getServerRuntimeCapabilities()).databaseCompileTimeCapabilities(this.pro.getServerCompileTimeCapabilities()).connectionProtocolVersion(this.pro.getProtocolVersion()).databaseCharSet(this.pro.getCharacterSet()).databaseNCharSet(this.pro.getncharCHARSET()).databaseCharSetFlag(this.pro.getFlags()).databasePortage(this.pro.getSvrPortDescription()).build());
                        } catch (IllegalStateException e) {
                        }
                    }
                    if (timeoutTask != null) {
                        timeoutTask.cancel();
                    }
                    logConnectionInfoAfterLogonAlways();
                    initializeResultSetCacheAfterLogonAlways();
                    initializeDRCPAfterLogonAlways();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (Throwable th3) {
                    if (timeoutTask != null) {
                        timeoutTask.cancel();
                    }
                    logConnectionInfoAfterLogonAlways();
                    initializeResultSetCacheAfterLogonAlways();
                    initializeDRCPAfterLogonAlways();
                    throw th3;
                }
            } catch (InterruptedIOException interruptFailure) {
                throw handleLogonInterruptedIOException(interruptFailure, (timeoutTask == null || timeoutTask.cancel()) ? false : true, 0L);
            } catch (NetException e2) {
                throw handleLogonNetException(e2);
            } catch (IOException ex) {
                throw handleLogonIOException(ex, 0L);
            } catch (SQLException se) {
                throw handleLogonSQLException(se);
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void cookieBasedNegotiateSession(T4CTTICookie cookie) throws SQLException, IOException {
        if (!$assertionsDisabled && cookie == null) {
            throw new AssertionError("cookie cannot be null");
        }
        begin(Metrics.ConnectionEvent.TTC_NEGOTIATION);
        this.serverRuntimeCapabilities = cookie.getDatabaseRuntimeCapabilities();
        this.serverCompileTimeCapabilities = cookie.getDatabaseCompileTimeCapabilities();
        getMarshalEngine().proSvrVer = cookie.getConnectionProtocolVersion();
        this.pro = new T4C8TTIpro(this);
        this.pro.initFrom(cookie);
        this.pro.marshal();
        doCharSetNegotiation(this.pro.oVersion, cookie.getDatabaseCharSet(), cookie.getDatabaseNCharSet());
        setCLRBigChunksCapability();
        new T4CTTICookieMsg(this).marshal(cookie);
        this.dty = new T4C8TTIdty(this, cookie.getDatabaseRuntimeCapabilities(), this.logonCap != null && this.logonCap.trim().equals("o3"), this.thinNetUseZeroCopyIO);
        this.dty.marshal();
        setNegotiatedTTCVersion(this.serverCompileTimeCapabilities);
    }

    private void ttiInitBasedNegotiateSession() throws SQLException, IOException {
        this.mare.conv = new DBConversion((short) 873, (short) 873, (short) 2000);
        this.mare.proSvrVer = (short) 6;
        this.mare.types.setFlags((byte) 1);
        this.pro = new T4C8TTIpro(this);
        if (!this.forceAL32UTF8ForCombinedRoundtrips) {
            T4CTTIInitMsg.builder().connection(this).protocolMessage(this.pro).assumedDBServerCharset((short) 873).assumedDBServernCharset((short) 2000).build().marshal();
        } else {
            T4CTTIInitMsg.builder().connection(this).protocolMessage(this.pro).build().marshal();
        }
        this.serverRuntimeCapabilities = T4CTTIInitMsg.ASSUMED_SRV_RT_CAPS;
        this.serverCompileTimeCapabilities = T4CTTIInitMsg.ASSUMED_SRV_CT_CAPS;
        this.dty = new T4C8TTIdty(this, this.serverRuntimeCapabilities, this.logonCap != null && this.logonCap.trim().equals("o3"), this.thinNetUseZeroCopyIO);
        this.dty.marshal();
        this.conversion = this.mare.conv;
        setNegotiatedTTCVersion(T4CTTIInitMsg.ASSUMED_SRV_CT_CAPS);
        setCLRBigChunksCapability();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    final CompletionStage<Void> logonAsync(AbstractConnectionBuilder<?, ?> builder) {
        AsyncOutboundTimeoutHandler loginTimeoutHandler;
        if (this.isLoggedOn) {
            return CompletionStageUtil.failedStage(DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0200).fillInStackTrace());
        }
        if (this.loginTimeout < 1) {
            loginTimeoutHandler = null;
        } else {
            loginTimeoutHandler = AsyncOutboundTimeoutHandler.newScheduledInstance(null, Duration.ofSeconds(this.loginTimeout), new InterruptedIOException("Socket read interrupted"));
        }
        try {
            this.net = new NSProtocolNIO(this.database, createNSProperties(builder), builder == null ? null : builder.getSSLContext(), builder == null ? null : builder.getHostnameResolver(), this.nsDirectBuffer, getDiagnosable(), getTraceEventListener());
            long authStartTime = System.currentTimeMillis();
            AsyncOutboundTimeoutHandler asyncOutboundTimeoutHandler = loginTimeoutHandler;
            AsyncOutboundTimeoutHandler asyncOutboundTimeoutHandler2 = loginTimeoutHandler;
            return connectSessionAsync(builder, loginTimeoutHandler).thenCompose(CompletionStageUtil.normalCompletionHandler(nil -> {
                initializeTTC();
                return authenticateUserForLogonAsync(configureAccessTokenBuilder(builder, this.net.getConnectedDescription()).build());
            })).thenCompose(nil2 -> {
                initializeSessionInfoAfterLogon();
                return this.versionNumberString == null ? initializeDatabaseVersionInfoAsync() : CompletionStageUtil.VOID_COMPLETED_FUTURE;
            }).thenApply(CompletionStageUtil.normalCompletionHandler(nil3 -> {
                Monitor.CloseableLock lock = acquireCloseableLock();
                Throwable th = null;
                try {
                    try {
                        initializeAfterLogon();
                        Void r0 = (Void) null;
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                lock.close();
                            }
                        }
                        return r0;
                    } finally {
                    }
                } catch (Throwable th3) {
                    if (lock != null) {
                        if (th != null) {
                            try {
                                lock.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    throw th3;
                }
            })).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(Exception.class, logonException -> {
                if (logonException instanceof InterruptedIOException) {
                    InterruptedIOException interruptFailure = (InterruptedIOException) logonException;
                    boolean isInterruptedByLoginTimeout = (asyncOutboundTimeoutHandler == null || asyncOutboundTimeoutHandler.cancelTimeout()) ? false : true;
                    throw handleLogonInterruptedIOException(interruptFailure, isInterruptedByLoginTimeout, authStartTime);
                }
                if (logonException instanceof NetException) {
                    throw handleLogonNetException((NetException) logonException);
                }
                if (logonException instanceof IOException) {
                    throw handleLogonIOException((IOException) logonException, 0L);
                }
                if (logonException instanceof SQLException) {
                    throw handleLogonSQLException((SQLException) logonException);
                }
                throw logonException;
            })).handle(CompletionStageUtil.completionHandler(() -> {
                if (asyncOutboundTimeoutHandler2 != null) {
                    asyncOutboundTimeoutHandler2.cancelTimeout();
                }
                logConnectionInfoAfterLogonAlways();
                initializeResultSetCacheAfterLogonAlways();
                initializeDRCPAfterLogonAlways();
                if (this.net.getSessionAttributes().isConnected()) {
                    this.net.restoreBlockingMode();
                }
            }));
        } catch (NetException netException) {
            try {
                return CompletionStageUtil.failedStage(handleLogonNetException(netException));
            } catch (SQLException sqlException) {
                return CompletionStageUtil.failedStage(sqlException);
            }
        }
    }

    private void initializeAfterLogon() throws SQLException, NumberFormatException, IOException {
        this.isLoggedOn = true;
        disableTempLobRefCntForOracle10();
        initializeResultSetCacheAfterLogon();
        int rowPrefetchDirective = Integer.parseInt(this.sessionProperties.getProperty("AUTH_CLIENT_PREFETCH_ROWS", "-1"));
        if (rowPrefetchDirective != -1 && !this.isRowPrefetchSetExplicitly) {
            this.defaultRowPrefetch = rowPrefetchDirective;
        }
        doAddFeature(OracleConnection.ClientFeature.THIN_DRIVER);
        Properties properties = new Properties();
        properties.setProperty("DatabaseProductVersion", String.valueOf((int) this.versionNumber));
        properties.setProperty("URL", getURL());
        debug(Level.CONFIG, SecurityLabel.CONFIG, CLASS_NAME, "initializeAfterLogon", "properties={0}. ", (String) null, (String) null, new PropertiesBlinder().blind(properties));
        this.connectionDiagnosable.addConfig(properties);
        checkAndEnableResetStateSupport();
        this.pipeline = Pipeline.create(this);
    }

    private void checkAndEnableResetStateSupport() throws SQLException, NumberFormatException {
        int wiredValue;
        String resetStateLevel = getServerSessionInfo(RESET_STATE_PROPERTY);
        if (resetStateLevel != null && !"".equals(resetStateLevel)) {
            try {
                wiredValue = Integer.parseInt(resetStateLevel);
            } catch (NumberFormatException e) {
                wiredValue = 0;
            }
            if ((wiredValue & 2) == 2) {
                debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "checkAndEnableResetStateSupport", "RESET_STATE=LEVEL2, enabling Reset State support on {0}", (String) null, (String) null, this);
                this.enableResetState = true;
                saveInitialClientState();
            }
        }
    }

    private void saveInitialClientState() throws SQLException {
        if (this.sessionPropertiesAtLogon == null) {
            this.sessionPropertiesAtLogon = new Properties();
        }
        this.sessionPropertiesAtLogon.putAll(this.sessionProperties);
        this.needToResetSessionProperties = false;
        this.autocommitAtLogon = this.autocommit;
        this.readOnlyAtLogon = this.readOnly;
        this.txnLevelAtLogon = this.txnLevel;
        this.defaultRowPrefetchAtLogon = this.defaultRowPrefetch;
        this.defaultExecuteBatchAtLogon = this.defaultExecuteBatch;
        this.reportRemarksAtLogon = this.reportRemarks;
        this.includeSynonymsAtLogon = this.includeSynonyms;
        if (this.endToEndValues != this.arrayOfNullStrings) {
            this.endToEndValuesAtLogon = new String[4];
            System.arraycopy(this.endToEndValues, 0, this.endToEndValuesAtLogon, 0, 4);
        }
        this.endToEndAnyChangedAtLogon = false;
        this.clientInfoAtLogon = (Properties) this.clientInfo.clone();
        this.restrictGettablesAtLogon = this.restrictGettables;
        this.fixedStringAtLogon = this.fixedString;
        this.defaultncharAtLogon = this.defaultnchar;
        this.defaultTimeZoneAtLogon = this.defaultTimeZone;
        this.sessionTimeZoneAtLogon = this.sessionTimeZone;
        this.databaseTimeZoneAtLogon = this.databaseTimeZone;
        this.databaseZoneIdAtLogon = this.databaseZoneId;
        this.sessionZoneIdAtLogon = this.sessionZoneId;
        this.dbTzCalendarAtLogon = this.dbTzCalendar;
        this.currentSchemaAtLogon = this.currentSchema;
        if (this.sessionState != null) {
            this.sessionStateAtLogon = this.sessionState.copy();
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "saveInitialClientState", "On {0}, saving initial session state: {1}", null, null, this, this.sessionStateAtLogon);
    }

    private void resetToInitialClientState() throws SQLException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "resetToInitialClientState", "Reset State done at endRequest on {0}", (String) null, (String) null, this);
        if (this.needToResetSessionProperties) {
            Properties props = new Properties();
            props.putAll(this.sessionPropertiesAtLogon);
            this.sessionProperties = props;
        }
        this.autocommit = this.autocommitAtLogon;
        this.readOnly = this.readOnlyAtLogon;
        this.txnLevel = this.txnLevelAtLogon;
        this.defaultRowPrefetch = this.defaultRowPrefetchAtLogon;
        this.defaultExecuteBatch = this.defaultExecuteBatchAtLogon;
        this.reportRemarks = this.reportRemarksAtLogon;
        this.includeSynonyms = this.includeSynonymsAtLogon;
        if (this.endToEndValuesAtLogon != this.arrayOfNullStrings) {
            this.endToEndValues = new String[4];
            System.arraycopy(this.endToEndValuesAtLogon, 0, this.endToEndValues, 0, 4);
        } else {
            this.endToEndValues = this.arrayOfNullStrings;
        }
        this.endToEndAnyChanged = false;
        if (this.clientInfoAtLogon.isEmpty()) {
            this.clientInfo.clear();
        } else {
            this.clientInfo.clear();
            this.clientInfo.putAll(this.clientInfoAtLogon);
        }
        this.restrictGettables = this.restrictGettablesAtLogon;
        this.fixedString = this.fixedStringAtLogon;
        this.defaultnchar = this.defaultncharAtLogon;
        this.defaultTimeZone = this.defaultTimeZoneAtLogon;
        this.sessionTimeZone = this.sessionTimeZoneAtLogon;
        this.databaseTimeZone = this.databaseTimeZoneAtLogon;
        this.databaseZoneId = this.databaseZoneIdAtLogon;
        this.sessionZoneId = this.sessionZoneIdAtLogon;
        this.dbTzCalendar = this.dbTzCalendarAtLogon;
        this.currentSchema = this.currentSchemaAtLogon;
        this.needToResetSessionProperties = false;
        if (this.sessionStateAtLogon != null) {
            this.sessionState = this.sessionStateAtLogon.copy();
        } else {
            this.sessionState = null;
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "resetToInitialClientState", "On {0}, resetting session state to: {1}", null, null, this, this.sessionState);
    }

    private TimerTask startLogonTimeout() {
        if (this.loginTimeout < 1) {
            return null;
        }
        Thread logonThread = Thread.currentThread();
        logonThread.getClass();
        return TimeoutInterruptHandler.scheduleTask(logonThread::interrupt, this.loginTimeout * 1000);
    }

    private SQLException handleLogonInterruptedIOException(InterruptedIOException logonFailure, boolean isInterruptedByLoginTimeout, long authStartTime) {
        if (isInterruptedByLoginTimeout) {
            Thread.interrupted();
            SQLException timeoutException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_LOGON_TIMEOUT_EXPIRED).fillInStackTrace();
            setInitCauseForLogonTimeout(timeoutException, logonFailure);
            return timeoutException;
        }
        return handleLogonIOException(logonFailure, authStartTime);
    }

    private void setInitCauseForLogonTimeout(SQLException timeoutException, InterruptedIOException logonFailure) {
        try {
            Throwable actualCause = this.net.lastConnectException();
            if (actualCause != null) {
                timeoutException.initCause(actualCause);
            }
            if (logonFailure.getSuppressed() == null || logonFailure.getSuppressed().length == 0) {
                return;
            }
            for (Throwable t : logonFailure.getSuppressed()) {
                timeoutException.addSuppressed(t);
            }
        } catch (Exception e) {
            timeoutException.addSuppressed(e);
        }
    }

    private SQLException handleLogonNetException(NetException logonFailure) throws SQLException {
        InetAddress localAddr;
        int localPort;
        if ((logonFailure.getErrorNumber() == 17902 || logonFailure.getErrorNumber() == 17800 || logonFailure.getErrorNumber() == 17909) && this.net != null && this.net.getSessionAttributes().getcOption() != null) {
            String serverType = oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_VSESSION_TERMINAL_DEFAULT;
            try {
                NVPair nvp = new NVFactory().createNVPair(this.net.getSessionAttributes().getcOption().conn_data.toString());
                NVNavigator nav = new NVNavigator();
                NVPair connDataNVP = nav.findNVPair(nvp, "CONNECT_DATA");
                NVPair servernvp = nav.findNVPair(connDataNVP, "SERVER");
                if (servernvp != null) {
                    serverType = servernvp.getAtom();
                }
            } catch (NLException e) {
            }
            try {
                localAddr = this.net.getSessionAttributes().getNTAdapter().getSocketChannel().socket().getLocalAddress();
                localPort = this.net.getSessionAttributes().getNTAdapter().getSocketChannel().socket().getLocalPort();
            } catch (UnsupportedOperationException e2) {
                localAddr = InetAddress.getLoopbackAddress();
                localPort = 0;
            }
            Object[] objArr = new Object[17];
            objArr[0] = "client";
            objArr[1] = localAddr;
            objArr[2] = String.valueOf(localPort);
            objArr[3] = this.net.getSessionAttributes().getcOption().host;
            objArr[4] = String.valueOf(this.net.getSessionAttributes().getcOption().port);
            objArr[5] = this.net.getSessionAttributes().getcOption().protocol;
            objArr[6] = this.net.getSessionAttributes().getcOption().service_name;
            objArr[7] = "client";
            objArr[8] = serverType;
            objArr[9] = this.thinVsessionProgram;
            objArr[10] = this.sessionProperties != null ? getServerSessionInfo("AUTH_SERVER_PID") : null;
            objArr[11] = this.sessionProperties != null ? getServerSessionInfo("AUTH_SESSION_ID") : null;
            objArr[12] = this.sessionProperties != null ? getServerSessionInfo("AUTH_SERIAL_NUM") : null;
            objArr[13] = getUserName() != null ? getUserName() : null;
            objArr[14] = this.lastExecutedFunCode != 0 ? DatabaseFunction.valueOfFunctionCode(this.lastExecutedFunCode).getDescription() : null;
            objArr[15] = getNetConnectionId();
            objArr[16] = getSecurityInformation() != null ? ", nne_encryption=" + getSecurityInformation().getEncryptionAlgorithm() + ", nne_checksumming=" + getSecurityInformation().getChecksummingAlgorithm() + ", authentication=" + getSecurityInformation().getAuthenticationAdaptor() : null;
            logonFailure = (NetException) new NetException(NetException.DATABASE_CONNECTION_LOST, null, false, objArr).initCause(logonFailure).fillInStackTrace();
        }
        try {
            logonFailure.setNetConnectionId(getNetConnectionId());
        } catch (SQLException e3) {
        }
        if (Thread.currentThread().isInterrupted()) {
            Thread.interrupted();
        }
        return (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) logonFailure).fillInStackTrace();
    }

    private SQLException handleLogonIOException(IOException logonFailure, long authStartTime) {
        try {
            handleIOException(logonFailure);
            long authenticationLapse = authStartTime == 0 ? 0L : System.currentTimeMillis() - authStartTime;
            String authenticationLapseMessage = String.format("%s, Authentication lapse %d ms.", logonFailure.getMessage(), Long.valueOf(authenticationLapse));
            IOException newEx = new IOException(authenticationLapseMessage, logonFailure);
            return (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), newEx).fillInStackTrace();
        } catch (SQLException handlingFailure) {
            return handlingFailure;
        }
    }

    private void initializeTTC() throws SQLException, IOException {
        this.all8 = new T4C8Oall(this);
        this.okpn = new T4CTTIokpn(this);
        this.close8 = new T4C8Oclose(this);
        this.oclFeatures = new T4CTTIoclfeatures(this);
        this.sto = new T4CTTIsto(this);
        this.spfp = new T4CTTIspfp(this);
        this.commoncall = new T4C7Ocommoncall(this);
        this.describe = new T4C8Odscrarr(this);
        this.describeTbl = new T4C8Odsy(this);
        this.bfileMsg = new T4C8TTIBfile(this);
        this.blobMsg = new T4C8TTIBlob(this);
        this.clobMsg = new T4C8TTIClob(this);
        this.jsonMsg = new T4C8TTIJson(this);
        this.lobMsg = new T4C8TTIBlob(this);
        this.otxen = new T4CTTIOtxen(this);
        this.otxse = new T4CTTIOtxse(this);
        this.pigOtxse = new T4CTTIOtxse(this);
        this.oping = new T4CTTIoping(this);
        this.k2rpc = new T4CTTIk2rpc(this);
        this.oses = new T4CTTIoses(this);
        this.okeyval = new T4CTTIokeyval(this);
        this.oxssro = new T4CTTIoxssro(this);
        this.oxsspo = new T4CTTIoxsspo(this);
        this.oxsscs = new T4CTTIoxsscs(this);
        this.oxscre = new T4CTTIoxscre(this);
        this.oxsdes = new T4CTTIoxsdes(this);
        this.oxsatt = new T4CTTIoxsatt(this);
        this.xsnsop = new T4CTTIxsnsop(this);
        this.xsnsop2 = new T4CTTIoxsns(this);
        this.oxsdet = new T4CTTIoxsdet(this);
        this.oxsset = new T4CTTIoxsset(this);
        this.aqe = new T4Caqe(this);
        this.aqdq = new T4Caqdq(this);
        this.oscid = new T4CTTIoscid(this);
        this.osessrls = new T4CTTIosessrls(this);
        this.ocsessret = new T4CTTIocsessret(this);
        this.osessget = new T4CTTIosessget(this);
        this.oaqenq = new T4CTTIoaqenq(this);
        this.oaqdeq = new T4CTTIoaqdeq(this);
        this.aqa = new T4CTTCaqa(this);
        this.kpdnrdeq = new T4CTTIkpdnrdeq(this);
        this.osesstate = new T4CTTIosesstate(this);
        this.oappcontreplay = new T4CTTIoappcontreplay(this);
        this.osesstemplate = new T4CTTIosesstemplate(this);
        this.kpdqidcscn = kscnForByteLength();
        this.oqcid = new T4CTTIoqcid(this);
        this.ochunkinfo = new T4CTTIochunkinfo(this);
        this.piggyBackOchunkinfo = new T4CTTIochunkinfo(this);
        this.odpp = new T4CTTIodpp(this);
        this.odpmop = new T4CTTIodpmop(this);
        this.odpls = new T4CTTIodpls(this);
        this.osaga = new T4CTTIosaga(this);
    }

    private AccessToken getAccessToken(AbstractConnectionBuilder<?, ?> connectionBuilder, List<ConnectDescription> descriptors) throws SQLException {
        ConnectDescription firstDescriptor = descriptors.get(0);
        for (int i = 1; i < descriptors.size(); i++) {
            ConnectDescription nextDescriptor = descriptors.get(i);
            if (!firstDescriptor.isTokenAuthenticationEqual(nextDescriptor)) {
                debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getAccessToken", "Deferring access token request: Multiple descriptors found having conflicting configurations.", null, null);
                return null;
            }
        }
        AccessTokenBuilder builder = configureAccessTokenBuilder(connectionBuilder, firstDescriptor);
        return builder.build();
    }

    private AccessTokenBuilder configureAccessTokenBuilder(AbstractConnectionBuilder<?, ?> connectionBuilder, ConnectDescription descriptor) {
        return new AccessTokenBuilder(getDiagnosable()).tokenSupplier(connectionBuilder.getTokenSupplier()).accessToken(this.accessToken).driverResources(getDriverResources()).tokenAuthentication(descriptor.getTokenAuthentication() != null ? descriptor.getTokenAuthentication() : this.tokenAuthentication).tokenLocation(descriptor.getTokenLocation() != null ? descriptor.getTokenLocation() : this.tokenLocation).passwordAuthentication(descriptor.getPasswordAuthentication() != null ? descriptor.getPasswordAuthentication() : this.passwordAuthentication).isSepsCredentials(this.isSepsCredentials).userName(this.userName).password(this.password).tlsConfig(getTlsConfig(descriptor)).ociIamUrl(descriptor.getOciIamUrl() != null ? descriptor.getOciIamUrl() : this.ociIamUrl).ociConfigFile(descriptor.getOciConfigFile() != null ? descriptor.getOciConfigFile() : this.ociConfigFile).ociProfile(descriptor.getOciProfile() != null ? descriptor.getOciProfile() : this.ociProfile).ociTenancy(descriptor.getOciTenancy() != null ? descriptor.getOciTenancy() : this.ociTenancy).ociCompartment(descriptor.getOciCompartment() != null ? descriptor.getOciCompartment() : this.ociCompartment).ociDatabase(descriptor.getOciDatabase() != null ? descriptor.getOciDatabase() : this.ociDatabase).azureDatabaseApplicationIdUri(descriptor.getAzureDbAppIdUri() != null ? descriptor.getAzureDbAppIdUri() : this.azureDatabaseApplicationIdUri).tenantId(descriptor.getTenantId() != null ? descriptor.getTenantId() : this.tenantId).clientId(descriptor.getClientId() != null ? descriptor.getClientId() : this.clientId).clientSecret(this.clientSecret).clientCertificate(descriptor.getClientCertificate() != null ? descriptor.getClientCertificate() : this.clientCertificate).clientCertificatePassword(this.clientCertificatePassword).redirectUri(descriptor.getRedirectUri() != null ? descriptor.getRedirectUri() : this.redirectUri).azureCredentials(descriptor.getAzureCredentials() != null ? descriptor.getAzureCredentials() : this.azureCredentials);
    }

    private Properties getTlsConfig(ConnectDescription descriptor) {
        Properties tlsConfig = new Properties();
        String walletLocation = descriptor.getWalletLocation() != null ? descriptor.getWalletLocation() : this.walletLocation;
        if (walletLocation != null) {
            tlsConfig.put(5, walletLocation);
        }
        if (!OpaqueString.isNull(this.walletPassword)) {
            tlsConfig.put(16, this.walletPassword);
        }
        if (this.thinSslCertificateAlias != null) {
            tlsConfig.put(29, this.thinSslCertificateAlias);
        }
        if (this.thinSslCertificateThumbprint != null) {
            tlsConfig.put(44, this.thinSslCertificateThumbprint);
        }
        if (this.thinJavaxNetSslKeystore != null) {
            tlsConfig.put(8, this.thinJavaxNetSslKeystore);
        }
        if (this.thinJavaxNetSslKeystoretype != null) {
            tlsConfig.put(9, this.thinJavaxNetSslKeystoretype);
        }
        if (!OpaqueString.isNull(this.thinJavaxNetSslKeystorepassword)) {
            tlsConfig.put(10, this.thinJavaxNetSslKeystorepassword);
        }
        if (this.thinSslKeymanagerfactoryAlgorithm != null) {
            tlsConfig.put(14, this.thinSslKeymanagerfactoryAlgorithm);
        }
        if (this.thinJavaxNetSslTruststore != null) {
            tlsConfig.put(11, this.thinJavaxNetSslTruststore);
        }
        if (this.thinJavaxNetSslTruststoretype != null) {
            tlsConfig.put(12, this.thinJavaxNetSslTruststoretype);
        }
        if (OpaqueString.isNull(this.thinJavaxNetSslTruststorepassword)) {
            tlsConfig.put(13, this.thinJavaxNetSslTruststorepassword);
        }
        if (this.thinSslTrustmanagerfactoryAlgorithm != null) {
            tlsConfig.put(15, this.thinSslTrustmanagerfactoryAlgorithm);
        }
        if (this.sslContextProtocol != null) {
            tlsConfig.put(38, this.sslContextProtocol);
        }
        return tlsConfig;
    }

    private final void authenticateUserForLogon(@Blind AccessToken accessToken) throws SQLException, IOException, NumberFormatException {
        begin(Metrics.ConnectionEvent.AUTH);
        this.auth = new T4CTTIoauthenticate(this, this.resourceManagerId);
        this.auth.setNamespaces(this.namespaces);
        long logonMode = getLogonMode();
        if (accessToken == null) {
            authenticateWithPassword(logonMode);
        } else {
            this.auth.doOAUTH(accessToken, logonMode);
        }
        this.namespaces.clear();
        end(Metrics.ConnectionEvent.AUTH);
    }

    private void authenticateWithPassword(long logonMode) throws SQLException, IOException, NumberFormatException {
        try {
            String authenticationAdaptor = net().getAuthenticationAdaptorName();
            boolean isKerberosAuthentication = AnoServices.AUTHENTICATION_KERBEROS5.equals(authenticationAdaptor);
            if (!this.net.getSessionAttributes().isTwoFactorAuthenticationDone() && !isKerberosAuthentication && this.userName != null && this.userName.length() != 0) {
                try {
                    this.auth.doOSESSKEY(this.userName, logonMode);
                    initializeResultSetCache();
                } catch (SQLException qe) {
                    if (qe.getErrorCode() == 1017) {
                        this.userName = null;
                    } else {
                        throw qe;
                    }
                }
            }
            this.auth.doOAUTH(this.userName, this.password.get(), this.newPasswordValue.get(), logonMode);
            if (this.newPasswordValue != OpaqueString.NULL) {
                initializePassword(this.newPasswordValue);
                this.newPasswordValue = OpaqueString.NULL;
            }
        } catch (SQLException sqlE) {
            if (0 != 0) {
                sqlE.initCause(null);
            }
            throw sqlE;
        }
    }

    private final CompletionStage<Void> authenticateUserForLogonAsync(AccessToken accessToken) {
        CompletionStage<SQLException> osesskeyStage;
        if (accessToken != null) {
            return CompletionStageUtil.failedStage(DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Asynchronous connection creation does not support token-based authentication").fillInStackTrace());
        }
        long logonMode = getLogonMode();
        try {
            this.auth = new T4CTTIoauthenticate(this, this.resourceManagerId);
            this.auth.setNamespaces(this.namespaces);
            try {
                this.pipeline = Pipeline.createHalfDuplex(this);
                if (this.userName != null && this.userName.length() != 0) {
                    osesskeyStage = this.auth.doOSESSKEYAsync(this.userName, logonMode).thenApply(nil -> {
                        return (SQLException) null;
                    }).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(SQLException.class, osesskeySQLFailure -> {
                        if (osesskeySQLFailure.getErrorCode() == 1017) {
                            this.userName = null;
                            return osesskeySQLFailure;
                        }
                        throw osesskeySQLFailure;
                    }));
                } else {
                    osesskeyStage = CompletionStageUtil.completedStage(null);
                }
                return osesskeyStage.thenCompose(ora1017Exception -> {
                    return this.auth.doOAUTHAsync(this.userName, this.password.get(), this.newPasswordValue.get(), logonMode).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(SQLException.class, oauthSQLFailure -> {
                        this.namespaces.clear();
                        if (ora1017Exception != null) {
                            oauthSQLFailure.initCause(ora1017Exception);
                        }
                        throw oauthSQLFailure;
                    }));
                }).thenRun(() -> {
                    this.namespaces.clear();
                    if (this.newPasswordValue != OpaqueString.NULL) {
                        initializePassword(this.newPasswordValue);
                        this.newPasswordValue = OpaqueString.NULL;
                    }
                });
            } catch (IOException ioException) {
                return CompletionStageUtil.failedStage(ioException);
            }
        } catch (SQLException preAuthenticationFailure) {
            return CompletionStageUtil.failedStage(preAuthenticationFailure);
        }
    }

    private final long getLogonMode() {
        long logonMode = 0;
        if (this.internalLogon != null) {
            if (this.internalLogon.equalsIgnoreCase("sysoper")) {
                logonMode = 64;
            } else if (this.internalLogon.equalsIgnoreCase("sysdba")) {
                logonMode = 32;
            } else if (this.internalLogon.equalsIgnoreCase("sysasm")) {
                logonMode = 4194304;
            } else if (this.internalLogon.equalsIgnoreCase("sysbackup")) {
                logonMode = 16777216;
            } else if (this.internalLogon.equalsIgnoreCase("sysdg")) {
                logonMode = 33554432;
            } else if (this.internalLogon.equalsIgnoreCase("syskm")) {
                logonMode = 67108864;
            }
        }
        if (this.prelimAuth) {
            logonMode |= 128;
        }
        return logonMode;
    }

    private final void initializeResultSetCache() throws NumberFormatException {
        if (this.isResultSetCacheEnabled && this.sessionProperties != null) {
            String dbId = this.sessionProperties.getProperty("AUTH_GLOBALLY_UNIQUE_DBID", "");
            long qcacheMaxSize = Long.parseLong(this.sessionProperties.getProperty("AUTH_QCACHE_MAXSIZE", "0"));
            int qcacheLag = Integer.parseInt(this.sessionProperties.getProperty("AUTH_QCACHE_CACHELAG", "0"));
            if (this.queryResultCacheMaxSize >= 0 && this.queryResultCacheMaxSize < 32768) {
                this.isResultSetCacheEnabled = false;
                return;
            }
            if (qcacheMaxSize > 0) {
                long qcacheMaxSize2 = this.queryResultCacheMaxSize >= 32768 ? this.queryResultCacheMaxSize : qcacheMaxSize;
                int qcacheLag2 = this.queryResultCacheMaxLag > 0 ? this.queryResultCacheMaxLag : qcacheLag;
                this.resultSetCache = ResultSetCacheManager.getResultSetCache(dbId, qcacheMaxSize2, qcacheLag2);
                this.resultSetCache.registerConnection(this);
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeResultSetCache", "resultSetState={0}, dbId={1}, qcacheMaxSize={2}, qcacheLag={3}. ", (String) null, null, this.resultSetCache.getState(), secure(dbId), Long.valueOf(qcacheMaxSize2), Integer.valueOf(qcacheLag2));
                this.resultSetCacheLocalInvalidations = new ArrayList<>();
                return;
            }
            this.isResultSetCacheEnabled = false;
            return;
        }
        this.isResultSetCacheEnabled = false;
    }

    private final void initializeSessionInfoAfterLogon() throws NumberFormatException {
        this.sessionId = getSessionId();
        this.serialNumber = getSerialNumber();
        this.internalName = this.auth.internalName;
        this.externalName = this.auth.externalName;
        this.instanceName = this.sessionProperties.getProperty("AUTH_INSTANCENAME");
        this.dbName = this.sessionProperties.getProperty("AUTH_DBNAME");
        this.databaseUniqueIdentifier = this.sessionProperties.getProperty("AUTH_DB_ID");
        this.versionNumberString = this.sessionProperties.getProperty("AUTH_VERSION_NO");
        if (this.versionNumberString != null) {
            long verNum = Long.parseLong(this.versionNumberString);
            this.majorNumber = T4C7Oversion.serverReleaseRel(verNum);
            this.minorNumber = T4C7Oversion.serverReleaseRelUpd(verNum);
            int ver = 0 + (this.majorNumber * 1000);
            if (ver < 18000) {
                ver += T4C7Oversion.serverReleaseRelUpd(verNum) * 100;
            }
            this.versionNumber = (short) ver;
        }
        updateMaximumCursorCount(this.sessionProperties.getProperty("AUTH_MAX_OPEN_CURSORS"));
    }

    private final void initializeDatabaseVersionInfo() throws SQLException, IOException {
        String instanceStartTime = (String) this.sessionProperties.get("AUTH_SC_INSTANCE_START_TIME");
        String[] cacheValue = null;
        if (this.databaseUniqueIdentifier != null) {
            cacheValue = cachedVersionTable.get(this.databaseUniqueIdentifier + instanceStartTime);
        }
        if (!this.prelimAuth && !this.jmsNotificationConnection && cacheValue == null) {
            T4C7Oversion ver = new T4C7Oversion(this);
            ver.doOVERSION();
            byte[] resultBytes = ver.getVersion();
            this.databaseProductVersion = new String(resultBytes, StandardCharsets.UTF_8);
            this.versionNumber = ver.getVersionNumber();
            this.majorNumber = ver.getMajorVersionNumber();
            this.minorNumber = ver.getMinorVersionNumber();
            if (this.databaseUniqueIdentifier != null) {
                String[] cacheValue2 = {this.databaseProductVersion, String.valueOf((int) this.versionNumber), String.valueOf(this.majorNumber), String.valueOf(this.minorNumber)};
                cachedVersionTable.put(this.databaseUniqueIdentifier + instanceStartTime, cacheValue2);
                return;
            }
            return;
        }
        if (cacheValue == null) {
            this.versionNumber = (short) 0;
            return;
        }
        this.databaseProductVersion = cacheValue[0];
        this.versionNumber = Short.parseShort(cacheValue[1]);
        this.majorNumber = Short.parseShort(cacheValue[2]);
        this.minorNumber = Short.parseShort(cacheValue[3]);
    }

    private final CompletionStage<Void> initializeDatabaseVersionInfoAsync() {
        String instanceStartTime = (String) this.sessionProperties.get("AUTH_SC_INSTANCE_START_TIME");
        String[] cacheValue = null;
        if (this.drcpEnabled && this.databaseUniqueIdentifier != null) {
            cacheValue = cachedVersionTable.get(this.databaseUniqueIdentifier + instanceStartTime);
        }
        if (!this.prelimAuth && !this.jmsNotificationConnection && cacheValue == null) {
            T4C7Oversion ver = new T4C7Oversion(this);
            return ver.doOVERSIONAsync().thenApply(CompletionStageUtil.normalCompletionHandler(nil -> {
                byte[] resultBytes = ver.getVersion();
                this.databaseProductVersion = new String(resultBytes, StandardCharsets.UTF_8);
                this.versionNumber = ver.getVersionNumber();
                this.majorNumber = ver.getMajorVersionNumber();
                this.minorNumber = ver.getMinorVersionNumber();
                if (this.drcpEnabled && this.databaseUniqueIdentifier != null) {
                    String[] versionInfo = {this.databaseProductVersion, String.valueOf((int) this.versionNumber), String.valueOf(this.majorNumber), String.valueOf(this.minorNumber)};
                    cachedVersionTable.put(this.databaseUniqueIdentifier + instanceStartTime, versionInfo);
                }
                return (Void) null;
            }));
        }
        if (cacheValue == null) {
            this.versionNumber = (short) 0;
        } else {
            this.databaseProductVersion = cacheValue[0];
            this.versionNumber = Short.parseShort(cacheValue[1]);
            this.majorNumber = Short.parseShort(cacheValue[2]);
            this.minorNumber = Short.parseShort(cacheValue[3]);
        }
        return CompletableFuture.completedFuture(null);
    }

    private final void disableTempLobRefCntForOracle10() throws SQLException {
        if (getVersionNumber() < 11000) {
            this.enableTempLobRefCnt = false;
        }
    }

    private final SQLException handleLogonSQLException(SQLException logonFailure) {
        try {
            this.net.disconnect();
        } catch (Exception e) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "handleLogonSQLException", null, (String) null, e);
        }
        this.isLoggedOn = false;
        return logonFailure;
    }

    private final void logConnectionInfoAfterLogonAlways() throws SQLException {
        debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "logConnectionInfoAfterLogonAlways", "Operating System Process Identifier SPID={0}, DRCP Enabled={1}. ", (String) null, (Throwable) null, this.sessionProperties == null ? "null" : this.sessionProperties.getProperty("AUTH_SERVER_PID"), Boolean.valueOf(isDRCPEnabled()));
    }

    private final void initializeResultSetCacheAfterLogon() throws SQLException {
        if (this.isResultSetCacheEnabled && this.resultSetCache != null) {
            ResultSetCache.ResultSetCacheState state = this.resultSetCache.getState();
            if (state == ResultSetCache.ResultSetCacheState.STARTING) {
                long registrationId = registerInbandNotification(this.resultSetCache.getCacheLag(), DEFAULT_CACHE_LOCATION);
                this.resultSetCache.setRegistrationId(registrationId);
                doPingDatabase();
                this.oqcsta = new T4CTTIOqcsta(this, registrationId);
                this.oqcsta.connection = null;
                this.oqcsta.meg = null;
                this.resultSetCache.setOQCSTA(this.oqcsta);
                this.resultSetCache.setState(ResultSetCache.ResultSetCacheState.STARTED);
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeResultSetCacheAfterLogon", "resultSetCacheState={0}. ", (String) null, (String) null, this.resultSetCache.getState());
            }
            this.oqcsta = this.resultSetCache.getOQCSTA();
        }
    }

    private final void deregisterResultSetCacheBeforeLogoff() throws SQLException {
        if (this.isResultSetCacheEnabled && this.resultSetCache != null) {
            Monitor.CloseableLock lock = this.resultSetCache.acquireCloseableLock();
            Throwable th = null;
            try {
                if (this.resultSetCache.deregisterConnection(this)) {
                    doUnregisterDatabaseChangeNotification(this.resultSetCache.getRegistrationId(), DEFAULT_CACHE_LOCATION);
                    this.resultSetCache.setState(ResultSetCache.ResultSetCacheState.CLOSED);
                }
                this.resultSetCache = null;
            } finally {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            }
        }
    }

    private final void initializeResultSetCacheAfterLogonAlways() {
        if (this.isResultSetCacheEnabled && this.resultSetCache != null) {
            if (this.resultSetCache.getState() == ResultSetCache.ResultSetCacheState.STARTING) {
                this.resultSetCache.setState(ResultSetCache.ResultSetCacheState.STARTUP_FAILED);
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeResultSetCacheAfterLogonAlways", "resultSetCacheState={0}. ", (String) null, (String) null, this.resultSetCache.getState());
                Monitor.CloseableLock lock = this.resultSetCache.acquireCloseableLock();
                Throwable th = null;
                try {
                    this.resultSetCache.monitorNotifyAll();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                } catch (Throwable th3) {
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    throw th3;
                }
            }
            return;
        }
        this.isResultSetCacheEnabled = false;
    }

    private final void initializeDRCPAfterLogonAlways() throws SQLException {
        if (this.drcpEnabled && this.isLoggedOn && this.drcpState != OracleConnection.DRCPState.DETACHED) {
            detachServerConnection(this.drcpTagName);
        }
    }

    T4CTTIkscn kscnForByteLength() {
        T4CTTIkscn kscn;
        if (this.isServerBigSCN) {
            kscn = new T4CTTIkscn8(this);
        } else {
            kscn = new T4CTTIkscn(this);
        }
        return kscn;
    }

    void handleIOException(IOException ea) throws SQLException {
        Monitor.CloseableLock lock;
        Throwable th;
        if ((ea instanceof NetException) && ((NetException) ea).getErrorNumber() == 56611) {
            SQLException ex = (SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_DRCP_ATTACH_TIMEOUT).fillInStackTrace();
            ex.initCause(ea);
            throw ex;
        }
        try {
            lock = this.cancelInProgressLockForThin.acquireCloseableLock();
            th = null;
        } catch (Exception e) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "handleIOException", null, (String) null, e);
        }
        try {
            try {
                this.pipeState = -1;
                this.net.disconnect();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                this.isLoggedOn = false;
                setLifecycle(4);
            } finally {
            }
        } finally {
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void logoff() throws SQLException {
        assertLockHeldByCurrentThread();
        try {
            try {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "logoff", null, null, null);
                assertLoggedOn("T4CConnection.logoff");
                if (getLifecycle() == 8) {
                    return;
                }
                this.net.cancelTimeout();
                deregisterResultSetCacheBeforeLogoff();
                this.commoncall.doOLOGOFF();
                this.net.disconnect();
                try {
                    if (this.net.getSessionAttributes().isConnected()) {
                        this.net.disconnect();
                    }
                } catch (Exception e) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "logoff", null, (String) null, e);
                }
                this.isLoggedOn = false;
            } catch (IOException ex) {
                handleIOException(ex);
                if (getLifecycle() != 8) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
                try {
                    if (this.net.getSessionAttributes().isConnected()) {
                        this.net.disconnect();
                    }
                } catch (Exception e2) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "logoff", null, (String) null, e2);
                }
                this.isLoggedOn = false;
            }
        } finally {
            try {
                if (this.net.getSessionAttributes().isConnected()) {
                    this.net.disconnect();
                }
            } catch (Exception e3) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "logoff", null, null, e3);
            }
            this.isLoggedOn = false;
        }
    }

    T4CMAREngine getMarshalEngine() {
        return this.mare;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doCommit(int flags) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("T4CConnection.do_commit");
            try {
                EnumSet<OracleConnection.TransactionState> transactionState = getTransactionState();
                if (this.disableCommitOptimizationOnPDBChange || isDRCPEnabled() || transactionState.size() != 1 || !transactionState.contains(OracleConnection.TransactionState.TRANSACTION_READONLY) || inSessionlessTxnMode()) {
                    this.disableCommitOptimizationOnPDBChange = false;
                    if (this.dppstmt != null) {
                        directPathFinish();
                    } else if (flags == 0) {
                        this.commoncall.doOCOMMIT();
                    } else {
                        doCommitWithOptions(flags);
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private final void doCommitWithOptions(int optionFlags) throws SQLException, IOException {
        int otxenFlags = getOTXENFlagsFromCommitOption(optionFlags);
        this.otxen.doOTXEN(1, null, null, 0, 0, 0, 0, 4, otxenFlags);
        int outState = this.otxen.getOutStateFromServer();
        if (outState == 2 || outState != 4) {
        }
    }

    private final int getOTXENFlagsFromCommitOption(int optionFlags) {
        int txnflgs = 0;
        if ((optionFlags & OracleConnection.CommitOption.WRITEBATCH.getCode()) != 0) {
            txnflgs = 0 | 2 | 1;
        } else if ((optionFlags & OracleConnection.CommitOption.WRITEIMMED.getCode()) != 0) {
            txnflgs = 0 | 2;
        }
        if ((optionFlags & OracleConnection.CommitOption.NOWAIT.getCode()) != 0) {
            txnflgs = txnflgs | 8 | 4;
        } else if ((optionFlags & OracleConnection.CommitOption.WAIT.getCode()) != 0) {
            txnflgs |= 8;
        }
        return txnflgs;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doRollback() throws SQLException {
        boolean isUCPWorker = Thread.currentThread().getName().startsWith(UCP_THREAD_NAME_PREFIX);
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("T4CConnection.do_rollback");
                if (this.dppstmt == null) {
                    if (isUCPWorker && this.replayModes.contains(ReplayMode.RUNTIME_REPLAY_ENABLED)) {
                        beginNonRequestCalls();
                    }
                    try {
                        this.commoncall.doOROLLBACK();
                        if (isUCPWorker && this.replayModes.contains(ReplayMode.RUNTIME_REPLAY_ENABLED)) {
                            endNonRequestCalls();
                        }
                    } catch (Throwable th2) {
                        if (isUCPWorker && this.replayModes.contains(ReplayMode.RUNTIME_REPLAY_ENABLED)) {
                            endNonRequestCalls();
                        }
                        throw th2;
                    }
                } else {
                    directPathAbort();
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doSetAutoCommit(boolean on) throws SQLException {
        assertLockHeldByCurrentThread();
        if (inSessionlessTxnMode() && this.sessionlessTxn.getLastPiggyBackOperation() != 4) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN).fillInStackTrace());
        }
        if (this.autoCommitSpecCompliant && on && inLocalTransaction()) {
            commit();
        }
        this.autocommit = on;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void open(OracleStatement stmt) throws SQLException {
        assertLoggedOn("T4CConnection.open");
        stmt.setCursorId(0);
    }

    void incrementAsyncCursorCount() {
        this.asyncCursorCount++;
    }

    void decrementAsyncCursorCount() {
        this.asyncCursorCount--;
    }

    int asyncCursorCount() {
        return this.asyncCursorCount;
    }

    int asyncCursorLimit() {
        return this.computedAsyncCursorLimit;
    }

    int maximumCursorCount() {
        return this.maximumCursorCount;
    }

    private void updateMaximumCursorCount(String value) {
        try {
            int existingCount = this.maximumCursorCount;
            this.maximumCursorCount = Integer.parseInt(value);
            if (existingCount == this.maximumCursorCount) {
                return;
            }
            int i = this.computedAsyncCursorLimit;
            if (this.asyncCursorLimit != ((int) this.asyncCursorLimit)) {
                this.computedAsyncCursorLimit = (int) (this.asyncCursorLimit * this.maximumCursorCount);
            } else if (this.asyncCursorLimit == 0.0f) {
                this.computedAsyncCursorLimit = Integer.MAX_VALUE;
            } else if (this.asyncCursorLimit < 0.0f) {
                this.computedAsyncCursorLimit = this.maximumCursorCount + ((int) this.asyncCursorLimit);
            } else {
                this.computedAsyncCursorLimit = (int) this.asyncCursorLimit;
            }
            this.computedAsyncCursorLimit = Math.max(this.computedAsyncCursorLimit, 1);
        } catch (NullPointerException | NumberFormatException e) {
            this.maximumCursorCount = Integer.MAX_VALUE;
            this.computedAsyncCursorLimit = Integer.MAX_VALUE;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    String doGetDatabaseProductVersion() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("T4CConnection.do_getDatabaseProductVersion");
            T4C7Oversion ver = new T4C7Oversion(this);
            try {
                ver.doOVERSION();
                byte[] resultBytes = ver.getVersion();
                try {
                    String databaseProductVersion = new String(resultBytes, "UTF8");
                    return databaseProductVersion;
                } catch (UnsupportedEncodingException ex) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) ex).fillInStackTrace());
                }
            } catch (IOException e) {
                handleIOException(e);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    short doGetVersionNumber() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("T4CConnection.doGetVersionNumber");
            T4C7Oversion ver = new T4C7Oversion(this);
            try {
                ver.doOVERSION();
                short versionNumber = ver.getVersionNumber();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return versionNumber;
            } catch (IOException e) {
                handleIOException(e);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    int doGetMajorVersionNumber() throws SQLException {
        return Integer.valueOf(this.majorNumber).intValue();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    int doGetMinorVersionNumber() throws SQLException {
        return Integer.valueOf(this.minorNumber).intValue();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    OracleStatement RefCursorBytesToStatement(byte[] bytes, OracleStatement parent) throws SQLException {
        T4CStatement newstmt = new T4CStatement(this, OracleResultSet.ResultSetType.UNKNOWN);
        try {
            int cursor = this.mare.unmarshalRefCursor(bytes);
            newstmt.setCursorId(cursor);
            newstmt.isOpen = true;
            newstmt.sqlObject = parent.sqlObject;
            newstmt.serverCursor = true;
            parent.addChild(newstmt);
            newstmt.prepareForNewResults(true, false, true);
            newstmt.needToParse = false;
            return newstmt;
        } catch (IOException e) {
            handleIOException(e);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    OracleStatement createImplicitResultSetStatement(OracleStatement parent) throws SQLException {
        T4CStatement newstmt = new T4CStatement(this, OracleResultSet.ResultSetType.UNKNOWN);
        newstmt.setPrefetchInternal(parent.defaultRowPrefetch, false, false);
        newstmt.sqlObject = parent.sqlObject;
        newstmt.sqlKind = OracleStatement.SqlKind.SELECT;
        newstmt.numberOfDefinePositions = parent.numberOfDefinePositions;
        newstmt.isOpen = parent.isOpen;
        newstmt.prepareForNewResults(true, false, true);
        parent.addImplicitResultSetStmt(newstmt);
        return newstmt;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // oracle.jdbc.driver.PhysicalConnection
    void cancelOperationOnServer(boolean isStatementCancel) throws SQLException {
        Monitor.CloseableLock lock = this.cancelInProgressLockForThin.acquireCloseableLock();
        Throwable th = null;
        try {
            if (!this.cancelInProgressFlag) {
                try {
                    switch (this.pipeState) {
                        case -1:
                            if (!this.pipeline.cancel()) {
                                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cancelOperationOnServer", "pipeState=FREE, aborting cancel. ", (String) null, (Throwable) null);
                                if (lock != null) {
                                    if (0 != 0) {
                                        try {
                                            lock.close();
                                            return;
                                        } catch (Throwable th2) {
                                            th.addSuppressed(th2);
                                            return;
                                        }
                                    }
                                    lock.close();
                                    return;
                                }
                                return;
                            }
                            this.sentCancel = true;
                            this.cancelInProgressFlag = true;
                            this.statementCancel = isStatementCancel;
                            break;
                        case 0:
                        default:
                            this.sentCancel = true;
                            this.cancelInProgressFlag = true;
                            this.statementCancel = isStatementCancel;
                            break;
                        case 1:
                            this.net.sendBreak();
                            this.sentCancel = true;
                            this.cancelInProgressFlag = true;
                            this.statementCancel = isStatementCancel;
                            break;
                        case 2:
                            this.net.sendInterrupt();
                            this.sentCancel = true;
                            this.cancelInProgressFlag = true;
                            this.statementCancel = isStatementCancel;
                            break;
                    }
                } catch (NetException ne) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) ne).fillInStackTrace());
                } catch (IOException ne2) {
                    handleIOException(ne2);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ne2).fillInStackTrace());
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    Communication net() {
        return this.net;
    }

    void connectNetworkSessionProtocol(AbstractConnectionBuilder<?, ?> builder) throws SQLException, IOException {
        if (!$assertionsDisabled && this.net == null) {
            throw new AssertionError("NSProtocol object must be instantiated before calling this method");
        }
        begin(Metrics.ConnectionEvent.NET_SESSION_ESTABLISHMENT);
        this.net.connect(builder == null ? null : builder.getGSSCredential(), this.dmsParent);
        end(Metrics.ConnectionEvent.NET_SESSION_ESTABLISHMENT);
        this.mare = new T4CMAREngineNIO(this.net, this);
        this.mare.setConnectionDuringExceptionHandling(this);
    }

    private void postTTIdtyInitialization(T4C8TTIdty dty) throws SQLException {
        if (dty == null) {
            throw new IllegalArgumentException("dty cannot be null");
        }
        setNegotiatedTTCVersion(dty.jdbcThinCompileTimeCapabilities);
        setZeroCopyIOCapability();
        setDynamicVectorIOCapability();
        setLobPrefetchCapability(dty.jdbcThinCompileTimeCapabilities);
        set32kVarcharCapability();
        setFeatureTrackingCapability();
        setRequestBoundariesCapability();
    }

    private T4CTTICookie negotiateSession() throws SQLException, IOException, NumberFormatException {
        begin(Metrics.ConnectionEvent.TTC_NEGOTIATION);
        this.pro = new T4C8TTIpro(this);
        begin(Metrics.ConnectionEvent.TTC_TTIPRO);
        this.pro.doRPC();
        end(Metrics.ConnectionEvent.TTC_TTIPRO);
        this.serverCompileTimeCapabilities = this.pro.getServerCompileTimeCapabilities();
        this.serverRuntimeCapabilities = this.pro.getServerRuntimeCapabilities();
        validateServerLogonCapability();
        doCharSetNegotiation(this.pro.oVersion, this.pro.getCharacterSet(), this.pro.getncharCHARSET());
        setCLRBigChunksCapability();
        this.dty = new T4C8TTIdty(this, this.serverRuntimeCapabilities, this.logonCap != null && this.logonCap.trim().equals("o3"), this.thinNetUseZeroCopyIO);
        begin(Metrics.ConnectionEvent.TTC_TTIDTY);
        this.dty.doRPC();
        end(Metrics.ConnectionEvent.TTC_TTIDTY);
        postTTIdtyInitialization(this.dty);
        end(Metrics.ConnectionEvent.TTC_NEGOTIATION);
        return T4CTTICookie.builder().databaseRuntimeCapabilities(this.serverRuntimeCapabilities).databaseCompileTimeCapabilities(this.serverCompileTimeCapabilities).connectionProtocolVersion(this.pro.getProtocolVersion()).databaseCharSet(this.pro.getCharacterSet()).databaseNCharSet(this.pro.getncharCHARSET()).databaseCharSetFlag(this.pro.getFlags()).databasePortage(this.pro.getSvrPortDescription()).build();
    }

    private final CompletionStage<Void> connectSessionAsync(AbstractConnectionBuilder<?, ?> builder, AsyncOutboundTimeoutHandler loginTimeoutHandler) {
        if (!this.javaNetNio) {
            return CompletionStageUtil.failedStage(new SQLException("Asynchronous connection is not supported when  oracle.jdbc.javaNetNio=false"));
        }
        Executor executor = getAsyncExecutor();
        return this.net.connectAsync(builder == null ? null : builder.getGSSCredential(), this.dmsParent, loginTimeoutHandler, executor).thenCompose(CompletionStageUtil.normalCompletionHandler(nil -> {
            this.mare = new T4CMAREngineNIO(this.net, this);
            this.mare.setConnectionDuringExceptionHandling(this);
            this.pro = new T4C8TTIpro(this);
            this.pro.marshal();
            return this.pro.receiveAsync(executor);
        })).thenCompose(CompletionStageUtil.normalCompletionHandler(proReceived -> {
            this.serverCompileTimeCapabilities = proReceived;
            this.serverRuntimeCapabilities = this.pro.getServerRuntimeCapabilities();
            validateServerLogonCapability();
            doCharSetNegotiation(this.pro.oVersion, this.pro.getCharacterSet(), this.pro.getncharCHARSET());
            setCLRBigChunksCapability();
            boolean downgradeToO3Logon = this.logonCap != null && this.logonCap.trim().equals("o3");
            this.dty = new T4C8TTIdty(this, this.serverRuntimeCapabilities, downgradeToO3Logon, this.thinNetUseZeroCopyIO);
            return this.dty.doRPCAsync(executor).thenApply(CompletionStageUtil.normalCompletionHandler(nil2 -> {
                postTTIdtyInitialization(this.dty);
                return (Void) null;
            }));
        }));
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v386, types: [java.lang.String] */
    @Debug(level = Debug.Level.FINER)
    @Blind(PropertiesBlinder.class)
    private final Properties createNSProperties(AbstractConnectionBuilder<?, ?> builder) {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "createNSProperties", "entering args ({0})", (String) null, (String) null, builder);
            Properties nsProperties = new Properties();
            if (this.isUseTcpFastOpen) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NET_USE_TCP_FAST_OPEN, "true");
            }
            if (this.thinNetProfile != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_PROFILE, this.thinNetProfile);
            }
            if (this.thinNetSetFIPSMode != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_SET_FIPS_MODE, this.thinNetSetFIPSMode);
            }
            if (this.useSNI != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NET_USE_SNI, this.useSNI);
            }
            if (this.sniIgnoreList != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NET_SNI_IGNORE_LIST, this.sniIgnoreList);
            }
            if (this.thinNetAuthenticationServices != null) {
                nsProperties.setProperty("oracle.net.authentication_services", this.thinNetAuthenticationServices);
                if (this.thinNetAuthenticationServices.toUpperCase().contains(AnoServices.AUTHENTICATION_RADIUS)) {
                    Function<byte[], byte[]> radiusChallengeResponseHandler = null;
                    if (builder != null && builder.getRadiusChallengeResponseHandler() != null) {
                        radiusChallengeResponseHandler = builder.getRadiusChallengeResponseHandler();
                    } else if (this.thinNetRadiusCRHandler != null) {
                        radiusChallengeResponseHandler = this.thinNetRadiusCRHandler;
                    }
                    if (radiusChallengeResponseHandler != null) {
                        nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_RADIUS_CHALLENGE_RESPONSE_HANDLER, radiusChallengeResponseHandler);
                        if (this.userName != null) {
                            nsProperties.setProperty(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_USER, this.userName);
                        }
                        if (this.password != null) {
                            nsProperties.put(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_PWD, this.password);
                        }
                    }
                }
                if (this.thinNetAuthenticationServices.toUpperCase().contains(AnoServices.AUTHENTICATION_KERBEROS5)) {
                    if (this.userName != null) {
                        nsProperties.setProperty(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER, this.userName);
                    }
                    if (this.password != null) {
                        nsProperties.put(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD, this.password);
                    }
                }
            }
            if (this.thinNetAuthenticationKrb5Mutual != null) {
                nsProperties.setProperty("oracle.net.kerberos5_mutual_authentication", this.thinNetAuthenticationKrb5Mutual);
            }
            if (this.thinNetAuthenticationKrb5CcName != null) {
                nsProperties.setProperty("oracle.net.kerberos5_cc_name", this.thinNetAuthenticationKrb5CcName);
            }
            if (this.thinNetAuthenticationKrbJaasLoginModule != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_JAAS_LOGIN_MODULE, this.thinNetAuthenticationKrbJaasLoginModule);
            }
            if (this.thinNetAllowWeakCrypto != Boolean.valueOf("true").booleanValue()) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_ALLOW_WEAK_CRYPTO, Boolean.toString(this.thinNetAllowWeakCrypto));
            }
            if (this.thinNetEncryptionLevel != null) {
                nsProperties.setProperty("oracle.net.encryption_client", this.thinNetEncryptionLevel);
            }
            if (this.thinNetEncryptionTypes != null) {
                nsProperties.setProperty("oracle.net.encryption_types_client", this.thinNetEncryptionTypes);
            }
            if (this.thinNetChecksumLevel != null) {
                nsProperties.setProperty("oracle.net.crypto_checksum_client", this.thinNetChecksumLevel);
            }
            if (this.thinNetChecksumTypes != null) {
                nsProperties.setProperty("oracle.net.crypto_checksum_types_client", this.thinNetChecksumTypes);
            }
            if (this.thinNetCryptoSeed != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_CRYPTO_SEED, this.thinNetCryptoSeed);
            }
            if (this.thinNetAuthenticationKrbRealm != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_REALM, this.thinNetAuthenticationKrbRealm);
            }
            if (this.thinTcpNoDelay) {
                nsProperties.setProperty(SQLnetDef.TCP_NODELAY_STR, "YES");
            }
            if (this.thinReadTimeout != null) {
                nsProperties.setProperty(SQLnetDef.TCP_READTIMEOUT_STR, String.valueOf(TimeUnitSuffixUtility.getTimeInMilliseconds(this.thinReadTimeout, false, 0)));
            }
            if (this.thinNetConnectTimeout != null) {
                nsProperties.setProperty("oracle.net.CONNECT_TIMEOUT", this.thinNetConnectTimeout);
            }
            if (this.thinSslServerDnMatch != null) {
                nsProperties.setProperty("oracle.net.ssl_server_dn_match", this.thinSslServerDnMatch);
            }
            if (this.thinSslAllowWeakDnMatch != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_SSL_ALLOW_WEAK_DN_MATCH, this.thinSslAllowWeakDnMatch);
            }
            if (this.thinSslServerCertDn != null) {
                nsProperties.setProperty("oracle.net.ssl_server_cert_dn", this.thinSslServerCertDn);
            }
            if (this.walletLocation != null) {
                nsProperties.setProperty("oracle.net.wallet_location", this.walletLocation);
            }
            if (this.walletPassword != null && this.walletPassword != OpaqueString.NULL) {
                nsProperties.put("oracle.net.wallet_password", this.walletPassword);
            }
            if (this.thinSslVersion != null) {
                nsProperties.setProperty("oracle.net.ssl_version", this.thinSslVersion);
            }
            if (this.thinSslCipherSuites != null) {
                nsProperties.setProperty("oracle.net.ssl_cipher_suites", this.thinSslCipherSuites);
            }
            if (this.thinSslCertificateAlias != null) {
                nsProperties.setProperty("oracle.net.ssl_certificate_alias", this.thinSslCertificateAlias);
            }
            if (this.thinSslCertificateThumbprint != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CERTIFICATE_THUMBPRINT, this.thinSslCertificateThumbprint);
            }
            if (this.thinJavaxNetSslKeystore != null) {
                nsProperties.setProperty("javax.net.ssl.keyStore", this.thinJavaxNetSslKeystore);
            }
            if (this.thinJavaxNetSslKeystoretype != null) {
                nsProperties.setProperty("javax.net.ssl.keyStoreType", this.thinJavaxNetSslKeystoretype);
            }
            if (this.thinJavaxNetSslKeystorepassword != null && this.thinJavaxNetSslKeystorepassword != OpaqueString.NULL) {
                nsProperties.put("javax.net.ssl.keyStorePassword", this.thinJavaxNetSslKeystorepassword);
            }
            if (this.thinJavaxNetSslTruststore != null) {
                nsProperties.setProperty("javax.net.ssl.trustStore", this.thinJavaxNetSslTruststore);
            }
            if (this.thinJavaxNetSslTruststoretype != null) {
                nsProperties.setProperty("javax.net.ssl.trustStoreType", this.thinJavaxNetSslTruststoretype);
            }
            if (this.thinJavaxNetSslTruststorepassword != null && this.thinJavaxNetSslTruststorepassword != OpaqueString.NULL) {
                nsProperties.put("javax.net.ssl.trustStorePassword", this.thinJavaxNetSslTruststorepassword);
            }
            if (this.thinSslKeymanagerfactoryAlgorithm != null) {
                nsProperties.setProperty("ssl.keyManagerFactory.algorithm", this.thinSslKeymanagerfactoryAlgorithm);
            }
            if (this.thinSslTrustmanagerfactoryAlgorithm != null) {
                nsProperties.setProperty("ssl.trustManagerFactory.algorithm", this.thinSslTrustmanagerfactoryAlgorithm);
            }
            if (this.thinNetOldsyntax != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_OLDSYNTAX, this.thinNetOldsyntax);
            }
            if (this.thinJndiLdapConnectTimeout != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_JNDI_LDAP_CONNECT_TIMEOUT, this.thinJndiLdapConnectTimeout);
            }
            if (this.thinJndiLdapReadTimeout != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_JNDI_LDAP_READ_TIMEOUT, this.thinJndiLdapReadTimeout);
            }
            if (this.thinLdapSslCipherSuites != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_CIPHER_SUITES, this.thinLdapSslCipherSuites);
            }
            if (this.thinLdapSslVersions != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_VERSIONS, this.thinLdapSslVersions);
            }
            if (this.thinLdapSslKeyStore != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_KEYSTORE, this.thinLdapSslKeyStore);
            }
            if (this.thinLdapSslKeyStoreType != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_KEYSTORE_TYPE, this.thinLdapSslKeyStoreType);
            }
            if (this.thinLdapSslKeyStorePwd != null && this.thinLdapSslKeyStorePwd != OpaqueString.NULL) {
                nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_KEYSTORE_PASSWORD, this.thinLdapSslKeyStorePwd);
            }
            if (this.thinLdapSslKeyManagerFactoryAlgo != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_KEYMANAGER_FACTORY_ALGORITHM, this.thinLdapSslKeyManagerFactoryAlgo);
            }
            if (this.thinLdapSslTrustStore != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_TRUSTSTORE, this.thinLdapSslTrustStore);
            }
            if (this.thinLdapSslTrustStoreType != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_TRUSTSTORE_TYPE, this.thinLdapSslTrustStoreType);
            }
            if (this.thinLdapSslTrustStorePassword != null && this.thinLdapSslTrustStorePassword != OpaqueString.NULL) {
                nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_TRUSTSTORE_PASSWORD, this.thinLdapSslTrustStorePassword);
            }
            if (this.thinLdapSslTrustManagerFactoryAlgo != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_TRUSTMANAGER_FACTORY_ALGORITHM, this.thinLdapSslTrustManagerFactoryAlgo);
            }
            if (this.thinLdapSslWalletLocation != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_WALLET_LOCATION, this.thinLdapSslWalletLocation);
            }
            if (this.thinLdapSslWalletPassword != null && this.thinLdapSslWalletPassword != OpaqueString.NULL) {
                nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_WALLET_PASSWORD, this.thinLdapSslWalletPassword);
            }
            if (this.thinLdapSecurityAuthentication != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_AUTHENTICATION, this.thinLdapSecurityAuthentication);
            }
            if (this.thinLdapSecurityPrincipal != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_PRINCIPAL, this.thinLdapSecurityPrincipal);
            }
            if (this.thinLdapSecurityCredetials != null && !this.thinLdapSecurityCredetials.isNull()) {
                nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_CREDENTIALS, this.thinLdapSecurityCredetials);
            }
            if (this.thinLdapsslContextProtocol != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_CONTEXT_PROTOCOL, this.thinLdapsslContextProtocol);
            }
            if (this.thinNetConnectionIdPrefix != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_CONNECTIONID_PREFIX, this.thinNetConnectionIdPrefix);
            }
            if (this.thinHttpsProxyHost != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_HTTPS_PROXY_HOST, this.thinHttpsProxyHost);
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_HTTPS_PROXY_PORT, Integer.toString(this.thinHttpsProxyPort));
            }
            if (this.tnsAdmin != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN, this.tnsAdmin);
            }
            if (this.thinNetDisableOutOfBandBreak) {
                nsProperties.setProperty(SQLnetDef.DISABLE_OOB_STR, "" + this.thinNetDisableOutOfBandBreak);
            }
            nsProperties.setProperty(SQLnetDef.USE_ZERO_COPY_IO_STR, "" + this.thinNetUseZeroCopyIO);
            nsProperties.setProperty(SQLnetDef.FORCE_DNS_LOAD_BALANCING_STR, "" + this.thinForceDnsLoadBalancing);
            if (this.thinOutboundConnectTimeout != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_OUTBOUND_CONNECT_TIMEOUT, this.thinOutboundConnectTimeout);
            }
            nsProperties.setProperty("oracle.jdbc.v$session.osuser", this.thinVsessionOsuser);
            nsProperties.setProperty("oracle.jdbc.v$session.program", this.thinVsessionProgram);
            nsProperties.setProperty("T4CConnection.hashCode", Integer.toHexString(hashCode()).toUpperCase());
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NET_KEEPALIVE, Boolean.toString(this.keepAlive));
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_DEFAULT_USE_NIO, Boolean.toString(this.javaNetNio));
            if (this.javaNetLocalIPForMsgq != null) {
                nsProperties.setProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_LOCAL_IP_MSGQ, this.javaNetLocalIPForMsgq);
            }
            if (this.javaNetMsgqTransport != null) {
                nsProperties.setProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_TRANSPORT, this.javaNetMsgqTransport);
            }
            nsProperties.setProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_BUSYWAIT, "" + this.javaNetMsgqBusyWait);
            nsProperties.setProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_KERNELWAIT, "" + this.javaNetMsgqKernelWait);
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_DOWN_HOSTS_TIMEOUT, "" + this.downHostsTimeout);
            if (this.targetInstanceName != null) {
                nsProperties.setProperty("oracle.jdbc.targetInstanceName", this.targetInstanceName);
            }
            if (this.targetServiceName != null) {
                nsProperties.setProperty("oracle.jdbc.targetServiceName", this.targetServiceName);
            }
            if (this.targetShardingKey != null) {
                nsProperties.setProperty("oracle.jdbc.targetShardingKey", this.targetShardingKey);
            }
            if (this.targetSuperShardingKey != null) {
                nsProperties.setProperty("oracle.jdbc.targetSuperShardingKey", this.targetSuperShardingKey);
            }
            if (this.readOnlyInstanceAllowed) {
                nsProperties.setProperty("oracle.jdbc.readOnlyInstanceAllowed", String.valueOf(this.readOnlyInstanceAllowed));
            }
            if (this.websocketUser != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_USER, this.websocketUser);
                if (this.websocketPassword != null && this.websocketPassword != OpaqueString.NULL) {
                    nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_PASSWORD, this.websocketPassword);
                } else {
                    nsProperties.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_PASSWORD, OpaqueString.EMPTY);
                }
            }
            if (this.socksProxyHost != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_SOCKS_PROXY_HOST, this.socksProxyHost);
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_SOCKS_PROXY_PORT, this.socksProxyPort + "");
            }
            if (this.proxyRemoteDNS != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_PROXY_REMOTE_DNS, Boolean.valueOf(this.proxyRemoteDNS) + "");
            } else if (this.socksRemoteDNS != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_PROXY_REMOTE_DNS, Boolean.valueOf(this.socksRemoteDNS) + "");
            }
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION, this.networkCompression);
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_LEVELS, this.networkCompressionLevels);
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_THRESHOLD, Integer.toString(this.networkCompressionThreshold));
            if (this.tcpKeepIdle != -1) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TCP_KEEPIDLE, Integer.toString(this.tcpKeepIdle));
            }
            if (this.tcpKeepInterval != -1) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TCP_KEEPINTERVAL, Integer.toString(this.tcpKeepInterval));
            }
            if (this.tcpKeepCount != -1) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TCP_KEEPCOUNT, Integer.toString(this.tcpKeepCount));
            }
            nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_SSL_CONTEXT_PROTOCOL, this.sslContextProtocol == null ? oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_SSL_CONTEXT_PROTOCOL_DEFAULT : this.sslContextProtocol);
            nsProperties.setProperty(SQLnetDef.SSL_SERVER_DN_MATCH_DEFAULT, Boolean.toString(builder.getTokenSupplier() != null || !OpaqueString.isNull(this.accessToken) || getDriverResources().isProviderConfigured(ResourceType.ACCESS_TOKEN) || this.tokenAuthentication != null || PasswordAuthentication.OCI_TOKEN.name().equalsIgnoreCase(this.passwordAuthentication) || PasswordAuthentication.AZURE_TOKEN.name().equalsIgnoreCase(this.passwordAuthentication)));
            if (this.tokenAuthentication != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION, this.tokenAuthentication);
            }
            if (this.passwordAuthentication != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION, this.passwordAuthentication);
            }
            if (this.localHostName != null) {
                nsProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_LOCAL_HOST_NAME, this.localHostName);
            }
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "createNSProperties", "returning {0}", (String) null, (String) null, new PropertiesBlinder().blind((PropertiesBlinder) nsProperties));
            return nsProperties;
        } catch (Throwable th) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "createNSProperties", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    private final void validateServerLogonCapability() throws SQLException, NumberFormatException {
        if (this.allowedLogonVersion.equals("12a")) {
            if (!hasServerCompileTimeCapability(4, 32)) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NO_VALID_LOGON_METHOD).fillInStackTrace());
            }
            return;
        }
        try {
            int allowedLogonVersionInteger = Integer.parseInt(this.allowedLogonVersion);
            switch (allowedLogonVersionInteger) {
                case 8:
                case 9:
                    break;
                case 10:
                case 11:
                    if (!hasServerCompileTimeCapability(4, 8)) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NO_VALID_LOGON_METHOD).fillInStackTrace());
                    }
                    break;
                case 12:
                    if (!hasServerCompileTimeCapability(4, 2)) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NO_VALID_LOGON_METHOD).fillInStackTrace());
                    }
                    break;
                default:
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_VALUE_ALLOWEDLOGONVERSION).fillInStackTrace());
            }
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_VALUE_ALLOWEDLOGONVERSION).fillInStackTrace());
        }
    }

    private void doCharSetNegotiation(short oracleVersion, short characterSet, short ncharacterSet) throws SQLException {
        short accessCharSet = DBConversion.findDriverCharSet(characterSet, oracleVersion);
        this.conversion = new DBConversion(characterSet, accessCharSet, ncharacterSet, this.isStrictAsciiConversion, this.isQuickAsciiConversion, this::getByteBuffer, this::cacheBuffer, this::getCharBuffer, this::cacheBuffer);
        this.mare.types.setServerConversion(accessCharSet != characterSet);
        if (DBConversion.isCharSetMultibyte(accessCharSet)) {
            if (DBConversion.isCharSetMultibyte(this.pro.getCharacterSet())) {
                this.mare.types.setFlags((byte) 1);
            } else {
                this.mare.types.setFlags((byte) 2);
            }
        } else {
            this.mare.types.setFlags(this.pro.getFlags());
        }
        this.mare.conv = this.conversion;
    }

    private final void setCLRBigChunksCapability() {
        if (hasServerCompileTimeCapability(37, 32)) {
            this.mare.setUseCLRBigChunks(true);
        }
    }

    private final void setNegotiatedTTCVersion(byte[] jdbcCapabilities) {
        byte serverVersion = this.serverCompileTimeCapabilities[7];
        if (serverVersion >= 14) {
            this.oer = new T4CTTIoer(this);
        } else if (serverVersion >= 7) {
            this.oer = new T4CTTIoer19(this);
        } else {
            this.oer = new T4CTTIoer11(this);
        }
        byte jdbcThinVersion = jdbcCapabilities[7];
        if (jdbcThinVersion < serverVersion) {
            this.negotiatedTTCversion = jdbcThinVersion;
        } else {
            this.negotiatedTTCversion = serverVersion;
        }
    }

    private final void setZeroCopyIOCapability() throws SQLException {
        if (this.serverRuntimeCapabilities != null && this.serverRuntimeCapabilities.length > 6 && (this.serverRuntimeCapabilities[6] & T4C8TTIdty.KPCCAP_RTB_TTC_ZCPY) != 0 && this.thinNetUseZeroCopyIO && (this.net.getSessionAttributes().getNegotiatedOptions() & 64) != 0 && getDataIntegrityAlgorithmName().equals("") && getEncryptionAlgorithmName().equals("")) {
            this.useZeroCopyIO = true;
        } else {
            this.useZeroCopyIO = false;
        }
    }

    private void setDynamicVectorIOCapability() {
        if (this.serverRuntimeCapabilities != null && this.serverRuntimeCapabilities.length > 9) {
            this.useDynamicVectorIO = (this.serverRuntimeCapabilities[T4C8TTIdty.KPCCAP_RTB_TTC1] & T4C8TTIdty.KPCCAP_RTB_TTC1_IOVOFF) == T4C8TTIdty.KPCCAP_RTB_TTC1_IOVOFF;
        }
    }

    boolean useDynamicVectorIO() {
        return this.useDynamicVectorIO;
    }

    private final void setLobPrefetchCapability(byte[] jdbcCapabilities) {
        if (hasServerCompileTimeCapability(23, 64) && bit((int) jdbcCapabilities[23], 64)) {
            this.useLobPrefetch = true;
        } else {
            this.useLobPrefetch = false;
        }
    }

    private final void set32kVarcharCapability() {
        if (this.serverRuntimeCapabilities != null && this.serverRuntimeCapabilities.length > T4C8TTIdty.KPCCAP_RTB_TTC && bit((int) this.serverRuntimeCapabilities[T4C8TTIdty.KPCCAP_RTB_TTC], (int) T4C8TTIdty.KPCCAP_RTB_TTC_32K)) {
            this.maxNonStreamBindByteSize = 32767;
            this.varTypeMaxLenCompat = 2;
        } else {
            this.maxNonStreamBindByteSize = 4000;
            this.varTypeMaxLenCompat = 1;
        }
    }

    private final void setFeatureTrackingCapability() {
        if (this.serverRuntimeCapabilities != null && this.serverRuntimeCapabilities.length > T4C8TTIdty.KPCCAP_RTB_TTC && bit((int) this.serverRuntimeCapabilities[T4C8TTIdty.KPCCAP_RTB_TTC], (int) T4C8TTIdty.KPCCAP_RTB_TTC_FEATURE_TRACK)) {
            this.isFeatureTrackingSupported = true;
        }
    }

    private final void setRequestBoundariesCapability() {
        if (this.serverRuntimeCapabilities != null && this.serverRuntimeCapabilities.length > T4C8TTIdty.KPCCAP_RTB_TTC && bit((int) this.serverRuntimeCapabilities[T4C8TTIdty.KPCCAP_RTB_TTC], (int) T4C8TTIdty.KPCCAP_RTB_TTC_SESSSTATEOPS)) {
            this.svrSupportsRequests = true;
        } else {
            this.svrSupportsRequests = false;
        }
        if (this.svrSupportsRequests && hasServerCompileTimeCapability(40, 64)) {
            this.svrSupportsExplicitRequestBit = true;
        } else {
            this.svrSupportsExplicitRequestBit = false;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean serverSupportsRequestBoundaries() throws SQLException {
        return this.svrSupportsRequests;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean serverSupportsExplicitBoundaryBit() throws SQLException {
        return this.svrSupportsExplicitRequestBit;
    }

    void reNegotiateTTCProDty() throws SQLException, NumberFormatException, IOException {
        this.serverCompileTimeCapabilities = this.pro.receivePacket();
        this.serverRuntimeCapabilities = this.pro.getServerRuntimeCapabilities();
        validateServerLogonCapability();
        doCharSetNegotiation(this.pro.oVersion, this.pro.getCharacterSet(), this.pro.getncharCHARSET());
        this.dty = new T4C8TTIdty(this, this.serverRuntimeCapabilities, this.logonCap != null && this.logonCap.trim().equals("o3"), this.thinNetUseZeroCopyIO);
        this.dty.doRPC();
        postTTIdtyInitialization(this.dty);
        if (!this.isLoggedOn) {
            DATABASE_NEGOTIATED_INFORMATION.get(this.net.getSessionAttributes().getDatabaseUUID()).ifPresent(cookie -> {
                T4CTTICookie updatedCookie = T4CTTICookie.builder().databaseRuntimeCapabilities(this.pro.getServerRuntimeCapabilities()).databaseCompileTimeCapabilities(this.pro.getServerCompileTimeCapabilities()).connectionProtocolVersion(this.pro.getProtocolVersion()).databaseCharSet(this.pro.getCharacterSet()).databaseNCharSet(this.pro.getncharCHARSET()).databaseCharSetFlag(this.pro.getFlags()).databasePortage(this.pro.getSvrPortDescription()).build();
                debug(Level.FINER, SecurityLabel.INTERNAL, CLASS_NAME, "reNegotiateTTCProDty", "update cookie for server {0}: {1} ", null, null, StringUtils.byteArrayToHexUnicode(this.net.getSessionAttributes().getDatabaseUUID().getBytes()), updatedCookie);
                DATABASE_NEGOTIATED_INFORMATION.put(this.net.getSessionAttributes().getDatabaseUUID(), updatedCookie);
            });
        }
    }

    void processTTIDtyResponse() throws SQLException, IOException {
        this.dty.receivePayload();
        postTTIdtyInitialization(this.dty);
    }

    void processTTIProResponse() throws SQLException, IOException, NumberFormatException {
        this.pro.receivePayload();
        validateServerLogonCapability();
        this.serverCompileTimeCapabilities = this.pro.getServerCompileTimeCapabilities();
        this.serverRuntimeCapabilities = this.pro.getServerRuntimeCapabilities();
    }

    boolean isZeroCopyIOEnabled() {
        return this.useZeroCopyIO;
    }

    final T4CTTIoer11 getT4CTTIoer() {
        return this.oer;
    }

    final byte getTTCVersion() {
        return this.negotiatedTTCversion;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doStartup(int mode) throws SQLException {
        try {
            int stomode = 0;
            if (mode == OracleConnection.DatabaseStartupMode.FORCE.getMode()) {
                stomode = 16;
            } else if (mode == OracleConnection.DatabaseStartupMode.RESTRICT.getMode()) {
                stomode = 1;
            }
            this.spfp.doOSPFPPUT();
            this.sto.doOV6STRT(stomode);
            this.isDatabaseShutdown = false;
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doShutdown(int mode) throws SQLException {
        try {
            int stomode = 4;
            if (mode == OracleConnection.DatabaseShutdownMode.TRANSACTIONAL.getMode()) {
                stomode = 128;
            } else if (mode == OracleConnection.DatabaseShutdownMode.TRANSACTIONAL_LOCAL.getMode()) {
                stomode = 256;
            } else if (mode == OracleConnection.DatabaseShutdownMode.IMMEDIATE.getMode()) {
                stomode = 2;
            } else if (mode == OracleConnection.DatabaseShutdownMode.FINAL.getMode()) {
                stomode = 8;
            } else if (mode == OracleConnection.DatabaseShutdownMode.ABORT.getMode()) {
                stomode = 64;
            }
            this.sto.doOV6STOP(stomode);
            this.isDatabaseShutdown = true;
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    void checkEndReplayCallback() throws SQLException {
        if (this.endReplayCallback != null && this.oappcontreplayContextsArr == null) {
            boolean isRndTrpAtOutageNonReq = this.replayModes.contains(ReplayMode.NONREQUEST);
            if (isRndTrpAtOutageNonReq) {
                this.replayModes.remove(ReplayMode.NONREQUEST);
            }
            OracleConnection.EndReplayCallback localCopy = this.endReplayCallback;
            this.endReplayCallback = null;
            T4C8Oall tempAll8 = new T4C8Oall(this);
            T4C8Oall savedAll8 = this.all8;
            this.all8 = tempAll8;
            try {
                localCopy.executeCallback();
                this.all8 = savedAll8;
                if (isRndTrpAtOutageNonReq) {
                    this.replayModes.add(ReplayMode.NONREQUEST);
                }
            } catch (Throwable th) {
                this.all8 = savedAll8;
                if (isRndTrpAtOutageNonReq) {
                    this.replayModes.add(ReplayMode.NONREQUEST);
                }
                throw th;
            }
        }
    }

    void redoCursorClose() {
        if (this.cursorToCloseOffset == 0 && this.lastCursorToCloseOffset != 0) {
            this.cursorToCloseOffset = this.lastCursorToCloseOffset;
            this.lastCursorToCloseOffset = 0;
        }
    }

    void sendPiggyBackedMessages() throws SQLException, IOException {
        ResultSetCache cache;
        if (this.switchFromProxySession) {
            this.oses.doO80SES(this.sessionId, this.serialNumber, 1);
            this.switchFromProxySession = false;
        }
        if (this.queryToCloseOffset > 0) {
            this.close8.doOCANA(this.queryToClose, this.queryToCloseOffset);
            this.queryToCloseOffset = 0;
        }
        if (this.cursorToCloseOffset > 0 && canSendCursorIds()) {
            this.close8.doOCCA(this.cursorToClose, this.cursorToCloseOffset);
            this.lastCursorToCloseOffset = this.cursorToCloseOffset;
            this.cursorToCloseOffset = 0;
        }
        this.sessionlessTxn.sendPiggyBackMessages();
        if (this.endToEndAnyChanged && getTTCVersion() >= 3) {
            this.oscid.doOSCID(this.endToEndHasChanged, this.endToEndValues, this.endToEndECIDSequenceNumber);
            for (int i = 0; i < 6; i++) {
                if (this.endToEndHasChanged[i]) {
                    this.endToEndHasChanged[i] = false;
                }
            }
        }
        this.endToEndAnyChanged = false;
        if (!this.namespaces.isEmpty() && this.isLoggedOn) {
            if (getTTCVersion() >= 4) {
                Object[] namespacesArr = this.namespaces.values().toArray();
                for (Object obj : namespacesArr) {
                    this.okeyval.doOKEYVAL((Namespace) obj);
                }
            }
            this.namespaces.clear();
        }
        if (this.lusOffset2 > 0) {
            for (int i2 = 0; i2 < this.lusOffset2; i2++) {
                this.oxsspo.doOXSSPO(this.lusFunctionId2[i2], this.lusSessionId2[i2], this.lusInKeyVal2[i2], this.lusInFlags2[i2]);
            }
            this.lusOffset2 = 0;
        }
        if (this.isResultSetCacheEnabled && this.oqcsta != null && (cache = getResultSetCacheInternal()) != null && cache.needToSendStatsResetIfTrue()) {
            long cacheSize = cache.getCurrentCacheSize();
            long maxBlocks = cacheSize / RESULTSET_CACHE_BLOCK_SIZE_FOR_STAT;
            if (cacheSize % RESULTSET_CACHE_BLOCK_SIZE_FOR_STAT > 0) {
                maxBlocks++;
            }
            this.oqcsta.doOQCSTA(this, this.mare, RESULTSET_CACHE_BLOCK_SIZE_FOR_STAT, maxBlocks, maxBlocks, 0L, cache.getNumberOfCacheEntries(), cache.getInvalidatedBeforeCompletion(), cache.getCacheHits(), cache.getInvalidationCount(), cache.getInvalidatedQueryCount(), cache.getValidQueriesPurged());
            this.oqcsta.connection = null;
            this.oqcsta.meg = null;
        }
        sendOsesssstateFlags();
        if (!this.replayModes.contains(ReplayMode.NONREQUEST) && this.oappcontreplayContextsArr != null) {
            while (this.oappcontreplayOffset < this.oappcontreplayContextsArr.length - 1 && this.oappcontreplayContextsArr[this.oappcontreplayOffset] == null) {
                this.oappcontreplayOffset++;
            }
            if (this.oappcontreplayContextsArr[this.oappcontreplayOffset] != null && this.oappcontreplayContextsArr[this.oappcontreplayOffset].getContext() != null) {
                this.oappcontreplay.doOAPPCONTREPLAY(this.oappcontreplayContextsArr[this.oappcontreplayOffset]);
            }
            if (this.oappcontreplayOffset == this.oappcontreplayContextsArr.length - 1) {
                this.oappcontreplayContextsArr = null;
            } else {
                this.oappcontreplayOffset++;
            }
        }
        if (!this.replayModes.contains(ReplayMode.NONREQUEST) && this.sessionStateOut != null) {
            this.osesstemplate.doOSESSTEMPLATE(this.sessionStateOut);
            this.sessionStateOut = null;
        }
        if (this.isResultSetCacheEnabled && this.resultSetCache != null) {
            ResultSetCache.ResultSetCacheState state = this.resultSetCache.getState();
            if (state == ResultSetCache.ResultSetCacheState.STARTING && this.resultSetCache.getRegistrationId() != -1) {
                byte[] cacheId = this.resultSetCache.getCacheId();
                long registrationId = this.resultSetCache.getRegistrationId();
                this.oqcid.doOQCID(cacheId, registrationId);
            }
        }
        if (this.shardingKey != null || this.superShardingKey != null || this.chunkName != null) {
            this.piggyBackOchunkinfo.doOCHUNKINFO(this.shardingKey, this.superShardingKey, this.chunkName, true);
            this.shardingKey = null;
            this.superShardingKey = null;
            this.chunkName = null;
        }
        sendOCLFEATURES();
    }

    void sendPiggyBackedMessages(boolean isClose) throws SQLException, IOException {
        sendPiggyBackedMessages();
        if (!isClose) {
            this.lobMsg.doFreeLobPiggyback();
        } else {
            this.lobMsg.resetLobPiggyback();
        }
    }

    private void sendOCLFEATURES() throws SQLException, IOException {
        if (this.commoncall.getFunCode() == 9 && !this.isProxySessionLogoff && this.isFeatureTrackingSupported && !this.isDatabaseShutdown) {
            if (this.isResultSetCacheEnabled) {
                addFeature(OracleConnection.ClientFeature.CACHED_RESULTSET);
            }
            this.oclFeatures.doOCLFEATURES();
        }
    }

    void sendOsesssstateFlags() throws SQLException, IOException {
        assertLockHeldByCurrentThread();
        if (!$assertionsDisabled && this.replayModes.contains(ReplayMode.RUNTIME_REPLAY_ENABLED) && this.replayModes.contains(ReplayMode.REPLAYING)) {
            throw new AssertionError("RUNTIME_REPLAY_ENABLED and REPLAYING modes cannot bet set at the same time");
        }
        if (this.replayModes.contains(ReplayMode.NONREQUEST)) {
            if (this.replayModes.contains(ReplayMode.RUNTIME_REPLAY_ENABLED)) {
                if (this.osessstateFlags == -1) {
                    this.osessstateFlags = 0L;
                }
                this.osessstateFlags |= OracleConnection.ReplayOperation.KPDSS_SESSSTATE_NONREQUEST_CALL.getCode() | OracleConnection.ReplayOperation.KPDSS_SESSSTATE_APPCONT_ENABLED.getCode();
            } else if (this.replayModes.contains(ReplayMode.REPLAYING)) {
                this.osessstateFlags = OracleConnection.ReplayOperation.KPDSS_SESSSTATE_NONREQUEST_CALL.getCode();
            }
            if (this.replayModes.contains(ReplayMode.RUNTIME_OR_REPLAYING_STATIC)) {
                this.osessstateFlags |= OracleConnection.ReplayOperation.KPDSS_SESSSTATE_STATIC.getCode();
            }
        }
        if (this.osessstateFlags >= 0) {
            this.osesstate.doOSESSSTATE(this.osessstateFlags, false);
        }
        this.osessstateFlags = -1L;
    }

    void closeCursor(int cursorId) {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "closeCursor", "cursorId={0}. ", (String) null, (String) null, (Object) Integer.valueOf(cursorId));
        if (this.cursorToCloseOffset == this.cursorToClose.length) {
            int[] cursorToClose2 = new int[this.cursorToClose.length * 2];
            System.arraycopy(this.cursorToClose, 0, cursorToClose2, 0, this.cursorToClose.length);
            this.cursorToClose = cursorToClose2;
        }
        int[] iArr = this.cursorToClose;
        int i = this.cursorToCloseOffset;
        this.cursorToCloseOffset = i + 1;
        iArr[i] = cursorId;
    }

    void closeQuery(int cursorId) {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "closeQuery", "cursorId={0}. ", (String) null, (String) null, (Object) Integer.valueOf(cursorId));
        assertLockHeldByCurrentThread();
        if (this.queryToCloseOffset == this.queryToClose.length) {
            int[] queryToClose2 = new int[this.queryToClose.length * 2];
            System.arraycopy(this.queryToClose, 0, queryToClose2, 0, this.queryToClose.length);
            this.queryToClose = queryToClose2;
        }
        int[] iArr = this.queryToClose;
        int i = this.queryToCloseOffset;
        this.queryToCloseOffset = i + 1;
        iArr[i] = cursorId;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doProxySession(int type, @Blind(PropertiesBlinder.class) Properties prop) throws SQLException, NumberFormatException {
        if (type == 1) {
            try {
                String clientPassword = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_PASSWORD);
                if (clientPassword != null && clientPassword.length() != 0) {
                    String clientUser = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME);
                    this.auth.doOSESSKEY(clientUser, 0L);
                }
            } catch (IOException ioe) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioe).fillInStackTrace());
            }
        }
        this.auth.doOAUTH(type, prop, this.sessionId, this.serialNumber);
        int prox_session_id = getSessionId();
        int prox_serial_nb = getSerialNumber();
        this.currentSchema = null;
        this.oses.doO80SES(prox_session_id, prox_serial_nb, 1);
        this.savedUser = this.userName;
        if (type == 1) {
            this.userName = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME);
        } else {
            this.userName = null;
        }
        this.isProxy = true;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void closeProxySession() throws SQLException {
        try {
            try {
                this.isProxySessionLogoff = true;
                this.commoncall.doOLOGOFF();
                this.switchFromProxySession = true;
                this.userName = this.savedUser;
                this.currentSchema = null;
                this.isProxySessionLogoff = false;
            } catch (IOException ioe) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioe).fillInStackTrace());
            }
        } catch (Throwable th) {
            this.isProxySessionLogoff = false;
            throw th;
        }
    }

    void updateSessionProperties(KeywordValue[] kvalArr) throws SQLException {
        int hour;
        int minute;
        String tz;
        if (this.sessionProperties == null) {
            this.sessionProperties = new Properties();
        }
        for (int i = 0; i < kvalArr.length; i++) {
            int keyword = kvalArr[i].getKeyword();
            byte[] value = kvalArr[i].getBinaryValue();
            if (keyword < T4C8Oall.NLS_KEYS.length) {
                String key = T4C8Oall.NLS_KEYS[keyword];
                if (key != null) {
                    if (value != null) {
                        updateSessionProperties(key, this.mare.conv.CharBytesToString(value, value.length));
                    } else if (kvalArr[i].getTextValue() != null) {
                        updateSessionProperties(key, kvalArr[i].getTextValue().trim());
                    }
                }
            } else if (keyword == 163) {
                if (value != null) {
                    String regionName = null;
                    if ((value[2] & 255) > 120) {
                        int regid = (value[2] & Byte.MAX_VALUE) << 6;
                        regionName = ZONEIDMAP.getRegion(regid + ((value[3] & 252) >> 2));
                        hour = (value[4] & 255) - 181;
                        minute = (value[5] & 255) - 60;
                    } else {
                        hour = (value[4] & 255) - 60;
                        minute = (value[5] & 255) - 60;
                    }
                    if (regionName == null || regionName.equals("")) {
                        tz = "GMT" + (hour > 0 ? "+" : "") + hour + (minute <= 9 ? ":0" : ":") + minute;
                    } else {
                        tz = regionName;
                    }
                    updateSessionProperties("SESSION_TIME_ZONE", tz);
                    this.sessionTimeZone = tz;
                }
            } else if (keyword != 165 && keyword != 166 && keyword != 167) {
                if (keyword == 168) {
                    String schema = kvalArr[i].getTextValue();
                    if (schema != null) {
                        this.currentSchema = schema.trim();
                        updateSessionProperties("AL8KW_SCHEMA_NAME", this.currentSchema);
                    }
                } else if (keyword != 169) {
                    if (keyword == 199) {
                        String roleNames = kvalArr[i].getTextValue();
                        if (roleNames != null) {
                            updateSessionProperties("AL8KW_ENABLED_ROLE_NAMES", roleNames.trim());
                        }
                    } else if (keyword == 171) {
                        if (value != null) {
                            updateSessionProperties("AL8KW_AUX_SESSSTATE", this.mare.conv.CharBytesToString(value, value.length));
                        }
                    } else if (keyword == 175) {
                        updateMaximumCursorCount(value == null ? null : this.mare.conv.CharBytesToString(value, value.length));
                    } else if (keyword != 176) {
                        if (keyword == 177) {
                            long dbID = 0;
                            for (int j = 3; j >= 0; j--) {
                                dbID |= (value[3 - j] & 255) << (8 * j);
                            }
                            String dbIDStr = String.valueOf(dbID);
                            String currentDbID = this.sessionProperties.getProperty("AUTH_DB_ID");
                            if (!currentDbID.equals(dbIDStr)) {
                                this.isPDBChanged = true;
                                if (inLocalTransaction()) {
                                    this.disableCommitOptimizationOnPDBChange = true;
                                }
                                updateSessionProperties("AUTH_DB_ID", dbIDStr);
                            }
                        } else if (keyword != 178) {
                            if (keyword == 179) {
                                if (kvalArr[i].getTextValue() != null) {
                                    this.dbName = kvalArr[i].getTextValue();
                                    updateSessionProperties("AUTH_DBNAME", this.dbName);
                                }
                            } else if (keyword == 164) {
                                if (value != null && value.length == 1) {
                                    String errOvlap = value[0] > 0 ? "TRUE" : "FALSE";
                                    updateSessionProperties("AL8KW_ERR_OVLAP", errOvlap);
                                }
                            } else if (keyword == 172) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String editionStr = kvalArr[i].getTextValue();
                                    updateSessionProperties("AUTH_ORA_EDITION", editionStr);
                                }
                            } else if (keyword == 197) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String containerName = kvalArr[i].getTextValue();
                                    updateSessionProperties("CONTAINER_NAME", containerName.trim());
                                }
                            } else if (keyword == 183) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String svcName = kvalArr[i].getTextValue();
                                    updateSessionProperties("SERVICE_NAME", svcName.trim());
                                }
                            } else if (keyword == 173) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String sqlTxlp = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_SQL_TXLP", sqlTxlp);
                                }
                            } else if (keyword == 174) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String fsqlSyntax = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_FSQL_SNTX", fsqlSyntax);
                                }
                            } else if (keyword == 187) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String archivalStr = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_ROW_ARCHIVAL", archivalStr);
                                }
                            } else if (keyword == 184) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String module = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_MODULE", module);
                                    boolean isClntValSet = false;
                                    String e2eCxtValSet = this.clientInfo.getProperty(E2E_CONTEXT_MODULE);
                                    String ocsidValSet = this.clientInfo.getProperty(OCSID_MODULE);
                                    if (e2eCxtValSet != null && !"".equals(e2eCxtValSet)) {
                                        this.clientInfo.put(E2E_CONTEXT_MODULE, module);
                                        isClntValSet = true;
                                    }
                                    if (ocsidValSet != null && !"".equals(ocsidValSet)) {
                                        this.clientInfo.put(OCSID_MODULE, module);
                                        isClntValSet = true;
                                    }
                                    if (!isClntValSet) {
                                        this.clientInfo.put(E2E_CONTEXT_MODULE, module);
                                        this.clientInfo.put(OCSID_MODULE, module);
                                    }
                                }
                            } else if (keyword == 185) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String action = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_ACTION", action);
                                    boolean isClntValSet2 = false;
                                    String e2eCxtValSet2 = this.clientInfo.getProperty(E2E_CONTEXT_ACTION);
                                    String ocsidValSet2 = this.clientInfo.getProperty(OCSID_ACTION);
                                    if (e2eCxtValSet2 != null && !"".equals(e2eCxtValSet2)) {
                                        this.clientInfo.put(E2E_CONTEXT_ACTION, action);
                                        isClntValSet2 = true;
                                    }
                                    if (ocsidValSet2 != null && !"".equals(ocsidValSet2)) {
                                        this.clientInfo.put(OCSID_ACTION, action);
                                        isClntValSet2 = true;
                                    }
                                    if (!isClntValSet2) {
                                        this.clientInfo.put(E2E_CONTEXT_ACTION, action);
                                        this.clientInfo.put(OCSID_ACTION, action);
                                    }
                                }
                            } else if (keyword == 186) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String clientInfoStr = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_CLIENT_INFO", clientInfoStr);
                                    boolean isClntValSet3 = false;
                                    String e2eCxtValSet3 = this.clientInfo.getProperty(E2E_CONTEXT_CLIENT_INFO);
                                    String ocsidValSet3 = this.clientInfo.getProperty(OCSID_CLIENT_INFO);
                                    if (e2eCxtValSet3 != null && !"".equals(e2eCxtValSet3)) {
                                        this.clientInfo.put(E2E_CONTEXT_CLIENT_INFO, clientInfoStr);
                                        isClntValSet3 = true;
                                    }
                                    if (ocsidValSet3 != null && !"".equals(ocsidValSet3)) {
                                        this.clientInfo.put(OCSID_CLIENT_INFO, clientInfoStr);
                                        isClntValSet3 = true;
                                    }
                                    if (!isClntValSet3) {
                                        this.clientInfo.put(E2E_CONTEXT_CLIENT_INFO, clientInfoStr);
                                        this.clientInfo.put(OCSID_CLIENT_INFO, clientInfoStr);
                                    }
                                }
                            } else if (keyword == 198) {
                                if (kvalArr[i].getTextValue() != null) {
                                    String clientIdStr = kvalArr[i].getTextValue();
                                    updateSessionProperties("AL8KW_CLIENT_ID", clientIdStr);
                                    boolean isClntValSet4 = false;
                                    String e2eCxtValSet4 = this.clientInfo.getProperty(E2E_CONTEXT_CLIENT_ID);
                                    String ocsidValSet4 = this.clientInfo.getProperty(OCSID_CLIENT_ID);
                                    if (e2eCxtValSet4 != null && !"".equals(e2eCxtValSet4)) {
                                        this.clientInfo.put(E2E_CONTEXT_CLIENT_ID, clientIdStr);
                                        isClntValSet4 = true;
                                    }
                                    if (ocsidValSet4 != null && !"".equals(ocsidValSet4)) {
                                        this.clientInfo.put(OCSID_CLIENT_ID, clientIdStr);
                                        isClntValSet4 = true;
                                    }
                                    if (!isClntValSet4) {
                                        this.clientInfo.put(E2E_CONTEXT_CLIENT_ID, clientIdStr);
                                        this.clientInfo.put(OCSID_CLIENT_ID, clientIdStr);
                                    }
                                }
                            } else if (keyword == 200) {
                                if (kvalArr[i].getBinaryValue() != null) {
                                    int rowPrefetchDirective = ByteBuffer.wrap(kvalArr[i].getBinaryValue()).getInt();
                                    if (!this.isRowPrefetchSetExplicitly && rowPrefetchDirective != 0) {
                                        this.defaultRowPrefetch = rowPrefetchDirective;
                                    }
                                    updateSessionProperties("AL8KW_PREFETCH_ROWS", String.valueOf(rowPrefetchDirective));
                                }
                            } else if (keyword == 201) {
                                debugp(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "updateSessionProperties", "Client GTRID = {0}", (String) null, (Throwable) null, () -> {
                                    return new Object[]{Arrays.toString(this.sessionlessTxn.getGTRID())};
                                });
                                if (value != null) {
                                    byte flags = value[value.length - 2];
                                    if ((flags & (-64)) == -128) {
                                        if ((flags & 63) == 3 && !inSessionlessTxnMode() && getAutoCommit()) {
                                            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "updateSessionProperties", "Sessionless transaction has ended, autocommit is on!", null, null);
                                        }
                                        this.sessionlessTxn.setGTRID(null);
                                        this.sessionlessTxn.exitSessionlessTxnMode();
                                    } else if ((flags & (-64)) == 64) {
                                        byte[] gtrid = Arrays.copyOfRange(value, 0, value.length - 2);
                                        debugp(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "updateSessionProperties", "AL8KW_GTRID = {0}", (String) null, (Throwable) null, () -> {
                                            return new Object[]{Arrays.toString(gtrid)};
                                        });
                                        if (getTxnMode() != 3) {
                                            this.sessionlessTxn.enterSessionlessTxnMode();
                                        }
                                        if ((flags & 63) == 2 && this.sessionlessTxn.getGTRID() != null && !this.sessionlessTxn.getGTRID().equals(gtrid)) {
                                            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "updateSessionProperties", "Client GTRID is incompatible with server GTRID!!", null, null);
                                        }
                                        this.sessionlessTxn.setGTRID(gtrid);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "updateSessionProperties", "session Properties={0}. ", (String) null, (String) null, (Object) this.sessionProperties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void updateSessionProperties(String propertyName, String propertyValue) {
        super.updateSessionProperties(propertyName, propertyValue);
        if (this.enableResetState) {
            this.needToResetSessionProperties = true;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void updateSessionProperties(Properties props) {
        super.updateSessionProperties(props);
        if (this.enableResetState) {
            this.needToResetSessionProperties = true;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void onPDBChange(OracleStatement catalyst) throws SQLException {
        super.onPDBChange(catalyst);
        this.databaseUniqueIdentifier = this.sessionProperties.getProperty("AUTH_DB_ID");
        notify(new NTFPDBChangeEvent(this));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected OracleStatement getCachedStatementWithKey(String key, int statementType) throws SQLException {
        OracleStatement cachedStatement = super.getCachedStatementWithKey(key, statementType);
        if (cachedStatement instanceof T4CDirectPathPreparedStatement) {
            setDirectPathState((T4CDirectPathPreparedStatement) cachedStatement);
        }
        return cachedStatement;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                PreparedStatement preparedStatementPrepareDirectPathInternal = prepareDirectPathInternal(schemaName, tableName, colNames, null, null);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return preparedStatementPrepareDirectPathInternal;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                PreparedStatement preparedStatementPrepareDirectPathInternal = prepareDirectPathInternal(schemaName, tableName, colNames, null, dpStmtProps);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return preparedStatementPrepareDirectPathInternal;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, String partitionName) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (partitionName != null) {
                if (partitionName.length() != 0) {
                    PreparedStatement preparedStatementPrepareDirectPathInternal = prepareDirectPathInternal(schemaName, tableName, colNames, partitionName, null);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return preparedStatementPrepareDirectPathInternal;
                }
            }
            throw new IllegalArgumentException("Table partition name argument cannot be null or empty");
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, String partitionName, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (partitionName != null) {
                if (partitionName.length() != 0) {
                    PreparedStatement preparedStatementPrepareDirectPathInternal = prepareDirectPathInternal(schemaName, tableName, colNames, partitionName, dpStmtProps);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return preparedStatementPrepareDirectPathInternal;
                }
            }
            throw new IllegalArgumentException("Table partition name argument cannot be null or empty");
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private PreparedStatement prepareDirectPathInternal(String schemaName, String tableName, String[] colNames, String partitionName, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException {
        if (tableName == null || tableName.length() == 0) {
            throw new IllegalArgumentException("Table name argument cannot be null or empty");
        }
        if (colNames == null || colNames.length == 0) {
            throw new IllegalArgumentException("Column name list argument cannot be null or empty");
        }
        if (this.dppstmt != null && !this.dppstmt.isDirectPathCommitted()) {
            throw new IllegalStateException("Multiple Direct Path statements are not allowed");
        }
        if (getAutoCommit()) {
            throw new IllegalStateException("Auto commit should be turned OFF");
        }
        if (this.dppstmt != null && !this.dppstmt.isDirectPathClosed()) {
            this.dppstmt.close();
        }
        this.dppstmt = null;
        try {
            OracleResultSet.ResultSetType resultSetType = OracleResultSet.ResultSetType.FORWARD_READ_ONLY;
            String sql = T4CDirectPathPreparedStatement.getSQLStatement(schemaName, tableName, colNames, partitionName, this);
            if (this.statementCache != null) {
                this.dppstmt = (T4CDirectPathPreparedStatement) this.statementCache.searchImplicitCache(sql, 1, resultSetType.ordinal(), this);
                if (this.dppstmt != null) {
                    this.dppstmt.dpStmtProps = dpStmtProps;
                }
            }
            if (this.dppstmt == null) {
                this.dppstmt = new T4CDirectPathPreparedStatement(this, schemaName, tableName, colNames, partitionName, resultSetType, dpStmtProps, sql);
            }
            this.odpp.doODPP(schemaName, tableName, colNames, partitionName, dpStmtProps);
            setTxnMode(0);
            this.dppstmt.setDirectPathCursor((int) this.odpp.getO4Value(3));
            this.dppstmt.setSDBAOfBits((int) this.odpp.getO4Value(5));
            this.dppstmt.setSDBABits((int) this.odpp.getO4Value(7));
            this.dppstmt.setDBABBits((int) this.odpp.getO4Value(8));
            this.dppstmt.updateAccessors(this.odpp.getDescribedAccessors());
            this.dppstmt.setDirectPathStatus(1);
            return this.dppstmt;
        } catch (IOException ex) {
            this.dppstmt = null;
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        } catch (SQLException sqe) {
            this.dppstmt = null;
            throw sqe;
        }
    }

    void directPathLoadStream(DirectPathBufferMarshaler.BufferPlanner bufferPlanner, int _directPathCursor, int[] errorOffsets) throws SQLException {
        try {
            this.odpls.doODPLS(_directPathCursor, bufferPlanner);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        } catch (SQLException sqe) {
            if (errorOffsets != null) {
                errorOffsets[0] = this.odpls.startErrorOffset;
                errorOffsets[1] = this.odpls.endErrorOffset;
            }
            throw sqe;
        }
    }

    void directPathFinish() throws SQLException {
        if (this.dppstmt == null || !this.dppstmt.isDirectPathUncommitted()) {
            throw new IllegalStateException("Cannot finish direct path load before calling prepare!");
        }
        try {
            this.odpmop.doDPMOP(2, this.dppstmt.getDirectPathCursor(), null);
            this.dppstmt.setDirectPathStatus(3);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    void directPathAbort() throws SQLException {
        if (this.dppstmt == null || !this.dppstmt.isDirectPathUncommitted()) {
            throw new IllegalStateException("Cannot abort direct path load before calling prepare!");
        }
        try {
            this.odpmop.doDPMOP(1, this.dppstmt.getDirectPathCursor(), null);
            this.dppstmt.setDirectPathStatus(4);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    void clearDirectPathState() {
        this.dppstmt = null;
    }

    void setDirectPathState(T4CDirectPathPreparedStatement _dppstmt) {
        this.dppstmt = _dppstmt;
        if (this.dppstmt != null && this.dppstmt.isDirectPathClosed()) {
            this.dppstmt.setDirectPathStatus(3);
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public byte[] doBeginSaga(String initiatorName, int timeout, String currentUser, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    byte[] sagaId = this.osaga.doOSAGA(opcode, flags, timeout, version, null, initiatorName, null, null, currentUser, spareNumeric, spareText);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return sagaId;
                } catch (IOException ioex) {
                    handleIOException(ioex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                }
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public Integer doJoinSaga(String participantName, byte[] sagaId, String coordinatorName, String initiatorName, int timeout, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] joinstatus = this.osaga.doOSAGA(opcode, flags, timeout, version, sagaId, initiatorName, participantName, coordinatorName, null, spareNumeric, spareText);
                if (joinstatus == null || joinstatus.length == 0) {
                    return null;
                }
                return Integer.valueOf(joinstatus[0]);
            } finally {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            }
        } catch (IOException ioex) {
            handleIOException(ioex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public Integer doCommitRollbackSaga(String participantName, byte[] sagaId, String currentUser, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] sagastatus = this.osaga.doOSAGA(opcode, flags, 86400, 1, sagaId, participantName, null, null, currentUser, spareNumeric, spareText);
                if (sagastatus == null || sagastatus.length == 0) {
                    return null;
                }
                return Integer.valueOf(sagastatus[0]);
            } finally {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            }
        } catch (IOException ioex) {
            handleIOException(ioex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getCurrentSchema() throws SQLException {
        requireOpenConnection();
        if (this.currentSchema == null || getVersionNumber() < 11100) {
            this.currentSchema = super.getCurrentSchema();
        }
        return this.currentSchema;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getSessionTimeZoneOffset() throws SQLException {
        String ret;
        String ret2 = getServerSessionInfo().getProperty("SESSION_TIME_ZONE");
        if (ret2 == null) {
            ret = super.getSessionTimeZoneOffset();
        } else {
            ret = tzToOffset(ret2);
        }
        return ret;
    }

    int getSessionId() throws NumberFormatException {
        int sessionId = -1;
        String valueStr = this.sessionProperties.getProperty("AUTH_SESSION_ID");
        try {
            sessionId = Integer.parseInt(valueStr);
        } catch (NumberFormatException e) {
        }
        return sessionId;
    }

    int getSerialNumber() throws NumberFormatException {
        int serialNumber = -1;
        String valueStr = this.sessionProperties.getProperty("AUTH_SERIAL_NUM");
        try {
            serialNumber = Integer.parseInt(valueStr);
        } catch (NumberFormatException e) {
        }
        return serialNumber;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte getInstanceProperty(OracleConnection.InstanceProperty whatProperty) throws SQLException {
        byte ret = 0;
        if (whatProperty == OracleConnection.InstanceProperty.ASM_VOLUME_SUPPORTED) {
            if (this.serverRuntimeCapabilities == null || this.serverRuntimeCapabilities.length < 6) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 256).fillInStackTrace());
            }
            ret = this.serverRuntimeCapabilities[5];
        } else if (whatProperty == OracleConnection.InstanceProperty.INSTANCE_TYPE) {
            if (this.serverRuntimeCapabilities == null || this.serverRuntimeCapabilities.length < 4) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 256).fillInStackTrace());
            }
            ret = this.serverRuntimeCapabilities[3];
        }
        return ret;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public BlobDBAccess createBlobDBAccess() throws SQLException {
        return this;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public ClobDBAccess createClobDBAccess() throws SQLException {
        return this;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public BfileDBAccess createBfileDBAccess() throws SQLException {
        return this;
    }

    @Override // oracle.sql.BfileDBAccess
    public long length(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("length");
            assertNotNull(bfile.shareBytes(), "length");
            needLine();
            try {
                long length = this.bfileMsg.getLength(bfile.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return length;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public long position(oracle.jdbc.internal.OracleBfile bfile, Datum bfileDatum, byte[] pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertNotNull(bfile.shareBytes(), "position");
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            debugp(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "position", "pattern={0}, start={1}. ", (String) null, (Throwable) null, () -> {
                return new Object[]{Arrays.toString(pattern), Long.valueOf(start)};
            });
            long result = LobPlsqlUtil.hasPattern(bfile, bfileDatum, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public long position(oracle.jdbc.internal.OracleBfile bfile, Datum bfileDatum, Datum pattern, long start) throws SQLException {
        assertNotNull(bfile.shareBytes(), "position");
        if (start < 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        long result = LobPlsqlUtil.isSubLob(bfile, bfileDatum, pattern, start);
        return result == 0 ? -1L : result;
    }

    @Override // oracle.sql.BfileDBAccess
    public int getBytes(oracle.jdbc.internal.OracleBfile bfile, long pos, int length, byte[] bytes) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("getBytes");
            assertNotNull(bfile.shareBytes(), "getBytes");
            if (pos < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "getBytes()").fillInStackTrace());
            }
            if (length <= 0 || bytes == null) {
                return 0;
            }
            if (this.pipeState != -1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0226, "getBytes()").fillInStackTrace());
            }
            needLine();
            try {
                int i = (int) this.bfileMsg.read(bfile.shareBytes(), pos, length, bytes, 0);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return i;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public String getName(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        assertLoggedOn("getName");
        assertNotNull(bfile.shareBytes(), "getName");
        String result = LobPlsqlUtil.fileGetName(bfile);
        return result;
    }

    @Override // oracle.sql.BfileDBAccess
    public String getDirAlias(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        assertLoggedOn("getDirAlias");
        assertNotNull(bfile.shareBytes(), "getDirAlias");
        String result = LobPlsqlUtil.fileGetDirAlias(bfile);
        return result;
    }

    @Override // oracle.sql.BfileDBAccess
    public void openFile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("openFile");
            assertNotNull(bfile.shareBytes(), "openFile");
            needLine();
            try {
                this.bfileMsg.openLob(bfile.shareBytes(), 11);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public boolean isFileOpen(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("openFile");
            assertNotNull(bfile.shareBytes(), "openFile");
            needLine();
            try {
                boolean zIsOpenLob = this.bfileMsg.isOpenLob(bfile.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsOpenLob;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public boolean fileExists(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("fileExists");
            assertNotNull(bfile.shareBytes(), "fileExists");
            needLine();
            try {
                boolean zDoesExist = this.bfileMsg.doesExist(bfile.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zDoesExist;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public void closeFile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("closeFile");
                assertNotNull(bfile.shareBytes(), "closeFile");
                needLine();
                try {
                    this.bfileMsg.closeLob(bfile.shareBytes());
                    removeBfile(bfile);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public void openLob(oracle.jdbc.internal.OracleBfile bfile, int mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("open");
            assertNotNull(bfile.shareBytes(), "open");
            needLine();
            try {
                this.bfileMsg.openLob(bfile.shareBytes(), mode);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public void closeLob(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("close");
                assertNotNull(bfile.shareBytes(), "close");
                needLine();
                try {
                    this.bfileMsg.closeLob(bfile.shareBytes());
                    removeBfile(bfile);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public boolean isOpenLob(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("isOpen");
            assertNotNull(bfile.shareBytes(), "isOpen");
            needLine();
            try {
                boolean zIsOpenLob = this.bfileMsg.isOpenLob(bfile.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsOpenLob;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBfile bfile, int chunkSize, long pos) throws SQLException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "newInputStream", "chunkSize={0}, pos={1}. ", (String) null, (Throwable) null, Integer.valueOf(chunkSize), Long.valueOf(pos));
        if (pos == 0) {
            return new OracleBlobInputStream(bfile, chunkSize);
        }
        return new OracleBlobInputStream(bfile, chunkSize, pos);
    }

    @Override // oracle.sql.BfileDBAccess
    public InputStream newConversionInputStream(oracle.jdbc.internal.OracleBfile bfile, int conversionType) throws SQLException {
        assertNotNull(bfile.shareBytes(), "newConversionInputStream");
        InputStream result = new OracleConversionInputStream(this.conversion, bfile.getBinaryStream(), conversionType, getPhysicalConnection());
        return result;
    }

    @Override // oracle.sql.BfileDBAccess
    public Reader newConversionReader(oracle.jdbc.internal.OracleBfile bfile, int conversionType) throws SQLException {
        assertNotNull(bfile.shareBytes(), "newConversionReader");
        Reader result = new OracleConversionReader(this.conversion, bfile.getBinaryStream(), conversionType, getPhysicalConnection());
        return result;
    }

    @Override // oracle.sql.BlobDBAccess
    public long length(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("length");
                assertNotNull(blob.shareBytes(), "length");
                needLine();
                try {
                    long result = this.blobMsg.getLength(blob.shareBytes());
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return result;
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public long position(oracle.jdbc.internal.OracleBlob blob, Datum blobDatum, byte[] pattern, long start) throws SQLException {
        assertLoggedOn("position");
        assertNotNull(blob.shareBytes(), "position");
        if (start < 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        long result = LobPlsqlUtil.hasPattern(blob, blobDatum, pattern, start);
        return result == 0 ? -1L : result;
    }

    @Override // oracle.sql.BlobDBAccess
    public long position(oracle.jdbc.internal.OracleBlob blob, Datum blobDatum, Datum pattern, long start) throws SQLException {
        assertLoggedOn("position");
        assertNotNull(blob.shareBytes(), "position");
        assertNotNull(pattern.shareBytes(), "position");
        if (start < 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        long result = LobPlsqlUtil.isSubLob(blob, blobDatum, pattern, start);
        return result == 0 ? -1L : result;
    }

    @Override // oracle.sql.BlobDBAccess
    public int getBytes(oracle.jdbc.internal.OracleBlob blob, long pos, int length, byte[] bytes) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("getBytes");
            assertNotNull(blob.shareBytes(), "getBytes");
            if (pos < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "getBytes()").fillInStackTrace());
            }
            if (this.pipeState != -1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0226, "getBytes()").fillInStackTrace());
            }
            if (length <= 0 || bytes == null) {
                return 0;
            }
            long result = 0;
            long lobLength = -1;
            if (blob.isActivePrefetch()) {
                result = 0 + copyPrefetchedBlobBytes(blob, pos, length, bytes);
                lobLength = blob.lengthInternal();
            }
            if (result < length && (lobLength == -1 || (pos - 1) + result < lobLength)) {
                needLine();
                try {
                    result += this.blobMsg.read(blob.shareBytes(), pos + result, length - result, bytes, (int) result);
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            }
            int i = (int) result;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private final int copyPrefetchedBlobBytes(oracle.jdbc.internal.OracleBlob blob, long pos, int length, byte[] bytes) {
        OracleLargeObject.PrefetchData<byte[]> prefetchData = blob.getPrefetchData();
        if (prefetchData != null && pos <= prefetchData.length()) {
            return prefetchData.copy((int) (pos - 1), bytes, 0, length);
        }
        return 0;
    }

    @Override // oracle.sql.BlobDBAccess
    public int putBytes(oracle.jdbc.internal.OracleBlob blob, long pos, byte[] bytes, int bytesOffset, int length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("putBytes");
            assertNotNull(blob.shareBytes(), "putBytes");
            if (pos < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "putBytes()").fillInStackTrace());
            }
            if (bytes == null || length <= 0) {
                return 0;
            }
            if (bytesOffset < 0 || bytesOffset + length > bytes.length) {
                throw new ArrayIndexOutOfBoundsException();
            }
            needLine();
            try {
                blob.setActivePrefetch(false);
                blob.clearCachedData();
                int iWrite = (int) this.blobMsg.write(blob.shareBytes(), pos, bytes, bytesOffset, length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return iWrite;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public int getChunkSize(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("getChunkSize");
            assertNotNull(blob.shareBytes(), "getChunkSize");
            needLine();
            try {
                int chunkSize = (int) this.blobMsg.getChunkSize(blob.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return chunkSize;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public void trim(oracle.jdbc.internal.OracleBlob blob, long length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("trim");
            assertNotNull(blob.shareBytes(), "trim");
            if (length < 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "trim()").fillInStackTrace());
            }
            needLine();
            try {
                blob.setActivePrefetch(false);
                blob.clearCachedData();
                this.blobMsg.trim(blob.shareBytes(), length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public BLOB createTemporaryBlob(Connection conn, boolean cache, int duration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("createTemporaryBlob");
            needLine();
            try {
                BLOB result = (BLOB) this.blobMsg.createTemporaryLob(this, cache, duration);
                addTemporaryLob(result.getInternal());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return result;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private final Long getLocatorHash(byte[] locator) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.checksumEngine.reset();
                this.checksumEngine.update(locator, 10, 10);
                long checksum = this.checksumEngine.getValue();
                Long checksumLong = Long.valueOf(checksum);
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "getLocatorHash", "locator={0}, returns={1}. ", (String) null, (Throwable) null, locator, Long.valueOf(checksum));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return checksumLong;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess, oracle.sql.ClobDBAccess
    public final int decrementTempLobReferenceCount(byte[] locator) {
        if (PhysicalConnection.isValueBasedLocator(locator) || PhysicalConnection.isQuasiLocator(locator)) {
            return 0;
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int remainingCnt = 0;
            if (this.enableTempLobRefCnt && locator != null && ((locator[7] & 1) > 0 || (locator[4] & 64) > 0)) {
                Long hashKey = getLocatorHash(locator);
                Integer refCnt = this.tempLobRefCount.get(hashKey);
                if (refCnt != null) {
                    remainingCnt = refCnt.intValue() - 1;
                    if (remainingCnt == 0) {
                        this.tempLobRefCount.remove(hashKey);
                    } else {
                        this.tempLobRefCount.put(hashKey, Integer.valueOf(remainingCnt));
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "decrementTempLobReferenceCount", "LOB ID hash={0}, to={1}. ", (String) null, (Throwable) null, hashKey, Integer.valueOf(remainingCnt));
                } else {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "decrementTempLobReferenceCount", "LOB ID not found in hashtable. hash={0}. ", (String) null, (String) null, (Object) hashKey);
                }
            }
            return remainingCnt;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BlobDBAccess, oracle.sql.ClobDBAccess
    public final void incrementTempLobReferenceCount(byte[] locator) {
        if (PhysicalConnection.isValueBasedLocator(locator) || PhysicalConnection.isQuasiLocator(locator)) {
            return;
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.enableTempLobRefCnt && locator != null && ((locator[7] & 1) > 0 || (locator[4] & 64) > 0)) {
                Long hashKey = getLocatorHash(locator);
                Integer refCnt = this.tempLobRefCount.get(hashKey);
                if (refCnt != null) {
                    int refCntInt = refCnt.intValue();
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "incrementTempLobReferenceCount", "LOB ID hash={0}, to={1}. ", (String) null, (Throwable) null, hashKey, Integer.valueOf(refCntInt + 1));
                    this.tempLobRefCount.put(hashKey, Integer.valueOf(refCntInt + 1));
                } else {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "incrementTempLobReferenceCount", "LOB ID hash={0}, to=1. ", (String) null, (String) null, (Object) hashKey);
                    this.tempLobRefCount.put(hashKey, 1);
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public void freeTemporary(oracle.jdbc.internal.OracleBlob blob, Datum blobDatum, boolean fromObjectIgnore) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("freeTemporary");
            assertNotNull(blob.shareBytes(), "freeTemporary");
            needLine();
            try {
                this.blobMsg.freeTemporaryLob(blob.shareBytes());
                removeFromTemporaryLobs(blob);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public boolean isTemporary(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        assertNotNull(blob.shareBytes(), "isTemporary");
        byte[] locator = blob.shareBytes();
        return isTemporary(locator);
    }

    @Override // oracle.sql.BlobDBAccess
    public short getDuration(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        assertNotNull(blob.shareBytes(), "getDuration");
        byte[] locator = blob.shareBytes();
        short duration = -1;
        if ((locator[7] & 1) > 0 || (locator[4] & 64) > 0) {
            duration = (short) ((locator[22] << 8) | (locator[23] & 255));
        }
        return duration;
    }

    @Override // oracle.sql.BlobDBAccess
    public void openLob(oracle.jdbc.internal.OracleBlob blob, int mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("open");
            assertNotNull(blob.shareBytes(), "open");
            needLine();
            try {
                this.blobMsg.openLob(blob.shareBytes(), mode);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public void closeLob(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("close");
                assertNotNull(blob.shareBytes(), "close");
                needLine();
                try {
                    this.blobMsg.closeLob(blob.shareBytes());
                    removeLargeObject(blob);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public final boolean isOpenLob(oracle.jdbc.internal.OracleBlob blob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("isOpen");
            assertNotNull(blob.shareBytes(), "isOpen");
            needLine();
            try {
                boolean zIsOpenLob = this.blobMsg.isOpenLob(blob.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsOpenLob;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos) throws SQLException {
        return newInputStream(blob, chunkSize, pos, false);
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, boolean isInternal) throws SQLException {
        if (pos == 0) {
            return new OracleBlobInputStream(blob, chunkSize, isInternal);
        }
        return new OracleBlobInputStream(blob, chunkSize, pos, isInternal);
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, long length) throws SQLException {
        return newInputStream(blob, chunkSize, pos, length, false);
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, long length, boolean isInternal) throws SQLException {
        return new OracleBlobInputStream(blob, chunkSize, pos, length, isInternal);
    }

    public OutputStream newOutputStream(BLOB blob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        return newOutputStream((oracle.jdbc.internal.OracleBlob) blob, chunkSize, pos, zeroInvalid);
    }

    @Override // oracle.sql.BlobDBAccess
    public OutputStream newOutputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "newOutputStream", "chunkSize={0}, pos={1}, zeroInvalid={2}. ", (String) null, (Throwable) null, Integer.valueOf(chunkSize), Long.valueOf(pos), Boolean.valueOf(zeroInvalid));
        if (pos == 0) {
            if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            return new OracleBlobOutputStream(blob, chunkSize);
        }
        return new OracleBlobOutputStream(blob, chunkSize, pos);
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newConversionInputStream(oracle.jdbc.internal.OracleBlob blob, int conversionType, boolean isInternal) throws SQLException {
        assertNotNull(blob.shareBytes(), "newConversionInputStream");
        InputStream result = new OracleConversionInputStream(this.conversion, blob.binaryStreamValue(isInternal), conversionType, getPhysicalConnection());
        return result;
    }

    @Override // oracle.sql.BlobDBAccess
    public Reader newConversionReader(oracle.jdbc.internal.OracleBlob blob, int conversionType, boolean isInternal) throws SQLException {
        assertNotNull(blob.shareBytes(), "newConversionReader");
        Reader result = new OracleConversionReader(this.conversion, blob.binaryStreamValue(isInternal), conversionType, getPhysicalConnection());
        return result;
    }

    @Override // oracle.sql.ClobDBAccess
    public long length(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("length");
            assertNotNull(clob.shareBytes(), "length");
            needLine();
            try {
                long length = this.clobMsg.getLength(clob.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return length;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public long position(oracle.jdbc.internal.OracleClob clob, String pattern, long start) throws SQLException {
        if (pattern == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        assertLoggedOn("position");
        assertNotNull(clob.shareBytes(), "position");
        if (start < 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        char[] chars = new char[pattern.length()];
        pattern.getChars(0, chars.length, chars, 0);
        long result = LobPlsqlUtil.hasPattern(clob, chars, start);
        return result == 0 ? -1L : result;
    }

    @Override // oracle.sql.ClobDBAccess
    public long position(oracle.jdbc.internal.OracleClob clob, oracle.jdbc.internal.OracleClob pattern, long start) throws SQLException {
        if (pattern == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        assertLoggedOn("position");
        assertNotNull(clob.shareBytes(), "position");
        assertNotNull(pattern.shareBytes(), "position");
        if (start < 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
        }
        long result = LobPlsqlUtil.isSubLob(clob, pattern, start);
        return result == 0 ? -1L : result;
    }

    @Override // oracle.sql.ClobDBAccess
    public int getChars(oracle.jdbc.internal.OracleClob clob, long pos, int length, char[] chars) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("getChars");
            assertNotNull(clob.shareBytes(), "getChars");
            if (pos < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "getChars()").fillInStackTrace());
            }
            if (this.pipeState != -1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0226, "getChars()").fillInStackTrace());
            }
            if (length <= 0 || chars == null) {
                return 0;
            }
            long result = 0;
            long lobLength = -1;
            if (clob.isActivePrefetch()) {
                lobLength = clob.lengthInternal();
                result = 0 + copyPrefetchedClobChars(clob, pos, length, chars);
            }
            if (result < length && (lobLength == -1 || (pos - 1) + result < lobLength)) {
                needLine();
                try {
                    boolean isNCLOB = clob.isNCLOB();
                    result += this.clobMsg.read(clob.shareBytes(), pos + result, length - result, isNCLOB, chars, (int) result);
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            }
            int i = (int) result;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private final int copyPrefetchedClobChars(oracle.jdbc.internal.OracleClob clob, long pos, int length, char[] chars) {
        OracleLargeObject.PrefetchData<char[]> prefetchedChars = clob.getPrefetchData();
        if (prefetchedChars != null && pos <= prefetchedChars.length()) {
            return prefetchedChars.copy(((int) pos) - 1, chars, 0, length);
        }
        return 0;
    }

    @Override // oracle.sql.ClobDBAccess
    public int putChars(oracle.jdbc.internal.OracleClob clob, long pos, char[] chars, int charsOffset, int length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("putChars");
            assertNotNull(clob.shareBytes(), "putChars");
            if (pos < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "putChars()").fillInStackTrace());
            }
            if (chars == null || length <= 0) {
                return 0;
            }
            needLine();
            try {
                boolean isNCLOB = clob.isNCLOB();
                clob.setActivePrefetch(false);
                clob.clearCachedData();
                int iWrite = (int) this.clobMsg.write(clob.shareBytes(), pos, isNCLOB, chars, charsOffset, length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return iWrite;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public int getChunkSize(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("getChunkSize");
            assertNotNull(clob.shareBytes(), "getChunkSize");
            needLine();
            try {
                int chunkSize = (int) this.clobMsg.getChunkSize(clob.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return chunkSize;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public void trim(oracle.jdbc.internal.OracleClob clob, long length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("trim");
            assertNotNull(clob.shareBytes(), "trim");
            if (length < 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "trim()").fillInStackTrace());
            }
            needLine();
            try {
                clob.setActivePrefetch(false);
                clob.clearCachedData();
                this.clobMsg.trim(clob.shareBytes(), length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public CLOB createTemporaryClob(Connection conn, boolean cache, int duration, short form_of_use) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("createTemporaryClob");
            if (form_of_use != 2 && form_of_use != 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 184).fillInStackTrace());
            }
            needLine();
            try {
                CLOB result = (CLOB) this.clobMsg.createTemporaryLob(this, cache, duration, form_of_use);
                addTemporaryLob(result.getInternal());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return result;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public void freeTemporary(oracle.jdbc.internal.OracleClob clob, Datum temp_lob_datum, boolean fromObjectIgnore) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("freeTemporary");
            assertNotNull(clob.shareBytes(), "freeTemporary");
            needLine();
            try {
                this.clobMsg.freeTemporaryLob(clob.shareBytes());
                removeFromTemporaryLobs(clob);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public boolean isTemporary(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        byte[] locator = clob.shareBytes();
        return isTemporary(locator);
    }

    @Override // oracle.sql.ClobDBAccess
    public short getDuration(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        assertNotNull(clob.shareBytes(), "getDuration");
        byte[] locator = clob.shareBytes();
        short duration = -1;
        if ((locator[7] & 1) > 0 || (locator[4] & 64) > 0) {
            duration = (short) ((locator[22] << 8) | (locator[23] & 255));
        }
        return duration;
    }

    @Override // oracle.sql.ClobDBAccess
    public void openLob(oracle.jdbc.internal.OracleClob clob, int mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("open");
            assertNotNull(clob.shareBytes(), "open");
            needLine();
            try {
                this.clobMsg.openLob(clob.shareBytes(), mode);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public void closeLob(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                assertLoggedOn("close");
                assertNotNull(clob.shareBytes(), "close");
                needLine();
                try {
                    this.clobMsg.closeLob(clob.shareBytes());
                    removeLargeObject(clob);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public final boolean isOpenLob(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            assertLoggedOn("isOpen");
            assertNotNull(clob.shareBytes(), "isOpen");
            needLine();
            try {
                boolean zIsOpenLob = this.clobMsg.isOpenLob(clob.shareBytes());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsOpenLob;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos) throws SQLException {
        return newInputStream(clob, chunkSize, pos, false);
    }

    @Override // oracle.sql.ClobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean isInternal) throws SQLException {
        if (pos == 0) {
            return new OracleClobInputStream(clob, chunkSize, isInternal);
        }
        return new OracleClobInputStream(clob, chunkSize, pos, isInternal);
    }

    @Override // oracle.sql.ClobDBAccess
    public OutputStream newOutputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        if (pos == 0) {
            if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            return new OracleClobOutputStream(clob, chunkSize);
        }
        return new OracleClobOutputStream(clob, chunkSize, pos);
    }

    @Override // oracle.sql.ClobDBAccess
    public Reader newReader(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos) throws SQLException {
        if (pos == 0) {
            return new OracleClobReader(clob, chunkSize);
        }
        return new OracleClobReader(clob, chunkSize, pos);
    }

    @Override // oracle.sql.ClobDBAccess
    public Reader newReader(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, long length) throws SQLException {
        return new OracleClobReader(clob, chunkSize, pos, length);
    }

    @Override // oracle.sql.ClobDBAccess
    public Writer newWriter(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        if (pos == 0) {
            if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            return new OracleClobWriter(clob, chunkSize);
        }
        return new OracleClobWriter(clob, chunkSize, pos);
    }

    void assertLoggedOn(String caller) throws SQLException {
        if (!this.isLoggedOn) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0202).fillInStackTrace());
        }
    }

    boolean isLoggedOn() {
        return this.isLoggedOn;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void internalClose() throws SQLException {
        super.internalClose();
        if (this.all8 != null) {
            this.all8.definesAccessors = null;
        }
        this.isLoggedOn = false;
        try {
            if (this.net.getSessionAttributes().isConnected()) {
                this.net.disconnect();
            }
        } catch (Exception e) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "internalClose", null, (String) null, e);
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doAbort() throws SQLException {
        try {
            this.net.abort();
        } catch (NetException ne) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) ne).fillInStackTrace());
        } catch (IOException ne2) {
            handleIOException(ne2);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ne2).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doDescribeTable(AutoKeyInfo info) throws SQLException {
        initializeAutoKeyInfo(info, getColumnMetaData(info.getTableName()));
    }

    private T4C8Kpcdsc[] getColumnMetaData(String tableName) throws SQLException {
        try {
            return this.describeTbl.doODSYTable(tableName);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    private void initializeAutoKeyInfo(AutoKeyInfo info, T4C8Kpcdsc[] metaData) throws SQLException {
        int numColumns = metaData.length;
        info.allocateSpaceForDescribedData(numColumns);
        for (int i = 0; i < numColumns; i++) {
            T4C8Kpcdsc kc = metaData[i];
            info.fillDescribedData(i, kc.name_kpcdsc, kc.dty_kpcdsc, kc.size_kpcdsc, kc.isnull_kpcdsc, kc.charsetform_kpcdsc, kc.precision_kpcdsc, kc.scale_kpcdsc, kc.typnm_kpcdsc, kc.domname_kpcdsc, kc.domsch_kpcdsc, kc.getAnnotations());
        }
    }

    private void loadApplicationContextFromProperty() {
        if (this.applicationContext == null) {
            return;
        }
        String[] applicationContexts = this.applicationContext.split(";");
        for (int i = 0; i < applicationContexts.length; i++) {
            String[] keyValuePair = applicationContexts[i].split("=");
            if (keyValuePair.length != 2) {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Invalid client context: " + applicationContexts[i], null, null);
            } else {
                String[] nameKeyPair = keyValuePair[0].split("\\.", 2);
                if (nameKeyPair.length != 2) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Invalid client context: " + applicationContexts[i], null, null);
                } else {
                    String value = keyValuePair[1];
                    String nameSpace = nameKeyPair[0];
                    String attribute = nameKeyPair[1];
                    if (RESERVED_NAMESPACES.contains(nameSpace)) {
                        debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Attempt to use a reserved namespace: " + applicationContexts[i], null, null);
                    } else if (attribute.length() > 30) {
                        debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Invalid attribute length: " + attribute, null, null);
                    } else if (value != null && value.length() > 4000) {
                        debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Invalid value length: " + value, null, null);
                    } else {
                        try {
                            doSetApplicationContext(nameSpace, attribute, value);
                            if (value == null) {
                                this.clientInfo.remove(keyValuePair[0]);
                            } else {
                                this.clientInfo.put(keyValuePair[0], value);
                            }
                        } catch (SQLException sqlEx) {
                            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "readNamespacesFromProperty", "Error setting application context: " + applicationContexts[i], null, sqlEx);
                        }
                    }
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doSetApplicationContext(String nameSpace, String attribute, String value) throws SQLException {
        Namespace ns = this.namespaces.get(nameSpace);
        if (ns == null) {
            ns = new Namespace(nameSpace);
            this.namespaces.put(nameSpace, ns);
        }
        ns.setAttribute(attribute, value);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doClearAllApplicationContext(String nameSpace) throws SQLException {
        Namespace ns = new Namespace(nameSpace);
        ns.clear();
        this.namespaces.put(nameSpace, ns);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void getPropertyForPooledConnection(OraclePooledConnection pc) throws SQLException {
        super.getPropertyForPooledConnection(pc, this.password.get());
    }

    final void getPasswordInternal(T4CXAResource caller) throws SQLException {
        caller.setPasswordInternal(this.password);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doEnqueue(String queueName, AQEnqueueOptions enqueueOptions, AQMessagePropertiesI prop, byte[] payloadTDO, int payloadVersion, byte[] payload, byte[][] msgId, boolean isRawPayload) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    needLine();
                    this.aqe.doOAQEQ(queueName, enqueueOptions, prop, payload, payloadTDO, payloadVersion, isRawPayload);
                    if (enqueueOptions.getRetrieveMessageId()) {
                        msgId[0] = this.aqe.getMessageId();
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ioex) {
                    handleIOException(ioex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean doDequeue(String queueName, AQDequeueOptions dequeueOptions, AQMessagePropertiesI msgProp, byte[] payloadTDO, int payloadVersion, byte[][] payload, byte[][] msgid, boolean isRawQueue) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                needLine();
                this.aqdq.doOAQDQ(queueName, dequeueOptions, payloadTDO, payloadVersion, isRawQueue, msgProp);
                payload[0] = this.aqdq.getPayload();
                msgid[0] = this.aqdq.getDequeuedMessageId();
                boolean hasAMessageBeenDequeued = this.aqdq.hasAMessageBeenDequeued();
                return hasAMessageBeenDequeued;
            } catch (IOException ioex) {
                handleIOException(ioex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doJMSEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOptions, AQMessagePropertiesI aqMesgPropI, JMSMessageProperties jmsProp, byte[] payloadTDO, byte[] payload, byte[][] msgId) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                needLine();
                this.oaqenq.doJMSEnq(queueName, jmsEnqueueOptions, aqMesgPropI, jmsProp, payloadTDO, payload);
                if (jmsEnqueueOptions.isRetrieveMessageId()) {
                    msgId[0] = this.oaqenq.getMsgId();
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ioex) {
                handleIOException(ioex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doJMSEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOptions, AQMessagePropertiesI aqMesgPropI, JMSMessageProperties jmsProp, byte[] payloadTDO, InputStream byteStream, byte[][] msgId, int blockSize) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    needLine();
                    this.oaqenq.doJMSEnq(queueName, jmsEnqueueOptions, aqMesgPropI, jmsProp, payloadTDO, byteStream, blockSize);
                    if (jmsEnqueueOptions.isRetrieveMessageId()) {
                        msgId[0] = this.oaqenq.getMsgId();
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ioex) {
                    handleIOException(ioex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void jmsEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOpt, JMSMessage[] mesgs, AQMessageProperties[] aqMesgProps) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (mesgs != null) {
                if (mesgs.length > 0) {
                    try {
                        AQMessagePropertiesI[] aqMesgPropsI = new AQMessagePropertiesI[aqMesgProps.length];
                        for (int i = 0; i < aqMesgProps.length; i++) {
                            aqMesgPropsI[i] = (AQMessagePropertiesI) aqMesgProps[i];
                        }
                        needLine();
                        this.aqa.doJMSEnq(queueName, jmsEnqueueOpt, mesgs, aqMesgPropsI);
                    } catch (IOException ioex) {
                        handleIOException(ioex);
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                    }
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int enqueue(String queueName, AQEnqueueOptions opt, AQMessage[] mesgs) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (mesgs != null) {
                if (mesgs.length > 0) {
                    try {
                        needLine();
                        int iDoAQEnq = this.aqa.doAQEnq(queueName, opt, mesgs);
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                lock.close();
                            }
                        }
                        return iDoAQEnq;
                    } catch (IOException ioex) {
                        handleIOException(ioex);
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                    }
                }
            }
            if (lock == null) {
                return 0;
            }
            if (0 != 0) {
                try {
                    lock.close();
                    return 0;
                } catch (Throwable th3) {
                    th.addSuppressed(th3);
                    return 0;
                }
            }
            lock.close();
            return 0;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage[] dequeue(String queueName, AQDequeueOptions opt, String typeName, int deqSize) throws SQLException {
        byte[] toid;
        int version = 1;
        TypeDescriptor sd = null;
        if ("JSON".equals(typeName)) {
            toid = TypeDescriptor.JSONTOID;
        } else if ("RAW".equals(typeName) || "SYS.RAW".equals(typeName)) {
            toid = TypeDescriptor.RAWTOID;
        } else if ("SYS.ANYDATA".equals(typeName)) {
            toid = TypeDescriptor.ANYDATATOID;
        } else if ("SYS.XMLTYPE".equals(typeName)) {
            toid = TypeDescriptor.XMLTYPETOID;
        } else {
            sd = TypeDescriptor.getTypeDescriptor(typeName, this);
            toid = ((OracleTypeADT) sd.getPickler()).getTOID();
            version = ((OracleTypeADT) sd.getPickler()).getTypeVersion();
        }
        AQMessageI[] msgs = (AQMessageI[]) dequeue(queueName, opt, toid, version, deqSize);
        if (msgs != null) {
            for (AQMessageI msg : msgs) {
                msg.setTypeName(typeName);
                msg.setTypeDescriptor(sd);
            }
        }
        return msgs;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage[] dequeue(String queueName, AQDequeueOptions opt, byte[] tdo, int version, int size) throws SQLException {
        AQMessage[] mesgs = null;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (size > 0) {
                try {
                    AQMessagePropertiesI[] aqMesgProps = new AQMessagePropertiesI[size];
                    for (int i = 0; i < size; i++) {
                        aqMesgProps[i] = new AQMessagePropertiesI();
                    }
                    needLine();
                    mesgs = this.aqa.doAQDeq(queueName, opt, tdo, version, size, aqMesgProps);
                } catch (IOException ioex) {
                    handleIOException(ioex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                }
            }
            return mesgs;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean doJmsDequeue(String queueName, JMSDequeueOptions jmsDequeueOptions, AQMessagePropertiesI msgProp, JMSMessagePropertiesI jmsProp, byte[] payloadTDO, OutputStream payload, byte[][] msgid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    needLine();
                    this.oaqdeq.doJMSDeq(queueName, jmsDequeueOptions, payloadTDO, msgProp, jmsProp, payload);
                    msgid[0] = this.oaqdeq.getDequeuedMessageId();
                    boolean hasAMessageBeenDequeued = this.oaqdeq.isHasAMessageBeenDequeued();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return hasAMessageBeenDequeued;
                } catch (IOException ioex) {
                    handleIOException(ioex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                }
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public JMSMessage[] jmsDequeue(String queueName, JMSDequeueOptions jmsDequeueOpt, int size) throws SQLException {
        JMSMessage[] mesgs = null;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (size > 0) {
            try {
                try {
                    try {
                        AQMessagePropertiesI[] aqMesgProps = new AQMessagePropertiesI[size];
                        JMSMessagePropertiesI jmsProp = new JMSMessagePropertiesI();
                        for (int i = 0; i < size; i++) {
                            aqMesgProps[i] = new AQMessagePropertiesI();
                        }
                        needLine();
                        mesgs = this.aqa.doJMSDeq(queueName, jmsDequeueOpt, size, aqMesgProps, jmsProp);
                    } catch (IOException ioex) {
                        handleIOException(ioex);
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
                    }
                } finally {
                }
            } catch (Throwable th2) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th2;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th4) {
                    th.addSuppressed(th4);
                }
            } else {
                lock.close();
            }
        }
        return mesgs;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean doJmsDequeue(String queueName, JMSDequeueOptions jmsDequeueOptions, AQMessagePropertiesI msgProp, JMSMessagePropertiesI jmsProp, byte[] payloadTDO, byte[][] payload, byte[][] msgid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                needLine();
                this.oaqdeq.doJMSDeq(queueName, jmsDequeueOptions, payloadTDO, msgProp, jmsProp);
                payload[0] = this.oaqdeq.getPayload();
                msgid[0] = this.oaqdeq.getDequeuedMessageId();
                boolean hasAMessageBeenDequeued = this.oaqdeq.isHasAMessageBeenDequeued();
                return hasAMessageBeenDequeued;
            } catch (IOException ioex) {
                handleIOException(ioex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int pingDatabase(int timeout) throws SQLException {
        int pingResult;
        if (getLifecycle() != 1) {
            return -1;
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (checkAndDrain()) {
                return -1;
            }
            requireNonNegativeTimeout(timeout);
            int existingTimeout = -1;
            int timeoutMillis = timeout * 1000;
            try {
                existingTimeout = this.net.getSocketReadTimeout();
                if (timeoutMillis < existingTimeout || existingTimeout == 0) {
                    this.net.setSocketReadTimeout(timeoutMillis);
                }
                pingResult = doPingDatabase();
                if (existingTimeout >= 0) {
                    try {
                        this.net.setSocketReadTimeout(existingTimeout);
                    } catch (IOException e) {
                        pingResult = -2;
                    }
                }
            } catch (IOException e2) {
                pingResult = -2;
                if (existingTimeout >= 0) {
                    try {
                        this.net.setSocketReadTimeout(existingTimeout);
                    } catch (IOException e3) {
                        pingResult = -2;
                    }
                }
            } catch (Throwable th2) {
                if (existingTimeout >= 0) {
                    try {
                        this.net.setSocketReadTimeout(existingTimeout);
                    } catch (IOException e4) {
                        throw th2;
                    }
                }
                throw th2;
            }
            int i = pingResult;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    int doPingDatabase() throws SQLException {
        assertLockHeldByCurrentThread();
        if (this.versionNumber < 10102) {
            return super.doPingDatabase();
        }
        beginNonRequestCalls();
        try {
            try {
                try {
                    needLine();
                    this.oping.doOPING();
                    endNonRequestCalls();
                    return 0;
                } catch (SocketTimeoutException e) {
                    endNonRequestCalls();
                    return -3;
                } catch (InterruptedIOException iioe) {
                    handleIOException(iioe);
                    endNonRequestCalls();
                    return -1;
                }
            } catch (IOException e2) {
                endNonRequestCalls();
                return -1;
            } catch (SQLException sqle) {
                if (sqle.getErrorCode() == 18745) {
                    throw sqle;
                }
                endNonRequestCalls();
                return -1;
            }
        } catch (Throwable th) {
            endNonRequestCalls();
            throw th;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean isValidLight(int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (checkAndDrain()) {
                return false;
            }
            try {
                try {
                    this.net.sendZDP();
                    this.writeBufferIsDirty = true;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                } catch (IOException e) {
                    debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "isValidLight", "Received IOException while sending zero length NS data packet. ", (String) null, e);
                    this.writeBufferIsDirty = true;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return false;
                }
            } catch (Throwable th4) {
                this.writeBufferIsDirty = true;
                throw th4;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    void setNeedsToBeClosed(boolean needsToBeClosed) {
        this.needsToBeClosed = needsToBeClosed;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean drainOnInbandNotification() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.needsToBeClosed) {
                closeConnectionSafely();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return true;
            }
            this.net.readInbandNotification();
            if (!this.net.needsToBeClosed()) {
                return false;
            }
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "drainOnInbandNotification", "received in-band notification, about to close connection", (String) null, (Throwable) null, new Object[0]);
            closeConnectionSafely();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return true;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v25, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    Map<String, JMSNotificationRegistration> doRegisterJMSNotification(String[] name, Map<String, Properties> options, String selector) throws SQLException {
        String jmsConnectionId;
        ArrayList<String> listenerAddresses;
        Properties ntfProp;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int nbOfRegistration = name.length;
            int[] jdbcRegIdArr = new int[nbOfRegistration];
            int[] namespaceArr = new int[nbOfRegistration];
            int[] payloadTypeArr = new int[nbOfRegistration];
            int[] qosFlagsArr = new int[nbOfRegistration];
            int[] timeoutArr = new int[nbOfRegistration];
            ?? r0 = new byte[nbOfRegistration];
            int maxConn = 16;
            boolean useHostConInfo = false;
            if (options != null && (ntfProp = options.get(JMSNotificationRegistration.NTFPROPERTY)) != null) {
                String maxConnStr = ntfProp.getProperty(JMSNotificationRegistration.MAX_NTF_CONNECTIONS, "16");
                try {
                    maxConn = Integer.parseInt(maxConnStr);
                } catch (Exception e) {
                    maxConn = 16;
                }
            }
            NTFJMSConnectionGroup.setMaxNtfConnection(maxConn);
            NTFJMSConnectionGroup jmsConnectionGroup = PhysicalConnection.ntfManager.getJMSConnectionGroup(this.userName + this.instanceName);
            Map<String, JMSNotificationRegistration> registrations = new HashMap<>();
            for (int i = 0; i < nbOfRegistration; i++) {
                namespaceArr[i] = 1;
                payloadTypeArr[i] = 0;
                jdbcRegIdArr[i] = PhysicalConnection.ntfManager.getNextJdbcRegId();
                r0[i] = new byte[4];
                r0[i][0] = (byte) ((jdbcRegIdArr[i] & (-16777216)) >> 24);
                r0[i][1] = (byte) ((jdbcRegIdArr[i] & 16711680) >> 16);
                r0[i][2] = (byte) ((jdbcRegIdArr[i] & OracleXAResource.ORAISOLATIONMASK) >> 8);
                r0[i][3] = (byte) (jdbcRegIdArr[i] & 255);
                Properties option = options.get(name[i]);
                if (option != null) {
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_RELIABLE, "false").equalsIgnoreCase("true")) {
                        int i2 = i;
                        qosFlagsArr[i2] = qosFlagsArr[i2] | 1;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_PURGE_ON_NTFN, "false").equalsIgnoreCase("true")) {
                        int i3 = i;
                        qosFlagsArr[i3] = qosFlagsArr[i3] | 16;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_AQ_PAYLOAD, "false").equalsIgnoreCase("true")) {
                        int i4 = i;
                        qosFlagsArr[i4] = qosFlagsArr[i4] | 2;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_ASYNC_DEQ, "false").equalsIgnoreCase("true")) {
                        int i5 = i;
                        qosFlagsArr[i5] = qosFlagsArr[i5] | 512;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_SECURE, "false").equalsIgnoreCase("true")) {
                        int i6 = i;
                        qosFlagsArr[i6] = qosFlagsArr[i6] | 8;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_TX_ACK, "false").equalsIgnoreCase("true")) {
                        int i7 = i;
                        qosFlagsArr[i7] = qosFlagsArr[i7] | 2048;
                    }
                    if (option.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_AUTO_ACK, "false").equalsIgnoreCase("true")) {
                        int i8 = i;
                        qosFlagsArr[i8] = qosFlagsArr[i8] | 1024;
                    }
                    timeoutArr[i] = readNTFtimeout(option);
                    if (!useHostConInfo && option.getProperty(oracle.jdbc.OracleConnection.AQ_USE_HOST_CONNECTION_ADDR_INFO, "true").equalsIgnoreCase("true")) {
                        useHostConInfo = true;
                    }
                }
            }
            Monitor.CloseableLock groupLock = jmsConnectionGroup.acquireCloseableLock();
            Throwable th2 = null;
            try {
                boolean createNewConnection = false;
                NTFJMSConnection ntfConnectionForThisRegistration = jmsConnectionGroup.getNTFJMSConnection(nbOfRegistration);
                if (ntfConnectionForThisRegistration != null) {
                    jmsConnectionId = ntfConnectionForThisRegistration.getJMSConnectionId();
                } else {
                    jmsConnectionId = null;
                    createNewConnection = true;
                }
                try {
                    this.okpn.doOKPN(1, 4, this.userName, jmsConnectionId, nbOfRegistration, namespaceArr, name, r0, payloadTypeArr, qosFlagsArr, timeoutArr, null, null, null, null, null, null, null, null, null, selector);
                    long[] jmsRegIdArr = this.okpn.getRegistrationIdArray();
                    if (createNewConnection) {
                        jmsConnectionId = this.okpn.getJMSConnectionId();
                        if (jmsConnectionId == null) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "clientID returned by server is null").fillInStackTrace());
                        }
                        if (useHostConInfo) {
                            Properties connProp = new Properties();
                            connProp.setProperty(oracle.jdbc.OracleConnection.DCN_USE_HOST_CONNECTION_ADDR_INFO, "true");
                            listenerAddresses = getListenerAddressForDCN(connProp);
                        } else {
                            listenerAddresses = this.okpn.getListenerAddresses();
                        }
                        if (listenerAddresses == null) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "listenerAddress returned by server is null").fillInStackTrace());
                        }
                        String serviceNameSpecifiedByClient = this.net.getSessionAttributes().getcOption().service_name;
                        if (serviceNameSpecifiedByClient == null) {
                            serviceNameSpecifiedByClient = this.net.getSessionAttributes().getcOption().getOriginalConnOption().service_name;
                        }
                        if (serviceNameSpecifiedByClient == null) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "No service name found, please use a service name format URL to create a connection").fillInStackTrace());
                        }
                        Properties connectionProps = getConnectionPropsforListenerConnection();
                        jmsConnectionGroup.startJMSListenerConnection(this.instanceName, serviceNameSpecifiedByClient, this.userName, this.password, connectionProps, listenerAddresses, jmsConnectionId, nbOfRegistration);
                    }
                    for (int i9 = 0; i9 < nbOfRegistration; i9++) {
                        Properties option2 = options.get(name[i9]);
                        if (option2 == null) {
                            option2 = new Properties();
                        }
                        NTFJMSRegistration newRegistration = new NTFJMSRegistration(jdbcRegIdArr[i9], true, this.instanceName, this.userName, option2, name[i9], this.versionNumber, jmsConnectionId);
                        newRegistration.setState(NotificationRegistration.RegistrationState.DISABLED);
                        newRegistration.setJMSRegistrationId(jmsRegIdArr[i9]);
                        newRegistration.setQOSFlag(qosFlagsArr[i9]);
                        registrations.put(name[i9], newRegistration);
                        PhysicalConnection.ntfManager.addRegistration(newRegistration);
                        PhysicalConnection.ntfManager.mapJMSRegIdToJDBCRegId(Long.valueOf(jmsRegIdArr[i9]), jdbcRegIdArr[i9]);
                        jmsConnectionGroup.addNtfRegistrationByRegId(jmsRegIdArr[i9], newRegistration);
                    }
                    return registrations;
                } catch (IOException ex) {
                    jmsConnectionGroup.resetRegistrationNumbers(nbOfRegistration, ntfConnectionForThisRegistration);
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } finally {
                if (groupLock != null) {
                    if (0 != 0) {
                        try {
                            groupLock.close();
                        } catch (Throwable th3) {
                            th2.addSuppressed(th3);
                        }
                    } else {
                        groupLock.close();
                    }
                }
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doStartJMSNotification(NTFJMSRegistration registration) throws SQLException {
        if (registration.getState() != NotificationRegistration.RegistrationState.ACTIVE) {
            startOrStopJMSNotification(registration, JMSNotificationRegistration.Directive.ENABLE);
            registration.setState(NotificationRegistration.RegistrationState.ACTIVE);
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doStopJMSNotification(NTFJMSRegistration registration) throws SQLException {
        if (registration.getState() != NotificationRegistration.RegistrationState.DISABLED) {
            startOrStopJMSNotification(registration, JMSNotificationRegistration.Directive.DISABLE);
        }
    }

    void startOrStopJMSNotification(NTFJMSRegistration registration, JMSNotificationRegistration.Directive directive) throws SQLException {
        int opcode;
        Long jmsRegistrationId = Long.valueOf(registration.getJMSRegistrationId());
        registration.setState(NotificationRegistration.RegistrationState.ACTIVE);
        NTFJMSConnectionGroup jmsConnectionGroup = PhysicalConnection.ntfManager.getJMSConnectionGroup(this.userName + this.instanceName);
        if (!$assertionsDisabled && jmsConnectionGroup == null) {
            throw new AssertionError("jmsConnectionGroup is null");
        }
        String jmsConnectionId = registration.getJMSConnectionId();
        int[] namespaceArr = {1};
        String[] registeredAgentNameArr = {registration.getQueueName()};
        int[] payloadTypeArr = {0};
        int qosFlag = registration.getQOSFlag();
        int[] qosFlagsArr = {qosFlag};
        int[] timeoutArr = {0};
        long[] jmsRegIdArr = {jmsRegistrationId.longValue()};
        if (directive == JMSNotificationRegistration.Directive.DISABLE) {
            opcode = 5;
        } else {
            opcode = 4;
        }
        try {
            this.okpn.doOKPN(opcode, 4, this.userName, jmsConnectionId, 1, namespaceArr, registeredAgentNameArr, (byte[][]) null, payloadTypeArr, qosFlagsArr, timeoutArr, null, null, null, null, null, null, null, null, jmsRegIdArr);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doUnregisterJMSNotification(NTFJMSRegistration registration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            PhysicalConnection.ntfManager.removeRegistration(registration);
            PhysicalConnection.ntfManager.freeJdbcRegId(registration.getJdbcRegId());
            Long jmsRegistrationId = Long.valueOf(registration.getJMSRegistrationId());
            PhysicalConnection.ntfManager.removeJMSRegistrationId(jmsRegistrationId);
            registration.setState(NotificationRegistration.RegistrationState.CLOSED);
            NTFJMSConnectionGroup jmsConnectionGroup = PhysicalConnection.ntfManager.getJMSConnectionGroup(this.userName + this.instanceName);
            if (!$assertionsDisabled && jmsConnectionGroup == null) {
                throw new AssertionError("jmsConnectionGroup is null");
            }
            jmsConnectionGroup.decrementNumberOfRegistrations();
            String jmsConnectionId = registration.getJMSConnectionId();
            jmsConnectionGroup.stopNTFJMSConnection(jmsConnectionId);
            int[] namespaceArr = {1};
            String[] registeredAgentNameArr = {registration.getQueueName()};
            int[] payloadTypeArr = {0};
            int qosFlag = registration.getQOSFlag();
            int[] qosFlagsArr = {qosFlag};
            int[] timeoutArr = {0};
            long[] jmsRegIdArr = {jmsRegistrationId.longValue()};
            IOException unregIOExcp = null;
            try {
                this.okpn.doOKPN(2, 4, this.userName, jmsConnectionId, 1, namespaceArr, registeredAgentNameArr, (byte[][]) null, payloadTypeArr, qosFlagsArr, timeoutArr, null, null, null, null, null, null, null, null, jmsRegIdArr);
            } catch (IOException ex) {
                unregIOExcp = ex;
            }
            jmsConnectionGroup.checkNCloseActiveRegistrations();
            if (unregIOExcp != null) {
                handleIOException(unregIOExcp);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), unregIOExcp).fillInStackTrace());
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doAckJMSNtfn(ArrayList<JMSNotificationRegistration> registrationList, byte[][] lastMessageIDs, short directiveValue) throws SQLException {
        if (registrationList == null || registrationList.size() == 0) {
            return;
        }
        try {
            long[] registrationIds = new long[registrationList.size()];
            String[] queueNames = new String[registrationList.size()];
            Iterator<JMSNotificationRegistration> regIter = registrationList.iterator();
            NTFJMSRegistration ntfReg = null;
            int i = 0;
            while (regIter.hasNext()) {
                ntfReg = (NTFJMSRegistration) regIter.next();
                registrationIds[i] = ntfReg.getJMSRegistrationId();
                queueNames[i] = ntfReg.getQueueName();
                i++;
            }
            String jmsConnectionId = ntfReg.getJMSConnectionId();
            this.kpdnrdeq.doOAQEMNDEQ(jmsConnectionId, directiveValue, lastMessageIDs, registrationIds, queueNames);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    NTFAQRegistration[] doRegisterAQNotification(String[] name, String dcnhost, int tcpport, boolean useSSL, Properties[] options) throws SQLException {
        Monitor.CloseableLock lock = ntfManager.acquireCloseableLock();
        Throwable th = null;
        try {
            int nbOfRegistration = name.length;
            int[] jdbcRegIdArr = new int[nbOfRegistration];
            ?? r0 = new byte[nbOfRegistration];
            int[] namespaceArr = new int[nbOfRegistration];
            int[] payloadTypeArr = new int[nbOfRegistration];
            int[] qosFlagsArr = new int[nbOfRegistration];
            int[] timeoutArr = new int[nbOfRegistration];
            int[] dbchangeOpFilterArr = new int[nbOfRegistration];
            int[] dbchangeTxnLagArr = new int[nbOfRegistration];
            long[] dbchangeRegistrationIdArr = new long[nbOfRegistration];
            byte[] kpdnrgrpcla = new byte[nbOfRegistration];
            int[] kpdnrgrpval = new int[nbOfRegistration];
            byte[] kpdnrgrptyp = new byte[nbOfRegistration];
            TIMESTAMPTZ[] kpdnrgrpstatim = new TIMESTAMPTZ[nbOfRegistration];
            int[] kpdnrgrprepcnt = new int[nbOfRegistration];
            boolean forceFindPort = false;
            if (tcpport == 0) {
                forceFindPort = true;
                tcpport = 47632;
            }
            for (int i = 0; i < nbOfRegistration; i++) {
                jdbcRegIdArr[i] = PhysicalConnection.ntfManager.getNextJdbcRegId();
                r0[i] = new byte[4];
                r0[i][0] = (byte) ((jdbcRegIdArr[i] & (-16777216)) >> 24);
                r0[i][1] = (byte) ((jdbcRegIdArr[i] & 16711680) >> 16);
                r0[i][2] = (byte) ((jdbcRegIdArr[i] & OracleXAResource.ORAISOLATIONMASK) >> 8);
                r0[i][3] = (byte) (jdbcRegIdArr[i] & 255);
                namespaceArr[i] = 1;
                payloadTypeArr[i] = 23;
                if (options.length > i && options[i] != null) {
                    if (options[i].getProperty(oracle.jdbc.OracleConnection.NTF_QOS_RELIABLE, "false").compareToIgnoreCase("true") == 0) {
                        int i2 = i;
                        qosFlagsArr[i2] = qosFlagsArr[i2] | 1;
                    }
                    if (options[i].getProperty(oracle.jdbc.OracleConnection.NTF_QOS_PURGE_ON_NTFN, "false").compareToIgnoreCase("true") == 0) {
                        int i3 = i;
                        qosFlagsArr[i3] = qosFlagsArr[i3] | 16;
                    }
                    if (options[i].getProperty(oracle.jdbc.OracleConnection.NTF_AQ_PAYLOAD, "false").compareToIgnoreCase("true") == 0) {
                        int i4 = i;
                        qosFlagsArr[i4] = qosFlagsArr[i4] | 2;
                    }
                    timeoutArr[i] = readNTFtimeout(options[i]);
                }
            }
            setNtfGroupingOptions(kpdnrgrpcla, kpdnrgrpval, kpdnrgrptyp, kpdnrgrpstatim, kpdnrgrprepcnt, options);
            int[] tcpportArr = {tcpport};
            Exception[] connectionCreationExceptionArr = new Exception[1];
            Properties socketOptions = null;
            if (useSSL) {
                socketOptions = ((NSProtocol) this.net).getSocketOptions();
            }
            boolean isNewClient = PhysicalConnection.ntfManager.listenOnPortT4C(tcpportArr, forceFindPort, socketOptions, connectionCreationExceptionArr);
            int tcpport2 = tcpportArr[0];
            String location = "(ADDRESS=(PROTOCOL=" + (useSSL ? "tcps" : "tcp") + " )(HOST=" + dcnhost + ")(PORT=" + tcpport2 + "))?PR=0";
            try {
                try {
                    int mod = isNewClient ? 1 : 0;
                    this.okpn.doOKPN(1, mod, this.userName, location, nbOfRegistration, namespaceArr, name, r0, payloadTypeArr, qosFlagsArr, timeoutArr, dbchangeOpFilterArr, dbchangeTxnLagArr, dbchangeRegistrationIdArr, kpdnrgrpcla, kpdnrgrpval, kpdnrgrptyp, kpdnrgrpstatim, kpdnrgrprepcnt, dbchangeRegistrationIdArr);
                    NTFAQRegistration[] registrations = new NTFAQRegistration[nbOfRegistration];
                    for (int i5 = 0; i5 < nbOfRegistration; i5++) {
                        registrations[i5] = new NTFAQRegistration(jdbcRegIdArr[i5], useSSL, true, this.instanceName, this.userName, dcnhost, tcpport2, options[i5], name[i5], this.versionNumber, connectionCreationExceptionArr);
                    }
                    for (NTFAQRegistration nTFAQRegistration : registrations) {
                        PhysicalConnection.ntfManager.addRegistration(nTFAQRegistration);
                    }
                    return registrations;
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (SQLException generalEx) {
                if (isNewClient) {
                    PhysicalConnection.ntfManager.cleanListenersT4C(tcpport2);
                }
                throw generalEx;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private void setNtfGroupingOptions(byte[] kpdnrgrpcla, int[] kpdnrgrpval, byte[] kpdnrgrptyp, TIMESTAMPTZ[] kpdnrgrpstatim, int[] kpdnrgrprepcnt, Properties[] options) throws SQLException {
        for (int i = 0; i < options.length; i++) {
            String ntfGroupingClass = options[i].getProperty(oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS, oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS_NONE);
            String ntfGroupingValue = options[i].getProperty(oracle.jdbc.OracleConnection.NTF_GROUPING_VALUE);
            String ntfGroupingType = options[i].getProperty(oracle.jdbc.OracleConnection.NTF_GROUPING_TYPE);
            TIMESTAMPTZ ntfGroupingStartTime = null;
            if (options[i].get(oracle.jdbc.OracleConnection.NTF_GROUPING_START_TIME) != null) {
                ntfGroupingStartTime = (TIMESTAMPTZ) options[i].get(oracle.jdbc.OracleConnection.NTF_GROUPING_START_TIME);
            }
            String ntfGroupingRepeatTime = options[i].getProperty(oracle.jdbc.OracleConnection.NTF_GROUPING_REPEAT_TIME, oracle.jdbc.OracleConnection.NTF_GROUPING_REPEAT_FOREVER);
            if (ntfGroupingClass.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS_TIME) != 0 && ntfGroupingClass.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS_NONE) != 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            if (ntfGroupingClass.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS_NONE) != 0 && getTTCVersion() < 5) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23).fillInStackTrace());
            }
            if (ntfGroupingClass.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_CLASS_TIME) == 0) {
                kpdnrgrpcla[i] = 1;
                kpdnrgrpval[i] = 600;
                if (ntfGroupingValue != null) {
                    kpdnrgrpval[i] = Integer.parseInt(ntfGroupingValue);
                }
                kpdnrgrptyp[i] = 1;
                if (ntfGroupingType != null) {
                    if (ntfGroupingType.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_TYPE_SUMMARY) == 0) {
                        kpdnrgrptyp[i] = 1;
                    } else if (ntfGroupingType.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_TYPE_LAST) == 0) {
                        kpdnrgrptyp[i] = 2;
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                    }
                }
                kpdnrgrpstatim[i] = ntfGroupingStartTime;
                if (ntfGroupingRepeatTime.compareTo(oracle.jdbc.OracleConnection.NTF_GROUPING_REPEAT_FOREVER) == 0) {
                    kpdnrgrprepcnt[i] = 0;
                } else {
                    kpdnrgrprepcnt[i] = Integer.parseInt(ntfGroupingRepeatTime);
                }
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v58, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    void doUnregisterAQNotification(NTFAQRegistration registration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                String host = registration.getClientHost();
                int port = registration.getClientTCPPort();
                if (host != null) {
                    PhysicalConnection.ntfManager.removeRegistration(registration);
                    PhysicalConnection.ntfManager.freeJdbcRegId(registration.getJdbcRegId());
                    PhysicalConnection.ntfManager.cleanListenersT4C(registration.getClientTCPPort());
                    registration.setState(NotificationRegistration.RegistrationState.CLOSED);
                    String location = "(ADDRESS=(PROTOCOL=" + (registration.getUseSSL() ? "tcps" : "tcp") + " )(HOST=" + host + ")(PORT=" + port + "))?PR=0";
                    int[] namespaceArr = {1};
                    String[] registeredAgentNameArr = {registration.getQueueName()};
                    int[] payloadTypeArr = {0};
                    int[] qosFlagsArr = {0};
                    int[] timeoutArr = {0};
                    int[] dbchangeOpFilterArr = {0};
                    int[] dbchangeTxnLagArr = {0};
                    long[] dbchangeRegistrationIdArr = {0};
                    byte[] kpdnrgrpcla = {0};
                    int[] kpdnrgrpval = {0};
                    byte[] kpdnrgrptyp = {0};
                    TIMESTAMPTZ[] kpdnrgrpstatim = {null};
                    int[] kpdnrgrprepcnt = {0};
                    int jdbcRegIdArr = registration.getJdbcRegId();
                    ?? r0 = {new byte[4]};
                    r0[0][0] = (byte) ((jdbcRegIdArr & (-16777216)) >> 24);
                    r0[0][1] = (byte) ((jdbcRegIdArr & 16711680) >> 16);
                    r0[0][2] = (byte) ((jdbcRegIdArr & OracleXAResource.ORAISOLATIONMASK) >> 8);
                    r0[0][3] = (byte) (jdbcRegIdArr & 255);
                    try {
                        this.okpn.doOKPN(2, 0, this.userName, location, 1, namespaceArr, registeredAgentNameArr, r0, payloadTypeArr, qosFlagsArr, timeoutArr, dbchangeOpFilterArr, dbchangeTxnLagArr, dbchangeRegistrationIdArr, kpdnrgrpcla, kpdnrgrpval, kpdnrgrptyp, kpdnrgrpstatim, kpdnrgrprepcnt, dbchangeRegistrationIdArr);
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                    return;
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                    return;
                                }
                            }
                            lock.close();
                            return;
                        }
                        return;
                    } catch (IOException ex) {
                        handleIOException(ex);
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                    }
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v31, types: [byte[], byte[][]] */
    long registerInbandNotification(int cacheLag, String location) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int[] namespaceArr = {2};
                String[] registeredAgentNameArr = new String[1];
                int[] payloadTypeArr = {0};
                int[] qosFlagsArr = {0};
                int[] timeoutArr = {0};
                int[] dbchangeOpFilterArr = {128};
                int[] dbchangeTxnLagArr = {cacheLag};
                long[] dbchangeRegistrationIdArr = {0};
                int jdbcRegId = PhysicalConnection.ntfManager.getNextJdbcRegId();
                ?? r0 = {new byte[4]};
                r0[0][0] = (byte) ((jdbcRegId & (-16777216)) >> 24);
                r0[0][1] = (byte) ((jdbcRegId & 16711680) >> 16);
                r0[0][2] = (byte) ((jdbcRegId & OracleXAResource.ORAISOLATIONMASK) >> 8);
                r0[0][3] = (byte) (jdbcRegId & 255);
                byte[] kpdnrgrpclaArr = new byte[1];
                int[] kpdnrgrpvalArr = new int[1];
                byte[] kpdnrgrptypArr = new byte[1];
                TIMESTAMPTZ[] kpdnrgrpstatimArr = new TIMESTAMPTZ[1];
                int[] kpdnrgrprepcntArr = new int[1];
                try {
                    this.okpn.doOKPN(1, 0, this.userName, location, 1, namespaceArr, registeredAgentNameArr, r0, payloadTypeArr, qosFlagsArr, timeoutArr, dbchangeOpFilterArr, dbchangeTxnLagArr, dbchangeRegistrationIdArr, kpdnrgrpclaArr, kpdnrgrpvalArr, kpdnrgrptypArr, kpdnrgrpstatimArr, kpdnrgrprepcntArr, dbchangeRegistrationIdArr);
                    long regid = this.okpn.getRegistrationId();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return regid;
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r29v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r29v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r30v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r30v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 29, insn: 0x0539: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = 
      (r29 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('lock' oracle.jdbc.internal.Monitor$CloseableLock)])
     A[TRY_LEAVE], block:B:115:0x0539 */
    /* JADX WARN: Not initialized variable reg: 30, insn: 0x053e: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r30 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:117:0x053e */
    /* JADX WARN: Type inference failed for: r0v113, names: [contextArr], types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v116 */
    /* JADX WARN: Type inference failed for: r0v118 */
    /* JADX WARN: Type inference failed for: r0v120 */
    /* JADX WARN: Type inference failed for: r0v122 */
    /* JADX WARN: Type inference failed for: r0v143, types: [oracle.jdbc.driver.T4CTTIokpn] */
    /* JADX WARN: Type inference failed for: r29v0, names: [lock], types: [oracle.jdbc.internal.Monitor$CloseableLock] */
    /* JADX WARN: Type inference failed for: r30v0, types: [java.lang.Throwable] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    @Debug(level = Debug.Level.CONFIG)
    NTFDCNRegistration doRegisterDatabaseChangeNotification(String dcnhost, int tcpport, @Blind(PropertiesBlinder.class) Properties options, int kpdnrtmout, int kpdcntxl) throws SQLException {
        boolean zListenOnPortT4C;
        try {
            try {
                debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "doRegisterDatabaseChangeNotification", "entering args ({0}, {1}, {2}, {3}, {4})", (String) null, (String) null, dcnhost, Integer.valueOf(tcpport), new PropertiesBlinder().blind((PropertiesBlinder) options), Integer.valueOf(kpdnrtmout), Integer.valueOf(kpdcntxl));
                Monitor.CloseableLock closeableLockAcquireCloseableLock = ntfManager.acquireCloseableLock();
                Throwable th = null;
                boolean z = Boolean.parseBoolean(options.getProperty(oracle.jdbc.OracleConnection.DCN_CLIENT_INIT_CONNECTION, "false"));
                String jMSConnectionId = null;
                int i = 0;
                int i2 = 0;
                boolean z2 = false;
                if (tcpport == 0) {
                    z2 = true;
                    tcpport = 47632;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_RELIABLE, "false").compareToIgnoreCase("true") == 0) {
                    i2 = 0 | 1;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.NTF_QOS_PURGE_ON_NTFN, "false").compareToIgnoreCase("true") == 0) {
                    i2 |= 16;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_NOTIFY_ROWIDS, "false").compareToIgnoreCase("true") == 0) {
                    i = 0 | 16;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_QUERY_CHANGE_NOTIFICATION, "false").compareToIgnoreCase("true") == 0) {
                    i |= 32;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_BEST_EFFORT, "false").compareToIgnoreCase("true") == 0) {
                    i |= 64;
                }
                boolean z3 = false;
                boolean z4 = false;
                boolean z5 = false;
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_IGNORE_INSERTOP, "false").compareToIgnoreCase("true") == 0) {
                    z3 = true;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_IGNORE_UPDATEOP, "false").compareToIgnoreCase("true") == 0) {
                    z4 = true;
                }
                if (options.getProperty(oracle.jdbc.OracleConnection.DCN_IGNORE_DELETEOP, "false").compareToIgnoreCase("true") == 0) {
                    z5 = true;
                }
                if (z3 || z4 || z5) {
                    i |= 15;
                    if (z3) {
                        i -= 2;
                    }
                    if (z4) {
                        i -= 4;
                    }
                    if (z5) {
                        i -= 8;
                    }
                }
                byte[] bArr = new byte[1];
                int[] iArr = new int[1];
                byte[] bArr2 = new byte[1];
                TIMESTAMPTZ[] timestamptzArr = new TIMESTAMPTZ[1];
                int[] iArr2 = new int[1];
                setNtfGroupingOptions(bArr, iArr, bArr2, timestamptzArr, iArr2, new Properties[]{options});
                int[] iArr3 = {tcpport};
                Exception[] excArr = new Exception[1];
                int i3 = 1;
                String str = null;
                NTFDCNConnectionGroup dCNConnectionGroup = null;
                if (z) {
                    i3 = 4;
                    i2 |= 8;
                    dCNConnectionGroup = PhysicalConnection.ntfManager.getDCNConnectionGroup(this.userName + this.instanceName);
                    NTFDCNConnection nTFDCNConnection = dCNConnectionGroup.getNTFDCNConnection();
                    zListenOnPortT4C = nTFDCNConnection == null;
                    if (!zListenOnPortT4C) {
                        String clientId = nTFDCNConnection.getClientId();
                        str = clientId;
                        jMSConnectionId = clientId;
                    }
                } else {
                    zListenOnPortT4C = PhysicalConnection.ntfManager.listenOnPortT4C(iArr3, z2, null, excArr);
                    tcpport = iArr3[0];
                    if (!zListenOnPortT4C) {
                        i3 = 0;
                    }
                    str = "(ADDRESS=(PROTOCOL=tcp)(HOST=" + dcnhost + ")(PORT=" + tcpport + "))?PR=0";
                }
                int[] iArr4 = {2};
                String[] strArr = new String[1];
                int[] iArr5 = {23};
                int[] iArr6 = {i2};
                int[] iArr7 = {kpdnrtmout};
                int[] iArr8 = {i};
                int[] iArr9 = {kpdcntxl};
                long[] jArr = {0};
                int nextJdbcRegId = PhysicalConnection.ntfManager.getNextJdbcRegId();
                ?? r0 = {new byte[4]};
                r0[0][0] = (byte) ((nextJdbcRegId & (-16777216)) >> 24);
                r0[0][1] = (byte) ((nextJdbcRegId & 16711680) >> 16);
                r0[0][2] = (byte) ((nextJdbcRegId & OracleXAResource.ORAISOLATIONMASK) >> 8);
                r0[0][3] = (byte) (nextJdbcRegId & 255);
                String property = options.getProperty(oracle.jdbc.OracleConnection.DCN_CLIENT_INIT_REGID, "0");
                if (!property.equals("0")) {
                    trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "doRegisterDatabaseChangeNotification", "User set regid to: " + property, (String) null, (Throwable) null, new Object[0]);
                }
                try {
                    jArr[0] = Long.parseLong(property);
                    if (jArr[0] < 0) {
                        jArr[0] = 0;
                    }
                } catch (Exception e) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "doRegisterDatabaseChangeNotification", "Exception while parsing regid: " + property, null, e);
                    jArr[0] = 0;
                }
                try {
                    try {
                        this.okpn.doOKPN(1, i3, this.userName, str, 1, iArr4, strArr, r0, iArr5, iArr6, iArr7, iArr8, iArr9, jArr, bArr, iArr, bArr2, timestamptzArr, iArr2, jArr, null);
                        long registrationId = this.okpn.getRegistrationId();
                        if (z && zListenOnPortT4C) {
                            jMSConnectionId = this.okpn.getJMSConnectionId();
                            if (jMSConnectionId == null) {
                                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "clientID returned by server is null").fillInStackTrace());
                            }
                            ArrayList<String> listenerAddressForDCN = getListenerAddressForDCN(options);
                            if (listenerAddressForDCN == null) {
                                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "listenerAddress returned by server is null").fillInStackTrace());
                            }
                            String str2 = this.net.getSessionAttributes().getcOption().service_name;
                            if (str2 == null) {
                                str2 = this.net.getSessionAttributes().getcOption().getOriginalConnOption().service_name;
                            }
                            if (str2 == null) {
                                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 287, "No service name found, please use a service name format URL to create a connection").fillInStackTrace());
                            }
                            dCNConnectionGroup.startDCNListenerConnection(this.instanceName, str2, this.userName, this.password, getConnectionPropsforListenerConnection(), listenerAddressForDCN, jMSConnectionId, 1);
                        }
                        NTFDCNRegistration nTFDCNRegistration = new NTFDCNRegistration(nextJdbcRegId, true, this.dbName, registrationId, this.userName, dcnhost, tcpport, options, this.versionNumber, excArr, r0, jMSConnectionId, z);
                        PhysicalConnection.ntfManager.addDCNRegistration(nTFDCNRegistration);
                        if (closeableLockAcquireCloseableLock != null) {
                            if (0 != 0) {
                                try {
                                    closeableLockAcquireCloseableLock.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                closeableLockAcquireCloseableLock.close();
                            }
                        }
                        debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "doRegisterDatabaseChangeNotification", "returning {0}", (String) null, (String) null, nTFDCNRegistration);
                        return nTFDCNRegistration;
                    } catch (SQLException e2) {
                        if (zListenOnPortT4C && !z) {
                            PhysicalConnection.ntfManager.cleanListenersT4C(tcpport);
                        }
                        throw e2;
                    }
                } catch (IOException e3) {
                    handleIOException(e3);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e3).fillInStackTrace());
                }
            } catch (Throwable th3) {
                debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CConnection", "doRegisterDatabaseChangeNotification", "throwing", (String) null, (String) th3, new Object[0]);
                throw th3;
            }
        } finally {
        }
    }

    private ArrayList<String> getListenerAddressForDCN(@Blind(PropertiesBlinder.class) Properties options) {
        boolean useHostConnectionListener = Boolean.parseBoolean(options.getProperty(oracle.jdbc.OracleConnection.DCN_USE_HOST_CONNECTION_ADDR_INFO, "true"));
        NTAdapter ntAdapter = this.net.getSessionAttributes().getNTAdapter();
        if (useHostConnectionListener && (ntAdapter.getNetworkAdapterType() == NTAdapter.NetworkAdapterType.TCP || ntAdapter.getNetworkAdapterType() == NTAdapter.NetworkAdapterType.TCPS)) {
            ArrayList<String> addrList = new ArrayList<>();
            addrList.add(((TcpNTAdapter) ntAdapter).getAddressInfo());
            return addrList;
        }
        return this.okpn.getListenerAddresses();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v38, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    void doUnregisterDatabaseChangeNotification(long registrationId, String location) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int[] namespaceArr = {2};
                String[] registeredAgentNameArr = new String[1];
                int[] payloadTypeArr = {0};
                int[] qosFlagsArr = {0};
                int[] timeoutArr = {0};
                int[] dbchangeOpFilterArr = {0};
                int[] dbchangeTxnLagArr = {0};
                byte[] kpdnrgrpclaArr = {0};
                int[] kpdnrgrpvalArr = {0};
                byte[] kpdnrgrptypArr = {0};
                TIMESTAMPTZ[] kpdnrgrpstatimArr = {null};
                int[] kpdnrgrprepcntArr = {0};
                long[] dbchangeRegistrationIdArr = {registrationId};
                try {
                    this.okpn.doOKPN(2, (location == null || !location.startsWith("OCI:EP")) ? 0 : 4, null, location, 1, namespaceArr, registeredAgentNameArr, new byte[1], payloadTypeArr, qosFlagsArr, timeoutArr, dbchangeOpFilterArr, dbchangeTxnLagArr, dbchangeRegistrationIdArr, kpdnrgrpclaArr, kpdnrgrpvalArr, kpdnrgrptypArr, kpdnrgrpstatimArr, kpdnrgrprepcntArr, dbchangeRegistrationIdArr);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (IOException ex) {
                    handleIOException(ex);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doUnregisterDatabaseChangeNotification(NTFDCNRegistration dcnregistration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            PhysicalConnection.ntfManager.removeRegistration(dcnregistration);
            PhysicalConnection.ntfManager.freeJdbcRegId(dcnregistration.getJdbcRegId());
            if (dcnregistration.isClientInitiated()) {
                NTFDCNConnectionGroup dcnGroup = PhysicalConnection.ntfManager.getDCNConnectionGroup(this.userName + this.instanceName);
                dcnGroup.stopNTFDCNConnection(dcnregistration.getClientId());
                doUnregisterClientInitiatedDCN(dcnregistration);
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "doUnregisterDatabaseChangeNotification", "dcnregistration id={0}, JdbcRegId={1}, client id={2}, database name={3}. ", (String) null, null, Long.valueOf(dcnregistration.getRegId()), Integer.valueOf(dcnregistration.getJdbcRegId()), dcnregistration.getClientId(), dcnregistration.getDatabaseName());
            } else {
                PhysicalConnection.ntfManager.cleanListenersT4C(dcnregistration.getClientTCPPort());
                dcnregistration.setState(NotificationRegistration.RegistrationState.CLOSED);
                doUnregisterDatabaseChangeNotification(dcnregistration.getRegId(), "(ADDRESS=(PROTOCOL=tcp)(HOST=" + dcnregistration.getClientHost() + ")(PORT=" + dcnregistration.getClientTCPPort() + "))?PR=0");
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "doUnregisterDatabaseChangeNotification", "dcnregistration regId={0}, state={1}, JdbcRegId={2}, client id={3}, database name={4}. ", (String) null, null, Long.valueOf(dcnregistration.getRegId()), dcnregistration.getState().toString(), Integer.valueOf(dcnregistration.getJdbcRegId()), dcnregistration.getClientId(), dcnregistration.getDatabaseName());
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private void doUnregisterClientInitiatedDCN(NTFDCNRegistration dcnRegistration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int[] emptyInt = {0};
                byte[] emptyByte = {0};
                TIMESTAMPTZ[] emptyTZ = {null};
                long[] dbchangeRegistrationIdArr = {dcnRegistration.getRegId()};
                this.okpn.doOKPN(2, 4, this.userName, dcnRegistration.getClientId(), 1, new int[]{2}, new String[1], dcnRegistration.getContext(), emptyInt, emptyInt, emptyInt, emptyInt, emptyInt, dbchangeRegistrationIdArr, emptyByte, emptyInt, emptyByte, emptyTZ, emptyInt, dbchangeRegistrationIdArr);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getDataIntegrityAlgorithmName() throws SQLException {
        return this.net.getDataIntegrityName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getEncryptionAlgorithmName() throws SQLException {
        return this.net.getEncryptionName();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public SecurityInformation getSecurityInformation() throws SQLException {
        if (this.net != null && this.net.getSessionAttributes() != null) {
            return this.net.getSessionAttributes().getSecurityInformation();
        }
        return null;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getAuthenticationAdaptorName() throws SQLException {
        return this.net.getAuthenticationAdaptorName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void validateConnectionProperties() throws SQLException {
        super.validateConnectionProperties();
        if (this.thinVsessionProgram == null) {
            this.thinVsessionProgram = GeneratedPhysicalConnection.applicationProgramName;
            if (this.thinVsessionProgram == null) {
                this.thinVsessionProgram = "JDBC Thin Client";
            }
        }
        if (this.localHostName == null) {
            try {
                this.localHostName = InetAddress.getLocalHost().getHostName();
            } catch (UnknownHostException e) {
                this.localHostName = "__jdbc__";
            }
        }
        if (!this.networkCompression.equals("off") && !this.networkCompression.equals(oracle.jdbc.OracleConnection.NETWORK_COMPRESSION_ON) && !this.networkCompression.equals(oracle.jdbc.OracleConnection.NETWORK_COMPRESSION_AUTO)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 190, "Property is 'oracle.net.networkCompression' and value is '" + this.networkCompression + "'").fillInStackTrace());
        }
        if (this.networkCompressionThreshold < 200) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 190, "Property is 'oracle.net.networkCompressionThreshold' and value is '" + this.networkCompressionThreshold + "'").fillInStackTrace());
        }
        if (!this.networkCompressionLevels.equalsIgnoreCase(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_LEVELS_DEFAULT)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 190, "Property is 'oracle.net.networkCompressionLevels' and value is '" + this.networkCompressionLevels + "'. Only (high) is supported.").fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte[] createLightweightSession(String userName, KeywordValueLong[] inKeyVal, int inFlags, KeywordValueLong[][] outKeyVal, int[] outFlags) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (outKeyVal.length != 1 || outFlags.length != 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            try {
                this.oxsscs.doOXSSCS(userName, inKeyVal, inFlags);
                byte[] ret = this.oxsscs.getSessionId();
                outKeyVal[0] = this.oxsscs.getOutKV();
                outFlags[0] = this.oxsscs.getOutFlags();
                return ret;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private void doXSNamespaceOp(OracleConnection.XSOperationCode operationCode, byte[] sessionId, XSNamespace[] namespaces, XSNamespace[][] returnedNamespaces, XSSecureId secureId, boolean roundTripRPC) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            XSNamespace[] ret = null;
            try {
                this.xsnsop2.doOXSNS(operationCode, sessionId, namespaces, secureId, roundTripRPC);
                if (roundTripRPC) {
                    ret = this.xsnsop2.getNamespaces();
                }
                if (returnedNamespaces != null && returnedNamespaces.length > 0) {
                    returnedNamespaces[0] = ret;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSSessionDetachOp(int opcode, byte[] sessionId, XSSecureId sidp, boolean roundTripRPC) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oxsdet.doOXSDET(opcode, sessionId, sidp, roundTripRPC);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSNamespaceOp(OracleConnection.XSOperationCode operationCode, byte[] sessionId, XSNamespace[] namespaces, XSNamespace[][] returnedNamespaces, XSSecureId secureId) throws SQLException {
        doXSNamespaceOp(operationCode, sessionId, namespaces, returnedNamespaces, secureId, true);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSNamespaceOp(OracleConnection.XSOperationCode operationCode, byte[] sessionId, XSNamespace[] namespaces, XSSecureId secureId) throws SQLException {
        doXSNamespaceOp(operationCode, sessionId, namespaces, (XSNamespace[][]) null, secureId, false);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte[] doXSSessionCreateOp(OracleConnection.XSSessionOperationCode opcode, XSSecureId sidp, byte[] cookie, XSPrincipal username, String tenant, XSNamespace[] namespaces, OracleConnection.XSSessionModeFlag mode, XSKeyval Kv) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oxscre.doOXSCRE(opcode, sidp, cookie, username, tenant, namespaces, mode, Kv);
                byte[] ret = this.oxscre.getSessionId();
                return ret;
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSSessionDestroyOp(byte[] sessionId, XSSecureId sidp, byte[] cookie) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oxsdes.doOXSDES(sessionId, sidp, cookie);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSSessionAttachOp(int opCode, byte[] sessionId, XSSecureId sidp, byte[] cookie, XSPrincipal username, String[] disabledRoles, String[] enabledRoles, String[] externalRoles, XSNamespace[] namespaces, XSNamespace[] cacheNamespace, XSNamespace[] deleteNamespace, TIMESTAMPTZ midTierTimestamp, TIMESTAMPTZ authtime, int roleVersion, long inputFlag, XSKeyval Kv, int[] roleVersionOutput) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oxsatt.doOXSATT(opCode, sessionId, sidp, cookie, username, disabledRoles, enabledRoles, externalRoles, namespaces, cacheNamespace, deleteNamespace, midTierTimestamp, authtime, roleVersion, inputFlag, Kv, roleVersionOutput);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doXSSessionChangeOp(OracleConnection.XSSessionSetOperationCode opCode, byte[] sessionId, XSSecureId sidp, XSSessionParameters sessParam) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oxsset.doOXSSET(opCode, sessionId, sidp, (XSSessionParametersI) sessParam);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (IOException ex) {
                handleIOException(ex);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener l, Executor e) throws SQLException {
        requireOpenConnection();
        NTFEventListener listener = new NTFEventListener(l);
        listener.setExecutor(e);
        Monitor.CloseableLock lock = this.ltxidListenersMonitor.acquireCloseableLock();
        Throwable th = null;
        try {
            int length = this.ltxidListeners.length;
            for (int i = 0; i < length; i++) {
                if (this.ltxidListeners[i].getLogicalTransactionIdEventListener() == l) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 248).fillInStackTrace());
                }
            }
            NTFEventListener[] listeners2 = new NTFEventListener[length + 1];
            System.arraycopy(this.ltxidListeners, 0, listeners2, 0, length);
            listeners2[length] = listener;
            this.ltxidListeners = listeners2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener l) throws SQLException {
        addLogicalTransactionIdEventListener(l, null);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void removeLogicalTransactionIdEventListener(LogicalTransactionIdEventListener listener) throws SQLException {
        Monitor.CloseableLock lock = this.ltxidListenersMonitor.acquireCloseableLock();
        Throwable th = null;
        try {
            int length = this.ltxidListeners.length;
            int i = 0;
            while (i < length && this.ltxidListeners[i].getLogicalTransactionIdEventListener() != listener) {
                i++;
            }
            if (i == length) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_RM_MISSING_LISTENER).fillInStackTrace());
            }
            NTFEventListener[] listeners2 = new NTFEventListener[length - 1];
            int offset = 0;
            for (int i2 = 0; i2 < length; i2++) {
                if (this.ltxidListeners[i2].getLogicalTransactionIdEventListener() != listener) {
                    int i3 = offset;
                    offset++;
                    listeners2[i3] = this.ltxidListeners[i2];
                }
            }
            this.ltxidListeners = listeners2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.LogicalTransactionId getLogicalTransactionId() throws SQLException {
        return this.thinACCurrentLTXID;
    }

    boolean notify(final NTFLTXIDEvent event) {
        boolean listenersWereNotified = false;
        if (this.ltxidListeners != null) {
            NTFEventListener[] localListeners = this.ltxidListeners;
            int length = localListeners.length;
            if (length > 0) {
                listenersWereNotified = true;
            }
            for (int i = 0; i < length; i++) {
                Executor exec = localListeners[i].getExecutor();
                if (exec != null) {
                    final LogicalTransactionIdEventListener l = localListeners[i].getLogicalTransactionIdEventListener();
                    exec.execute(new Runnable() { // from class: oracle.jdbc.driver.T4CConnection.1
                        @Override // java.lang.Runnable
                        public void run() {
                            l.onLogicalTransactionIdEvent(event);
                        }
                    });
                } else {
                    localListeners[i].getLogicalTransactionIdEventListener().onLogicalTransactionIdEvent(event);
                }
            }
        }
        return listenersWereNotified;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void addXSEventListener(XSEventListener l, Executor e) throws SQLException {
        requireOpenConnection();
        NTFEventListener listener = new NTFEventListener(l);
        listener.setExecutor(e);
        Monitor.CloseableLock lock = this.xsListenersMonitor.acquireCloseableLock();
        Throwable th = null;
        try {
            int length = this.xsListeners.length;
            for (int i = 0; i < length; i++) {
                if (this.xsListeners[i].getXSEventListener() == l) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 248).fillInStackTrace());
                }
            }
            NTFEventListener[] listeners2 = new NTFEventListener[length + 1];
            System.arraycopy(this.xsListeners, 0, listeners2, 0, length);
            listeners2[length] = listener;
            this.xsListeners = listeners2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void addXSEventListener(XSEventListener l) throws SQLException {
        addXSEventListener(l, null);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void removeXSEventListener(XSEventListener listener) throws SQLException {
        Monitor.CloseableLock lock = this.xsListenersMonitor.acquireCloseableLock();
        Throwable th = null;
        try {
            int length = this.xsListeners.length;
            int i = 0;
            while (i < length && this.xsListeners[i].getXSEventListener() != listener) {
                i++;
            }
            if (i == length) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_RM_MISSING_LISTENER).fillInStackTrace());
            }
            NTFEventListener[] listeners2 = new NTFEventListener[length - 1];
            int offset = 0;
            for (int i2 = 0; i2 < length; i2++) {
                if (this.xsListeners[i2].getXSEventListener() != listener) {
                    int i3 = offset;
                    offset++;
                    listeners2[i3] = this.xsListeners[i2];
                }
            }
            this.xsListeners = listeners2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void removeAllXSEventListener() throws SQLException {
        Monitor.CloseableLock lock = this.xsListenersMonitor.acquireCloseableLock();
        Throwable th = null;
        try {
            NTFEventListener[] listeners2 = new NTFEventListener[0];
            this.xsListeners = listeners2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void notify(final NTFXSEvent event) {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "notify", "DCNRegistration got an event. ", (String) null, (Throwable) null);
        NTFEventListener[] localListeners = this.xsListeners;
        int length = localListeners.length;
        for (int i = 0; i < length; i++) {
            Executor exec = localListeners[i].getExecutor();
            if (exec != null) {
                final XSEventListener l = localListeners[i].getXSEventListener();
                exec.execute(new Runnable() { // from class: oracle.jdbc.driver.T4CConnection.2
                    @Override // java.lang.Runnable
                    public void run() {
                        l.onXSEvent(event);
                    }
                });
            } else {
                localListeners[i].getXSEventListener().onXSEvent(event);
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean isConnectionBigTZTC() throws SQLException {
        if (getLifecycle() != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
        }
        return hasServerCompileTimeCapability(40, 16);
    }

    final boolean hasServerCompileTimeCapability(int index, int flag) {
        boolean ret = false;
        if (this.serverCompileTimeCapabilities != null && this.serverCompileTimeCapabilities.length > index && (this.serverCompileTimeCapabilities[index] & flag & 255) != 0) {
            ret = true;
        }
        return ret;
    }

    final byte getServerCompileTimeCapability(int index) {
        byte capability;
        if (this.serverCompileTimeCapabilities == null || this.serverCompileTimeCapabilities.length <= index) {
            capability = 0;
        } else {
            capability = this.serverCompileTimeCapabilities[index];
        }
        return capability;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    long doGetCurrentSCN() throws SQLException {
        return this.outScn;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    EnumSet<OracleConnection.TransactionState> doGetTransactionState() throws SQLException {
        EnumSet<OracleConnection.TransactionState> ret = EnumSet.noneOf(OracleConnection.TransactionState.class);
        if ((this.eocs & 1) != 0) {
            ret.add(OracleConnection.TransactionState.TRANSACTION_READONLY);
        }
        if ((this.eocs & 2) != 0) {
            if (inSessionlessTxnMode()) {
                ret.add(OracleConnection.TransactionState.SESSIONLESS_TRANSACTION_STARTED);
            } else {
                ret.add(OracleConnection.TransactionState.LOCAL_TRANSACTION_STARTED);
            }
        }
        if ((this.eocs & 4) != 0) {
            ret.add(OracleConnection.TransactionState.TRANSACTION_ENDED);
        }
        if ((this.eocs & 1024) != 0) {
            ret.add(OracleConnection.TransactionState.TRANSACTION_INTENTION);
        }
        return ret;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.net.isConnectionSocketKeepAlive();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public int getEOC() throws SQLException {
        return this.eocs;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setReplayOperations(EnumSet<OracleConnection.ReplayOperation> ops) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.osessstateFlags != OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_END.getCode() || ops.size() != 0) {
                if (this.osessstateFlags != OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_END.getCode() && this.osessstateFlags != OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_END.getCode() + OracleConnection.ReplayOperation.KPDSS_SESSSTATE_EXPL_BOUND.getCode()) {
                    this.osessstateFlags = 0L;
                }
                Iterator<OracleConnection.ReplayOperation> it = ops.iterator();
                while (it.hasNext()) {
                    this.osessstateFlags |= it.next().getCode();
                }
                if ((this.osessstateFlags & OracleConnection.ReplayOperation.KPDSS_SESSSTATE_APPCONT_ENABLED.getCode()) != 0) {
                    this.replayModes.add(ReplayMode.RUNTIME_REPLAY_ENABLED);
                    this.isInRequest = true;
                } else {
                    this.replayModes.remove(ReplayMode.RUNTIME_REPLAY_ENABLED);
                }
                if ((this.osessstateFlags & OracleConnection.ReplayOperation.KPDSS_SESSSTATE_STATIC.getCode()) != 0) {
                    this.replayModes.add(ReplayMode.RUNTIME_OR_REPLAYING_STATIC);
                } else {
                    this.replayModes.remove(ReplayMode.RUNTIME_OR_REPLAYING_STATIC);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setReplayingMode(boolean replaying) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (replaying) {
                    this.replayModes.remove(ReplayMode.RUNTIME_REPLAY_ENABLED);
                    this.replayModes.add(ReplayMode.REPLAYING);
                } else {
                    this.replayModes.remove(ReplayMode.REPLAYING);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void beginNonRequestCalls() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.replayModes.add(ReplayMode.NONREQUEST);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void endNonRequestCalls() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.replayModes.remove(ReplayMode.NONREQUEST);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setReplayContext(oracle.jdbc.internal.ReplayContext[] contexts) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (contexts != null) {
            try {
                try {
                    this.oappcontreplayContextsArr = contexts;
                    this.oappcontreplayOffset = 0;
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ReplayContext[] getReplayContext() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.thinACReplayContextReceivedCurrent != 0) {
                oracle.jdbc.internal.ReplayContext[] copy = new oracle.jdbc.internal.ReplayContext[this.thinACReplayContextReceivedCurrent];
                System.arraycopy(this.thinACReplayContextReceived, 0, copy, 0, this.thinACReplayContextReceivedCurrent);
                this.thinACReplayContextReceivedCurrent = 0;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return copy;
            }
            if (this.nonRequestDisableReplayCxt == null) {
                return null;
            }
            oracle.jdbc.internal.ReplayContext[] copy2 = {this.nonRequestDisableReplayCxt};
            this.nonRequestDisableReplayCxt = null;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return copy2;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ReplayContext getLastReplayContext() throws SQLException {
        return this.thinACLastReplayContextReceived;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setLastReplayContext(oracle.jdbc.internal.ReplayContext cxt) throws SQLException {
        this.thinACLastReplayContextReceived = (ReplayContext) cxt;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void registerEndReplayCallback(OracleConnection.EndReplayCallback callback) throws SQLException {
        this.endReplayCallback = callback;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte[] getDerivedKeyInternal(byte[] dhKey, int mode) throws SQLException, InvalidKeySpecException, NoSuchAlgorithmException {
        return this.auth.getDerivedKeyJdbc(dhKey, mode);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void releaseConnectionToPool() throws SQLException {
        if (this.drcpState == OracleConnection.DRCPState.DETACHED) {
            return;
        }
        awaitPipeline();
        closeStatements(true, false);
        this.releasedSessID = getSessionId();
        this.releasedSerial = getSerialNumber();
        try {
            if (this.currentlyInTransaction) {
                this.osessrls.setTTCCode((byte) 3);
                this.osessrls.doRPC();
            } else {
                this.osessrls.setTTCCode((byte) 26);
                this.osessrls.doOneWayRPC();
            }
            this.drcpState = OracleConnection.DRCPState.DETACHED;
            if (isDRCPEnabled() && !isClientInitiatedNTFConnection() && net().isTLSEnabled()) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "releaseConnectionToPool", "Initializing TLS renegotiation, it will be performed before any further roundtrips on this connection", (String) null, null);
                net().getSessionAttributes().initTLSRenegotiation();
            }
        } catch (IOException ea) {
            handleIOException(ea);
        } catch (SQLException ea2) {
            if (this.currentlyInTransaction) {
                throw ea2;
            }
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "releaseConnectionToPool", null, (String) null, ea2);
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean reusePooledConnection() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.drcpState == OracleConnection.DRCPState.DETACHED) {
                try {
                    if (this.drcpTagName == null) {
                        return true;
                    }
                    this.osessget.doRPC();
                    this.drcpState = OracleConnection.DRCPState.ATTACHED_EXPLICIT;
                } catch (IOException ea) {
                    handleIOException(ea);
                }
            }
            if (getTTCVersion() >= 8) {
                boolean z = (this.osessget.returnTag == null || bit(this.osessget.sessgetflags, 1L)) ? false : true;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return z;
            }
            boolean z2 = !bit(this.osessget.sessgetflags, 1L);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return z2;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private boolean isClientInitiatedNTFConnection() {
        return this.jmsNotificationConnection;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isDRCPMultitagEnabled() throws SQLException {
        return this.drcpEnabled && this.useDRCPMultipletag;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getDRCPReturnTag() throws SQLException {
        return this.osessget.returnTag;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean needToPurgeStatementCache() throws SQLException {
        requireOpenConnection();
        if (this.drcpEnabled) {
            return bit(this.ocsessret.sessretflags, 4L) || bit(this.ocsessret.sessretflags, 8L) || getSessionId() != this.releasedSessID || getSerialNumber() != this.releasedSerial;
        }
        return false;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void resetAfterReusePooledConnection() throws SQLException {
        if (!this.drcpEnabled) {
            return;
        }
        if (this.isPDBChanged) {
            OracleStatement executingStmt = getExecutingStatement();
            if (executingStmt == null) {
                onPDBChange(null);
                this.isPDBChanged = false;
            }
        }
        if (needToPurgeStatementCache()) {
            clearCursorIds();
        }
        this.drcpState = OracleConnection.DRCPState.ATTACHED_IMPLICIT;
    }

    protected void clearCursorIds() {
        cleanStatementCache();
        OracleStatement oracleStatement = this.statements;
        while (true) {
            OracleStatement s = oracleStatement;
            if (s != null) {
                if (!s.serverCursor) {
                    s.clearCursorId();
                }
                oracleStatement = s.next;
            } else {
                this.cursorToClose = new int[4];
                this.cursorToCloseOffset = 0;
                this.lastCursorToCloseOffset = 0;
                return;
            }
        }
    }

    protected boolean canSendCursorIds() {
        boolean canSend = true;
        if (this.drcpEnabled && this.drcpState == OracleConnection.DRCPState.DETACHED) {
            canSend = false;
        }
        return canSend;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper
    public int getNetworkTimeout() throws SQLException {
        if (!this.net.getSessionAttributes().isConnected()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
        }
        try {
            return this.net.getSocketReadTimeout();
        } catch (NetException ne) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) ne).fillInStackTrace());
        } catch (IOException ie) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ie).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doSetNetworkTimeout(int milliseconds) throws SQLException {
        if (!this.net.getSessionAttributes().isConnected()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
        }
        try {
            this.net.setSocketReadTimeout(milliseconds);
        } catch (NetException ne) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) ne).fillInStackTrace());
        } catch (IOException ie) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ie).fillInStackTrace());
        }
    }

    static final String dumpObject(Object o, String indent) {
        return dumpObject(o, indent, new StringBuilder());
    }

    static final String dumpObject(Object o, String indent, StringBuilder sb) throws IllegalAccessException, IllegalArgumentException {
        Class<?> c = o.getClass();
        Field[] fields = c.getDeclaredFields();
        sb.append(indent + c.getName() + " { \n");
        for (Field f : fields) {
            if ((f.getModifiers() & 8) == 0) {
                f.setAccessible(true);
                try {
                    sb.append(indent + "  " + f.getName() + " = ");
                    Object _o = f.get(o);
                    if (_o == null) {
                        sb.append("null");
                    } else {
                        sb.append(_o);
                    }
                    sb.append("\n");
                } catch (IllegalAccessException e) {
                }
            }
        }
        sb.append(indent + "}\n");
        return sb.toString();
    }

    byte getNextSeqNumber() {
        if (this.currentTTCSeqNumber == Byte.MAX_VALUE) {
            this.currentTTCSeqNumber = (byte) 1;
            return this.currentTTCSeqNumber;
        }
        byte b = (byte) (this.currentTTCSeqNumber + 1);
        this.currentTTCSeqNumber = b;
        return b;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public int getNegotiatedSDU() throws SQLException {
        try {
            return this.net.getNegotiatedSDU();
        } catch (NetException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (IOException) e).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte getNegotiatedTTCVersion() throws SQLException {
        return this.negotiatedTTCversion;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    String getAuditBanner() throws SQLException {
        requireOpenConnection();
        String tmpAuditBanner = this.sessionProperties.getProperty("AUTH_AUDIT_BANNER");
        return tmpAuditBanner;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    String getAccessBanner() throws SQLException {
        requireOpenConnection();
        String tmpAccessBanner = this.net.getAccessBanner();
        return tmpAccessBanner;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    ResultSetCache getResultSetCacheInternal() throws SQLException {
        return this.resultSetCache;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    ArrayList<Long> getResultSetCacheLocalInvalidations() {
        return this.resultSetCacheLocalInvalidations;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ResultSetCache getResultSetCache() throws SQLException {
        return this.resultSetCache;
    }

    final boolean isResultSetCacheActive() {
        return this.isResultSetCacheEnabled && this.resultSetCache != null && this.resultSetCache.getState() == ResultSetCache.ResultSetCacheState.STARTED;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public boolean isServerBigSCN() throws SQLException {
        return this.isServerBigSCN;
    }

    final long getResultSetCacheVisibleSCN() {
        return this.resultSetCache.getVisibleSCN();
    }

    final void setResultSetCacheVisibleSCN(long scn) {
        this.resultSetCache.setVisibleSCN(scn);
    }

    final byte[] getResultSetCacheId() {
        return this.resultSetCache.getCacheId();
    }

    final byte[] getResultSetCacheIdAsNibbles() {
        return this.resultSetCache.getCacheIdAsNibbles();
    }

    final long getResultSetCacheRegistrationId() {
        return this.resultSetCache.getRegistrationId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setChunkInfo(OracleShardingKey shardingKey, OracleShardingKey superShardingKey, String chunkName) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (shardingKey != null) {
            try {
                try {
                    this.shardingKey = ((OracleShardingKeyImpl) shardingKey).encodeKeyinB64Format();
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (superShardingKey != null) {
            this.superShardingKey = ((OracleShardingKeyImpl) superShardingKey).encodeKeyinB64Format();
        }
        if (chunkName != null) {
            this.chunkName = chunkName;
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setShardingKey(OracleShardingKey shardingKey, OracleShardingKey superShardingKey) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String shardingKeyStr = null;
            String superShardingKeyStr = null;
            if (shardingKey != null) {
                try {
                    shardingKeyStr = ((OracleShardingKeyImpl) shardingKey).encodeKeyinB64Format();
                } catch (IOException e) {
                    throw ((SQLException) DatabaseError.createSqlException(e).fillInStackTrace());
                }
            }
            if (superShardingKey != null) {
                superShardingKeyStr = ((OracleShardingKeyImpl) superShardingKey).encodeKeyinB64Format();
            }
            this.ochunkinfo.doOCHUNKINFO(shardingKeyStr, superShardingKeyStr, null, false);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean setShardingKeyIfValid(OracleShardingKey shardingKey, OracleShardingKey superShardingKey, int timeout) throws SQLException {
        boolean isValid;
        int existingTimeout;
        String superKeyStr;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireNonNegativeTimeout(timeout);
            if (getLifecycle() != 1) {
                return false;
            }
            int timeoutMillis = timeout * 1000;
            try {
                try {
                    existingTimeout = this.net.getSocketReadTimeout();
                    if (timeoutMillis > 0 && (timeoutMillis < existingTimeout || existingTimeout == 0)) {
                        this.net.setSocketReadTimeout(timeoutMillis);
                    }
                    superKeyStr = null;
                } catch (Throwable th2) {
                    if (-1 >= 0) {
                        try {
                            this.net.setSocketReadTimeout(-1);
                        } catch (IOException e) {
                            throw th2;
                        }
                    }
                    throw th2;
                }
            } catch (SocketTimeoutException e2) {
                isValid = false;
                if (-1 >= 0) {
                    try {
                        this.net.setSocketReadTimeout(-1);
                    } catch (IOException e3) {
                        isValid = false;
                    }
                }
            } catch (InterruptedIOException iioe) {
                handleIOException(iioe);
                isValid = false;
                if (-1 >= 0) {
                    try {
                        this.net.setSocketReadTimeout(-1);
                    } catch (IOException e4) {
                        isValid = false;
                    }
                }
            } catch (IOException e5) {
                isValid = false;
                if (-1 >= 0) {
                    try {
                        this.net.setSocketReadTimeout(-1);
                    } catch (IOException e6) {
                        isValid = false;
                    }
                }
            } catch (SQLException e7) {
                if (e7.getErrorCode() != 45582 && e7.getErrorCode() != 5016) {
                    throw e7;
                }
                isValid = false;
                if (-1 >= 0) {
                    try {
                        this.net.setSocketReadTimeout(-1);
                    } catch (IOException e8) {
                        isValid = false;
                    }
                }
            }
            if (shardingKey == null) {
                throw new IllegalArgumentException("Shard key argument cannot be null");
            }
            String shardingKeyStr = ((OracleShardingKeyImpl) shardingKey).encodeKeyinB64Format();
            if (superShardingKey != null) {
                superKeyStr = ((OracleShardingKeyImpl) superShardingKey).encodeKeyinB64Format();
            }
            this.ochunkinfo.doOCHUNKINFO(shardingKeyStr, superKeyStr, null, false);
            isValid = true;
            if (existingTimeout >= 0) {
                try {
                    this.net.setSocketReadTimeout(existingTimeout);
                } catch (IOException e9) {
                    isValid = false;
                }
            }
            boolean z = isValid;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private void requireNonNegativeTimeout(int timeout) throws SQLException {
        if (timeout < 0) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NEGATIVE_TIMEOUT, Integer.valueOf(timeout));
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setShardingKey(OracleShardingKey shardingKey) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                setShardingKey(shardingKey, (OracleShardingKey) null);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean setShardingKeyIfValid(OracleShardingKey shardingKey, int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean shardingKeyIfValid = setShardingKeyIfValid(shardingKey, (OracleShardingKey) null, timeout);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return shardingKeyIfValid;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private OracleStatement getExecutingStatement() {
        if (getExecutingRPCFunctionCode() != 94 || this.all8 == null) {
            return null;
        }
        return this.all8.oracleStatement;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public NetStat getNetworkStat() {
        return this.net.getNetworkStat();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean isNetworkCompressionEnabled() {
        return this.net.isNetworkCompressionEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public int getOutboundConnectTimeout() {
        return this.net.getOutboundConnectTimeout();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void beginRequest(boolean implicit, boolean enableAC) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            super.beginRequest(implicit, enableAC);
            if (this.svrSupportsRequests) {
                EnumSet<OracleConnection.ReplayOperation> opSet = EnumSet.of(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_BEGIN);
                if (this.svrSupportsExplicitRequestBit) {
                    if (!implicit) {
                        opSet.add(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_EXPL_BOUND);
                    }
                } else if (implicit) {
                    opSet.add(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_NO_OPEN_HANDLES);
                }
                if (enableAC) {
                    opSet.add(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_APPCONT_ENABLED);
                }
                setReplayOperations(opSet);
                debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "beginRequest", "Sending BEGIN_REQUEST to server: {0}", (String) null, (String) null, (Object) opSet);
            } else {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "beginRequest", "Exiting beginRequest(), server does not support request boundaries", (String) null, (Throwable) null);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void endRequest(boolean implicit) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (!isInRequest()) {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "endRequest", "endRequest called without beginRequest on {0}, implicit: {1}", null, null, this, Boolean.valueOf(implicit));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            if (this.net.getSessionAttributes().isConnected()) {
                this.net.cancelTimeout();
            }
            if (this.svrSupportsRequests) {
                if (this.svrSupportsExplicitRequestBit && !implicit) {
                    setReplayOperations(EnumSet.of(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_END, OracleConnection.ReplayOperation.KPDSS_SESSSTATE_EXPL_BOUND));
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "endRequest", "Set END_REQUEST+EXPLICIT bits on client-side. ", (String) null, (Throwable) null);
                } else {
                    setReplayOperations(EnumSet.of(OracleConnection.ReplayOperation.KPDSS_SESSSTATE_REQUEST_END));
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "endRequest", "Set END_REQUEST bit on client-side. ", (String) null, (Throwable) null);
                }
            } else {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "endRequest", "Server does not support request boundary, no bit set. ", (String) null, (Throwable) null);
            }
            if (!implicit && this.enableResetState) {
                resetToInitialClientState();
            }
            super.endRequest(implicit);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean hasNoOpenHandles() throws SQLException {
        OracleStatement stmt;
        if (!this.enableImplicitRequests) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Implicit request support is explicitly disabled via property, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        if (this.pipeline != null && this.pipeline.isStarted()) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Pipelining has started, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        if (this.sessionState != null && this.sessionState.isUnrestorable()) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Current session state is unrestorable, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        int _txnMode = getTxnMode();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "txn mode={0}. ", (String) null, (String) null, Integer.valueOf(_txnMode));
        if (_txnMode == 1) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Connection is still in a global txn, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        if (inSessionlessTxnMode()) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Connection is still in a sessionless txn, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        if (inLocalTransaction()) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "Connection is still in a local txn, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        if (this.statements != null) {
            OracleStatement oracleStatement = this.statements;
            while (true) {
                stmt = oracleStatement;
                if (stmt != null) {
                    if ((stmt.cacheState == 3 || stmt.cacheState == 1) && ((stmt.sqlKind.isSELECT() && !(this.enableSSSCursor && stmt.isSSSCursor)) || stmt.sqlKind.isPlsqlOrCall() || ((stmt.sqlKind.isDML() || stmt.sqlKind.isOTHER()) && (stmt.isAutoKeyUsed() || stmt.doesBatchExist())))) {
                        break;
                    }
                    oracleStatement = stmt.next;
                } else {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection has NO open statements. ", (String) null, (Throwable) null);
                    break;
                }
            }
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection still has open statements (for example, {0}), returning false.", (String) null, (String) null, (Object) stmt);
            return false;
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection has NO open statements. ", (String) null, (Throwable) null);
        if (this.bfileCount.get() > 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection still has open Bfile locators, returning false. ", (String) null, (Throwable) null);
            return false;
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection has NO open Bfile locators. ", (String) null, (Throwable) null);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "hasNoOpenHandles", "connection has NO open handles. ", (String) null, (Throwable) null);
        return true;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.DatabaseSessionState getDatabaseSessionState() {
        if (this.sessionState == null) {
            return null;
        }
        return this.sessionState.copy();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void setDatabaseSessionState(oracle.jdbc.internal.DatabaseSessionState newSessionState) {
        this.sessionStateOut = (DatabaseSessionState) newSessionState;
    }

    final void updateSessionState(StateSignatures stateSignatures, TemplateOverflow templateOverflow) {
        if (this.sessionState == null) {
            this.sessionState = new DatabaseSessionState();
        }
        this.sessionState.update(stateSignatures, templateOverflow);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void addLargeObject(OracleLargeObject lob) throws SQLException {
        this.lobCount.incrementAndGet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void removeLargeObject(OracleLargeObject lob) throws SQLException {
        this.lobCount.decrementAndGet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void addBfile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        this.bfileCount.incrementAndGet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void removeBfile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
        this.bfileCount.decrementAndGet();
    }

    void enterMarshalling() throws NetException {
        if (!this.net.getSessionAttributes().isConnected()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        if (this.writeBufferIsDirty) {
            this.mare.clearWriteBuffer();
        }
        this.writeBufferIsDirty = true;
    }

    void exitMarshalling() {
        this.writeBufferIsDirty = false;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getEncryptionProviderName() throws SQLException {
        if (this.net.getSessionAttributes().isEncryptionActive) {
            return this.net.getSessionAttributes().ano.getEncryptionProvider();
        }
        return null;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getChecksumProviderName() throws SQLException {
        if (this.net.getSessionAttributes().isChecksumActive) {
            return this.net.getSessionAttributes().ano.getChecksumProvider();
        }
        return null;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getJavaNetProperties() throws SQLException {
        return this.net.getSessionAttributes().getNetProperties();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getNetConnectionId() throws SQLException {
        if (this.net != null && this.net.getSessionAttributes() != null) {
            return this.net.getSessionAttributes().getNetConnectionId();
        }
        return null;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean readInbandDownNotification() {
        this.net.readInbandNotification();
        return this.net.needsToBeClosed();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection, java.lang.AutoCloseable
    public void close() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (getLifecycle() != 2 && getLifecycle() != 4) {
                awaitPipeline();
            }
            super.doClose();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean isShardingDriverMode() {
        return this.shardingDriverMode;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean isTrueCacheDriverMode() {
        return this.tcDriverMode;
    }

    boolean isUsingCustomHostnameResolver() {
        return this.net.isUsingCustomHostnameResolver();
    }

    boolean useShardingDriverConnection() {
        return this.useShardingDriverConnection;
    }

    boolean useTrueCacheDriverConnection() {
        return this.useTrueCacheDriverConnection;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isSSSHybrid() throws SQLException {
        return this.isHybrid;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setSSSHybrid(boolean isHybrid) throws SQLException {
        this.isHybrid = isHybrid;
    }

    byte[] getOsonBytes(byte[] jsonLobLocator, long lobOffset) throws SQLException {
        needLine();
        try {
            int readLength = (int) this.jsonMsg.getLength(jsonLobLocator);
            byte[] osonBuffer = new byte[readLength];
            getOsonBytes(jsonLobLocator, lobOffset, readLength, osonBuffer, 0);
            return osonBuffer;
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    void getOsonBytes(byte[] jsonLobLocator, long lobOffset, int readLength, byte[] osonBuffer, int osonBufferOffset) throws SQLException {
        needLine();
        try {
            this.jsonMsg.read(jsonLobLocator, lobOffset, readLength, osonBuffer, osonBufferOffset);
        } catch (IOException ex) {
            handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void addFeature(OracleConnection.ClientFeature cf) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                doAddFeature(cf);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void doAddFeature(OracleConnection.ClientFeature cf) throws SQLException {
        assertLockHeldByCurrentThread();
        this.oclFeatures.add(cf);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    ByteBuffer convertClobDataInNetworkCharSet(oracle.jdbc.internal.OracleClob clob, char[] clobData, int length) throws SQLException {
        boolean varWidthChar = this.clobMsg.isLobCharsetVariableWidth(clob.shareBytes());
        int byteBufferSize = this.clobMsg.getByteBufferSizeForConversion(varWidthChar, length);
        byte[] clobDataBuffer = new byte[byteBufferSize];
        int bytesConverted = this.clobMsg.encodeNetworkCharSet(clobData, 0, length, clobDataBuffer, varWidthChar, clob.isNCLOB(), null);
        return ByteBuffer.wrap(clobDataBuffer, 0, bytesConverted);
    }

    byte[] setupJsonQuasiLocator(long lobDataLength) {
        return setupQuasiLocator(lobDataLength, (short) 1, (byte) 33, (byte) 0, canDisableZeroCopyIO(lobDataLength) ? (byte) 16 : (byte) 0, (short) 0);
    }

    boolean canDisableZeroCopyIO(long lobDataLength) {
        try {
            if (this.useDynamicVectorIO) {
                if (lobDataLength <= getNegotiatedSDU()) {
                    return true;
                }
            }
            return false;
        } catch (SQLException e) {
            return false;
        }
    }

    boolean isZeroCopyIOEnabled(byte[] quasiLocator) {
        return isZeroCopyIOEnabled() && (quasiLocator[6] & 16) != 16;
    }

    static byte[] setupClobVectorQuasiLocator(long characterLength, short characterSetId) throws SQLException {
        boolean isFixedWidth = CharacterSetMetaData.isFixedWidth(characterSetId);
        int characterWidth = CharacterSetMetaData.getRatio(characterSetId, 1);
        short bytl = isFixedWidth ? (short) characterWidth : (short) 2;
        long dataLength = bytl * characterLength;
        return setupQuasiLocator(dataLength, bytl, (byte) 34, (byte) 0, (isFixedWidth || characterWidth == 1) ? (byte) 0 : Byte.MIN_VALUE, characterSetId);
    }

    static byte[] setupQuasiLocator(long lobDataLength, short byteWidth, byte flag1, byte flag2, byte flag3, short characterSetId) {
        byte[] locator = new byte[40];
        locator[1] = 38;
        locator[3] = 4;
        locator[4] = (byte) (flag1 | 64);
        locator[5] = (byte) (flag2 | 8);
        locator[6] = flag3;
        locator[8] = (byte) ((byteWidth >> 8) & 255);
        locator[9] = (byte) (byteWidth & 255);
        locator[10] = (byte) ((lobDataLength >> 56) & 255);
        locator[11] = (byte) ((lobDataLength >> 48) & 255);
        locator[12] = (byte) ((lobDataLength >> 40) & 255);
        locator[13] = (byte) ((lobDataLength >> 32) & 255);
        locator[14] = (byte) ((lobDataLength >> 24) & 255);
        locator[15] = (byte) ((lobDataLength >> 16) & 255);
        locator[16] = (byte) ((lobDataLength >> 8) & 255);
        locator[17] = (byte) (lobDataLength & 255);
        locator[20] = (byte) ((characterSetId >> 8) & 255);
        locator[21] = (byte) (characterSetId & 255);
        return locator;
    }

    private Properties getConnectionPropsforListenerConnection() {
        Properties connectionProps = new Properties();
        if (this.thinNetAuthenticationServices != null) {
            connectionProps.setProperty("oracle.net.authentication_services", this.thinNetAuthenticationServices);
        }
        if (this.thinNetAuthenticationKrb5Mutual != null) {
            connectionProps.setProperty("oracle.net.kerberos5_mutual_authentication", this.thinNetAuthenticationKrb5Mutual);
        }
        if (this.thinNetAuthenticationKrb5CcName != null) {
            connectionProps.setProperty("oracle.net.kerberos5_cc_name", this.thinNetAuthenticationKrb5CcName);
        }
        if (this.thinNetAuthenticationKrbJaasLoginModule != null) {
            connectionProps.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_JAAS_LOGIN_MODULE, this.thinNetAuthenticationKrbJaasLoginModule);
        }
        if (this.walletLocation != null) {
            connectionProps.setProperty("oracle.net.wallet_location", this.walletLocation);
        }
        if (this.walletPassword != null && this.walletPassword != OpaqueString.NULL) {
            connectionProps.setProperty("oracle.net.wallet_password", this.walletPassword.get());
        }
        if (this.tnsAdmin != null) {
            connectionProps.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN, this.tnsAdmin);
        }
        if (this.thinJavaxNetSslTruststore != null) {
            connectionProps.setProperty("javax.net.ssl.trustStore", this.thinJavaxNetSslTruststore);
        }
        if (this.thinJavaxNetSslTruststoretype != null) {
            connectionProps.setProperty("javax.net.ssl.trustStoreType", this.thinJavaxNetSslTruststoretype);
        }
        if (this.thinJavaxNetSslTruststorepassword != null && this.thinJavaxNetSslTruststorepassword != OpaqueString.NULL) {
            connectionProps.setProperty("javax.net.ssl.trustStorePassword", this.thinJavaxNetSslTruststorepassword.get());
        }
        if (this.thinJavaxNetSslKeystore != null) {
            connectionProps.setProperty("javax.net.ssl.keyStore", this.thinJavaxNetSslKeystore);
        }
        if (this.thinJavaxNetSslKeystoretype != null) {
            connectionProps.setProperty("javax.net.ssl.keyStoreType", this.thinJavaxNetSslKeystoretype);
        }
        if (this.thinJavaxNetSslKeystorepassword != null && this.thinJavaxNetSslKeystorepassword != OpaqueString.NULL) {
            connectionProps.setProperty("javax.net.ssl.keyStorePassword", this.thinJavaxNetSslKeystorepassword.get());
        }
        connectionProps.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_NET_KEEPALIVE, "true");
        return connectionProps;
    }

    Pipeline pipeline() {
        return this.pipeline;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    OracleTimeout getTimeout() throws SQLException {
        if (this.pipeline.isExecuting()) {
            return this.pipeline.createTimeout();
        }
        return super.getTimeout();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected final void awaitPipeline() throws SQLException {
        if (this.pipeline == null) {
            return;
        }
        this.pipeline.await();
    }

    public void lastExecutedFunCode(short funCode) {
        this.lastExecutedFunCode = funCode;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startTransaction(byte[] GTRID, int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.sessionlessTxn.doStartOrResume(GTRID, timeout, 1, 2);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void resumeTransaction(byte[] GTRID, int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.sessionlessTxn.doStartOrResume(GTRID, timeout, 2, 2);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void suspendTransactionImmediately() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.sessionlessTxn.doSuspendImmediately();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void suspendTransaction() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.sessionlessTxn.doPreCallSuspend(2);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void postCallSuspend() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.sessionlessTxn.doPostCallSuspend();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public byte[] getTransactionId() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.sessionlessTxn.ensureSessionlessTxnIsSupported();
            byte[] gtrid = this.sessionlessTxn.getGTRID();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return gtrid;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doStartOrResume(byte[] GTRID, int timeout, int operation, int txnMode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.sessionlessTxn.doStartOrResume(GTRID, timeout, operation, txnMode);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void doPreCallSuspend(int txnMode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.sessionlessTxn.doPreCallSuspend(txnMode);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }
}
