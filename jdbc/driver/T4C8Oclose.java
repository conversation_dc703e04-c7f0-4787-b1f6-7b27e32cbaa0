package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Oclose.class */
final class T4C8Oclose extends T4CTTIfun {
    private int[] cursorId;
    private int offset;

    T4C8Oclose(T4CConnection _conn) {
        super(_conn, (byte) 17);
        this.cursorId = null;
        this.offset = 0;
    }

    void doOCANA(int[] _cursorId, int _offset) throws IOException {
        setFunCode((short) 120);
        this.cursorId = _cursorId;
        this.offset = _offset;
        doPigRPC();
    }

    void doOCCA(int[] _cursorId, int _offset) throws IOException {
        setFunCode((short) 105);
        this.cursorId = _cursorId;
        this.offset = _offset;
        doPigRPC();
        this.connection.lastPiggyBackCursorCloseSeqNumber = this.connection.currentTTCSeqNumber;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.offset);
        for (int i = 0; i < this.offset; i++) {
            this.meg.marshalUB4(this.cursorId[i]);
        }
    }
}
