package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIOtxen.class */
final class T4CTTIOtxen extends T4CTTIfun {
    static final int OTXCOMIT = 1;
    static final int OTXABORT = 2;
    static final int OTXPREPA = 3;
    static final int OTXFORGT = 4;
    static final int OTXRECOV = 5;
    static final int OTXMLPRE = 6;
    static final int K2CMDprepare = 0;
    static final int K2CMDrqcommit = 1;
    static final int K2CMDcommit = 2;
    static final int K2CMDabort = 3;
    static final int K2CMDrdonly = 4;
    static final int K2CMDforget = 5;
    static final int K2CMDrecovered = 7;
    static final int K2CMDtimeout = 8;
    static final int K2STAidle = 0;
    static final int K2STAcollecting = 1;
    static final int K2STAprepared = 2;
    static final int K2STAcommitted = 3;
    static final int K2STAhabort = 4;
    static final int K2STAhcommit = 5;
    static final int K2STAhdamage = 6;
    static final int K2STAtimeout = 7;
    static final int K2STAinactive = 9;
    static final int K2STAactive = 10;
    static final int K2STAptprepared = 11;
    static final int K2STAptcommitted = 12;
    static final int K2STAmax = 13;
    static final int OTXNDEF_F_CWRBATCH = 1;
    static final int OTXNDEF_F_CWRBATOPT = 2;
    static final int OTXNDEF_F_CWRNOWAIT = 4;
    static final int OTXNDEF_F_CWRWATOPT = 8;
    static final int OTXNDEF_F_CWRBATMSK = 3;
    static final int OTXNDEF_F_CWRWATMSK = 12;
    private int operation;
    private int formatId;
    private int gtridLength;
    private int bqualLength;
    private int timeout;
    private int inState;
    private int txnflg;
    private byte[] transactionContext;
    private byte[] xid;
    private int outState;

    T4CTTIOtxen(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.xid = null;
        this.outState = -1;
        setFunCode((short) 104);
    }

    void doOTXEN(int _operation, byte[] _transactionContext, byte[] _xid, int _formatId, int _gtridLength, int _bqualLength, int _timeout, int _inState, int _txnflg) throws SQLException, IOException {
        validateOperationType(_operation);
        this.operation = _operation;
        this.formatId = _formatId;
        this.gtridLength = _gtridLength;
        this.bqualLength = _bqualLength;
        this.timeout = _timeout;
        this.inState = _inState;
        this.txnflg = _txnflg;
        this.transactionContext = _transactionContext;
        this.xid = _xid;
        this.outState = -1;
        doRPC();
    }

    private final void validateOperationType(int operation) throws SQLException {
        if (operation != 1 && operation != 2 && operation != 3 && operation != 4 && operation != 5 && operation != 6) {
            throw new SQLException("Invalid operation.");
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        int txnopc = this.operation;
        this.meg.marshalSWORD(txnopc);
        if (this.transactionContext == null) {
            this.meg.marshalNULLPTR();
        } else {
            this.meg.marshalPTR();
        }
        if (this.transactionContext == null) {
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalUB4(this.transactionContext.length);
        }
        this.meg.marshalUB4(this.formatId);
        this.meg.marshalUB4(this.gtridLength);
        this.meg.marshalUB4(this.bqualLength);
        if (this.xid != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.xid != null) {
            this.meg.marshalUB4(this.xid.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUWORD(this.timeout);
        this.meg.marshalUB4(this.inState);
        this.meg.marshalPTR();
        if (this.connection.getTTCVersion() >= 4) {
            this.meg.marshalUB4(this.txnflg);
        }
        if (this.transactionContext != null) {
            this.meg.marshalB1Array(this.transactionContext);
        }
        if (this.xid != null) {
            this.meg.marshalB1Array(this.xid);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.outState = (int) this.meg.unmarshalUB4();
    }

    int getOutStateFromServer() {
        return this.outState;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
