package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIJson.class */
final class T4C8TT<PERSON><PERSON><PERSON> extends T4C8TTILob {
    T4C8TTIJson(T4CConnection conn) {
        super(conn);
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    Datum createTemporaryLob(Connection conn, boolean cache, int duration) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean openLob(byte[] lobLocator, int mode) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean closeLob(byte[] lobLocator) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean isOpenLob(byte[] lobLocator) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }
}
