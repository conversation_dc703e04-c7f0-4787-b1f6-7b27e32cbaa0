package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkscn8.class */
class T4CTTIkscn8 extends T4CTTIkscn {
    int kscnwrp2;

    T4CTTIkscn8(T4CConnection _conn) {
        super(_conn, (byte) 0);
    }

    T4CTTIkscn8(T4CConnection _conn, byte _ttccode) {
        super(_conn, _ttccode);
    }

    @Override // oracle.jdbc.driver.T4CTTIkscn
    public void unmarshal() throws SQLException, IOException {
        this.kscnbas = this.meg.unmarshalUB4();
        this.kscnwrp = this.meg.unmarshalUB2() & (-32769);
        this.kscnwrp2 = this.meg.unmarshalUB2();
    }

    @Override // oracle.jdbc.driver.T4CTTIkscn
    public void marshal() throws SQLException, IOException {
        this.meg.marshalUB4(this.kscnbas);
        this.meg.marshalUB2(this.kscnwrp);
        this.meg.marshalUB2(this.kscnwrp2);
    }

    @Override // oracle.jdbc.driver.T4CTTIkscn
    public long getSCN() {
        long scn = (this.kscnbas & SQLnetDef.NSPDDLSLMAX) | ((this.kscnwrp & SQLnetDef.NSPDDLSLMAX) << 32) | ((this.kscnwrp2 & SQLnetDef.NSPDDLSLMAX) << 48);
        return scn;
    }

    @Override // oracle.jdbc.driver.T4CTTIkscn
    public void setSCN(long scn) {
        this.kscnbas = scn & SQLnetDef.NSPDDLSLMAX;
        this.kscnwrp = (int) (scn >> 32);
        this.kscnwrp2 = (int) (scn >> 48);
    }
}
