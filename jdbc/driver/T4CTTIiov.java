package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIiov.class */
class T4CTTIiov extends T4CTTIMsg {
    T4C8TTIrxh rxh;
    T4CTTIrxd rxd;
    short bindtype;
    byte[] iovector;
    int bindcnt;
    int inbinds;
    int outbinds;
    static final byte BV_IN_V = 32;
    static final byte BV_OUT_V = 16;
    public static final boolean TRACE = false;
    private static final String CLASS_NAME = T4CTTIiov.class.getName();
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_ = null;

    T4CTTIiov(T4CConnection _conn, T4C8TTIrxh _rxh, T4CTTIrxd _rxd) throws SQLException, IOException {
        super(_conn, (byte) 0);
        this.bindtype = (short) 0;
        this.bindcnt = 0;
        this.inbinds = 0;
        this.outbinds = 0;
        this.rxh = _rxh;
        this.rxd = _rxd;
    }

    void init() throws SQLException, IOException {
    }

    Accessor[] processRXD(Accessor[] outBindAccessors, int number_of_bind_positions, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bindIndicatorSubRange, DBConversion conversion, byte[] tmpBindsByteArray, byte[] ioVector, InputStream[][] parameterStream, OracleStatement oracleStatement, byte[] ibtBindBytes, char[] ibtBindChars, short[] ibtBindIndicators) throws SQLException, IOException {
        if (ioVector != null) {
            for (int i = 0; i < ioVector.length; i++) {
                if ((ioVector[i] & 16) != 0 && (outBindAccessors == null || outBindAccessors.length <= i || outBindAccessors[i] == null)) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "processRXD", "The OUT paramter at index {0} was not registered with registerOutParamter. All the OUT paramters of a PL/SQL procedure need to be registered.", (String) null, (String) null, (Object) Integer.valueOf(i));
                    int subRangeOffset = bindIndicatorSubRange + 5 + (10 * i);
                    int type = bindIndicators[subRangeOffset + 0] & 65535;
                    if (type == 9) {
                        type = 1;
                    }
                    Accessor acc = oracleStatement.allocateAccessor(type, type, i, 0, (short) 0, null, false);
                    acc.rowSpaceIndicator = null;
                    if (outBindAccessors == null) {
                        outBindAccessors = new Accessor[i + 1];
                        outBindAccessors[i] = acc;
                    } else if (outBindAccessors.length <= i) {
                        Accessor[] outBindAccessorsNew = new Accessor[i + 1];
                        outBindAccessorsNew[i] = acc;
                        for (int ii = 0; ii < outBindAccessors.length; ii++) {
                            if (outBindAccessors[ii] != null) {
                                outBindAccessorsNew[ii] = outBindAccessors[ii];
                            }
                        }
                        outBindAccessors = outBindAccessorsNew;
                    } else {
                        outBindAccessors[i] = acc;
                    }
                } else if ((ioVector[i] & 16) == 0 && outBindAccessors != null && i < outBindAccessors.length && outBindAccessors[i] != null) {
                    outBindAccessors[i].isUseLess = true;
                }
            }
        }
        return outBindAccessors;
    }

    void unmarshalV10(int numberOfBindPositions) throws SQLException, IOException {
        this.rxh.unmarshalV10(this.rxd);
        this.bindcnt = this.rxh.numRqsts;
        this.iovector = new byte[numberOfBindPositions];
        for (int i = 0; i < this.iovector.length; i++) {
            short sUnmarshalUB1 = this.meg.unmarshalUB1();
            this.bindtype = sUnmarshalUB1;
            if (sUnmarshalUB1 == 0) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalV10", "bindtype == 0", (String) null, (Throwable) null);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
            }
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalV10", "bindtype = {0}", (String) null, (String) null, (Object) Short.valueOf(this.bindtype));
            if ((this.bindtype & 32) > 0) {
                byte[] bArr = this.iovector;
                int i2 = i;
                bArr[i2] = (byte) (bArr[i2] | 32);
                this.inbinds++;
            }
            if ((this.bindtype & 16) > 0) {
                byte[] bArr2 = this.iovector;
                int i3 = i;
                bArr2[i3] = (byte) (bArr2[i3] | 16);
                this.outbinds++;
            }
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalV10", "inbinds = {0}", (String) null, (String) null, (Object) Integer.valueOf(this.inbinds));
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalV10", "outbinds = {0}", (String) null, (String) null, (Object) Integer.valueOf(this.outbinds));
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalV10", "return", (String) null, (Throwable) null);
    }

    byte[] getIOVector() {
        return this.iovector;
    }

    boolean isIOVectorEmpty() {
        return this.iovector.length == 0;
    }
}
