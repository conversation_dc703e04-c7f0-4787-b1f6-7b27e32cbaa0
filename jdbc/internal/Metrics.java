package oracle.jdbc.internal;

import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.TimeUnit;

/* loaded from: ojdbc8.jar:oracle/jdbc/internal/Metrics.class */
public interface Metrics {
    static void enable() {
        oracle.jdbc.diagnostics.Metrics.enable(true);
    }

    static void disable() {
        oracle.jdbc.diagnostics.Metrics.enable(false);
    }

    static void print(OutputStream outputStream, TimeUnit timeUnit) throws IOException {
        if (outputStream == null || timeUnit == null) {
            throw new IllegalArgumentException("Parameter(s) cannot be null.");
        }
        oracle.jdbc.diagnostics.Metrics.print(outputStream, timeUnit);
    }

    static void clear() {
        oracle.jdbc.diagnostics.Metrics.clear();
    }
}
