package oracle.jdbc.internal;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/* loaded from: ojdbc8.jar:oracle/jdbc/internal/Monitor.class */
public interface Monitor {
    CloseableLock getMonitorLock();

    static {
        if (AnonymousClass1.$assertionsDisabled) {
        }
    }

    static CloseableLock newDefaultLock() {
        return CloseableLock.wrap(new ReentrantLock());
    }

    default CloseableLock acquireCloseableLock() {
        acquireLock();
        return getMonitorLock();
    }

    default void acquireLock() {
        getMonitorLock().lock.lock();
    }

    default void releaseLock() {
        getMonitorLock().lock.unlock();
    }

    default boolean isReentrantLock() {
        return true;
    }

    default void assertLockHeldByCurrentThread() {
        Lock lock = getMonitorLock().lock;
        if (isReentrantLock() && !AnonymousClass1.$assertionsDisabled && !((ReentrantLock) lock).isHeldByCurrentThread()) {
            throw new AssertionError();
        }
    }

    /* renamed from: oracle.jdbc.internal.Monitor$1, reason: invalid class name */
    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/Monitor$1.class */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ boolean $assertionsDisabled;

        static {
            $assertionsDisabled = !Monitor.class.desiredAssertionStatus();
        }
    }

    /* renamed from: oracle.jdbc.internal.Monitor$1MonitorImpl, reason: invalid class name */
    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/Monitor$1MonitorImpl.class */
    class C1MonitorImpl implements Monitor, Serializable {
        private static final long serialVersionUID = -2318644678533776943L;
        private final CloseableLock monitorLock = Monitor.newDefaultLock();

        C1MonitorImpl() {
        }

        @Override // oracle.jdbc.internal.Monitor
        public CloseableLock getMonitorLock() {
            return this.monitorLock;
        }
    }

    static Monitor newInstance() {
        return new C1MonitorImpl();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/Monitor$WaitableMonitor.class */
    public interface WaitableMonitor extends Monitor {
        Condition getMonitorCondition();

        default Condition newMonitorCondition() {
            return getMonitorLock().lock.newCondition();
        }

        default void monitorWait() throws InterruptedException {
            getMonitorCondition().await();
        }

        default void monitorWait(long timeoutMillis) throws InterruptedException {
            monitorWait(timeoutMillis, 0);
        }

        default void monitorWait(long timeout, int nanos) throws InterruptedException {
            if (!AnonymousClass1.$assertionsDisabled && timeout < 0) {
                throw new AssertionError("Negative millisecond timeout");
            }
            if (!AnonymousClass1.$assertionsDisabled && nanos < 0) {
                throw new AssertionError("Negative nanosecond timeout");
            }
            getMonitorCondition().awaitNanos(TimeUnit.MILLISECONDS.toNanos(timeout) + nanos);
        }

        default void monitorNotify() {
            getMonitorCondition().signal();
        }

        default void monitorNotifyAll() {
            getMonitorCondition().signalAll();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/Monitor$CloseableLock.class */
    public static final class CloseableLock implements Lock, AutoCloseable, Serializable {
        private static final long serialVersionUID = -285233395800863550L;
        private final Lock lock;

        private CloseableLock(Lock lock) {
            this.lock = lock;
        }

        public static CloseableLock wrap(Lock lock) {
            return new CloseableLock(lock);
        }

        @Override // java.lang.AutoCloseable
        public void close() {
            this.lock.unlock();
        }

        @Override // java.util.concurrent.locks.Lock
        public void lock() {
            this.lock.lock();
        }

        @Override // java.util.concurrent.locks.Lock
        public void lockInterruptibly() throws InterruptedException {
            this.lock.lockInterruptibly();
        }

        @Override // java.util.concurrent.locks.Lock
        public boolean tryLock() {
            return this.lock.tryLock();
        }

        @Override // java.util.concurrent.locks.Lock
        public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
            return this.lock.tryLock(time, unit);
        }

        @Override // java.util.concurrent.locks.Lock
        public void unlock() {
            this.lock.unlock();
        }

        @Override // java.util.concurrent.locks.Lock
        public Condition newCondition() {
            return this.lock.newCondition();
        }
    }
}
