package oracle.jdbc.internal;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/* loaded from: ojdbc8.jar:oracle/jdbc/internal/CompletionStageUtil.class */
public final class CompletionStageUtil {
    public static final CompletableFuture<Void> VOID_COMPLETED_FUTURE = CompletableFuture.completedFuture(null);

    private CompletionStageUtil() {
    }

    @FunctionalInterface
    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/CompletionStageUtil$CompletionHandler.class */
    public interface CompletionHandler extends Runnable {
        void handle() throws Exception;

        @Override // java.lang.Runnable
        default void run() {
            try {
                handle();
            } catch (Exception exception) {
                throw new CompletionException(exception);
            }
        }
    }

    @FunctionalInterface
    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/CompletionStageUtil$NormalCompletionHandler.class */
    public interface NormalCompletionHandler<T, U> extends Function<T, U> {
        U handle(T t) throws Exception;

        @Override // java.util.function.Function
        default U apply(T input) {
            try {
                return handle(input);
            } catch (Exception exception) {
                throw new CompletionException(exception);
            }
        }
    }

    @FunctionalInterface
    /* loaded from: ojdbc8.jar:oracle/jdbc/internal/CompletionStageUtil$ExceptionalCompletionHandler.class */
    public interface ExceptionalCompletionHandler<E extends Throwable, T> extends Function<E, T> {
        T handle(E e) throws Exception;

        @Override // java.util.function.Function
        default T apply(E input) {
            try {
                return handle(input);
            } catch (Exception exception) {
                throw new CompletionException(exception);
            }
        }
    }

    public static void acceptCompletion(CompletionHandler handler) {
        try {
            handler.handle();
        } catch (Exception unhandledException) {
            throw new CompletionException(unhandledException);
        }
    }

    public static <T> T handleCompletion(T result, Throwable throwable, CompletionHandler handler) {
        acceptCompletion(handler);
        if (throwable == null) {
            return result;
        }
        if (throwable instanceof CompletionException) {
            throw ((CompletionException) throwable);
        }
        throw new CompletionException(throwable);
    }

    public static <T> BiFunction<T, Throwable, T> completionHandler(CompletionHandler handler) {
        return (r, e) -> {
            return handleCompletion(r, e, handler);
        };
    }

    public static <T, U> U handleNormalCompletion(T result, NormalCompletionHandler<T, U> handler) {
        try {
            return handler.handle(result);
        } catch (Exception unhandledException) {
            throw new CompletionException(unhandledException);
        }
    }

    public static <T, U> U handleNormalCompletion(T t, Throwable th, NormalCompletionHandler<T, U> normalCompletionHandler) {
        if (th == null) {
            return (U) handleNormalCompletion(t, normalCompletionHandler);
        }
        if (th instanceof CompletionException) {
            throw ((CompletionException) th);
        }
        throw new CompletionException(th);
    }

    public static <T, U> Function<T, U> normalCompletionHandler(NormalCompletionHandler<T, U> handler) {
        return r -> {
            return handleNormalCompletion(r, handler);
        };
    }

    public static <E extends Throwable, T> T handleExceptionalCompletion(Throwable throwable, Class<E> handledType, ExceptionalCompletionHandler<? super E, ? extends T> handler) {
        if (throwable == null) {
            return null;
        }
        Throwable unwrappedThrowable = unwrapCompletionException(throwable);
        if (handledType.isInstance(unwrappedThrowable)) {
            try {
                return handler.handle(unwrappedThrowable);
            } catch (Exception unhandledException) {
                throw new CompletionException(unhandledException);
            }
        }
        throw new CompletionException(unwrappedThrowable);
    }

    public static <E extends Throwable, T> Function<Throwable, T> exceptionalCompletionHandler(Class<E> handledType, ExceptionalCompletionHandler<? super E, T> handler) {
        return e -> {
            return handleExceptionalCompletion(e, handledType, handler);
        };
    }

    public static <T, E extends Throwable, U> U handleCompletion(T t, NormalCompletionHandler<? super T, U> normalCompletionHandler, Throwable th, Class<E> cls, ExceptionalCompletionHandler<? super E, ? extends U> exceptionalCompletionHandler) {
        if (th == null) {
            return (U) handleNormalCompletion(t, normalCompletionHandler);
        }
        return (U) handleExceptionalCompletion(th, cls, exceptionalCompletionHandler);
    }

    public static <T, E extends Throwable, U> BiFunction<T, Throwable, U> completionHandler(NormalCompletionHandler<T, U> normalHandler, Class<E> handledErrorType, ExceptionalCompletionHandler<? super E, U> errorHandler) {
        return (t, e) -> {
            return handleCompletion(t, normalHandler, e, handledErrorType, errorHandler);
        };
    }

    public static Throwable unwrapCompletionException(Throwable throwable) {
        if (throwable instanceof CompletionException) {
            return throwable.getCause();
        }
        return throwable;
    }

    public static <T> CompletionStage<T> completedStage(T value) {
        return CompletableFuture.completedFuture(value);
    }

    public static <T> CompletionStage<T> failedStage(Throwable failure) {
        CompletableFuture<T> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(failure);
        return failedFuture;
    }

    public static void callOnComplete(CompletionStage<Void> completionStage, Consumer<Throwable> callback) {
        callOnComplete(completionStage, (nil, error) -> {
            callback.accept(error);
        });
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static <T> void callOnComplete(CompletionStage<T> completionStage, BiConsumer<T, Throwable> biConsumer) {
        CompletableFuture<T> future = completionStage.toCompletableFuture();
        if (future.isDone() && !future.isCompletedExceptionally()) {
            biConsumer.accept(future.join(), null);
        } else {
            future.whenComplete((BiConsumer) biConsumer);
        }
    }

    public static <T extends Throwable> T suppress(T throwable, Throwable suppressed) {
        if (suppressed != null) {
            throwable.addSuppressed(suppressed);
        }
        return throwable;
    }
}
