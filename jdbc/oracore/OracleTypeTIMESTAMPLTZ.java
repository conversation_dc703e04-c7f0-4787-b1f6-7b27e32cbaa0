package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.util.Map;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.JavaToJavaConverter;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMPLTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeTIMESTAMPLTZ.class */
public class OracleTypeTIMESTAMPLTZ extends OracleType implements Serializable {
    static final long serialVersionUID = 1615519855865602397L;
    int precision = 0;
    transient OracleConnection connection;

    protected OracleTypeTIMESTAMPLTZ() {
    }

    public OracleTypeTIMESTAMPLTZ(OracleConnection _connection) {
        this.connection = _connection;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return OracleTypes.TIMESTAMPLTZ;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        this.precision = tdsReader.readByte();
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getScale() throws SQLException {
        return 0;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getPrecision() throws SQLException {
        return this.precision;
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.precision = in.readByte();
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeByte(this.precision);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int otype, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        switch (otype) {
            case 1:
                return new TIMESTAMPLTZ(bytes);
            case 2:
                return JavaToJavaConverter.convert(new TIMESTAMPLTZ(bytes), OffsetDateTime.class, (oracle.jdbc.driver.OracleConnection) this.connection.getPhysicalConnection(), null, null);
            case 3:
                return bytes;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (value != null) {
                try {
                    if (value instanceof TIMESTAMPLTZ) {
                        datum = (TIMESTAMPLTZ) value;
                    } else if (value instanceof byte[]) {
                        datum = new TIMESTAMPLTZ((byte[]) value);
                    } else if (value instanceof Timestamp) {
                        datum = new TIMESTAMPLTZ(conn, (Timestamp) value);
                    } else if (value instanceof DATE) {
                        datum = new TIMESTAMPLTZ(conn, (DATE) value);
                    } else if (value instanceof String) {
                        datum = new TIMESTAMPLTZ(conn, (String) value);
                    } else if (value instanceof Date) {
                        datum = new TIMESTAMPLTZ(conn, (Date) value);
                    } else if (value instanceof Time) {
                        datum = new TIMESTAMPLTZ(conn, (Time) value);
                    } else {
                        datum = (Datum) JavaToJavaConverter.convert(value, TIMESTAMPLTZ.class, (oracle.jdbc.driver.OracleConnection) this.connection.getPhysicalConnection(), null, null);
                    }
                } catch (Exception e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
                }
            }
            return datum;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    protected Object unpickle81rec(UnpickleContext context, int format, int otype, Map map) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 90).fillInStackTrace());
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
