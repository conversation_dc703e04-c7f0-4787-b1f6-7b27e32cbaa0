package oracle.jdbc.oracore;

import java.sql.SQLException;
import java.util.Vector;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/UnpickleContext.class */
public final class UnpickleContext {
    byte[] image;
    int absoluteOffset;
    int beginOffset;
    int markedOffset;
    Vector patches;
    long[] ldsOffsets;
    boolean[] nullIndicators;
    boolean bigEndian;

    public UnpickleContext() {
    }

    public UnpickleContext(byte[] image, int begin_offset, boolean[] null_bytes, long[] lds_offset_array, boolean big_endian) {
        this.image = image;
        this.beginOffset = begin_offset;
        this.absoluteOffset = begin_offset;
        this.bigEndian = big_endian;
        this.nullIndicators = null_bytes;
        this.patches = null;
        this.ldsOffsets = lds_offset_array;
    }

    public byte readByte() throws SQLException {
        try {
            return this.image[this.absoluteOffset];
        } finally {
            this.absoluteOffset++;
        }
    }

    public byte[] readVarNumBytes() throws SQLException {
        byte[] varNumBytes = new byte[this.image[this.absoluteOffset] & 255];
        try {
            System.arraycopy(this.image, this.absoluteOffset + 1, varNumBytes, 0, varNumBytes.length);
            return varNumBytes;
        } finally {
            this.absoluteOffset += 22;
        }
    }

    public byte[] readPtrBytes() throws SQLException {
        byte[] bytes = new byte[((this.image[this.absoluteOffset] & 255) * 256) + (this.image[this.absoluteOffset + 1] & 255) + 2];
        System.arraycopy(this.image, this.absoluteOffset, bytes, 0, bytes.length);
        this.absoluteOffset += bytes.length;
        return bytes;
    }

    public void skipPtrBytes() throws SQLException {
        this.absoluteOffset += ((this.image[this.absoluteOffset] & 255) * 256) + (this.image[this.absoluteOffset + 1] & 255) + 2;
    }

    public byte[] readBytes(int n) throws SQLException {
        try {
            byte[] bytes = new byte[n];
            System.arraycopy(this.image, this.absoluteOffset, bytes, 0, n);
            this.absoluteOffset += n;
            return bytes;
        } catch (Throwable th) {
            this.absoluteOffset += n;
            throw th;
        }
    }

    public long readLong() throws SQLException {
        try {
            return ((((((this.image[this.absoluteOffset] & 255) * 256) + (this.image[this.absoluteOffset + 1] & 255)) * 256) + (this.image[this.absoluteOffset + 2] & 255)) * 256) + (this.image[this.absoluteOffset + 3] & 255);
        } finally {
            this.absoluteOffset += 4;
        }
    }

    public short readShort() throws SQLException {
        try {
            return (short) (((this.image[this.absoluteOffset] & 255) * 256) + (this.image[this.absoluteOffset + 1] & 255));
        } finally {
            this.absoluteOffset += 2;
        }
    }

    public byte[] readLengthBytes() throws SQLException {
        long variable_length = readLong();
        return readBytes((int) variable_length);
    }

    public void skipLengthBytes() throws SQLException {
        long variable_length = readLong();
        this.absoluteOffset = (int) (this.absoluteOffset + variable_length);
    }

    public void skipTo(long offset) throws SQLException {
        if (offset > this.absoluteOffset - this.beginOffset) {
            this.absoluteOffset = this.beginOffset + ((int) offset);
        }
    }

    public void skipTo(int offset) throws SQLException {
        if (offset > this.absoluteOffset - this.beginOffset) {
            this.absoluteOffset = this.beginOffset + offset;
        }
    }

    public void mark() throws SQLException {
        this.markedOffset = this.absoluteOffset;
    }

    public void reset() throws SQLException {
        this.absoluteOffset = this.markedOffset;
    }

    public void markAndSkip() throws SQLException {
        this.markedOffset = this.absoluteOffset + 4;
        this.absoluteOffset = this.beginOffset + ((int) readLong());
    }

    public void markAndSkip(long offset) throws SQLException {
        this.markedOffset = this.absoluteOffset;
        this.absoluteOffset = this.beginOffset + ((int) offset);
    }

    public void skipBytes(int n) throws SQLException {
        if (n >= 0) {
            this.absoluteOffset += n;
        }
    }

    public boolean isNull(int idx) {
        return this.nullIndicators[idx];
    }

    public int absoluteOffset() throws SQLException {
        return this.absoluteOffset;
    }

    public int offset() throws SQLException {
        return this.absoluteOffset - this.beginOffset;
    }

    public byte[] image() throws SQLException {
        return this.image;
    }
}
