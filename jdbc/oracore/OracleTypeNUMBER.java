package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.NUMBER;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeNUMBER.class */
public class OracleTypeNUMBER extends OracleType implements Serializable {
    static final long serialVersionUID = -7182242886677299812L;
    int precision;
    int scale;

    protected OracleTypeNUMBER() {
    }

    protected OracleTypeNUMBER(int typecode) {
        super(typecode);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        return toNUMBER(value, conn);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        return toNUMBERArray(obj, conn, beginIdx, count);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        this.precision = tdsReader.readUnsignedByte();
        this.scale = tdsReader.readByte();
    }

    protected static Object unpickle81NativeArray(PickleContext context, long beginIdx, int size, int style) throws SQLException {
        for (int i = 1; i < beginIdx && size > 0; i++) {
            context.skipDataValue();
        }
        switch (style) {
            case 4:
                int[] holder = new int[size];
                for (int i2 = 0; i2 < size; i2++) {
                    byte[] val = context.readDataValue();
                    if (val != null) {
                        holder[i2] = NUMBER.toInt(val);
                    }
                }
                return holder;
            case 5:
                double[] holder2 = new double[size];
                for (int i3 = 0; i3 < size; i3++) {
                    byte[] val2 = context.readDataValue();
                    if (val2 != null) {
                        holder2[i3] = NUMBER.toDouble(val2);
                    }
                }
                return holder2;
            case 6:
                float[] holder3 = new float[size];
                for (int i4 = 0; i4 < size; i4++) {
                    byte[] val3 = context.readDataValue();
                    if (val3 != null) {
                        holder3[i4] = NUMBER.toFloat(val3);
                    }
                }
                return holder3;
            case 7:
                long[] holder4 = new long[size];
                for (int i5 = 0; i5 < size; i5++) {
                    byte[] val4 = context.readDataValue();
                    if (val4 != null) {
                        holder4[i5] = NUMBER.toLong(val4);
                    }
                }
                return holder4;
            case 8:
                short[] holder5 = new short[size];
                for (int i6 = 0; i6 < size; i6++) {
                    byte[] val5 = context.readDataValue();
                    if (val5 != null) {
                        holder5[i6] = NUMBER.toShort(val5);
                    }
                }
                return holder5;
            default:
                throw ((SQLException) DatabaseError.createSqlException(23).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int style, Map map) throws SQLException {
        return toNumericObject(bytes, style, map);
    }

    static Object toNumericObject(byte[] bytes, int style, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        switch (style) {
            case 1:
                return new NUMBER(bytes);
            case 2:
                return NUMBER.toBigDecimal(bytes);
            case 3:
                return bytes;
            default:
                throw ((SQLException) DatabaseError.createSqlException(23).fillInStackTrace());
        }
    }

    public static NUMBER toNUMBER(Object value, OracleConnection conn) throws SQLException {
        NUMBER datum = null;
        if (value != null) {
            try {
                if (value instanceof NUMBER) {
                    datum = (NUMBER) value;
                } else {
                    datum = new NUMBER(value);
                }
            } catch (SQLException e) {
                throw ((SQLException) DatabaseError.createSqlException(59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    public static Datum[] toNUMBERArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if ((obj instanceof Object[]) && !(obj instanceof char[][])) {
                Object[] objArray = (Object[]) obj;
                int length = (int) (count == -1 ? objArray.length : Math.min((objArray.length - beginIdx) + 1, count));
                datumArray = new Datum[length];
                for (int i = 0; i < length; i++) {
                    datumArray[i] = toNUMBER(objArray[(((int) beginIdx) + i) - 1], conn);
                }
            } else {
                datumArray = cArrayToNUMBERArray(obj, conn, beginIdx, count);
            }
        }
        return datumArray;
    }

    static Datum[] cArrayToNUMBERArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof short[]) {
                short[] parray = (short[]) obj;
                int len = (int) (count == -1 ? parray.length : Math.min((parray.length - beginIdx) + 1, count));
                datumArray = new Datum[len];
                for (int i = 0; i < len; i++) {
                    datumArray[i] = new NUMBER(parray[(((int) beginIdx) + i) - 1]);
                }
            } else if (obj instanceof int[]) {
                int[] parray2 = (int[]) obj;
                int len2 = (int) (count == -1 ? parray2.length : Math.min((parray2.length - beginIdx) + 1, count));
                datumArray = new Datum[len2];
                for (int i2 = 0; i2 < len2; i2++) {
                    datumArray[i2] = new NUMBER(parray2[(((int) beginIdx) + i2) - 1]);
                }
            } else if (obj instanceof long[]) {
                long[] parray3 = (long[]) obj;
                int len3 = (int) (count == -1 ? parray3.length : Math.min((parray3.length - beginIdx) + 1, count));
                datumArray = new Datum[len3];
                for (int i3 = 0; i3 < len3; i3++) {
                    datumArray[i3] = new NUMBER(parray3[(((int) beginIdx) + i3) - 1]);
                }
            } else if (obj instanceof float[]) {
                float[] parray4 = (float[]) obj;
                int len4 = (int) (count == -1 ? parray4.length : Math.min((parray4.length - beginIdx) + 1, count));
                datumArray = new Datum[len4];
                for (int i4 = 0; i4 < len4; i4++) {
                    datumArray[i4] = new NUMBER(parray4[(((int) beginIdx) + i4) - 1]);
                }
            } else if (obj instanceof double[]) {
                double[] parray5 = (double[]) obj;
                int len5 = (int) (count == -1 ? parray5.length : Math.min((parray5.length - beginIdx) + 1, count));
                datumArray = new Datum[len5];
                for (int i5 = 0; i5 < len5; i5++) {
                    datumArray[i5] = new NUMBER(parray5[(((int) beginIdx) + i5) - 1]);
                }
            } else if (obj instanceof boolean[]) {
                boolean[] parray6 = (boolean[]) obj;
                int len6 = (int) (count == -1 ? parray6.length : Math.min((parray6.length - beginIdx) + 1, count));
                datumArray = new Datum[len6];
                for (int i6 = 0; i6 < len6; i6++) {
                    datumArray[i6] = new NUMBER(Boolean.valueOf(parray6[(((int) beginIdx) + i6) - 1]));
                }
            } else if (obj instanceof char[][]) {
                char[][] parray7 = (char[][]) obj;
                int len7 = (int) (count == -1 ? parray7.length : Math.min((parray7.length - beginIdx) + 1, count));
                datumArray = new Datum[len7];
                for (int i7 = 0; i7 < len7; i7++) {
                    datumArray[i7] = new NUMBER(new String(parray7[(((int) beginIdx) + i7) - 1]));
                }
            } else {
                throw ((SQLException) DatabaseError.createSqlException(59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getPrecision() {
        return this.precision;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getScale() {
        return this.scale;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.scale);
        out.writeInt(this.precision);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.scale = in.readInt();
        this.precision = in.readInt();
    }
}
