package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleStruct;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.Datum;
import oracle.sql.OPAQUE;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeUPT.class */
public class OracleTypeUPT extends OracleTypeADT implements Serializable {
    static final long serialVersionUID = -1994358478872378695L;
    static final byte KOPU_UPT_ADT = -6;
    static final byte KOPU_UPT_COLL = -5;
    static final byte KOPU_UPT_REFCUR = 102;
    static final byte KOTTCOPQ = 58;
    byte uptCode;
    OracleNamedType realType;

    protected OracleTypeUPT() {
        this.uptCode = (byte) 0;
        this.realType = null;
    }

    public OracleTypeUPT(String sql_name, OracleConnection conn) throws SQLException {
        super(sql_name, conn);
        this.uptCode = (byte) 0;
        this.realType = null;
    }

    public OracleTypeUPT(OracleTypeADT parent, int id, OracleConnection conn) throws SQLException {
        super(parent, id, conn);
        this.uptCode = (byte) 0;
        this.realType = null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        if (value != null) {
            return this.realType.toDatum(value, conn);
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object value, OracleConnection conn, long beginIdx, int count) throws SQLException {
        if (value != null) {
            return this.realType.toDatumArray(value, conn, beginIdx, count);
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public int getTypeCode() throws SQLException {
        switch (this.uptCode) {
            case -6:
                return this.realType.getTypeCode();
            case -5:
                return 2003;
            case 58:
                return OracleTypes.OPAQUE;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid type code").fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(OracleType anOracleType) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(StructDescriptor aStructDescriptor) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isObjectType() {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        long offsetPosition = tdsReader.readLong();
        this.uptCode = tdsReader.readByte();
        tdsReader.addNormalPatch(offsetPosition, this.uptCode, this);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext ctx, Datum data) throws SQLException {
        int realLen;
        int imglen;
        if (data == null) {
            imglen = 0 + ctx.writeElementNull();
        } else {
            int lenOffset = ctx.offset();
            int imglen2 = 0 + ctx.writeLength(PickleContext.KOPI20_LN_MAXV + 1);
            if (this.uptCode == -6 && !((OracleTypeADT) this.realType).isFinalType()) {
                if (data instanceof STRUCT) {
                    realLen = ((STRUCT) data).getDescriptor().getOracleTypeADT().pickle81(ctx, data);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "invalid upt state").fillInStackTrace());
                }
            } else {
                realLen = this.realType.pickle81(ctx, data);
            }
            imglen = imglen2 + realLen;
            ctx.patchImageLen(lenOffset, realLen);
        }
        return imglen;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected Object unpickle81rec(PickleContext context, int type, Map map) throws SQLException {
        byte lengthbyte = context.readByte();
        if (PickleContext.isElementNull(lengthbyte)) {
            return null;
        }
        if (type == 9) {
            context.skipBytes(context.readRestOfLength(lengthbyte));
            return null;
        }
        context.skipRestOfLength(lengthbyte);
        return unpickle81UPT(context, type, map);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected Object unpickle81rec(PickleContext context, byte length, int type, Map map) throws SQLException {
        long len = context.readRestOfLength(length);
        if (type == 9) {
            context.skipBytes((int) len);
            return null;
        }
        return unpickle81UPT(context, type, map);
    }

    private Object unpickle81UPT(PickleContext context, int style, Map map) throws SQLException {
        switch (this.uptCode) {
            case -6:
                switch (style) {
                    case 1:
                        return ((OracleTypeADT) this.realType).unpickle81(context, (OracleStruct) null, 3, style, map);
                    case 2:
                        OracleStruct result = ((OracleTypeADT) this.realType).unpickle81(context, (OracleStruct) null, 1, style, map);
                        return result == null ? result : result.toJdbc(map);
                    case 9:
                        return ((OracleTypeADT) this.realType).unpickle81(context, (OracleStruct) null, 9, 1, map);
                    default:
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
                }
            case -5:
                return ((OracleTypeCOLLECTION) this.realType).unpickle81(context, (ARRAY) null, style == 9 ? style : 3, style, map);
            case 58:
                switch (style) {
                    case 1:
                    case 9:
                        return ((OracleTypeOPAQUE) this.realType).unpickle81(context, (OPAQUE) null, style, map);
                    case 2:
                        OPAQUE result2 = ((OracleTypeOPAQUE) this.realType).unpickle81(context, (OPAQUE) null, style, map);
                        return result2 == null ? result2 : result2.toJdbc(map);
                    default:
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
                }
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Unrecognized UPT code").fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected Datum unpickle81datumAsNull(PickleContext context, byte len_flags, byte immemb) throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    StructDescriptor createStructDescriptor() throws SQLException {
        StructDescriptor desc;
        if (this.sqlName == null) {
            desc = new StructDescriptor((OracleTypeADT) this.realType, this.connection);
        } else {
            desc = StructDescriptor.createDescriptor(this.sqlName, this.connection);
        }
        return desc;
    }

    ArrayDescriptor createArrayDescriptor() throws SQLException {
        ArrayDescriptor desc;
        if (this.sqlName == null) {
            desc = new ArrayDescriptor((OracleTypeCOLLECTION) this.realType, this.connection);
        } else {
            desc = ArrayDescriptor.createDescriptor(this.sqlName, this.connection);
        }
        return desc;
    }

    public OracleType getRealType() throws SQLException {
        return this.realType;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public int getNumAttrs() throws SQLException {
        return ((OracleTypeADT) this.realType).getNumAttrs();
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public OracleType getAttrTypeAt(int idx) throws SQLException {
        return ((OracleTypeADT) this.realType).getAttrTypeAt(idx);
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeByte(this.uptCode);
        out.writeObject(this.realType);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.uptCode = in.readByte();
        this.realType = (OracleNamedType) in.readObject();
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
        this.realType.setConnection(conn);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void initChildNamesRecursively(Map typesMap) throws SQLException {
        if (this.realType != null) {
            this.realType.setSqlName(this.sqlName);
            this.realType.initChildNamesRecursively(typesMap);
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void initMetadataRecursively() throws SQLException {
        initMetadata(this.connection);
        if (this.realType != null) {
            this.realType.initMetadataRecursively();
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void cacheDescriptor() throws SQLException {
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        for (int i = 0; i < indent; i++) {
            pw.print("  ");
        }
        pw.println("<OracleTypeUPT sqlName=\"" + this.sqlName + "\"  toid=\"" + this.toid + "\" >");
        if (this.realType != null) {
            this.realType.printXML(pw, indent + 1, fetchAllMetaDataAsNeeded);
        }
        for (int i2 = 0; i2 < indent; i2++) {
            pw.print("  ");
        }
        pw.println("</OracleTypeUPT>");
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent) throws SQLException {
        printXML(pw, indent, false);
    }
}
