package oracle.jdbc.oracore;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/Util.class */
public class Util {
    private static int[] ldsRoundTable = {0, 1, 0, 2, 0, 0, 0, 3, 0};

    static void checkNextByte(InputStream in, byte value) throws SQLException {
        try {
            if (in.read() != value) {
                throw ((SQLException) DatabaseError.createSqlException(47, "parseTDS").fillInStackTrace());
            }
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    public static int[] toJavaUnsignedBytes(byte[] array) {
        int[] result = new int[array.length];
        for (int i = 0; i < array.length; i++) {
            result[i] = array[i] & 255;
        }
        return result;
    }

    static byte[] readBytes(InputStream in, int length) throws SQLException, IOException {
        byte[] array = new byte[length];
        try {
            int length_read = in.read(array);
            if (length_read != length) {
                byte[] final_bytes = new byte[length_read];
                System.arraycopy(array, 0, final_bytes, 0, length_read);
                return final_bytes;
            }
            return array;
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static void writeBytes(OutputStream out, byte[] array) throws SQLException, IOException {
        try {
            out.write(array);
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static void skipBytes(InputStream in, int byte_num) throws SQLException, IOException {
        try {
            in.skip(byte_num);
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static long readLong(InputStream in) throws SQLException, IOException {
        byte[] bytes = new byte[4];
        try {
            in.read(bytes);
            return ((((((bytes[0] & 255) * 256) + (bytes[1] & 255)) * 256) + (bytes[2] & 255)) * 256) + (bytes[3] & 255);
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static short readShort(InputStream in) throws SQLException, IOException {
        byte[] bytes = new byte[2];
        try {
            in.read(bytes);
            return (short) (((bytes[0] & 255) * 256) + (bytes[1] & 255));
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static byte readByte(InputStream in) throws SQLException {
        try {
            return (byte) in.read();
        } catch (IOException ex) {
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    static byte fdoGetSize(byte[] FDO, int mapping_offset) {
        byte value = fdoGetEntry(FDO, mapping_offset);
        return (byte) ((value >> 3) & 31);
    }

    static byte fdoGetAlign(byte[] FDO, int mapping_offset) {
        byte value = fdoGetEntry(FDO, mapping_offset);
        return (byte) (value & 7);
    }

    static int ldsRound(int size, int alignvalue) {
        int sval = ldsRoundTable[alignvalue];
        return ((size >> sval) + 1) << sval;
    }

    private static byte fdoGetEntry(byte[] FDO, int mapping_offset) {
        short fdo_5 = getUnsignedByte(FDO[5]);
        byte value = FDO[6 + fdo_5 + mapping_offset];
        return value;
    }

    public static short getUnsignedByte(byte b) {
        return (short) (b & 255);
    }

    public static byte[] serializeObject(Object obj) throws IOException {
        if (obj == null) {
            return null;
        }
        ByteArrayOutputStream ostream = new ByteArrayOutputStream();
        ObjectOutputStream p = new ObjectOutputStream(ostream);
        p.writeObject(obj);
        p.flush();
        return ostream.toByteArray();
    }

    public static Object deserializeObject(byte[] bytes) throws IOException, ClassNotFoundException {
        if (bytes == null) {
            return null;
        }
        InputStream istream = new ByteArrayInputStream(bytes);
        return new ObjectInputStream(istream).readObject();
    }

    public static void printByteArray(byte[] x) {
        System.out.println("DONT CALL THIS -- oracle.jdbc.oracore.Util.printByteArray");
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
