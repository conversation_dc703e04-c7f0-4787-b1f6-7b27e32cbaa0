package oracle.jdbc.oracore;

import java.math.BigDecimal;
import oracle.sql.Datum;

/* compiled from: OracleTypeSINT32.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/SINT32.class */
class SINT32 extends Datum {
    boolean isBigEndian;

    private SINT32() {
    }

    private SINT32(byte[] bytes, boolean bigEndian) {
        setBytes(bytes);
        this.isBigEndian = bigEndian;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public boolean isConvertibleTo(Class c) {
        return false;
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object toJdbc() {
        return BigDecimal.valueOf(bytesToInt(shareBytes(), this.isBigEndian));
    }

    @Override // oracle.sql.Datum, oracle.jdbc.internal.OracleDatumWithConnection
    public Object makeJdbcArray(int count) {
        return new SINT32[count];
    }

    static SINT32[] createArray(int count) {
        return new SINT32[count];
    }

    static int bytesToInt(byte[] bytes, boolean isBigEndian) {
        int out = 0;
        int count = isBigEndian ? 0 : bytes.length - 1;
        for (byte b : bytes) {
            out |= (b & 255) << (count * 8);
            count += isBigEndian ? 1 : -1;
        }
        return out;
    }

    static SINT32 valueOf(int intValue, boolean isBigEndian) {
        byte[] bytes = new byte[4];
        int count = isBigEndian ? 0 : bytes.length - 1;
        for (int i = 0; i < 4; i++) {
            bytes[i] = (byte) ((intValue >> (count * 8)) & 255);
            count += isBigEndian ? 1 : -1;
        }
        return new SINT32(bytes, isBigEndian);
    }
}
