package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleRef;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.REF;
import oracle.sql.StructDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeREF.class */
public class OracleTypeREF extends OracleNamedType implements Serializable {
    private static final String CLASS_NAME = OracleTypeREF.class.getName();
    static final long serialVersionUID = 3186448715463064573L;

    protected OracleTypeREF() {
    }

    public OracleTypeREF(String sql_name, OracleConnection conn) throws SQLException {
        super(sql_name, conn);
    }

    public OracleTypeREF(OracleTypeADT parent, int idx, OracleConnection conn) {
        super(parent, idx, conn);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        if (value != null) {
            if ((value instanceof REF) || (value instanceof OracleRef)) {
                datum = (Datum) value;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 2006;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] val, int style, Map map) throws SQLException {
        if (val == null || val.length == 0) {
            return null;
        }
        if (style == 1 || style == 2) {
            StructDescriptor desc = createStructDescriptor();
            return new REF(desc, this.connection, val);
        }
        if (style == 3) {
            return val;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, val).fillInStackTrace());
    }

    StructDescriptor createStructDescriptor() throws SQLException {
        if (this.descriptor == null) {
            if (this.sqlName == null && getFullName(false) == null) {
                OracleTypeADT otype = new OracleTypeADT(getParent(), getOrder(), this.connection);
                this.descriptor = new StructDescriptor(otype, this.connection);
            } else {
                this.descriptor = StructDescriptor.createDescriptor(this.sqlName, this.connection);
            }
        }
        return (StructDescriptor) this.descriptor;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "writeObject", "Invoked", null, null);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "readObject", "Invoked", null, null);
    }
}
