package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleBlob;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.BLOB;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeBLOB.class */
public class OracleTypeBLOB extends OracleType implements Serializable {
    static final long serialVersionUID = -2311211431562030662L;
    static int fixedDataSize = 86;
    transient OracleConnection connection;

    protected OracleTypeBLOB() {
    }

    public OracleTypeBLOB(OracleConnection conn) {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        if (value != null) {
            if ((value instanceof BLOB) || (value instanceof OracleBlob)) {
                datum = (Datum) value;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return OracleTypes.BLOB;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int style, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        switch (style) {
            case 1:
            case 2:
                return this.connection.createBlobWithUnpickledBytes(bytes);
            case 3:
                return bytes;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
        }
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
