package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMP;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeDATE.class */
public class OracleTypeDATE extends OracleType implements Serializable {
    static final long serialVersionUID = -5858803341118747965L;

    public OracleTypeDATE() {
    }

    public OracleTypeDATE(int typecode) {
        super(typecode);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        DATE datum = null;
        if (value != null) {
            try {
                if (value instanceof DATE) {
                    datum = (DATE) value;
                } else if (value instanceof TIMESTAMP) {
                    datum = new DATE(((TIMESTAMP) value).timestampValue());
                } else {
                    datum = new DATE(value);
                }
            } catch (SQLException e) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof char[][]) {
                char[][] strArray = (char[][]) obj;
                int length = (int) (count == -1 ? strArray.length : Math.min((strArray.length - beginIdx) + 1, count));
                datumArray = new Datum[length];
                for (int i = 0; i < length; i++) {
                    datumArray[i] = toDatum(new String(strArray[(((int) beginIdx) + i) - 1]), conn);
                }
            } else {
                if (obj instanceof Object[]) {
                    return super.toDatumArray(obj, conn, beginIdx, count);
                }
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 91;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        if (type == 1) {
            return new DATE(bytes);
        }
        if (type == 2) {
            return DATE.toTimestamp(bytes);
        }
        if (type == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }
}
