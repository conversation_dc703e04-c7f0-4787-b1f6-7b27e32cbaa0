package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.JavaToJavaConverter;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.CHAR;
import oracle.sql.CLOB;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.NUMBER;
import oracle.sql.RAW;
import oracle.sql.ROWID;
import oracle.sql.StructDescriptor;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleType.class */
public abstract class OracleType implements Serializable, Diagnosable {
    static final long serialVersionUID = -4124152314660261528L;
    private static final String CLASS_NAME = OracleType.class.getName();
    public static final int STYLE_ARRAY_LENGTH = 0;
    public static final int STYLE_DATUM = 1;
    public static final int STYLE_JAVA = 2;
    public static final int STYLE_RAWBYTE = 3;
    public static final int STYLE_INT = 4;
    public static final int STYLE_DOUBLE = 5;
    public static final int STYLE_FLOAT = 6;
    public static final int STYLE_LONG = 7;
    public static final int STYLE_SHORT = 8;
    public static final int STYLE_SKIP = 9;
    static final int FORMAT_ADT_ATTR = 1;
    static final int FORMAT_COLL_ELEM = 2;
    static final int FORMAT_COLL_ELEM_NO_INDICATOR = 3;
    static final int SQLCS_IMPLICIT = 1;
    static final int SQLCS_NCHAR = 2;
    static final int SQLCS_EXPLICIT = 3;
    static final int SQLCS_FLEXIBLE = 4;
    static final int SQLCS_LIT_NULL = 5;
    int typeCode;
    int dbTypeCode;
    boolean metaDataInitialized;

    public abstract Datum toDatum(Object obj, OracleConnection oracleConnection) throws SQLException;

    public OracleType() {
        this.metaDataInitialized = false;
    }

    public OracleType(int typecode) {
        this();
        this.typeCode = typecode;
    }

    public boolean isInHierarchyOf(OracleType anOracleType) throws SQLException {
        return false;
    }

    public boolean isInHierarchyOf(StructDescriptor aStructDescriptor) throws SQLException {
        return false;
    }

    public boolean isObjectType() {
        return false;
    }

    public TypeDescriptor getTypeDescriptor() {
        return null;
    }

    private Class getOracleSqlClassType() throws SQLException {
        Class oracleSqlClassType;
        int oracleTypeCode = getTypeCode();
        switch (oracleTypeCode) {
            case OracleTypes.INTERVALDS /* -104 */:
                oracleSqlClassType = INTERVALDS.class;
                break;
            case OracleTypes.INTERVALYM /* -103 */:
                oracleSqlClassType = INTERVALYM.class;
                break;
            case OracleTypes.TIMESTAMPLTZ /* -102 */:
                oracleSqlClassType = TIMESTAMPLTZ.class;
                break;
            case OracleTypes.TIMESTAMPTZ /* -101 */:
                oracleSqlClassType = TIMESTAMPTZ.class;
                break;
            case OracleTypes.NCHAR /* -15 */:
            case OracleTypes.BFILE /* -13 */:
            case OracleTypes.CURSOR /* -10 */:
            case OracleTypes.NVARCHAR /* -9 */:
            case OracleTypes.BIT /* -7 */:
            case -5:
            case OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -1:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 12:
            case 92:
            case OracleTypes.FIXED_CHAR /* 999 */:
            case 2002:
            case 2003:
            case OracleTypes.BLOB /* 2004 */:
            case 2006:
            case OracleTypes.OPAQUE /* 2007 */:
            case 2008:
            case 2009:
            case OracleTypes.NCLOB /* 2011 */:
            case OracleTypes.REF_CURSOR /* 2012 */:
            default:
                oracleSqlClassType = null;
                break;
            case OracleTypes.ROWID /* -8 */:
                oracleSqlClassType = ROWID.class;
                break;
            case -2:
                oracleSqlClassType = RAW.class;
                break;
            case 1:
                oracleSqlClassType = CHAR.class;
                break;
            case 2:
            case 3:
                oracleSqlClassType = NUMBER.class;
                break;
            case 91:
                oracleSqlClassType = DATE.class;
                break;
            case 93:
                oracleSqlClassType = TIMESTAMP.class;
                break;
            case 100:
                oracleSqlClassType = BINARY_FLOAT.class;
                break;
            case 101:
                oracleSqlClassType = BINARY_DOUBLE.class;
                break;
            case OracleTypes.CLOB /* 2005 */:
                oracleSqlClassType = CLOB.class;
                break;
        }
        return oracleSqlClassType;
    }

    public Datum toDatumInternal(Object value, OracleConnection conn) throws SQLException {
        return toDatum(value, conn);
    }

    public Datum[] toDatumArrayInternal(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        return toDatumArray(obj, conn, beginIdx, count);
    }

    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Class oracleSqlClass;
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof Object[]) {
                Object[] objArray = (Object[]) obj;
                int length = (int) (count == -1 ? objArray.length : Math.min((objArray.length - beginIdx) + 1, count));
                datumArray = new Datum[length];
                for (int i = 0; i < length; i++) {
                    Object value = objArray[(((int) beginIdx) + i) - 1];
                    if (conn != null && (conn instanceof oracle.jdbc.driver.OracleConnection) && value != null && (oracleSqlClass = getOracleSqlClassType()) != null) {
                        try {
                            value = JavaToJavaConverter.convert(value, oracleSqlClass, (oracle.jdbc.driver.OracleConnection) conn, null, null);
                        } catch (SQLException e) {
                        }
                    }
                    datumArray[i] = toDatum(value, conn);
                }
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    public void setTypeCode(int code) {
        this.typeCode = code;
    }

    public int getTypeCode() throws SQLException {
        return this.typeCode;
    }

    public void setDBTypeCode(int code) {
        this.dbTypeCode = code;
    }

    public int getDBTypeCode() throws SQLException {
        return this.dbTypeCode;
    }

    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
    }

    protected Object unpickle81rec(PickleContext context, int type, Map map) throws SQLException {
        if (type == 9) {
            context.skipDataValue();
            return null;
        }
        byte[] val = context.readDataValue();
        return toObject(val, type, map);
    }

    protected Object unpickle81rec(PickleContext context, byte byte1, int type, Map map) throws SQLException {
        if (type == 9) {
            context.skipDataValue();
            return null;
        }
        byte[] val = context.readDataValue(byte1);
        return toObject(val, type, map);
    }

    protected Datum unpickle81datumAsNull(PickleContext context, byte len_flags, byte immemb) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
    }

    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        return null;
    }

    protected int pickle81(PickleContext ctx, Datum data) throws SQLException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "pickle81", "data.getLength()={0}", (String) null, (String) null, Long.valueOf(data.getLength()));
        int len = ctx.writeLength((int) data.getLength());
        return len + ctx.writeData(data.shareBytes());
    }

    void writeSerializedFields(ObjectOutputStream out) throws IOException {
        writeObject(out);
    }

    void readSerializedFields(ObjectInputStream in) throws IOException, ClassNotFoundException {
        readObject(in);
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(0);
        out.writeInt(0);
        out.writeInt(0);
        out.writeInt(0);
        out.writeInt(this.typeCode);
        out.writeInt(this.dbTypeCode);
        out.writeBoolean(this.metaDataInitialized);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.readInt();
        in.readInt();
        in.readInt();
        in.readInt();
        this.typeCode = in.readInt();
        this.dbTypeCode = in.readInt();
        this.metaDataInitialized = in.readBoolean();
    }

    public void setConnection(OracleConnection conn) throws SQLException {
    }

    public boolean isNCHAR() throws SQLException {
        return false;
    }

    public int getPrecision() throws SQLException {
        return 0;
    }

    public int getScale() throws SQLException {
        return 0;
    }

    public void initMetadataRecursively() throws SQLException {
    }

    public void initNamesRecursively() throws SQLException {
    }

    public void initChildNamesRecursively(Map typesMap) throws SQLException {
    }

    public void cacheDescriptor() throws SQLException {
    }

    public void setNames(String schemaName, String typeName) throws SQLException {
    }

    public String toXMLString() throws SQLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        printXMLHeader(pw);
        printXML(pw, 0);
        return sw.getBuffer().substring(0);
    }

    public void printXML(PrintStream ps) throws SQLException {
        PrintWriter pw = new PrintWriter((OutputStream) ps, true);
        printXMLHeader(pw);
        printXML(pw, 0);
    }

    void printXMLHeader(PrintWriter pw) throws SQLException {
        pw.println("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>");
    }

    public void printXML(PrintWriter pw, int indent) throws SQLException {
        printXML(pw, indent, false);
    }

    public void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        for (int i = 0; i < indent; i++) {
            pw.print("  ");
        }
        pw.println("<OracleType typecode=\"" + this.typeCode + "\" />");
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }
}
