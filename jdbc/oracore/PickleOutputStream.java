package oracle.jdbc.oracore;

import java.io.ByteArrayOutputStream;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/PickleOutputStream.class */
public class PickleOutputStream extends ByteArrayOutputStream implements Monitor {
    private final Monitor.CloseableLock monitorLock;

    public PickleOutputStream() {
        this.monitorLock = Monitor.newDefaultLock();
    }

    public PickleOutputStream(int size) {
        super(size);
        this.monitorLock = Monitor.newDefaultLock();
    }

    public int offset() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int i = this.count;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void overwrite(int beginOff, byte[] b, int off, int len) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (off >= 0) {
                if (off <= b.length && len >= 0 && off + len <= b.length && off + len >= 0 && beginOff + len <= this.buf.length) {
                    if (len != 0) {
                        for (int i = 0; i < len; i++) {
                            this.buf[beginOff + i] = b[off + i];
                        }
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                    return;
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                    return;
                                }
                            }
                            lock.close();
                            return;
                        }
                        return;
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
            }
            throw new IndexOutOfBoundsException();
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }
}
