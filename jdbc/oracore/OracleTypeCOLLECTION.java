package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleCallableStatement;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.AssociativeArrayEntry;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleArray;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.Datum;
import oracle.sql.SQLName;
import oracle.sql.StructDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeCOLLECTION.class */
public class OracleTypeCOLLECTION extends OracleTypeADT implements Serializable {
    static final long serialVersionUID = -7279638692691669378L;
    int userCode;
    long maxSize;
    OracleType elementType;
    private int collectionFlag;
    static final int CURRENT_USER_OBJECT = 0;
    static final int CURRENT_USER_SYNONYM = 1;
    static final int CURRENT_USER_SYNONYM_10g = 2;
    static final int CURRENT_USER_PUBLIC_SYNONYM = 3;
    static final int CURRENT_USER_PUBLIC_SYNONYM_10g = 4;
    static final int POSSIBLY_OTHER_USER_OBJECT = 5;
    static final int POSSIBLY_OTHER_USER_OBJECT_10g = 6;
    static final int OTHER_USER_OBJECT = 7;
    static final int OTHER_USER_SYNONYM = 8;
    static final int PUBLIC_SYNONYM = 9;
    static final int PUBLIC_SYNONYM_10g = 10;
    static final int BREAK = 11;
    private static final String CLASS_NAME = OracleTypeCOLLECTION.class.getName();
    static final String[] sqlString = {"SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM USER_COLL_TYPES WHERE TYPE_NAME = :1", "DECLARE CURSOR usyn_cur IS SELECT table_name from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; BEGIN SELECT TABLE_NAME BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name; END IF; OPEN ? FOR SELECT  ELEM_TYPE_NAME, ELEM_TYPE_OWNER   FROM USER_COLL_TYPES   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?); END;", "DECLARE CURSOR usyn_cur IS SELECT table_name from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; BEGIN SELECT TABLE_NAME BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name; END IF; OPEN ? FOR SELECT  ELEM_TYPE_NAME, ELEM_TYPE_OWNER   FROM USER_COLL_TYPES   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?); END;", "SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM USER_COLL_TYPES WHERE TYPE_NAME IN (SELECT TABLE_NAME FROM ALL_SYNONYMS START WITH SYNONYM_NAME = :1 AND  OWNER = 'PUBLIC' CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER UNION SELECT :2  FROM DUAL) ", "SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM USER_COLL_TYPES WHERE TYPE_NAME IN (SELECT TABLE_NAME FROM ALL_SYNONYMS START WITH SYNONYM_NAME = :1 AND  OWNER = 'PUBLIC' CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER UNION SELECT :2  FROM DUAL) ", "DECLARE CURSOR usyn_cur IS SELECT table_name from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; BEGIN SELECT TABLE_NAME BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name; END IF; OPEN ? FOR SELECT  ELEM_TYPE_NAME, ELEM_TYPE_OWNER   FROM ALL_COLL_TYPES   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?); END;", "DECLARE CURSOR usyn_cur IS SELECT table_name from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; BEGIN SELECT TABLE_NAME BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name; END IF; OPEN ? FOR SELECT  ELEM_TYPE_NAME, ELEM_TYPE_OWNER   FROM ALL_COLL_TYPES   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?); END;", "SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM ALL_COLL_TYPES WHERE OWNER = :1 AND TYPE_NAME = :2", "SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM ALL_COLL_TYPES WHERE OWNER = (SELECT DISTINCT TABLE_OWNER FROM ALL_SYNONYMS WHERE SYNONYM_NAME=:1) AND TYPE_NAME = (SELECT DISTINCT TABLE_NAME FROM ALL_SYNONYMS WHERE SYNONYM_NAME=:2) ", "DECLARE   the_owner VARCHAR2(100);   the_type  VARCHAR2(100); BEGIN  SELECT TABLE_NAME, TABLE_OWNER INTO THE_TYPE, THE_OWNER  FROM ALL_SYNONYMS  WHERE TABLE_NAME IN (SELECT TYPE_NAME FROM ALL_TYPES)  START WITH SYNONYM_NAME = :1 AND OWNER = 'PUBLIC'  CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER; OPEN :2 FOR SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM ALL_COLL_TYPES  WHERE TYPE_NAME = THE_TYPE and OWNER = THE_OWNER; END;", "DECLARE   the_owner VARCHAR2(100);   the_type  VARCHAR2(100); BEGIN  SELECT TABLE_NAME, TABLE_OWNER INTO THE_TYPE, THE_OWNER  FROM ALL_SYNONYMS  WHERE TABLE_NAME IN (SELECT TYPE_NAME FROM ALL_TYPES)  START WITH SYNONYM_NAME = :1 AND OWNER = 'PUBLIC'  CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER; OPEN :2 FOR SELECT ELEM_TYPE_NAME, ELEM_TYPE_OWNER FROM ALL_COLL_TYPES  WHERE TYPE_NAME = THE_TYPE and OWNER = THE_OWNER; END;"};

    public OracleTypeCOLLECTION(String sql_name, OracleConnection conn) throws SQLException {
        super(sql_name, conn);
        this.userCode = 0;
        this.maxSize = 0L;
        this.elementType = null;
        this.collectionFlag = 0;
    }

    public OracleTypeCOLLECTION(OracleTypeADT parent, int idx, OracleConnection conn) throws SQLException {
        super(parent, idx, conn);
        this.userCode = 0;
        this.maxSize = 0L;
        this.elementType = null;
        this.collectionFlag = 0;
    }

    public OracleTypeCOLLECTION(SQLName sqlName, byte[] typoid, int version, byte[] tds, OracleConnection conn) throws SQLException {
        super(sqlName, typoid, version, tds, conn);
        this.userCode = 0;
        this.maxSize = 0L;
        this.elementType = null;
        this.collectionFlag = 0;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        if (value != null) {
            if (value instanceof ARRAY) {
                return (Datum) value;
            }
            ArrayDescriptor desc = createArrayDescriptor();
            return new ARRAY(desc, this.connection, value);
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 2003;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(OracleType anOracleType) throws SQLException {
        if (anOracleType == null) {
            return false;
        }
        if (anOracleType == this) {
            return true;
        }
        if (anOracleType.getClass() != getClass()) {
            return false;
        }
        return anOracleType.getTypeDescriptor().getName().equals(this.descriptor.getName());
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(StructDescriptor aStructDescriptor) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isObjectType() {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        long elementPos = tdsReader.readLong();
        this.maxSize = tdsReader.readLong();
        this.userCode = tdsReader.readByte();
        tdsReader.addSimplePatch(elementPos, this);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType
    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, int type, Map objmap) throws SQLException {
        return unlinearize(pickled_bytes, offset, container, 1L, -1, type, objmap);
    }

    @Override // oracle.jdbc.oracore.OracleNamedType
    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, long idx, int cnt, int style, Map objmap) throws SQLException {
        Datum ret;
        OracleConnection mc = getConnection();
        if (mc == null) {
            ret = unlinearizeInternal(pickled_bytes, offset, container, idx, cnt, style, objmap);
        } else {
            Monitor.CloseableLock lock = mc.acquireCloseableLock();
            Throwable th = null;
            try {
                ret = unlinearizeInternal(pickled_bytes, offset, container, idx, cnt, style, objmap);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        return ret;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public Datum unlinearizeInternal(byte[] pickled_bytes, long offset, Datum datum, long idx, int cnt, int style, Map objmap) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (pickled_bytes != null) {
            try {
                try {
                    PickleContext context = new PickleContext(pickled_bytes, offset);
                    Datum datum2 = (Datum) unpickle81(context, (OracleArray) datum, idx, cnt, 1, style, objmap);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return datum2;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                }
            } else {
                lock.close();
            }
        }
        return null;
    }

    public boolean isInlineImage(byte[] pickled_bytes, int offset) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (pickled_bytes == null) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return false;
        }
        try {
            try {
                if (PickleContext.isCollectionImage_pctx(pickled_bytes[offset])) {
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (!PickleContext.isDegenerateImage_pctx(pickled_bytes[offset])) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image is not a collection image").fillInStackTrace());
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext ctx, Datum datum) throws SQLException {
        int imglen;
        int i;
        int iPickle81;
        OracleArray data = (OracleArray) datum;
        boolean inline = data.hasDataSeg();
        int lenOffset = ctx.offset() + 2;
        if (inline) {
            if (!this.metaDataInitialized) {
                copy_properties((OracleTypeCOLLECTION) data.getDescriptor().getPickler());
            }
            Datum[] dataValues = data.getOracleArray();
            if (this.userCode == 3 && dataValues.length > this.maxSize) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 71, (Object) null).fillInStackTrace());
            }
            ctx.setCollectionUserCode(this.userCode);
            imglen = 0 + ctx.writeCollImageHeader(dataValues.length, this.typeVersion);
            for (int ctr = 0; ctr < dataValues.length; ctr++) {
                if (this.userCode == 1) {
                    AssociativeArrayEntry<Integer, Datum> entry = (AssociativeArrayEntry) dataValues[ctr];
                    Integer index = (Integer) entry.getKey();
                    int imglen2 = imglen + ctx.writeUB4(index.intValue());
                    Datum value = (Datum) ((AssociativeArrayEntry) dataValues[ctr]).getValue();
                    if (value == null) {
                        i = imglen2;
                        iPickle81 = ctx.writeElementNull();
                    } else {
                        i = imglen2;
                        iPickle81 = this.elementType.pickle81(ctx, value);
                    }
                } else if (dataValues[ctr] == null) {
                    i = imglen;
                    iPickle81 = ctx.writeElementNull();
                } else {
                    i = imglen;
                    iPickle81 = this.elementType.pickle81(ctx, dataValues[ctr]);
                }
                imglen = i + iPickle81;
                int idx = ctr;
                debugp(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "pickle81", "idx={0}, bytes={1}", null, null, () -> {
                    Object[] objArr = new Object[2];
                    objArr[0] = Integer.valueOf(idx);
                    objArr[1] = dataValues[idx] == null ? null : Parameter.arg(Format.Style.BYTE_ARRAY_CLONE, dataValues[idx].shareBytes(), new long[0]);
                    return objArr;
                });
            }
        } else {
            imglen = 0 + ctx.writeCollImageHeader(data.getLocator());
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "pickle81", "imglen={0}", (String) null, (String) null, Integer.valueOf(imglen));
        ctx.patchImageLen(lenOffset, imglen);
        return imglen;
    }

    OracleArray unpickle81(PickleContext context, OracleArray container, int style, int elemStyle, Map elemMap) throws SQLException {
        return unpickle81(context, container, 1L, -1, style, elemStyle, elemMap);
    }

    OracleArray unpickle81(PickleContext context, OracleArray container, long beginIdx, int count, int style, int elemStyle, Map elemMap) throws SQLException {
        OracleArray coll_obj = container;
        if (coll_obj == null) {
            ArrayDescriptor desc = createArrayDescriptor();
            coll_obj = new ARRAY(desc, (byte[]) null, this.connection);
        }
        if (unpickle81ImgHeader(context, coll_obj, style, elemStyle)) {
            this.collectionFlag = context.readByte();
            context.setCollectionUserCode(this.collectionFlag);
            if (beginIdx == 1 && count == -1) {
                unpickle81ImgBody(context, coll_obj, elemStyle, elemMap);
            } else {
                unpickle81ImgBody(context, coll_obj, beginIdx, count, elemStyle, elemMap);
            }
        }
        return coll_obj;
    }

    boolean unpickle81ImgHeader(PickleContext context, OracleArray container, int style, int elemStyle) throws SQLException {
        if (style == 3) {
            container.setImage(context.image(), context.absoluteOffset(), 0L);
        }
        byte flags = context.readByte();
        if (!PickleContext.is81format(flags)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image is not in 8.1 format").fillInStackTrace());
        }
        if (!PickleContext.hasPrefix(flags)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image has no prefix segment").fillInStackTrace());
        }
        if (!PickleContext.isCollectionImage_pctx(flags) && !PickleContext.isDegenerateImage_pctx(flags)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image is not a collection image").fillInStackTrace());
        }
        context.readByte();
        if (style == 9) {
            context.skipBytes(context.readLength(true) - 2);
            return false;
        }
        if (style == 3) {
            long length = context.readLength();
            container.setImageLength(length);
            context.skipTo(container.getImageOffset() + length);
            return false;
        }
        context.skipLength();
        int psegLen = context.readLength();
        container.setPrefixFlag(context.readByte());
        if (container.isInline()) {
            context.readDataValue(psegLen - 1);
        } else {
            container.setLocator(context.readDataValue(psegLen - 1));
        }
        return container.isInline();
    }

    void unpickle81ImgBody(PickleContext context, OracleArray container, long beginIdx, int count, int elemStyle, Map elemMap) throws SQLException {
        int coll_len = context.readLength();
        container.setLength(coll_len);
        if (elemStyle == 0) {
            return;
        }
        int length = (int) getAccessLength(coll_len, beginIdx, count);
        boolean cacheAll = ArrayDescriptor.getCacheStyle(container) == 1;
        if (beginIdx > 1 && length > 0) {
            long lastIdx = container.getLastIndex();
            if (lastIdx < beginIdx) {
                if (lastIdx > 0) {
                    context.skipTo(container.getLastOffset());
                } else {
                    lastIdx = 1;
                }
                if (cacheAll) {
                    long j = lastIdx;
                    while (true) {
                        long i = j;
                        if (i >= beginIdx) {
                            break;
                        }
                        container.setIndexOffset(i, context.offset());
                        this.elementType.unpickle81rec(context, 9, null);
                        j = i + 1;
                    }
                } else {
                    long j2 = lastIdx;
                    while (true) {
                        long i2 = j2;
                        if (i2 >= beginIdx) {
                            break;
                        }
                        this.elementType.unpickle81rec(context, 9, null);
                        j2 = i2 + 1;
                    }
                }
            } else if (lastIdx > beginIdx) {
                long offset = container.getOffset(beginIdx);
                if (offset != -1) {
                    context.skipTo(offset);
                } else if (cacheAll) {
                    for (int i3 = 1; i3 < beginIdx; i3++) {
                        container.setIndexOffset(i3, context.offset());
                        this.elementType.unpickle81rec(context, 9, null);
                    }
                } else {
                    for (int i4 = 1; i4 < beginIdx; i4++) {
                        this.elementType.unpickle81rec(context, 9, null);
                    }
                }
            } else {
                context.skipTo(container.getLastOffset());
            }
            container.setLastIndexOffset(beginIdx, context.offset());
        }
        unpickle81ImgBodyElements(context, container, (int) beginIdx, length, elemStyle, elemMap);
    }

    void unpickle81ImgBody(PickleContext context, OracleArray container, int elemStyle, Map elemMap) throws SQLException {
        int length = context.readLength();
        container.setLength(length);
        if (elemStyle == 0) {
            return;
        }
        unpickle81ImgBodyElements(context, container, 1, length, elemStyle, elemMap);
    }

    private void unpickle81ImgBodyElements(PickleContext context, OracleArray container, int beginIdx, int length, int elemStyle, Map elemMap) throws SQLException {
        boolean cacheAll = ArrayDescriptor.getCacheStyle(container) == 1;
        switch (elemStyle) {
            case 1:
                Datum[] datumArray = new Datum[length];
                if (cacheAll) {
                    for (int i = 0; i < length; i++) {
                        container.setIndexOffset(beginIdx + i, context.offset());
                        datumArray[i] = (Datum) this.elementType.unpickle81rec(context, elemStyle, elemMap);
                    }
                } else {
                    for (int i2 = 0; i2 < length; i2++) {
                        datumArray[i2] = (Datum) this.elementType.unpickle81rec(context, elemStyle, elemMap);
                    }
                }
                container.setDatumArray(datumArray);
                break;
            case 2:
                Object[] darray = ArrayDescriptor.makeJavaArray(length, this.elementType.getTypeCode());
                Map<Integer, Object> javaMap = null;
                if (this.userCode == 1) {
                    javaMap = new HashMap<>();
                }
                if (cacheAll) {
                    for (int i3 = 0; i3 < length; i3++) {
                        if (this.collectionFlag == PickleContext.KOPI20_CF_INDX) {
                            Integer index = new Integer((int) context.readUB4());
                            container.setIndexOffset(beginIdx + i3, context.offset());
                            darray[i3] = this.elementType.unpickle81rec(context, elemStyle, elemMap);
                            if (javaMap != null) {
                                javaMap.put(index, darray[i3]);
                            }
                        } else {
                            container.setIndexOffset(beginIdx + i3, context.offset());
                            darray[i3] = this.elementType.unpickle81rec(context, elemStyle, elemMap);
                        }
                    }
                } else {
                    for (int i4 = 0; i4 < length; i4++) {
                        if (this.collectionFlag == PickleContext.KOPI20_CF_INDX) {
                            Integer index2 = new Integer((int) context.readUB4());
                            darray[i4] = this.elementType.unpickle81rec(context, elemStyle, elemMap);
                            if (javaMap != null) {
                                javaMap.put(index2, darray[i4]);
                            }
                        } else {
                            darray[i4] = this.elementType.unpickle81rec(context, elemStyle, elemMap);
                        }
                    }
                }
                container.setObjArray(darray);
                container.setJavaMap(javaMap);
                break;
            case 3:
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "Invalid conversion type " + this.elementType).fillInStackTrace());
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                if ((this.elementType instanceof OracleTypeNUMBER) || (this.elementType instanceof OracleTypeFLOAT)) {
                    container.setObjArray(OracleTypeNUMBER.unpickle81NativeArray(context, 1L, length, elemStyle));
                    break;
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23, "This feature is limited to numeric collection").fillInStackTrace());
                }
                break;
        }
        container.setLastIndexOffset(beginIdx + length, context.offset());
    }

    /* JADX WARN: Finally extract failed */
    private void initCollElemTypeName() throws SQLException {
        if (this.connection == null) {
            return;
        }
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.sqlName == null) {
                getFullName();
            }
            CallableStatement cstmt = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            this.connection.beginNonRequestCalls();
            try {
                int state = this.sqlName.getSchema().equalsIgnoreCase(this.connection.getDefaultSchemaNameForNamedTypes()) ? 0 : 7;
                while (state != 11) {
                    switch (state) {
                        case 0:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try CURRENT_USER_OBJECT, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                            ps.setString(1, this.sqlName.getSimpleName());
                            ps.setFetchSize(1);
                            rs = ps.executeQuery();
                            state = 5;
                            break;
                        case 1:
                            if (this.connection.getVersionNumber() >= 10000) {
                                state = 2;
                            }
                        case 2:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try CURRENT_USER_SYNONYM, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            cstmt = (OracleCallableStatement) this.connection.prepareCall(getSqlHint() + sqlString[state]);
                            cstmt.setString(1, this.sqlName.getSimpleName());
                            cstmt.setString(3, this.sqlName.getSimpleName());
                            cstmt.registerOutParameter(2, -10);
                            cstmt.execute();
                            rs = ((OracleCallableStatement) cstmt).getCursor(2);
                            rs.setFetchSize(1);
                            state = 3;
                            break;
                        case 3:
                            if (this.connection.getVersionNumber() >= 10000) {
                                state = 4;
                            }
                        case 4:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try CURRENT_USER_PUBLIC_SYNONYM, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                            ps.setString(1, this.sqlName.getSimpleName());
                            ps.setString(2, this.sqlName.getSimpleName());
                            ps.setFetchSize(1);
                            rs = ps.executeQuery();
                            state = 8;
                            break;
                        case 5:
                            if (this.connection.getVersionNumber() >= 10000) {
                                state = 6;
                            }
                        case 6:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try POSSIBLY_OTHER_USER_OBJECT, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            cstmt = (OracleCallableStatement) this.connection.prepareCall(getSqlHint() + sqlString[state]);
                            cstmt.setString(1, this.sqlName.getSimpleName());
                            cstmt.setString(3, this.sqlName.getSimpleName());
                            cstmt.registerOutParameter(2, -10);
                            cstmt.execute();
                            rs = ((OracleCallableStatement) cstmt).getCursor(2);
                            rs.setFetchSize(1);
                            state = 1;
                            break;
                        case 7:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try OTHER_USER_OBJECT, bind: schema={0} simpleName={1}, state: {2}", null, null, this.sqlName.getSchema(), this.sqlName.getSimpleName(), Integer.valueOf(state));
                            ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                            ps.setString(1, this.sqlName.getSchema());
                            ps.setString(2, this.sqlName.getSimpleName());
                            ps.setFetchSize(1);
                            rs = ps.executeQuery();
                            state = 8;
                            break;
                        case 8:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try OTHER_USER_SYNONYM, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                            ps.setString(1, this.sqlName.getSimpleName());
                            ps.setString(2, this.sqlName.getSimpleName());
                            ps.setFetchSize(1);
                            rs = ps.executeQuery();
                            state = 9;
                            break;
                        case 9:
                            if (this.connection.getVersionNumber() >= 10000) {
                                state = 10;
                            }
                        case 10:
                            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCollElemTypeName", "try PUBLIC_SYNONYM, bind: {0}, state: {1}", null, null, this.sqlName.getSimpleName(), Integer.valueOf(state));
                            cstmt = this.connection.prepareCall(getSqlHint() + sqlString[state]);
                            cstmt.setString(1, this.sqlName.getSimpleName());
                            cstmt.registerOutParameter(2, -10);
                            cstmt.execute();
                            rs = ((OracleCallableStatement) cstmt).getCursor(2);
                            state = 11;
                            break;
                    }
                    if (rs != null && rs.next()) {
                        if (this.attrTypeNames == null) {
                            this.attrTypeNames = new String[1];
                        }
                        String owner = rs.getString(2);
                        String type = rs.getString(1);
                        this.attrTypeNames[0] = (owner == null || owner.isEmpty()) ? type : SQLName.getTypeName(owner, type);
                        state = 11;
                    } else if (state == 11) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
                    }
                    if (rs != null) {
                        rs.close();
                    }
                    if (ps != null) {
                        ps.close();
                    }
                    if (cstmt != null) {
                        cstmt.close();
                    }
                }
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
                if (cstmt != null) {
                    cstmt.close();
                }
                this.connection.endNonRequestCalls();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
                if (cstmt != null) {
                    cstmt.close();
                }
                this.connection.endNonRequestCalls();
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public String getAttributeName(int idx) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public String getAttributeName(int idx, boolean force) throws SQLException {
        return getAttributeName(idx);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public String getAttributeType(int idx) throws SQLException {
        if (idx != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        }
        if (this.sqlName == null) {
            getFullName();
        }
        if (this.attrTypeNames == null) {
            initCollElemTypeName();
        }
        return this.attrTypeNames[0];
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public String getAttributeType(int idx, boolean force) throws SQLException {
        if (force) {
            return getAttributeType(idx);
        }
        if (idx != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        }
        if (this.sqlName != null && this.attrTypeNames != null) {
            return this.attrTypeNames[0];
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public int getNumAttrs() throws SQLException {
        return 0;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT
    public OracleType getAttrTypeAt(int idx) throws SQLException {
        return null;
    }

    ArrayDescriptor createArrayDescriptor() throws SQLException {
        return new ArrayDescriptor(this, this.connection);
    }

    ArrayDescriptor createArrayDescriptorWithItsOwnTree() throws SQLException {
        if (this.descriptor == null) {
            if (this.sqlName == null && getFullName(false) == null) {
                this.descriptor = new ArrayDescriptor(this, this.connection);
            } else {
                this.descriptor = ArrayDescriptor.createDescriptor(this.sqlName, this.connection);
            }
        }
        return (ArrayDescriptor) this.descriptor;
    }

    public OracleType getElementType() throws SQLException {
        return this.elementType;
    }

    public int getUserCode() throws SQLException {
        return this.userCode;
    }

    public long getMaxLength() throws SQLException {
        return this.maxSize;
    }

    private long getAccessLength(long coll_len, long beginIdx, int count) throws SQLException {
        if (beginIdx > coll_len) {
            return 0L;
        }
        if (count < 0) {
            return (coll_len - beginIdx) + 1;
        }
        return Math.min((coll_len - beginIdx) + 1, count);
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.userCode);
        out.writeLong(this.maxSize);
        out.writeObject(this.elementType);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.userCode = in.readInt();
        this.maxSize = in.readLong();
        this.elementType = (OracleType) in.readObject();
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
        this.elementType.setConnection(conn);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void initMetadataRecursively() throws SQLException {
        initMetadata(this.connection);
        if (this.elementType != null) {
            this.elementType.initMetadataRecursively();
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void initChildNamesRecursively(Map typesMap) throws SQLException {
        TypeTreeElement element = (TypeTreeElement) typesMap.get(this.sqlName);
        if (this.elementType != null) {
            this.elementType.setNames(element.getChildSchemaName(0), element.getChildTypeName(0));
            this.elementType.initChildNamesRecursively(typesMap);
            this.elementType.cacheDescriptor();
        }
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void cacheDescriptor() throws SQLException {
        this.descriptor = ArrayDescriptor.createDescriptor(this);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent) throws SQLException {
        printXML(pw, indent, false);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        for (int i = 0; i < indent; i++) {
            pw.print("  ");
        }
        pw.println("<OracleTypeCOLLECTION sqlName=\"" + this.sqlName + "\" >");
        if (this.elementType != null) {
            this.elementType.printXML(pw, indent + 1, fetchAllMetaDataAsNeeded);
        }
        for (int i2 = 0; i2 < indent; i2++) {
            pw.print("  ");
        }
        pw.println("</OracleTypeCOLLECTION>");
    }
}
