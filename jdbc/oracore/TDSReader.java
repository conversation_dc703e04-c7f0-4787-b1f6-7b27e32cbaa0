package oracle.jdbc.oracore;

import java.sql.SQLException;
import java.util.Vector;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/TDSReader.class */
public class TDSReader {
    static final int KOPT_NONE_FINAL_TYPE = 1;
    static final int KOPT_JAVA_OBJECT = 2;
    long fixedDataSize = 0;
    Vector patches = null;
    byte[] tds;
    int beginIndex;
    int index;

    TDSReader(byte[] tds, long beginIndex) {
        this.tds = tds;
        this.beginIndex = (int) beginIndex;
        this.index = (int) beginIndex;
    }

    void skipBytes(int number) throws SQLException {
        this.index += number;
    }

    void checkNextByte(byte value) throws SQLException {
        try {
            if (value != this.tds[this.index]) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 47, "parseTDS").fillInStackTrace());
            }
        } finally {
            this.index++;
        }
    }

    byte readByte() throws SQLException {
        try {
            return this.tds[this.index];
        } finally {
            this.index++;
        }
    }

    int readUnsignedByte() throws SQLException {
        try {
            return this.tds[this.index] & 255;
        } finally {
            this.index++;
        }
    }

    int readUB2() throws SQLException {
        try {
            return ((this.tds[this.index] & 255) << 8) + (this.tds[this.index + 1] & 255);
        } finally {
            this.index += 2;
        }
    }

    long readLong() throws SQLException {
        try {
            return ((((((this.tds[this.index] & 255) * 256) + (this.tds[this.index + 1] & 255)) * 256) + (this.tds[this.index + 2] & 255)) * 256) + (this.tds[this.index + 3] & 255);
        } finally {
            this.index += 4;
        }
    }

    void addNormalPatch(long pos, byte uptStyle, OracleType type) throws SQLException {
        addPatch(new TDSPatch(0, type, pos, uptStyle));
    }

    void addSimplePatch(long pos, OracleType type) throws SQLException {
        addPatch(new TDSPatch(1, type, pos, 0));
    }

    void addPatch(TDSPatch patch) throws SQLException {
        if (this.patches == null) {
            this.patches = new Vector(5);
        }
        this.patches.addElement(patch);
    }

    long moveToPatchPos(TDSPatch patch) throws SQLException {
        long patchPos = patch.getPosition();
        if (this.beginIndex + patchPos > this.tds.length) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 47, "parseTDS").fillInStackTrace());
        }
        skip_to(patchPos);
        return patchPos;
    }

    TDSPatch getNextPatch() throws SQLException {
        TDSPatch patch = null;
        if (this.patches != null && this.patches.size() > 0) {
            patch = (TDSPatch) this.patches.firstElement();
            this.patches.removeElementAt(0);
        }
        return patch;
    }

    void skip_to(long offset) {
        this.index = this.beginIndex + ((int) offset);
    }

    long offset() throws SQLException {
        return this.index - this.beginIndex;
    }

    long absoluteOffset() throws SQLException {
        return this.index;
    }

    byte[] tds() throws SQLException {
        return this.tds;
    }

    boolean isJavaObject(int version, byte flag) {
        return version >= 3 && (flag & 2) != 0;
    }

    boolean isFinalType(int version, byte flag) {
        return version >= 3 && (flag & 1) == 0;
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
