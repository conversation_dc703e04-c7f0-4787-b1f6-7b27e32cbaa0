package oracle.jdbc.oracore;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.ROWID;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeROWID.class */
public class Oracle<PERSON>ype<PERSON>WID extends OracleType implements Serializable {
    private static final long serialVersionUID = -8461138981231838724L;

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object obj, OracleConnection conn) throws SQLException {
        if (obj != null) {
            if (obj instanceof ROWID) {
                return (Datum) obj;
            }
            if (obj instanceof String) {
                return new ROWID((String) obj);
            }
            if (obj instanceof byte[]) {
                return new ROWID((byte[]) obj);
            }
            return null;
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() throws SQLException {
        return -8;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        return new ROWID(bytes);
    }
}
