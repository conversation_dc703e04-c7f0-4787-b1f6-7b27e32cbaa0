package oracle.jdbc.oracore;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeBINARY_FLOAT.class */
public class OracleTypeBINARY_FLOAT extends OracleType implements Serializable {
    protected OracleTypeBINARY_FLOAT() {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        BINARY_FLOAT datum = null;
        if (value != null) {
            if (value instanceof BINARY_FLOAT) {
                datum = (BINARY_FLOAT) value;
            } else if (value instanceof Float) {
                datum = new BINARY_FLOAT((Float) value);
            } else if (value instanceof byte[]) {
                datum = new BINARY_FLOAT((byte[]) value);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null && (obj instanceof Object[])) {
            Object[] objArray = (Object[]) obj;
            int length = (int) (count == -1 ? objArray.length : Math.min((objArray.length - beginIdx) + 1, count));
            datumArray = new Datum[length];
            for (int i = 0; i < length; i++) {
                Object o = objArray[(((int) beginIdx) + i) - 1];
                if (o != null) {
                    if (o instanceof Float) {
                        datumArray[i] = new BINARY_FLOAT(((Float) o).floatValue());
                    } else if (o instanceof BINARY_FLOAT) {
                        datumArray[i] = (BINARY_FLOAT) o;
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                    }
                } else {
                    datumArray[i] = null;
                }
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 100;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        if (type == 1) {
            return new BINARY_FLOAT(bytes);
        }
        if (type == 2) {
            return new BINARY_FLOAT(bytes).toJdbc();
        }
        if (type == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }
}
