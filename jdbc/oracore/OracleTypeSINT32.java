package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.NUMBER;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeSINT32.class */
public class OracleTypeSINT32 extends OracleType implements Serializable {
    static final long serialVersionUID = -5465988397261455848L;
    boolean isBigEndian;

    protected OracleTypeSINT32(boolean isBigEndian) {
        this.isBigEndian = false;
        this.isBigEndian = isBigEndian;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        return toDatumInternal(value, conn);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        return toDatumArrayInternal(obj, conn, 0L, ((Object[]) obj).length);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatumInternal(Object value, OracleConnection conn) throws SQLException {
        int intValue;
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            intValue = ((Number) value).intValue();
        } else if (value instanceof NUMBER) {
            NUMBER n = (NUMBER) value;
            intValue = n.intValue();
        } else {
            NUMBER n2 = new NUMBER(value);
            intValue = n2.intValue();
        }
        return SINT32.valueOf(intValue, this.isBigEndian);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArrayInternal(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        if (obj == null) {
            return null;
        }
        Object[] arr = (Object[]) obj;
        Datum[] datumArray = SINT32.createArray(count);
        long endIdx = beginIdx + count;
        int i = 0;
        while (beginIdx < endIdx) {
            datumArray[i] = toDatumInternal(arr[(int) beginIdx], conn);
            i++;
            beginIdx++;
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 2;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        Object obj;
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        int out = SINT32.bytesToInt(bytes, this.isBigEndian);
        switch (type) {
            case 1:
                obj = new NUMBER(out);
                return obj;
            case 2:
                obj = new BigDecimal(out);
                return obj;
            case 3:
                NUMBER.toBytes(out);
                break;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23).fillInStackTrace());
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }
}
