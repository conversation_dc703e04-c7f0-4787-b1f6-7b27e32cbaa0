package oracle.jdbc.oracore;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeBINARY_DOUBLE.class */
public class OracleTypeBINARY_DOUBLE extends OracleType implements Serializable {
    protected OracleTypeBINARY_DOUBLE() {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        BINARY_DOUBLE datum = null;
        if (value != null) {
            if (value instanceof BINARY_DOUBLE) {
                datum = (BINARY_DOUBLE) value;
            } else if (value instanceof Double) {
                datum = new BINARY_DOUBLE((Double) value);
            } else if (value instanceof byte[]) {
                datum = new BINARY_DOUBLE((byte[]) value);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null && (obj instanceof Object[])) {
            Object[] objArray = (Object[]) obj;
            int length = (int) (count == -1 ? objArray.length : Math.min((objArray.length - beginIdx) + 1, count));
            datumArray = new Datum[length];
            for (int i = 0; i < length; i++) {
                Object o = objArray[(((int) beginIdx) + i) - 1];
                if (o != null) {
                    if (o instanceof Double) {
                        datumArray[i] = new BINARY_DOUBLE(((Double) o).doubleValue());
                    } else if (o instanceof BINARY_DOUBLE) {
                        datumArray[i] = (BINARY_DOUBLE) o;
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                    }
                } else {
                    datumArray[i] = null;
                }
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 101;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        if (type == 1) {
            return new BINARY_DOUBLE(bytes);
        }
        if (type == 2) {
            return new BINARY_DOUBLE(bytes).toJdbc();
        }
        if (type == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }
}
