package oracle.jdbc.oracore;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.xa.OracleXAResource;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/PickleContext.class */
public final class PickleContext implements Diagnosable {
    private PickleOutputStream outStream;
    byte[] image;
    int imageOffset;
    private byte[] byteBuffer;
    private int userCode;
    public static final byte KOPU_UPT_INDEX_TBL = 1;
    public static final byte KOPU_UPT_NEST_TBL = 2;
    public static final byte KOPU_UPT_VARRAY = 3;
    public static final byte KOPU_UPT_ASSOC_ARRAY = 4;
    static final byte KOPUP_INLINE_COLL = 1;
    static final byte KOPUP_TYPEINFO_NONE = 0;
    static final byte KOPUP_TYPEINFO_TOID = 4;
    static final byte KOPUP_TYPEINFO_TOBJN = 8;
    static final byte KOPUP_TYPEINFO_TDS = 12;
    static final byte KOPUP_VSN_PRESENT = 16;
    private static final String CLASS_NAME = PickleContext.class.getName();
    static short KOPI20_LN_ELNL = 255;
    static short KOPI20_LN_5BLN = 254;
    static short KOPI20_LN_ATMN = 253;
    static short KOPI20_LN_IEMN = 252;
    static short KOPI20_LN_MAXV = 245;
    static short KOPI20_IF_IS81 = 128;
    static short KOPI20_IF_CMSB = 64;
    static short KOPI20_IF_CLSB = 32;
    static short KOPI20_IF_DEGN = 16;
    static short KOPI20_IF_COLL = 8;
    static short KOPI20_IF_NOPS = 4;
    static short KOPI20_IF_ANY = 2;
    static short KOPI20_IF_NONL = 1;
    static short KOPI20_CF_CMSB = 64;
    static short KOPI20_CF_CLSB = 32;
    static short KOPI20_CF_INDX = 16;
    static short KOPI20_CF_NOLN = 8;
    static short KOPI20_VERSION = 1;

    public PickleContext() {
        this.userCode = 0;
        this.byteBuffer = new byte[5];
    }

    public PickleContext(byte[] pickled_bytes) {
        this.userCode = 0;
        this.byteBuffer = new byte[5];
        this.image = pickled_bytes;
        this.imageOffset = 0;
    }

    public PickleContext(byte[] pickled_bytes, long offset) {
        this.userCode = 0;
        this.byteBuffer = new byte[5];
        this.image = pickled_bytes;
        this.imageOffset = (int) offset;
    }

    public void initStream(int imglen) {
        this.outStream = new PickleOutputStream(imglen);
    }

    public void initStream() {
        this.outStream = new PickleOutputStream();
    }

    public int lengthInBytes(int v) {
        return v <= KOPI20_LN_MAXV ? 1 : 5;
    }

    public int writeElementNull() throws SQLException {
        this.outStream.write(KOPI20_LN_ELNL);
        return 1;
    }

    public int writeAtomicNull() throws SQLException {
        this.outStream.write(KOPI20_LN_ATMN);
        return 1;
    }

    public int writeImmediatelyEmbeddedElementNull(byte null_adtno) throws SQLException {
        this.byteBuffer[0] = (byte) KOPI20_LN_IEMN;
        this.byteBuffer[1] = null_adtno;
        this.outStream.write(this.byteBuffer, 0, 2);
        return 2;
    }

    public int writeSB2(int len) throws SQLException {
        this.byteBuffer[0] = (byte) ((len >> 8) & 255);
        this.byteBuffer[1] = (byte) (len & 255);
        this.outStream.write(this.byteBuffer, 0, 2);
        return 2;
    }

    public int writeUB4(int value) throws SQLException {
        this.byteBuffer[0] = (byte) ((value >> 24) & 255);
        this.byteBuffer[1] = (byte) ((value >> 16) & 255);
        this.byteBuffer[2] = (byte) ((value >> 8) & 255);
        this.byteBuffer[3] = (byte) (value & 255);
        this.outStream.write(this.byteBuffer, 0, 4);
        return 4;
    }

    public int writeLength(int len) throws SQLException {
        if (len <= KOPI20_LN_MAXV) {
            this.outStream.write((byte) len);
            return 1;
        }
        this.byteBuffer[0] = (byte) KOPI20_LN_5BLN;
        this.byteBuffer[1] = (byte) (len >> 24);
        int len2 = len & 16777215;
        this.byteBuffer[2] = (byte) (len2 >> 16);
        int len3 = len2 & 65535;
        this.byteBuffer[3] = (byte) (len3 >> 8);
        this.byteBuffer[4] = (byte) (len3 & 255);
        try {
            this.outStream.write(this.byteBuffer);
            return 5;
        } catch (IOException ioException) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "writeLength", ioException.getMessage(), null, ioException, new Object[0]);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioException).fillInStackTrace());
        }
    }

    public int writeLength(int datalen, boolean include) throws SQLException {
        if (!include) {
            return writeLength(datalen);
        }
        if (datalen <= KOPI20_LN_MAXV - 1) {
            this.outStream.write(((byte) datalen) + 1);
            return 1;
        }
        int datalen2 = datalen + 5;
        this.byteBuffer[0] = (byte) KOPI20_LN_5BLN;
        this.byteBuffer[1] = (byte) (datalen2 >> 24);
        int datalen3 = datalen2 & 16777215;
        this.byteBuffer[2] = (byte) (datalen3 >> 16);
        int datalen4 = datalen3 & 65535;
        this.byteBuffer[3] = (byte) (datalen4 >> 8);
        this.byteBuffer[4] = (byte) (datalen4 & 255);
        try {
            this.outStream.write(this.byteBuffer);
            return 5;
        } catch (IOException ioException) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "writeLength", ioException.getMessage(), null, ioException, new Object[0]);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioException).fillInStackTrace());
        }
    }

    public byte[] to5bLengthBytes_pctx(int len) throws SQLException {
        this.byteBuffer[0] = (byte) KOPI20_LN_5BLN;
        this.byteBuffer[1] = (byte) (len >> 24);
        int len2 = len & 16777215;
        this.byteBuffer[2] = (byte) (len2 >> 16);
        int len3 = len2 & 65535;
        this.byteBuffer[3] = (byte) (len3 >> 8);
        this.byteBuffer[4] = (byte) (len3 & 255);
        return this.byteBuffer;
    }

    public int writeData(byte b) throws SQLException {
        this.outStream.write(b);
        return 1;
    }

    public int writeData(byte[] b) throws SQLException {
        try {
            this.outStream.write(b);
            return b.length;
        } catch (IOException ioException) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "writeData", ioException.getMessage(), null, ioException, new Object[0]);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioException).fillInStackTrace());
        }
    }

    public void patchImageLen(int offset, int image_length) throws SQLException {
        byte[] lenbuf = to5bLengthBytes_pctx(image_length);
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "patchImageLen", "imglen={0},lenbuf={1}", null, null, Integer.valueOf(image_length), Parameter.arg(Format.Style.BYTE_ARRAY, lenbuf, new long[0]));
        this.outStream.overwrite(offset, lenbuf, 0, lenbuf.length);
    }

    public int writeImageHeader(boolean withPrefix) throws SQLException {
        return writeImageHeader(KOPI20_LN_MAXV + 1, withPrefix);
    }

    public int writeOpaqueImageHeader(int data_length) throws SQLException {
        this.byteBuffer[0] = (byte) (KOPI20_IF_IS81 | KOPI20_IF_NOPS | KOPI20_IF_NONL);
        this.byteBuffer[1] = (byte) KOPI20_VERSION;
        this.outStream.write(this.byteBuffer, 0, 2);
        int count = 2 + writeLength(data_length + 2, true);
        return count;
    }

    public int writeImageHeader(int image_length, boolean withPrefix) throws SQLException {
        if (withPrefix) {
            this.byteBuffer[0] = (byte) KOPI20_IF_IS81;
        } else {
            this.byteBuffer[0] = (byte) (KOPI20_IF_IS81 | KOPI20_IF_NOPS);
        }
        this.byteBuffer[1] = (byte) KOPI20_VERSION;
        this.outStream.write(this.byteBuffer, 0, 2);
        int count = 2 + writeLength(image_length);
        return count;
    }

    public int writeCollImageHeader(int num_collection_items, int typeVersion) throws SQLException {
        return writeCollImageHeader(KOPI20_LN_MAXV + 1, num_collection_items, typeVersion);
    }

    public int writeCollImageHeader(int image_length, int num_collection_items, int typeVersion) throws SQLException {
        int count;
        this.byteBuffer[0] = (byte) (KOPI20_IF_IS81 | KOPI20_IF_COLL);
        this.byteBuffer[1] = (byte) KOPI20_VERSION;
        this.outStream.write(this.byteBuffer, 0, 2);
        int count2 = 5 + writeLength(image_length);
        this.byteBuffer[0] = 1;
        this.byteBuffer[1] = 17;
        if (typeVersion > KOPI20_LN_MAXV) {
            byte[] bArr = this.byteBuffer;
            bArr[0] = (byte) (bArr[0] + 5);
            count = count2 + 5;
            this.outStream.write(this.byteBuffer, 0, 2);
            writeLength(typeVersion);
        } else {
            byte[] bArr2 = this.byteBuffer;
            bArr2[0] = (byte) (bArr2[0] + 2);
            count = count2 + 2;
            this.outStream.write(this.byteBuffer, 0, 2);
            writeSB2(typeVersion);
        }
        if (this.userCode == 1) {
            this.byteBuffer[0] = (byte) KOPI20_CF_INDX;
        } else {
            this.byteBuffer[0] = 0;
        }
        this.outStream.write(this.byteBuffer, 0, 1);
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "writeCollImageHeader", "image_length={0}, num_collection_items{1}, typeVersion={2}, userCode{3}", null, null, Integer.valueOf(image_length), Integer.valueOf(num_collection_items), Integer.valueOf(typeVersion), Integer.valueOf(this.userCode));
        return count + writeLength(num_collection_items);
    }

    public int writeCollImageHeader(byte[] prefix_segment) throws SQLException {
        return writeCollImageHeader(KOPI20_LN_MAXV + 1, prefix_segment);
    }

    public int writeCollImageHeader(int image_length, byte[] prefix_segment) throws SQLException {
        int psegLen = prefix_segment.length;
        int count = 3 + psegLen;
        this.byteBuffer[0] = (byte) (KOPI20_IF_IS81 | KOPI20_IF_DEGN);
        this.byteBuffer[1] = (byte) KOPI20_VERSION;
        this.outStream.write(this.byteBuffer, 0, 2);
        int count2 = count + writeLength(image_length) + writeLength(psegLen + 1);
        this.byteBuffer[0] = 0;
        this.outStream.write(this.byteBuffer, 0, 1);
        this.outStream.write(prefix_segment, 0, psegLen);
        return count2;
    }

    public byte[] stream2Bytes() throws SQLException {
        return this.outStream.toByteArray();
    }

    public byte readByte() throws SQLException {
        try {
            return this.image[this.imageOffset];
        } finally {
            this.imageOffset++;
        }
    }

    public boolean readAndCheckVersion() throws SQLException {
        try {
            return (this.image[this.imageOffset] & 255) <= KOPI20_VERSION;
        } finally {
            this.imageOffset++;
        }
    }

    public int readLength() throws SQLException {
        int len = this.image[this.imageOffset] & 255;
        if (len > KOPI20_LN_MAXV) {
            if (len == KOPI20_LN_ELNL) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid null flag read").fillInStackTrace());
            }
            len = ((((((this.image[this.imageOffset + 1] & 255) * 256) + (this.image[this.imageOffset + 2] & 255)) * 256) + (this.image[this.imageOffset + 3] & 255)) * 256) + (this.image[this.imageOffset + 4] & 255);
            this.imageOffset += 5;
        } else {
            this.imageOffset++;
        }
        return len;
    }

    public void skipLength() throws SQLException {
        int len = this.image[this.imageOffset] & 255;
        if (len > KOPI20_LN_MAXV) {
            this.imageOffset += 5;
        } else {
            this.imageOffset++;
        }
    }

    public int readRestOfLength(byte len) throws SQLException {
        if ((len & 255) != KOPI20_LN_5BLN) {
            return len & 255;
        }
        try {
            return ((((((this.image[this.imageOffset] & 255) * 256) + (this.image[this.imageOffset + 1] & 255)) * 256) + (this.image[this.imageOffset + 2] & 255)) * 256) + (this.image[this.imageOffset + 3] & 255);
        } finally {
            this.imageOffset += 4;
        }
    }

    public void skipRestOfLength(byte len) throws SQLException {
        if ((len & 255) > KOPI20_LN_MAXV) {
            if ((len & 255) == KOPI20_LN_5BLN) {
                this.imageOffset += 4;
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid first length byte").fillInStackTrace());
        }
    }

    public int readLength(boolean exclude) throws SQLException {
        int len = this.image[this.imageOffset] & 255;
        if (len > KOPI20_LN_MAXV) {
            len = ((((((this.image[this.imageOffset + 1] & 255) * 256) + (this.image[this.imageOffset + 2] & 255)) * 256) + (this.image[this.imageOffset + 3] & 255)) * 256) + (this.image[this.imageOffset + 4] & 255);
            if (exclude) {
                len -= 5;
            }
            this.imageOffset += 5;
        } else {
            if (exclude) {
                len--;
            }
            this.imageOffset++;
        }
        return len;
    }

    public byte[] readPrefixSegment() throws SQLException {
        byte[] b = new byte[readLength()];
        System.arraycopy(this.image, this.imageOffset, b, 0, b.length);
        this.imageOffset += b.length;
        return b;
    }

    public byte[] readDataValue() throws SQLException {
        int len = this.image[this.imageOffset] & 255;
        if (len == KOPI20_LN_ELNL) {
            this.imageOffset++;
            return null;
        }
        if (len > KOPI20_LN_MAXV) {
            len = ((((((this.image[this.imageOffset + 1] & 255) * 256) + (this.image[this.imageOffset + 2] & 255)) * 256) + (this.image[this.imageOffset + 3] & 255)) * 256) + (this.image[this.imageOffset + 4] & 255);
            this.imageOffset += 5;
        } else {
            this.imageOffset++;
        }
        byte[] b = new byte[len];
        System.arraycopy(this.image, this.imageOffset, b, 0, b.length);
        this.imageOffset += b.length;
        return b;
    }

    public byte[] readBytes(int length) throws SQLException {
        byte[] b = new byte[length];
        System.arraycopy(this.image, this.imageOffset, b, 0, length);
        this.imageOffset += length;
        return b;
    }

    public byte[] read1ByteDataValue() throws SQLException {
        if ((this.image[this.imageOffset] & 255) == KOPI20_LN_ELNL) {
            return null;
        }
        byte[] b = new byte[this.image[this.imageOffset] & 255];
        System.arraycopy(this.image, this.imageOffset + 1, b, 0, b.length);
        this.imageOffset += b.length + 1;
        return b;
    }

    public byte[] readDataValue(byte byte1) throws SQLException {
        byte[] b = new byte[readRestOfLength(byte1)];
        System.arraycopy(this.image, this.imageOffset, b, 0, b.length);
        this.imageOffset += b.length;
        return b;
    }

    public byte[] readDataValue(int len) throws SQLException {
        byte[] b = new byte[len];
        System.arraycopy(this.image, this.imageOffset, b, 0, len);
        this.imageOffset += len;
        return b;
    }

    public long readUB4() throws SQLException {
        byte[] bArr = this.image;
        this.imageOffset = this.imageOffset + 1;
        byte[] bArr2 = this.image;
        this.imageOffset = this.imageOffset + 1;
        long j = ((bArr[r2] << 24) & (-16777216)) | ((bArr2[r3] << 16) & 16711680);
        byte[] bArr3 = this.image;
        this.imageOffset = this.imageOffset + 1;
        long j2 = j | ((bArr3[r3] << 8) & 65280);
        byte[] bArr4 = this.image;
        this.imageOffset = this.imageOffset + 1;
        long ret = j2 | (bArr4[r3] & 255);
        return ret;
    }

    public int readUB2() throws SQLException {
        byte[] bArr = this.image;
        int i = this.imageOffset;
        this.imageOffset = i + 1;
        int i2 = (bArr[i] << 8) & OracleXAResource.ORAISOLATIONMASK;
        byte[] bArr2 = this.image;
        int i3 = this.imageOffset;
        this.imageOffset = i3 + 1;
        int ret = i2 | (bArr2[i3] & 255);
        return ret;
    }

    public void skipDataValue() throws SQLException {
        if ((this.image[this.imageOffset] & 255) == KOPI20_LN_ELNL) {
            this.imageOffset++;
        } else {
            skipBytes(readLength());
        }
    }

    public void skipDataValue(byte b) throws SQLException {
        skipBytes(readRestOfLength(b));
    }

    public void skipBytes(int b) throws SQLException {
        if (b > 0) {
            this.imageOffset += b;
        }
    }

    public int offset() throws SQLException {
        if (this.outStream != null) {
            return this.outStream.offset();
        }
        return this.imageOffset;
    }

    public int absoluteOffset() throws SQLException {
        return this.imageOffset;
    }

    public void skipTo(long offset) throws SQLException {
        if (offset > this.imageOffset) {
            this.imageOffset = (int) offset;
        }
    }

    public byte[] image() throws SQLException {
        return this.image;
    }

    public static boolean is81format(byte flag) throws SQLException {
        return ((flag & 255) & KOPI20_IF_IS81) != 0;
    }

    public static boolean isCollectionImage_pctx(byte flag) throws SQLException {
        return ((flag & 255) & KOPI20_IF_COLL) != 0;
    }

    public static boolean isDegenerateImage_pctx(byte flag) throws SQLException {
        return ((flag & 255) & KOPI20_IF_DEGN) != 0;
    }

    public static boolean hasPrefix(byte flag) throws SQLException {
        return ((flag & 255) & KOPI20_IF_NOPS) == 0;
    }

    public static boolean isAtomicNull(byte flag) throws SQLException {
        return (flag & 255) == KOPI20_LN_ATMN;
    }

    public static boolean isImmediatelyEmbeddedNull(byte flag) throws SQLException {
        return (flag & 255) == KOPI20_LN_IEMN;
    }

    public static boolean isElementNull(byte flag) throws SQLException {
        return (flag & 255) == KOPI20_LN_ELNL;
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    void setCollectionUserCode(int userCode) {
        this.userCode = userCode;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }
}
