package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeFLOAT.class */
public class OracleTypeFLOAT extends OracleType implements Serializable {
    static final long serialVersionUID = 4088841548269771109L;
    int precision;

    protected OracleTypeFLOAT() {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        return OracleTypeNUMBER.toNUMBER(value, conn);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        return OracleTypeNUMBER.toNUMBERArray(obj, conn, beginIdx, count);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 6;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        this.precision = tdsReader.readUnsignedByte();
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getScale() {
        return -127;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getPrecision() {
        return this.precision;
    }

    protected static Object unpickle81NativeArray(PickleContext context, long beginIdx, int size, int type) throws SQLException {
        return OracleTypeNUMBER.unpickle81NativeArray(context, beginIdx, size, type);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        return OracleTypeNUMBER.toNumericObject(bytes, type, map);
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.precision);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.precision = in.readInt();
    }
}
