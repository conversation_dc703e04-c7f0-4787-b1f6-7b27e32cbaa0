package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Vector;
import java.util.logging.Level;
import oracle.jdbc.OracleCallableStatement;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.ObjectData;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleStruct;
import oracle.sql.AttributeDescriptor;
import oracle.sql.BLOB;
import oracle.sql.Datum;
import oracle.sql.JAVA_STRUCT;
import oracle.sql.NUMBER;
import oracle.sql.SQLName;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeADT.class */
public class OracleTypeADT extends OracleNamedType implements Serializable, Monitor {
    static final long serialVersionUID = 3031304012507165702L;
    static final int S_TOP = 1;
    static final int S_EMBEDDED = 2;
    static final int S_UPT_ADT = 4;
    static final int S_JAVA_OBJECT = 16;
    static final int S_FINAL_TYPE = 32;
    static final int S_SUB_TYPE = 64;
    static final int S_ATTR_TDS = 128;
    static final int S_HAS_METADATA = 256;
    static final int S_TDS_PARSED = 512;
    private int statusBits;
    int tdsVersion;
    static final int KOPT_V80 = 1;
    static final int KOPT_V81 = 2;
    static final int KOPT_VNFT = 3;
    static final int KOPT_VERSION = 3;
    boolean endOfAdt;
    int typeVersion;
    long fixedDataSize;
    int alignmentRequirement;
    OracleType[] attrTypes;
    String[] attrNames;
    String[] attrTypeNames;
    public long tdoCState;
    byte[] toid;
    int charSetId;
    int charSetForm;
    int flattenedAttrNum;
    transient int opcode;
    transient int idx;
    boolean isTransient;
    private Monitor.CloseableLock monitorLock;
    static final int CURRENT_USER_OBJECT = 0;
    static final int CURRENT_USER_SYNONYM = 1;
    static final int CURRENT_USER_SYNONYM_10g = 2;
    static final int CURRENT_USER_PUBLIC_SYNONYM = 3;
    static final int CURRENT_USER_PUBLIC_SYNONYM_10g = 4;
    static final int POSSIBLY_OTHER_USER_OBJECT = 5;
    static final int POSSIBLY_OTHER_USER_OBJECT_10g = 6;
    static final int OTHER_USER_OBJECT = 7;
    static final int OTHER_USER_SYNONYM = 8;
    static final int PUBLIC_SYNONYM = 9;
    static final int PUBLIC_SYNONYM_10g = 10;
    static final int BREAK = 11;
    static final int SEARCH_USER_TYPES = 0;
    static final int SEARCH_ALL_TYPES = 1;
    Boolean isInstanciable;
    String superTypeName;
    int numberOfLocalAttributes;
    String[] subTypeNames;
    static final int TDS_SIZE = 4;
    static final int TDS_NUMBER = 1;
    static final int KOPM_OTS_SQL_CHAR = 1;
    static final int KOPM_OTS_DATE = 2;
    static final int KOPM_OTS_DECIMAL = 3;
    static final int KOPM_OTS_DOUBLE = 4;
    static final int KOPM_OTS_FLOAT = 5;
    static final int KOPM_OTS_NUMBER = 6;
    static final int KOPM_OTS_SQL_VARCHAR2 = 7;
    static final int KOPM_OTS_SINT32 = 8;
    static final int KOPM_OTS_REF = 9;
    static final int KOPM_OTS_VARRAY = 10;
    static final int KOPM_OTS_UINT8 = 11;
    static final int KOPM_OTS_SINT8 = 12;
    static final int KOPM_OTS_UINT16 = 13;
    static final int KOPM_OTS_UINT32 = 14;
    static final int KOPM_OTS_LOB = 15;
    static final int KOPM_OTS_UROWID = 16;
    static final int KOPM_OTS_CANONICAL = 17;
    static final int KOPM_OTS_OCTET = 18;
    static final int KOPM_OTS_RAW = 19;
    static final int KOPM_OTS_ROWID = 20;
    static final int KOPM_OTS_STAMP = 21;
    static final int KOPM_OTS_TZSTAMP = 23;
    static final int KOPM_OTS_INTERVAL = 24;
    static final int KOPM_OTS_PTR = 25;
    static final int KOPM_OTS_SINT16 = 26;
    static final int KOPM_OTS_UPT = 27;
    static final int KOPM_OTS_COLLECTION = 28;
    static final int KOPM_OTS_CLOB = 29;
    static final int KOPM_OTS_BLOB = 30;
    static final int KOPM_OTS_BFILE = 31;
    static final int KOPM_OTS_BINARY_INTEGE = 32;
    static final int KOPM_OTS_IMPTZSTAMP = 33;
    static final int KOPM_OTS_BFLOAT = 37;
    static final int KOPM_OTS_BDOUBLE = 45;
    static final int KOTTCOPQ = 58;
    static final int KOPT_OP_STARTEMBADT = 39;
    static final int KOPT_OP_ENDEMBADT = 40;
    static final int KOPT_OP_STARTADT = 41;
    static final int KOPT_OP_ENDADT = 42;
    static final int KOPT_OP_SUBTYPE_MARKER = 43;
    static final int KOPT_OP_EMBADT_INFO = 44;
    static final int KOPT_OPCODE_START = 38;
    static final int KOPT_OP_VERSION = 38;
    static final int REGULAR_PATCH = 0;
    static final int SIMPLE_PATCH = 1;
    private static final String CLASS_NAME = OracleTypeADT.class.getName();
    static final String[] sqlString = {"SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS WHERE TYPE_NAME = :1 ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS WHERE TYPE_NAME in (SELECT TABLE_NAME FROM USER_SYNONYMS START WITH SYNONYM_NAME = :1 CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME UNION SELECT :1 FROM DUAL) ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS WHERE TYPE_NAME in (SELECT TABLE_NAME FROM USER_SYNONYMS START WITH SYNONYM_NAME = :1 CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME UNION SELECT :1 FROM DUAL) ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS WHERE TYPE_NAME IN (SELECT TABLE_NAME FROM ALL_SYNONYMS START WITH SYNONYM_NAME = :1 AND  OWNER = 'PUBLIC' CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER UNION SELECT :2  FROM DUAL) ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS WHERE TYPE_NAME IN (SELECT TABLE_NAME FROM ALL_SYNONYMS START WITH SYNONYM_NAME = :1 AND  OWNER = 'PUBLIC' CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER UNION SELECT :2  FROM DUAL) ORDER BY ATTR_NO", "DECLARE CURSOR usyn_cur IS SELECT table_name, table_owner from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; l_syntown  user_synonyms.table_owner%TYPE; BEGIN SELECT TABLE_NAME, TABLE_OWNER BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name;   l_syntown :=  table_names(lastrow).table_owner; END IF; OPEN ? FOR SELECT  ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER   FROM ALL_TYPE_ATTRS  A   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?)  AND  A.OWNER = l_syntown   ORDER BY ATTR_NO; END;", "DECLARE CURSOR usyn_cur IS SELECT table_name, table_owner from user_synonyms; TYPE table_name_type IS TABLE OF usyn_cur%ROWTYPE; table_names table_name_type; lastrow BINARY_INTEGER := null; l_syntname user_synonyms.table_name%TYPE; l_syntown  user_synonyms.table_owner%TYPE; BEGIN SELECT TABLE_NAME, TABLE_OWNER BULK COLLECT INTO table_names FROM USER_SYNONYMS START WITH SYNONYM_NAME = ? CONNECT BY NOCYCLEPRIOR TABLE_NAME = SYNONYM_NAME; IF table_names.LAST IS NOT NULL THEN   lastrow := table_names.LAST;   l_syntname := table_names(lastrow).table_name;   l_syntown :=  table_names(lastrow).table_owner; END IF; OPEN ? FOR SELECT  ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER   FROM ALL_TYPE_ATTRS  A   WHERE (TYPE_NAME = l_syntname OR TYPE_NAME = ?)  AND  A.OWNER = l_syntown   ORDER BY ATTR_NO; END;", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM ALL_TYPE_ATTRS WHERE OWNER = :1 AND TYPE_NAME = :2 ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM ALL_TYPE_ATTRS WHERE OWNER = (SELECT TABLE_OWNER FROM ALL_SYNONYMS WHERE SYNONYM_NAME=:1) AND TYPE_NAME = (SELECT TABLE_NAME FROM ALL_SYNONYMS WHERE SYNONYM_NAME=:2) ORDER BY ATTR_NO", "DECLARE   the_owner VARCHAR2(100);   the_type  VARCHAR2(100); begin  SELECT TABLE_NAME, TABLE_OWNER INTO THE_TYPE, THE_OWNER  FROM ALL_SYNONYMS  WHERE TABLE_NAME IN (SELECT TYPE_NAME FROM ALL_TYPES)  START WITH SYNONYM_NAME = :1 AND OWNER = 'PUBLIC'  CONNECT BY PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER; OPEN :2 FOR SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME,  ATTR_TYPE_OWNER FROM ALL_TYPE_ATTRS  WHERE TYPE_NAME = THE_TYPE and OWNER = THE_OWNER; END;", "DECLARE   the_owner VARCHAR2(100);   the_type  VARCHAR2(100); begin  SELECT TABLE_NAME, TABLE_OWNER INTO THE_TYPE, THE_OWNER  FROM ALL_SYNONYMS  WHERE TABLE_NAME IN (SELECT TYPE_NAME FROM ALL_TYPES)  START WITH SYNONYM_NAME = :1 AND OWNER = 'PUBLIC'  CONNECT BY NOCYCLE PRIOR TABLE_NAME = SYNONYM_NAME AND TABLE_OWNER = OWNER; OPEN :2 FOR SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME,  ATTR_TYPE_OWNER FROM ALL_TYPE_ATTRS  WHERE TYPE_NAME = THE_TYPE and OWNER = THE_OWNER; END;"};
    static final String[] sqlStringTOID = {"SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM USER_TYPE_ATTRS a, USER_TYPES b WHERE b.TYPE_OID = :1 AND a.TYPE_NAME = b.TYPE_NAME ORDER BY ATTR_NO", "SELECT ATTR_NO, ATTR_NAME, ATTR_TYPE_NAME, ATTR_TYPE_OWNER FROM ALL_TYPE_ATTRS a, ALL_TYPES b WHERE b.TYPE_OID = :1 AND a.TYPE_NAME = b.TYPE_NAME AND a.OWNER = b.OWNER ORDER BY ATTR_NO"};

    protected OracleTypeADT() {
        this.statusBits = 1;
        this.tdsVersion = -9999;
        this.endOfAdt = false;
        this.typeVersion = 1;
        this.fixedDataSize = -1L;
        this.alignmentRequirement = -1;
        this.attrTypes = null;
        this.tdoCState = 0L;
        this.toid = null;
        this.idx = 1;
        this.isTransient = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.numberOfLocalAttributes = -1;
    }

    public OracleTypeADT(byte[] toid, int vsn, int csi, short csfrm, String fullName) throws SQLException {
        this(fullName, (OracleConnection) null);
        this.toid = toid;
        this.typeVersion = vsn;
        this.charSetId = csi;
        this.charSetForm = csfrm;
    }

    public OracleTypeADT(String sql_name, Connection conn) throws SQLException {
        super(sql_name, (OracleConnection) conn);
        this.statusBits = 1;
        this.tdsVersion = -9999;
        this.endOfAdt = false;
        this.typeVersion = 1;
        this.fixedDataSize = -1L;
        this.alignmentRequirement = -1;
        this.attrTypes = null;
        this.tdoCState = 0L;
        this.toid = null;
        this.idx = 1;
        this.isTransient = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.numberOfLocalAttributes = -1;
        this.typeNameByUser = sql_name;
    }

    public OracleTypeADT(OracleTypeADT parent, int idx, Connection conn) throws SQLException {
        super(parent, idx, (OracleConnection) conn);
        this.statusBits = 1;
        this.tdsVersion = -9999;
        this.endOfAdt = false;
        this.typeVersion = 1;
        this.fixedDataSize = -1L;
        this.alignmentRequirement = -1;
        this.attrTypes = null;
        this.tdoCState = 0L;
        this.toid = null;
        this.idx = 1;
        this.isTransient = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.numberOfLocalAttributes = -1;
    }

    public OracleTypeADT(SQLName sqlName, byte[] typoid, int version, byte[] tds, OracleConnection conn) throws SQLException {
        this.statusBits = 1;
        this.tdsVersion = -9999;
        this.endOfAdt = false;
        this.typeVersion = 1;
        this.fixedDataSize = -1L;
        this.alignmentRequirement = -1;
        this.attrTypes = null;
        this.tdoCState = 0L;
        this.toid = null;
        this.idx = 1;
        this.isTransient = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.numberOfLocalAttributes = -1;
        this.sqlName = sqlName;
        init(tds, conn);
        this.toid = typoid;
        this.typeVersion = version;
    }

    public OracleTypeADT(AttributeDescriptor[] attr, OracleConnection conn) throws SQLException {
        this.statusBits = 1;
        this.tdsVersion = -9999;
        this.endOfAdt = false;
        this.typeVersion = 1;
        this.fixedDataSize = -1L;
        this.alignmentRequirement = -1;
        this.attrTypes = null;
        this.tdoCState = 0L;
        this.toid = null;
        this.idx = 1;
        this.isTransient = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.numberOfLocalAttributes = -1;
        setConnectionInternal(conn);
        this.isTransient = true;
        this.flattenedAttrNum = attr.length;
        this.attrTypes = new OracleType[this.flattenedAttrNum];
        this.attrNames = new String[this.flattenedAttrNum];
        for (int i = 0; i < this.flattenedAttrNum; i++) {
            this.attrNames[i] = attr[i].getAttributeName();
        }
        this.statusBits |= 256;
        for (int i2 = 0; i2 < this.flattenedAttrNum; i2++) {
            TypeDescriptor td = attr[i2].getTypeDescriptor();
            switch (td.getInternalTypeCode()) {
                case 2:
                    this.attrTypes[i2] = new OracleTypeNUMBER(2);
                    ((OracleTypeNUMBER) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    ((OracleTypeNUMBER) this.attrTypes[i2]).scale = td.getScale();
                    break;
                case 4:
                    this.attrTypes[i2] = new OracleTypeFLOAT();
                    ((OracleTypeFLOAT) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    break;
                case 7:
                    this.attrTypes[i2] = new OracleTypeNUMBER(3);
                    ((OracleTypeNUMBER) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    ((OracleTypeNUMBER) this.attrTypes[i2]).scale = td.getScale();
                    break;
                case 9:
                    this.attrTypes[i2] = new OracleTypeCHAR(this.connection, 12);
                    ((OracleTypeCHAR) this.attrTypes[i2]).length = (int) td.getPrecision();
                    ((OracleTypeCHAR) this.attrTypes[i2]).form = 1;
                    break;
                case 12:
                    this.attrTypes[i2] = new OracleTypeDATE();
                    break;
                case 22:
                    this.attrTypes[i2] = new OracleTypeNUMBER(8);
                    ((OracleTypeNUMBER) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    ((OracleTypeNUMBER) this.attrTypes[i2]).scale = td.getScale();
                    break;
                case 29:
                    this.attrTypes[i2] = new OracleTypeSINT32(false);
                    break;
                case 95:
                    this.attrTypes[i2] = new OracleTypeRAW();
                    break;
                case 96:
                    this.attrTypes[i2] = new OracleTypeCHAR(this.connection, 1);
                    ((OracleTypeCHAR) this.attrTypes[i2]).length = (int) td.getPrecision();
                    ((OracleTypeCHAR) this.attrTypes[i2]).form = 1;
                    break;
                case 100:
                    this.attrTypes[i2] = new OracleTypeBINARY_FLOAT();
                    break;
                case 101:
                    this.attrTypes[i2] = new OracleTypeBINARY_DOUBLE();
                    break;
                case 108:
                    this.attrTypes[i2] = (OracleTypeADT) td.getPickler();
                    ((OracleTypeADT) this.attrTypes[i2]).setEmbeddedADT();
                    break;
                case 110:
                    this.attrTypes[i2] = new OracleTypeREF(this, i2, this.connection);
                    break;
                case 112:
                    this.attrTypes[i2] = new OracleTypeCLOB(this.connection);
                    break;
                case 113:
                    this.attrTypes[i2] = new OracleTypeBLOB(this.connection);
                    break;
                case 114:
                    this.attrTypes[i2] = new OracleTypeBFILE(this.connection);
                    break;
                case 122:
                    this.attrTypes[i2] = new OracleTypeCOLLECTION(this, i2, this.connection);
                    break;
                case 187:
                    this.attrTypes[i2] = new OracleTypeTIMESTAMP(this.connection);
                    ((OracleTypeTIMESTAMP) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    break;
                case 188:
                    this.attrTypes[i2] = new OracleTypeTIMESTAMPTZ(this.connection);
                    ((OracleTypeTIMESTAMPTZ) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    break;
                case 189:
                    this.attrTypes[i2] = new OracleTypeINTERVAL(this.connection);
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).typeId = (byte) 7;
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).scale = td.getScale();
                    break;
                case 190:
                    this.attrTypes[i2] = new OracleTypeINTERVAL(this.connection);
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).typeId = (byte) 10;
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    ((OracleTypeINTERVAL) this.attrTypes[i2]).scale = td.getScale();
                    break;
                case 232:
                    this.attrTypes[i2] = new OracleTypeTIMESTAMPLTZ(this.connection);
                    ((OracleTypeTIMESTAMPLTZ) this.attrTypes[i2]).precision = (int) td.getPrecision();
                    break;
                default:
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 48, "type: " + ((int) td.getInternalTypeCode())).fillInStackTrace());
            }
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        if (value != null) {
            if (value instanceof STRUCT) {
                return (STRUCT) value;
            }
            if ((value instanceof SQLData) || (value instanceof ObjectData)) {
                return STRUCT.toSTRUCT(value, conn);
            }
            if (value instanceof Object[]) {
                StructDescriptor desc = createStructDescriptor();
                Datum result = createObjSTRUCT(desc, (Object[]) value);
                return result;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof Object[]) {
                Object[] objArray = (Object[]) obj;
                int length = (int) (count == -1 ? objArray.length : Math.min((objArray.length - beginIdx) + 1, count));
                datumArray = new Datum[length];
                for (int i = 0; i < length; i++) {
                    datumArray[i] = toDatum(objArray[(((int) beginIdx) + i) - 1], conn);
                }
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() throws SQLException {
        if ((getStatus() & 16) != 0) {
            return 2008;
        }
        return 2002;
    }

    public OracleType[] getAttrTypes() throws SQLException {
        if (this.attrTypes == null) {
            init(this.connection);
        }
        return this.attrTypes;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(OracleType anOracleType) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        if (anOracleType == null) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return false;
        }
        try {
            try {
                if (!anOracleType.isObjectType()) {
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return false;
                }
                StructDescriptor theOtherDesc = (StructDescriptor) anOracleType.getTypeDescriptor();
                boolean zIsInHierarchyOf = this.descriptor.isInHierarchyOf(theOtherDesc.getName());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsInHierarchyOf;
            } finally {
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(StructDescriptor aStructDescriptor) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        if (aStructDescriptor != null) {
            try {
                try {
                    boolean zIsInHierarchyOf = this.descriptor.isInHierarchyOf(aStructDescriptor.getName());
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return zIsInHierarchyOf;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                }
            } else {
                lock.close();
            }
        }
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public boolean isObjectType() {
        return true;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public TypeDescriptor getTypeDescriptor() {
        return this.descriptor;
    }

    public void init(OracleConnection conn) throws SQLException {
        Monitor.CloseableLock lock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            byte[] tds = initMetadata(conn);
            init(tds, conn);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void init(byte[] tds, OracleConnection conn) throws SQLException {
        Monitor.CloseableLock lock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                doInit(tds, conn);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void doInit(byte[] tds, OracleConnection conn) throws SQLException {
        this.statusBits = 1;
        this.connection = conn;
        if (tds != null) {
            parseTDS(tds, 0L);
        }
        setStatusBits(256);
    }

    public byte[] initMetadata(OracleConnection conn) throws SQLException {
        byte[] returnValue;
        int dbVersion = conn.getVersionNumber();
        if (dbVersion >= 12000) {
            returnValue = initMetadata12(conn);
        } else {
            returnValue = initMetadata11_2(conn);
        }
        return returnValue;
    }

    private byte[] initMetadata12(OracleConnection conn) throws SQLException {
        byte[] myTDS;
        Monitor.CloseableLock lock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            if ((this.statusBits & 256) != 0) {
                return null;
            }
            this.connection.beginNonRequestCalls();
            try {
                CallableStatement getTypeShapeCall = prepareGetTypeShapeCall(this.connection);
                Throwable th2 = null;
                try {
                    debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata12", "typeNameByUser={0}", (String) null, (String) null, this.typeNameByUser);
                    getTypeShapeCall.execute();
                    int returnCode = getTypeShapeCall.getInt(1);
                    boolean useBlob = false;
                    if (returnCode != 0) {
                        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata12", "getTypeShapeCall returnCode={0}", (String) null, (String) null, Integer.valueOf(returnCode));
                        if (returnCode != 24331) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 74, this.typeNameByUser).fillInStackTrace());
                        }
                        if (returnCode == 24331) {
                            useBlob = true;
                            getTypeShapeCall.registerOutParameter(5, OracleTypes.BLOB);
                            getTypeShapeCall.execute();
                            if (getTypeShapeCall.getInt(1) != 0) {
                                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 74, this.typeNameByUser).fillInStackTrace());
                            }
                        }
                    }
                    this.toid = getTypeShapeCall.getBytes(3);
                    this.typeVersion = NUMBER.toInt(getTypeShapeCall.getBytes(4));
                    if (!useBlob) {
                        myTDS = getTypeShapeCall.getBytes(5);
                    } else {
                        try {
                            Blob l_blob = ((OracleCallableStatement) getTypeShapeCall).getBlob(5);
                            InputStream l_OutputStream = l_blob.getBinaryStream();
                            myTDS = new byte[(int) l_blob.length()];
                            l_OutputStream.read(myTDS);
                            l_OutputStream.close();
                            ((BLOB) l_blob).freeTemporary();
                        } catch (IOException ea) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ea).fillInStackTrace());
                        }
                    }
                    this.metaDataInitialized = true;
                    this.isInstanciable = Boolean.valueOf("YES".equalsIgnoreCase(getTypeShapeCall.getString(6)));
                    String superTypeName = getTypeShapeCall.getString(8);
                    if (superTypeName != null) {
                        String str = getTypeShapeCall.getString(7) + superTypeName;
                    }
                    ResultSet subTypeCursor = (ResultSet) getTypeShapeCall.getObject(9);
                    if (subTypeCursor != null) {
                        try {
                            initializeAttributeNames(subTypeCursor);
                            subTypeCursor.close();
                        } finally {
                        }
                    }
                    subTypeCursor = (ResultSet) getTypeShapeCall.getObject(10);
                    if (subTypeCursor != null) {
                        try {
                            initializeSubTypeNames(subTypeCursor);
                            subTypeCursor.close();
                        } finally {
                        }
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata12", "OracleTypeADT.initMetadata:tds={0}", (String) null, (String) null, Parameter.arg(Format.Style.BYTE_ARRAY, myTDS, new long[0]));
                    this.flattenedAttrNum = (Util.getUnsignedByte(myTDS[8]) * 256) + Util.getUnsignedByte(myTDS[9]);
                    setStatusBits(256);
                    byte[] bArr = myTDS;
                    if (getTypeShapeCall != null) {
                        if (0 != 0) {
                            try {
                                getTypeShapeCall.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            getTypeShapeCall.close();
                        }
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return bArr;
                } catch (Throwable th5) {
                    if (getTypeShapeCall != null) {
                        if (0 != 0) {
                            try {
                                getTypeShapeCall.close();
                            } catch (Throwable th6) {
                                th2.addSuppressed(th6);
                            }
                        } else {
                            getTypeShapeCall.close();
                        }
                    }
                    throw th5;
                }
            } finally {
                this.connection.endNonRequestCalls();
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th7) {
                        th.addSuppressed(th7);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private CallableStatement prepareGetTypeShapeCall(OracleConnection conn) throws SQLException {
        CallableStatement getTypeShapeCall = this.connection.prepareCall("begin :1 := sys.dbms_pickler.get_type_shape(:2,:3,:4,:5,:6,:7,:8,:9,:10); end;");
        getTypeShapeCall.registerOutParameter(1, 2);
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "prepareGetTypeShapeCall", "getFullName()={0}, typeNameByUser={1}", null, null, getFullName(), this.typeNameByUser);
        String name = this.typeNameByUser == null ? getFullName() : this.typeNameByUser;
        getTypeShapeCall.setString(2, name);
        getTypeShapeCall.registerOutParameter(2, 12, 256);
        getTypeShapeCall.registerOutParameter(3, -3, 16);
        getTypeShapeCall.registerOutParameter(4, 4);
        getTypeShapeCall.registerOutParameter(5, -4);
        getTypeShapeCall.registerOutParameter(6, 12, 5);
        getTypeShapeCall.registerOutParameter(7, 12, 256);
        getTypeShapeCall.registerOutParameter(8, 12, 256);
        getTypeShapeCall.registerOutParameter(9, -10);
        getTypeShapeCall.registerOutParameter(10, -10);
        return getTypeShapeCall;
    }

    private void initializeAttributeNames(ResultSet attributesCursor) throws SQLException {
        if (!attributesCursor.next()) {
            return;
        }
        if (attributesCursor.getInt(1) != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Unknown image format").fillInStackTrace());
        }
        ArrayList<String> attributeNames = new ArrayList<>(5);
        ArrayList<String> attributeTypeNames = new ArrayList<>(5);
        do {
            attributeNames.add(attributesCursor.getString(2));
            String owner = attributesCursor.getString(5);
            String pack = attributesCursor.getString(6);
            String type = getTypeName(owner, pack, attributesCursor.getString(4));
            attributeTypeNames.add(type);
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeAttributeNames", "{0} : {1} instantiable={2} supertype_name={3} oid={4}", null, null, attributesCursor.getString(2), attributesCursor.getString(4), attributesCursor.getString(8), attributesCursor.getString(10), Parameter.arg(Format.Style.BYTE_ARRAY, attributesCursor.getBytes(7), new long[0]));
        } while (attributesCursor.next());
        this.attrNames = (String[]) attributeNames.toArray(new String[0]);
        this.attrTypeNames = (String[]) attributeTypeNames.toArray(new String[0]);
    }

    private void initializeSubTypeNames(ResultSet subTypeCursor) throws SQLException {
        if (!subTypeCursor.next()) {
            return;
        }
        if (subTypeCursor.getInt(1) != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Unknown image format").fillInStackTrace());
        }
        ArrayList<String> namesList = new ArrayList<>(5);
        do {
            namesList.add(subTypeCursor.getString(2) + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + subTypeCursor.getString(3));
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeSubTypeNames", "Sub-type={0}", (String) null, (String) null, subTypeCursor.getString(3));
        } while (subTypeCursor.next());
        this.subTypeNames = (String[]) namesList.toArray(new String[0]);
    }

    private byte[] initMetadata11_2(OracleConnection conn) throws SQLException {
        Monitor.CloseableLock lock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            byte[] myTDS = null;
            if ((this.statusBits & 256) != 0) {
                return null;
            }
            if (this.sqlName == null) {
                getFullName();
            }
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata11_2", "conn={0} sqlName={1}", null, null, conn, this.sqlName);
            if ((this.statusBits & 256) == 0) {
                CallableStatement cstmt = null;
                this.connection.beginNonRequestCalls();
                try {
                    if (this.tdoCState == 0) {
                        this.tdoCState = this.connection.getTdoCState(this.sqlName.getSchema(), this.sqlName.getSimpleName());
                    }
                    boolean useBlob = false;
                    CallableStatement cstmt2 = this.connection.prepareCall("begin :1 := sys.dbms_pickler.get_type_shape(:2,:3,:4,:5,:6,:7); end;");
                    cstmt2.registerOutParameter(1, 2);
                    cstmt2.registerOutParameter(4, -3, 16);
                    cstmt2.registerOutParameter(5, 4);
                    cstmt2.registerOutParameter(6, -4);
                    cstmt2.registerOutParameter(7, -4);
                    this.sqlName.getSchema();
                    cstmt2.setString(2, this.sqlName.getSchema());
                    cstmt2.setString(3, this.sqlName.getSimpleName());
                    cstmt2.execute();
                    int returnCode = cstmt2.getInt(1);
                    if (returnCode != 0) {
                        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata11_2", "describing type: returnCode={0}", (String) null, (String) null, Integer.valueOf(returnCode));
                        if (returnCode != 24331) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 74, this.sqlName.toString()).fillInStackTrace());
                        }
                        if (returnCode == 24331) {
                            useBlob = true;
                            cstmt2.registerOutParameter(6, OracleTypes.BLOB);
                            cstmt2.execute();
                            if (cstmt2.getInt(1) != 0) {
                                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 74, this.sqlName.toString()).fillInStackTrace());
                            }
                        }
                    }
                    this.toid = cstmt2.getBytes(4);
                    this.typeVersion = NUMBER.toInt(cstmt2.getBytes(5));
                    if (!useBlob) {
                        myTDS = cstmt2.getBytes(6);
                    } else {
                        try {
                            Blob l_blob = ((OracleCallableStatement) cstmt2).getBlob(6);
                            InputStream l_OutputStream = l_blob.getBinaryStream();
                            myTDS = new byte[(int) l_blob.length()];
                            l_OutputStream.read(myTDS);
                            l_OutputStream.close();
                            ((BLOB) l_blob).freeTemporary();
                        } catch (IOException ea) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ea).fillInStackTrace());
                        }
                    }
                    this.metaDataInitialized = true;
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetadata11_2", "tds={0}", (String) null, (String) null, Parameter.arg(Format.Style.BYTE_ARRAY, myTDS, new long[0]));
                    this.flattenedAttrNum = (Util.getUnsignedByte(myTDS[8]) * 256) + Util.getUnsignedByte(myTDS[9]);
                    cstmt2.getBytes(7);
                    if (cstmt2 != null) {
                        cstmt2.close();
                    }
                    this.connection.endNonRequestCalls();
                } catch (Throwable th2) {
                    if (0 != 0) {
                        cstmt.close();
                    }
                    this.connection.endNonRequestCalls();
                    throw th2;
                }
            }
            setStatusBits(256);
            byte[] bArr = myTDS;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return bArr;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    TDSReader parseTDS(byte[] tds_bytes, long index) throws SQLException {
        if (this.attrTypes != null) {
            return null;
        }
        TDSReader tdsReader = new TDSReader(tds_bytes, index);
        long _endOffset = tdsReader.readLong() + tdsReader.offset();
        tdsReader.checkNextByte((byte) 38);
        this.tdsVersion = tdsReader.readByte();
        tdsReader.skipBytes(2);
        this.flattenedAttrNum = tdsReader.readUB2();
        if ((tdsReader.readByte() & 255) == 255) {
            setStatusBits(128);
        }
        long _tdsStart = tdsReader.offset();
        tdsReader.checkNextByte((byte) 41);
        if (tdsReader.readUB2() != 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 47, "parseTDS").fillInStackTrace());
        }
        long _offsetIndexTable = tdsReader.readLong();
        parseTDSrec(tdsReader);
        if (this.tdsVersion >= 3) {
            tdsReader.skip_to(_tdsStart + _offsetIndexTable + 2);
            tdsReader.skipBytes(2 * this.flattenedAttrNum);
            byte _flag = tdsReader.readByte();
            if (tdsReader.isJavaObject(this.tdsVersion, _flag)) {
                setStatusBits(16);
            }
            if (tdsReader.isFinalType(this.tdsVersion, _flag)) {
                setStatusBits(32);
            }
            if (tdsReader.readByte() != 1) {
                setStatusBits(64);
            }
        } else {
            setStatusBits(32);
        }
        tdsReader.skip_to(_endOffset);
        return tdsReader;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                Vector type_tree = new Vector(5);
                this.idx = 1;
                while (true) {
                    OracleType type_object = getNextTypeObject(tdsReader);
                    if (type_object == null) {
                        break;
                    } else {
                        type_tree.addElement(type_object);
                    }
                }
                if (this.opcode == 42) {
                    this.endOfAdt = true;
                    applyTDSpatches(tdsReader);
                }
                this.attrTypes = new OracleType[type_tree.size()];
                type_tree.copyInto(this.attrTypes);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void applyTDSpatches(TDSReader tdsReader) throws SQLException {
        OracleTypeOPAQUE opaque;
        OracleTypeADT newAdt;
        TDSPatch nextPatch = tdsReader.getNextPatch();
        while (true) {
            TDSPatch _patch = nextPatch;
            if (_patch != null) {
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "applyTDSpatches", "sqlName={0}", (String) null, (String) null, this.sqlName);
                tdsReader.moveToPatchPos(_patch);
                int patchSystem = _patch.getType();
                if (patchSystem == 0) {
                    tdsReader.readByte();
                    byte UPTcode = _patch.getUptTypeCode();
                    switch (UPTcode) {
                        case OracleTypes.TINYINT /* -6 */:
                            tdsReader.readLong();
                            break;
                        case -5:
                            break;
                        case 58:
                            OracleNamedType patchElem = _patch.getOwner();
                            if (patchElem.hasName()) {
                                opaque = new OracleTypeOPAQUE(patchElem.getFullName(), this.connection);
                            } else {
                                opaque = new OracleTypeOPAQUE(patchElem.getParent(), patchElem.getOrder(), this.connection);
                            }
                            opaque.parseTDSrec(tdsReader);
                            _patch.apply(opaque);
                            continue;
                        default:
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
                    }
                    OracleNamedType patchElem2 = _patch.getOwner();
                    if (patchElem2.hasName()) {
                        newAdt = new OracleTypeADT(patchElem2.getFullName(), this.connection);
                    } else {
                        newAdt = new OracleTypeADT(patchElem2.getParent(), patchElem2.getOrder(), this.connection);
                    }
                    newAdt.setUptADT();
                    TDSReader newTdsReader = newAdt.parseTDS(tdsReader.tds(), tdsReader.absoluteOffset());
                    tdsReader.skipBytes((int) newTdsReader.offset());
                    _patch.apply(newAdt.cleanup());
                } else if (patchSystem == 1) {
                    debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "applyTDSpatches", "patchSystem={0}", (String) null, (String) null, Integer.valueOf(patchSystem));
                    OracleType newType = getNextTypeObject(tdsReader);
                    _patch.apply(newType, this.opcode);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 47, "parseTDS").fillInStackTrace());
                }
                nextPatch = tdsReader.getNextPatch();
            } else {
                return;
            }
        }
    }

    public OracleNamedType cleanup() {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.attrTypes.length == 1 && (this.attrTypes[0] instanceof OracleTypeCOLLECTION)) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanup", "sqlName={0}", (String) null, (String) null, this.sqlName);
                OracleTypeCOLLECTION col = (OracleTypeCOLLECTION) this.attrTypes[0];
                col.copy_properties(this);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return col;
            }
            if (this.attrTypes.length != 1 || (this.statusBits & 128) == 0 || !(this.attrTypes[0] instanceof OracleTypeUPT) || !(((OracleTypeUPT) this.attrTypes[0]).realType instanceof OracleTypeOPAQUE)) {
                return this;
            }
            OracleTypeOPAQUE opq = (OracleTypeOPAQUE) ((OracleTypeUPT) this.attrTypes[0]).realType;
            opq.copy_properties(this);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return opq;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    void copy_properties(OracleTypeADT source) {
        this.sqlName = source.sqlName;
        this.typeNameByUser = source.typeNameByUser;
        this.parent = source.parent;
        this.idx = source.idx;
        this.connection = source.connection;
        this.toid = source.toid;
        this.tdsVersion = source.tdsVersion;
        this.typeVersion = source.typeVersion;
        this.tdoCState = source.tdoCState;
        this.endOfAdt = source.endOfAdt;
        this.attrTypeNames = source.attrTypeNames;
        if (this.attrNames != null && source.attrTypeNames == null) {
            this.attrNames = source.attrNames;
        }
    }

    OracleType getNextTypeObject(TDSReader tdsReader) throws SQLException {
        while (true) {
            this.opcode = tdsReader.readByte();
            if (this.opcode != 43) {
                if (this.opcode != 44) {
                    break;
                }
                byte _flag = tdsReader.readByte();
                if (tdsReader.isJavaObject(3, _flag)) {
                    setStatusBits(16);
                }
            }
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getNextTypeObject", "opcode={0}", (String) null, (String) null, Integer.valueOf(this.opcode));
        switch (this.opcode) {
            case 1:
                OracleTypeCHAR ochar = new OracleTypeCHAR(this.connection, 1);
                ochar.parseTDSrec(tdsReader);
                this.idx++;
                return ochar;
            case 2:
                OracleTypeDATE odate = new OracleTypeDATE();
                odate.parseTDSrec(tdsReader);
                this.idx++;
                return odate;
            case 3:
                OracleTypeNUMBER onum = new OracleTypeNUMBER(3);
                onum.parseTDSrec(tdsReader);
                this.idx++;
                return onum;
            case 4:
                OracleTypeNUMBER onum2 = new OracleTypeNUMBER(8);
                onum2.parseTDSrec(tdsReader);
                this.idx++;
                return onum2;
            case 5:
                OracleTypeFLOAT onum3 = new OracleTypeFLOAT();
                onum3.parseTDSrec(tdsReader);
                this.idx++;
                return onum3;
            case 6:
                OracleTypeNUMBER onum4 = new OracleTypeNUMBER(2);
                onum4.parseTDSrec(tdsReader);
                this.idx++;
                return onum4;
            case 7:
                OracleTypeCHAR ochar2 = new OracleTypeCHAR(this.connection, 12);
                ochar2.parseTDSrec(tdsReader);
                this.idx++;
                return ochar2;
            case 8:
                OracleTypeSINT32 onum5 = new OracleTypeSINT32(false);
                onum5.parseTDSrec(tdsReader);
                this.idx++;
                return onum5;
            case 9:
                OracleTypeREF oref = new OracleTypeREF(this, this.idx, this.connection);
                oref.parseTDSrec(tdsReader);
                this.idx++;
                return oref;
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
            case 17:
            case 18:
            case 20:
            case 22:
            case 25:
            case 26:
            case 32:
            case 34:
            case 35:
            case 36:
            case 38:
            case 41:
            case 43:
            case 44:
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 48, "get_next_type: " + this.opcode).fillInStackTrace());
            case 16:
                OracleTypeROWID oracleRowId = new OracleTypeROWID();
                oracleRowId.parseTDSrec(tdsReader);
                this.idx++;
                return oracleRowId;
            case 19:
                OracleTypeRAW oraw = new OracleTypeRAW();
                oraw.parseTDSrec(tdsReader);
                this.idx++;
                return oraw;
            case 21:
                OracleTypeTIMESTAMP timestamp = new OracleTypeTIMESTAMP(this.connection);
                timestamp.parseTDSrec(tdsReader);
                this.idx++;
                return timestamp;
            case 23:
                OracleTypeTIMESTAMPTZ timestamp2 = new OracleTypeTIMESTAMPTZ(this.connection);
                timestamp2.parseTDSrec(tdsReader);
                this.idx++;
                return timestamp2;
            case 24:
                OracleTypeINTERVAL interval = new OracleTypeINTERVAL(this.connection);
                interval.parseTDSrec(tdsReader);
                this.idx++;
                return interval;
            case 27:
                OracleTypeUPT oupt = new OracleTypeUPT(this, this.idx, this.connection);
                oupt.parseTDSrec(tdsReader);
                this.idx++;
                return oupt;
            case 28:
                OracleTypeCOLLECTION ocollection = new OracleTypeCOLLECTION(this, this.idx, this.connection);
                ocollection.parseTDSrec(tdsReader);
                this.idx++;
                return ocollection;
            case 29:
                OracleTypeCLOB oclob = new OracleTypeCLOB(this.connection);
                oclob.parseTDSrec(tdsReader);
                if (this.attrTypeNames != null && this.attrTypeNames.length > this.idx - 1) {
                    oclob.form = "NCLOB".equals(this.attrTypeNames[this.idx - 1]) ? 2 : 1;
                } else if (this.sqlName != null && !this.endOfAdt) {
                    this.connection.getForm(this, oclob, this.idx);
                }
                this.idx++;
                return oclob;
            case 30:
                OracleTypeBLOB oblob = new OracleTypeBLOB(this.connection);
                oblob.parseTDSrec(tdsReader);
                this.idx++;
                return oblob;
            case 31:
                OracleTypeBFILE obfile = new OracleTypeBFILE(this.connection);
                obfile.parseTDSrec(tdsReader);
                this.idx++;
                return obfile;
            case 33:
                OracleTypeTIMESTAMPLTZ timestamp3 = new OracleTypeTIMESTAMPLTZ(this.connection);
                timestamp3.parseTDSrec(tdsReader);
                this.idx++;
                return timestamp3;
            case 37:
                OracleTypeBINARY_FLOAT bfloat = new OracleTypeBINARY_FLOAT();
                bfloat.parseTDSrec(tdsReader);
                this.idx++;
                return bfloat;
            case 39:
                OracleTypeADT oadt = new OracleTypeADT(this, this.idx, this.connection);
                oadt.setEmbeddedADT();
                oadt.parseTDSrec(tdsReader);
                this.idx++;
                return oadt;
            case 40:
            case 42:
                return null;
            case 45:
                OracleTypeBINARY_DOUBLE bdouble = new OracleTypeBINARY_DOUBLE();
                bdouble.parseTDSrec(tdsReader);
                this.idx++;
                return bdouble;
        }
    }

    @Override // oracle.jdbc.oracore.OracleNamedType
    public byte[] linearize(Datum data) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] bArrPickle81 = pickle81(data);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return bArrPickle81;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.oracore.OracleNamedType
    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, int style, Map map) throws SQLException {
        return _unlinearize(pickled_bytes, offset, container, style, map);
    }

    /* JADX WARN: Multi-variable type inference failed */
    public Datum _unlinearize(byte[] pickled_bytes, long offset, Datum datum, int style, Map map) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        if (pickled_bytes != null) {
            try {
                try {
                    PickleContext context = new PickleContext(pickled_bytes, offset);
                    Datum datum2 = (Datum) unpickle81(context, (OracleStruct) datum, 1, style, map);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return datum2;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                }
            } else {
                lock.close();
            }
        }
        return null;
    }

    protected OracleStruct unpickle81(PickleContext context, OracleStruct container, int style, int attrStyle, Map attrMap) throws SQLException {
        OracleStruct the_adt = container;
        long _offset = context.offset();
        byte flags = context.readByte();
        if (!PickleContext.is81format(flags)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image is not in 8.1 format").fillInStackTrace());
        }
        if (PickleContext.isCollectionImage_pctx(flags)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image is a collection image, expecting ADT").fillInStackTrace());
        }
        if (!context.readAndCheckVersion()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Image version is not recognized").fillInStackTrace());
        }
        switch (style) {
            case 3:
                long length = context.readLength();
                the_adt = unpickle81Prefix(context, the_adt, flags);
                if (the_adt == null) {
                    StructDescriptor desc = createStructDescriptor();
                    the_adt = createByteSTRUCT(desc, (byte[]) null);
                }
                the_adt.setImage(context.image(), _offset, 0L);
                the_adt.setImageLength(length);
                context.skipTo(_offset + length);
                break;
            case 9:
                context.skipBytes(context.readLength(true) - 2);
                break;
            default:
                context.skipLength();
                the_adt = unpickle81Prefix(context, the_adt, flags);
                if (the_adt == null) {
                    StructDescriptor desc2 = createStructDescriptor();
                    the_adt = createByteSTRUCT(desc2, (byte[]) null);
                }
                OracleType[] _attrs = the_adt.getDescriptor().getOracleTypeADT().getAttrTypes();
                switch (attrStyle) {
                    case 1:
                        Datum[] datums = new Datum[_attrs.length];
                        for (int i = 0; i < _attrs.length; i++) {
                            datums[i] = (Datum) _attrs[i].unpickle81rec(context, attrStyle, attrMap);
                        }
                        the_adt.setDatumArray(datums);
                        break;
                    case 2:
                        Object[] oarray = new Object[_attrs.length];
                        for (int i2 = 0; i2 < _attrs.length; i2++) {
                            oarray[i2] = _attrs[i2].unpickle81rec(context, attrStyle, attrMap);
                        }
                        the_adt.setObjArray(oarray);
                        break;
                    default:
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
                }
        }
        return the_adt;
    }

    protected OracleStruct unpickle81Prefix(PickleContext context, OracleStruct container, byte flags) throws SQLException {
        OracleStruct the_adt = container;
        if (PickleContext.hasPrefix(flags)) {
            long _endOffset = context.readLength() + context.absoluteOffset();
            byte _prefixFlag = context.readByte();
            byte _TypeInfoEncodedBits = (byte) (_prefixFlag & 12);
            boolean z = _TypeInfoEncodedBits == 0;
            boolean hasTypeInfoTOID = _TypeInfoEncodedBits == 4;
            boolean hasTypeInfoTOBJN = _TypeInfoEncodedBits == 8;
            boolean hasTypeInfoTDS = _TypeInfoEncodedBits == 12;
            boolean hasVersion = (_prefixFlag & 16) != 0;
            if (hasTypeInfoTOID) {
                byte[] _toid = context.readBytes(16);
                String _subTypename = toid2typename(this.connection, _toid);
                StructDescriptor desc = (StructDescriptor) TypeDescriptor.getTypeDescriptor(_subTypename, this.connection);
                if (the_adt == null) {
                    the_adt = createByteSTRUCT(desc, (byte[]) null);
                } else {
                    the_adt.setDescriptor(desc);
                }
            }
            if (hasVersion) {
                context.readLength();
            }
            if (hasTypeInfoTOBJN | hasTypeInfoTDS) {
                throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
            }
            context.skipTo(_endOffset);
        }
        return the_adt;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object unpickle81rec(PickleContext context, int type, Map map) throws SQLException {
        byte firstbyte = context.readByte();
        byte immemb = 0;
        if (PickleContext.isAtomicNull(firstbyte)) {
            return null;
        }
        if (PickleContext.isImmediatelyEmbeddedNull(firstbyte)) {
            immemb = context.readByte();
        }
        STRUCT s = unpickle81datum(context, firstbyte, immemb);
        return toObject(s, type, map);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object unpickle81rec(PickleContext context, byte len_flags, int type, Map map) throws SQLException {
        STRUCT s = unpickle81datum(context, len_flags, (byte) 0);
        return toObject(s, type, map);
    }

    private STRUCT unpickle81datum(PickleContext context, byte len_flags, byte immemb) throws SQLException {
        Object firstElem;
        int size = getNumAttrs();
        StructDescriptor desc = createStructDescriptor();
        STRUCT the_adt = createByteSTRUCT(desc, (byte[]) null);
        OracleType this_attr = getAttrTypeAt(0);
        if (PickleContext.isImmediatelyEmbeddedNull(len_flags) && immemb == 1) {
            firstElem = null;
        } else if (PickleContext.isImmediatelyEmbeddedNull(len_flags)) {
            firstElem = ((OracleTypeADT) this_attr).unpickle81datum(context, len_flags, (byte) (immemb - 1));
        } else if (PickleContext.isElementNull(len_flags)) {
            if (this_attr.getTypeCode() == 2002 || this_attr.getTypeCode() == 2008) {
                firstElem = this_attr.unpickle81datumAsNull(context, len_flags, immemb);
            } else {
                firstElem = null;
            }
        } else {
            firstElem = this_attr.unpickle81rec(context, len_flags, 1, null);
        }
        Datum[] oarray = new Datum[size];
        oarray[0] = (Datum) firstElem;
        for (int ctr = 1; ctr < size; ctr++) {
            oarray[ctr] = (Datum) getAttrTypeAt(ctr).unpickle81rec(context, 1, null);
        }
        the_adt.setDatumArray(oarray);
        return the_adt;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Datum unpickle81datumAsNull(PickleContext context, byte len_flags, byte immemb) throws SQLException {
        int ctr;
        int size = getNumAttrs();
        StructDescriptor desc = createStructDescriptor();
        STRUCT the_adt = createByteSTRUCT(desc, (byte[]) null);
        Datum[] oarray = new Datum[size];
        OracleType this_attr = getAttrTypeAt(0);
        if (this_attr.getTypeCode() == 2002 || this_attr.getTypeCode() == 2008) {
            ctr = 0 + 1;
            oarray[0] = this_attr.unpickle81datumAsNull(context, len_flags, immemb);
        } else {
            ctr = 0 + 1;
            oarray[0] = (Datum) null;
        }
        while (ctr < size) {
            OracleType this_attr2 = getAttrTypeAt(ctr);
            if (this_attr2.getTypeCode() == 2002 || this_attr2.getTypeCode() == 2008) {
                oarray[ctr] = (Datum) this_attr2.unpickle81rec(context, 1, null);
            } else {
                oarray[ctr] = (Datum) this_attr2.unpickle81rec(context, 1, null);
            }
            ctr++;
        }
        the_adt.setDatumArray(oarray);
        return the_adt;
    }

    public byte[] pickle81(Datum data) throws SQLException {
        PickleContext ctx = new PickleContext();
        ctx.initStream();
        pickle81(ctx, data);
        byte[] pickledBytes = ctx.stream2Bytes();
        data.setShareBytes(pickledBytes);
        return pickledBytes;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext ctx, Datum data) throws SQLException {
        int lenOffset = ctx.offset() + 2;
        int imglen = 0 + ctx.writeImageHeader(shouldHavePrefix()) + pickle81Prefix(ctx) + pickle81rec(ctx, data, 0);
        ctx.patchImageLen(lenOffset, imglen);
        return imglen;
    }

    private boolean hasVersion() {
        return this.typeVersion > 1;
    }

    private boolean needsToid() {
        if (this.isTransient) {
            return false;
        }
        return (this.statusBits & 64) != 0 || (this.statusBits & 32) == 0 || hasVersion();
    }

    private boolean shouldHavePrefix() {
        if (this.isTransient) {
            return false;
        }
        return hasVersion() || needsToid();
    }

    protected int pickle81Prefix(PickleContext context) throws SQLException {
        if (shouldHavePrefix()) {
            int _prefix_flag = 1;
            int precalculated_length = 1;
            if (needsToid()) {
                precalculated_length = 1 + getTOID().length;
                _prefix_flag = 1 | 4;
            }
            if (hasVersion()) {
                _prefix_flag |= 16;
                if (this.typeVersion > PickleContext.KOPI20_LN_MAXV) {
                    precalculated_length += 5;
                } else {
                    precalculated_length += 2;
                }
            }
            int len = context.writeLength(precalculated_length) + context.writeData((byte) _prefix_flag);
            if (needsToid()) {
                len += context.writeData(this.toid);
            }
            if (hasVersion()) {
                if (this.typeVersion > PickleContext.KOPI20_LN_MAXV) {
                    len += context.writeLength(this.typeVersion);
                } else {
                    len += context.writeSB2(this.typeVersion);
                }
            }
            return len;
        }
        return 0;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private int pickle81rec(PickleContext ctx, Datum datum, int depth) throws SQLException {
        int i;
        int iPickle81;
        int imglen = 0;
        if (!this.metaDataInitialized) {
            copy_properties((OracleTypeADT) ((OracleStruct) datum).getDescriptor().getPickler());
        }
        Datum[] values = ((OracleStruct) datum).getOracleAttributes();
        int field_num = values.length;
        int ctr = 0;
        OracleType this_attr = getAttrTypeAt(0);
        if ((this_attr instanceof OracleTypeADT) && !(this_attr instanceof OracleTypeCOLLECTION) && !(this_attr instanceof OracleTypeUPT)) {
            debugp(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "pickle81rec", "sqlName={0} check first null case and this attr={1} depth={2}", null, null, () -> {
                Object[] objArr = new Object[3];
                objArr[0] = this.sqlName;
                objArr[1] = values[0] == null ? null : Parameter.arg(Format.Style.BYTE_ARRAY_CLONE, values[0].shareBytes(), new long[0]);
                objArr[2] = Integer.valueOf(depth);
                return objArr;
            });
            ctr = 1;
            imglen = values[0] == null ? depth > 0 ? 0 + ctx.writeImmediatelyEmbeddedElementNull((byte) depth) : 0 + ctx.writeAtomicNull() : 0 + ((OracleTypeADT) this_attr).pickle81rec(ctx, values[0], depth + 1);
        }
        while (ctr < field_num) {
            OracleType this_attr2 = getAttrTypeAt(ctr);
            int idx = ctr;
            debugp(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "pickle81rec", "sqlName={0} check idx={1} attr={2} depth={3}", null, null, () -> {
                Object[] objArr = new Object[4];
                objArr[0] = this.sqlName;
                objArr[1] = Integer.valueOf(idx);
                objArr[2] = values[idx] == null ? null : Parameter.arg(Format.Style.BYTE_ARRAY_CLONE, values[idx].shareBytes(), new long[0]);
                objArr[3] = Integer.valueOf(depth);
                return objArr;
            });
            if (values[ctr] == null) {
                if ((this_attr2 instanceof OracleTypeADT) && !(this_attr2 instanceof OracleTypeCOLLECTION) && !(this_attr2 instanceof OracleTypeUPT)) {
                    i = imglen;
                    iPickle81 = ctx.writeAtomicNull();
                } else {
                    i = imglen;
                    iPickle81 = ctx.writeElementNull();
                }
            } else if ((this_attr2 instanceof OracleTypeADT) && !(this_attr2 instanceof OracleTypeCOLLECTION) && !(this_attr2 instanceof OracleTypeUPT)) {
                i = imglen;
                iPickle81 = ((OracleTypeADT) this_attr2).pickle81rec(ctx, values[ctr], 1);
            } else {
                i = imglen;
                iPickle81 = this_attr2.pickle81(ctx, values[ctr]);
            }
            imglen = i + iPickle81;
            ctr++;
        }
        return imglen;
    }

    private Object toObject(STRUCT s, int type, Map map) throws SQLException {
        switch (type) {
            case 1:
                return s;
            case 2:
                if (s != null) {
                    return s.toJdbc(map);
                }
                return null;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
        }
    }

    public String getAttributeType(int idx) throws SQLException {
        return getAttributeType(idx, true);
    }

    public String getAttributeType(int idx, boolean force) throws SQLException {
        if (force) {
            if (this.sqlName == null) {
                getFullName();
            }
            if (this.attrNames == null) {
                initADTAttrNames();
            }
        }
        if (idx < 1 || (this.attrTypeNames != null && idx > this.attrTypeNames.length)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid index").fillInStackTrace());
        }
        if (this.attrTypeNames != null) {
            return this.attrTypeNames[idx - 1];
        }
        return null;
    }

    public String getAttributeName(int idx) throws SQLException {
        if (this.attrNames == null) {
            initADTAttrNames();
        }
        String ret = null;
        if (this.attrNames != null) {
            if (idx < 1 || idx > this.attrNames.length) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid index").fillInStackTrace());
            }
            ret = this.attrNames[idx - 1];
        }
        return ret;
    }

    public String getAttributeName(int idx, boolean force) throws SQLException {
        if (force && this.connection != null) {
            return getAttributeName(idx);
        }
        if (idx < 1 || (this.attrNames != null && idx > this.attrNames.length)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Invalid index").fillInStackTrace());
        }
        if (this.attrNames != null) {
            return this.attrNames[idx - 1];
        }
        return null;
    }

    /* JADX WARN: Finally extract failed */
    private void initADTAttrNames() throws SQLException {
        if (this.connection == null) {
            return;
        }
        if (this.sqlName == null) {
            getFullName();
        }
        if (this.toid != null) {
            initADTAttrNamesUsingTOID();
            return;
        }
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            CallableStatement cstmt = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            String[] temp_attrNames = new String[this.attrTypes.length];
            String[] temp_attrTypeNames = new String[this.attrTypes.length];
            int l_index = 0;
            if (this.attrNames == null) {
                this.connection.beginNonRequestCalls();
                try {
                    int state = this.sqlName.getSchema().equals(this.connection.getDefaultSchemaNameForNamedTypes()) ? 0 : 7;
                    while (state != 11) {
                        switch (state) {
                            case 0:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try CURRENT_USER_OBJECT, bind={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                                ps.setString(1, this.sqlName.getSimpleName());
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 5;
                                break;
                            case 1:
                                if (this.connection.getVersionNumber() >= 10000) {
                                    state = 2;
                                }
                            case 2:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try CURRENT_USER_SYNONYM, bind={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                                ps.setString(1, this.sqlName.getSimpleName());
                                ps.setString(2, this.sqlName.getSimpleName());
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 3;
                                break;
                            case 3:
                                if (this.connection.getVersionNumber() >= 10000) {
                                    state = 4;
                                }
                            case 4:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try CURRENT_USER_PUBLIC_SYNONYM, bind={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                                ps.setString(1, this.sqlName.getSimpleName());
                                ps.setString(2, this.sqlName.getSimpleName());
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 8;
                                break;
                            case 5:
                                if (this.connection.getVersionNumber() >= 10000) {
                                    state = 6;
                                }
                            case 6:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try POSSIBLY_OTHER_USER_OBJECT, bind={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                cstmt = (OracleCallableStatement) this.connection.prepareCall(getSqlHint() + sqlString[state]);
                                cstmt.setString(1, this.sqlName.getSimpleName());
                                cstmt.setString(3, this.sqlName.getSimpleName());
                                cstmt.registerOutParameter(2, -10);
                                cstmt.execute();
                                rs = ((OracleCallableStatement) cstmt).getCursor(2);
                                rs.setFetchSize(1);
                                state = 1;
                                break;
                            case 7:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try OTHER_USER_OBJECT, bind : schema={0} simple name={1}", null, null, this.sqlName.getSchema(), this.sqlName.getSimpleName());
                                ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                                ps.setString(1, this.sqlName.getSchema());
                                ps.setString(2, this.sqlName.getSimpleName());
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 8;
                                break;
                            case 8:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try OTHER_USER_SYNONYM, bind : simple name={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                ps = this.connection.prepareStatement(getSqlHint() + sqlString[state]);
                                ps.setString(1, this.sqlName.getSimpleName());
                                ps.setString(2, this.sqlName.getSimpleName());
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 9;
                                break;
                            case 9:
                                if (this.connection.getVersionNumber() >= 10000) {
                                    state = 10;
                                }
                            case 10:
                                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNames", "try PUBLIC_SYNONYM, bind : simple name={0}", (String) null, (String) null, this.sqlName.getSimpleName());
                                cstmt = this.connection.prepareCall(getSqlHint() + sqlString[state]);
                                cstmt.setString(1, this.sqlName.getSimpleName());
                                cstmt.registerOutParameter(2, -10);
                                cstmt.execute();
                                rs = ((OracleCallableStatement) cstmt).getCursor(2);
                                state = 11;
                                break;
                        }
                        if (rs != null) {
                            l_index = 0;
                            while (l_index < this.attrTypes.length && rs.next()) {
                                try {
                                    if (rs.getInt(1) != l_index + 1) {
                                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "inconsistent ADT attribute").fillInStackTrace());
                                    }
                                    temp_attrNames[l_index] = rs.getString(2);
                                    String owner = rs.getString(4);
                                    String type = rs.getString(3);
                                    temp_attrTypeNames[l_index] = (owner == null || owner.isEmpty()) ? type : SQLName.getTypeName(owner, type);
                                    l_index++;
                                } catch (Throwable th2) {
                                    if (rs != null) {
                                        rs.close();
                                    }
                                    if (ps != null) {
                                        ps.close();
                                    }
                                    if (cstmt != null) {
                                        cstmt.close();
                                    }
                                    throw th2;
                                }
                            }
                        }
                        if (l_index != 0) {
                            this.attrTypeNames = temp_attrTypeNames;
                            this.attrNames = temp_attrNames;
                            state = 11;
                        } else {
                            if (rs != null) {
                                rs.close();
                            }
                            if (ps != null) {
                                ps.close();
                            }
                            if (cstmt != null) {
                                cstmt.close();
                            }
                        }
                        if (rs != null) {
                            rs.close();
                        }
                        if (ps != null) {
                            ps.close();
                        }
                        if (cstmt != null) {
                            cstmt.close();
                        }
                    }
                    this.connection.endNonRequestCalls();
                } catch (Throwable th3) {
                    this.connection.endNonRequestCalls();
                    throw th3;
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    /* JADX WARN: Finally extract failed */
    private void initADTAttrNamesUsingTOID() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            CallableStatement cstmt = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            String[] temp_attrNames = new String[this.attrTypes.length];
            String[] temp_attrTypeNames = new String[this.attrTypes.length];
            int l_index = 0;
            if (this.attrNames == null) {
                this.connection.beginNonRequestCalls();
                try {
                    int state = this.sqlName.getSchema().equals(this.connection.getDefaultSchemaNameForNamedTypes()) ? 0 : 1;
                    while (state != 11) {
                        switch (state) {
                            case 0:
                                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNamesUsingTOID", "try SEARCH_USER_TYPES, bind={0}", (String) null, (String) null, Parameter.arg(Format.Style.BYTE_ARRAY, this.toid, new long[0]));
                                ps = this.connection.prepareStatement(getSqlHint() + sqlStringTOID[state]);
                                ps.setBytes(1, this.toid);
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 1;
                                break;
                            case 1:
                                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initADTAttrNamesUsingTOID", "SEARCH_ALL_TYPES, bind: ", (String) null, (String) null, Parameter.arg(Format.Style.BYTE_ARRAY, this.toid, new long[0]));
                                ps = this.connection.prepareStatement(getSqlHint() + sqlStringTOID[state]);
                                ps.setBytes(1, this.toid);
                                ps.setFetchSize(this.idx);
                                rs = ps.executeQuery();
                                state = 11;
                                break;
                        }
                        if (rs != null) {
                            l_index = 0;
                            while (l_index < this.attrTypes.length && rs.next()) {
                                try {
                                    if (rs.getInt(1) != l_index + 1 && state == 1) {
                                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "inconsistent ADT attribute").fillInStackTrace());
                                    }
                                    temp_attrNames[l_index] = rs.getString(2);
                                    String owner = rs.getString(4);
                                    String type = rs.getString(3);
                                    temp_attrTypeNames[l_index] = (owner == null || owner.isEmpty()) ? type : SQLName.getTypeName(owner, type);
                                    l_index++;
                                } catch (Throwable th2) {
                                    if (rs != null) {
                                        rs.close();
                                    }
                                    if (ps != null) {
                                        ps.close();
                                    }
                                    if (0 != 0) {
                                        cstmt.close();
                                    }
                                    throw th2;
                                }
                            }
                        }
                        if (l_index != 0) {
                            this.attrTypeNames = temp_attrTypeNames;
                            this.attrNames = temp_attrNames;
                            state = 11;
                        } else {
                            if (rs != null) {
                                rs.close();
                            }
                            if (ps != null) {
                                ps.close();
                            }
                        }
                        if (rs != null) {
                            rs.close();
                        }
                        if (ps != null) {
                            ps.close();
                        }
                        if (0 != 0) {
                            cstmt.close();
                        }
                    }
                    this.connection.endNonRequestCalls();
                } catch (Throwable th3) {
                    this.connection.endNonRequestCalls();
                    throw th3;
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    StructDescriptor createStructDescriptor() throws SQLException {
        StructDescriptor returnValue = (StructDescriptor) this.descriptor;
        if (this.typeNameByUser == null && this.parent != null && this.parent.getTypeCode() == 2003) {
            this.typeNameByUser = this.parent.getAttributeType(1);
        }
        if (returnValue == null) {
            returnValue = new StructDescriptor(this, this.connection);
        }
        return returnValue;
    }

    STRUCT createObjSTRUCT(StructDescriptor desc, Object[] value) throws SQLException {
        if ((this.statusBits & 16) != 0) {
            return new JAVA_STRUCT(desc, this.connection, value);
        }
        return new STRUCT(desc, this.connection, value);
    }

    STRUCT createByteSTRUCT(StructDescriptor desc, byte[] value) throws SQLException {
        if ((this.statusBits & 16) != 0) {
            return new JAVA_STRUCT(desc, value, this.connection);
        }
        return new STRUCT(desc, value, this.connection);
    }

    public static String getSubtypeName(Connection conn, byte[] image, long offset) throws SQLException {
        Monitor.CloseableLock lock = ((OracleConnection) conn).acquireCloseableLock();
        Throwable th = null;
        try {
            PickleContext context = new PickleContext(image, offset);
            byte flags = context.readByte();
            if (!PickleContext.is81format(flags) || PickleContext.isCollectionImage_pctx(flags) || !PickleContext.hasPrefix(flags)) {
                return null;
            }
            if (!context.readAndCheckVersion()) {
                throw ((SQLException) DatabaseError.createSqlException(1, "Image version is not recognized").fillInStackTrace());
            }
            context.skipLength();
            context.skipLength();
            if ((context.readByte() & 4) == 0) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            }
            byte[] _toid = context.readBytes(16);
            String str = toid2typename(conn, _toid);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Finally extract failed */
    public static String toid2typename(Connection conn, byte[] toid) throws SQLException {
        Monitor.CloseableLock lock = ((OracleConnection) conn).acquireCloseableLock();
        Throwable th = null;
        try {
            String typename = (String) ((OracleConnection) conn).getDescriptor(toid);
            if (typename == null) {
                PreparedStatement pstmt = null;
                ResultSet rset = null;
                ((OracleConnection) conn).beginNonRequestCalls();
                try {
                    PreparedStatement pstmt2 = conn.prepareStatement("select owner, type_name from all_types where type_oid = :1");
                    pstmt2.setBytes(1, toid);
                    ResultSet rset2 = pstmt2.executeQuery();
                    if (rset2.next()) {
                        typename = SQLName.getTypeName(rset2.getString(1), rset2.getString(2));
                        ((OracleConnection) conn).putDescriptor(toid, typename);
                        if (rset2 != null) {
                            rset2.close();
                        }
                        if (pstmt2 != null) {
                            pstmt2.close();
                        }
                        ((OracleConnection) conn).endNonRequestCalls();
                    } else {
                        throw ((SQLException) DatabaseError.createSqlException(1, "Invalid type oid").fillInStackTrace());
                    }
                } catch (Throwable th2) {
                    if (0 != 0) {
                        rset.close();
                    }
                    if (0 != 0) {
                        pstmt.close();
                    }
                    ((OracleConnection) conn).endNonRequestCalls();
                    throw th2;
                }
            }
            return typename;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    public int getTdsVersion() {
        return this.tdsVersion;
    }

    public void printDebug() {
    }

    public byte[] getTOID() {
        try {
            if (this.toid == null) {
                initMetadata(this.connection);
            }
        } catch (SQLException e) {
        }
        return this.toid;
    }

    public int getImageFormatVersion() {
        return PickleContext.KOPI20_VERSION;
    }

    @Override // oracle.jdbc.oracore.OracleNamedType
    public int getTypeVersion() {
        try {
            if (this.typeVersion == -1) {
                initMetadata(this.connection);
            }
        } catch (SQLException e) {
        }
        return this.typeVersion;
    }

    public int getCharSet() {
        return this.charSetId;
    }

    public int getCharSetForm() {
        return this.charSetForm;
    }

    public long getTdoCState() {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.tdoCState == 0) {
                    if (this.connection.getVersionNumber() >= 12000 && this.typeNameByUser != null) {
                        this.tdoCState = this.connection.getTdoCState(this.typeNameByUser);
                    } else {
                        getFullName();
                        this.tdoCState = this.connection.getTdoCState(this.sqlName.getSchema(), this.sqlName.getSimpleName());
                    }
                }
            } catch (SQLException e) {
            }
            long j = this.tdoCState;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return j;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public long getFIXED_DATA_SIZE() {
        try {
            return getFixedDataSize();
        } catch (SQLException e) {
            return 0L;
        }
    }

    public long getFixedDataSize() throws SQLException {
        return this.fixedDataSize;
    }

    public int getAlignmentReq() throws SQLException {
        return this.alignmentRequirement;
    }

    public int getNumAttrs() throws SQLException {
        if (this.attrTypes == null && this.connection != null) {
            init(this.connection);
        }
        return this.attrTypes.length;
    }

    public OracleType getAttrTypeAt(int idx) throws SQLException {
        if (this.attrTypes == null && this.connection != null) {
            init(this.connection);
        }
        return this.attrTypes[idx];
    }

    public boolean isEmbeddedADT() throws SQLException {
        return (this.statusBits & 2) != 0;
    }

    public boolean isUptADT() throws SQLException {
        return (this.statusBits & 4) != 0;
    }

    public boolean isTopADT() throws SQLException {
        return (this.statusBits & 1) != 0;
    }

    public void setStatus(int status) throws SQLException {
        this.statusBits = status;
    }

    void setEmbeddedADT() throws SQLException {
        maskAndSetStatusBits(-16, 2);
    }

    void setUptADT() throws SQLException {
        maskAndSetStatusBits(-16, 4);
    }

    public boolean isSubType() throws SQLException {
        return (this.statusBits & 64) != 0;
    }

    public boolean isFinalType() throws SQLException {
        return ((this.statusBits & 32) != 0) | ((this.statusBits & 2) != 0);
    }

    public boolean isJavaObject() throws SQLException {
        return (this.statusBits & 16) != 0;
    }

    public int getStatus() throws SQLException {
        if ((this.statusBits & 1) != 0 && (this.statusBits & 256) == 0) {
            init(this.connection);
        }
        return this.statusBits;
    }

    public static OracleTypeADT shallowClone(OracleTypeADT adt) throws SQLException {
        OracleTypeADT newADT = new OracleTypeADT();
        shallowCopy(adt, newADT);
        return newADT;
    }

    public static void shallowCopy(OracleTypeADT from, OracleTypeADT to) throws SQLException {
        to.connection = from.connection;
        to.sqlName = from.sqlName;
        to.parent = from.parent;
        to.idx = from.idx;
        to.descriptor = from.descriptor;
        to.statusBits = from.statusBits;
        to.typeCode = from.typeCode;
        to.dbTypeCode = from.dbTypeCode;
        to.tdsVersion = from.tdsVersion;
        to.typeVersion = from.typeVersion;
        to.fixedDataSize = from.fixedDataSize;
        to.alignmentRequirement = from.alignmentRequirement;
        to.attrTypes = from.attrTypes;
        to.sqlName = from.sqlName;
        to.tdoCState = from.tdoCState;
        to.toid = from.toid;
        to.charSetId = from.charSetId;
        to.charSetForm = from.charSetForm;
        to.flattenedAttrNum = from.flattenedAttrNum;
        to.statusBits = from.statusBits;
        to.attrNames = from.attrNames;
        to.attrTypeNames = from.attrTypeNames;
        to.opcode = from.opcode;
        to.idx = from.idx;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.statusBits);
        out.writeInt(this.tdsVersion);
        out.writeInt(this.typeVersion);
        out.writeObject(null);
        out.writeObject(null);
        out.writeLong(this.fixedDataSize);
        out.writeInt(this.alignmentRequirement);
        out.writeObject(this.attrTypes);
        out.writeObject(this.attrNames);
        out.writeObject(this.attrTypeNames);
        out.writeLong(this.tdoCState);
        out.writeObject(this.toid);
        out.writeObject(null);
        out.writeInt(this.charSetId);
        out.writeInt(this.charSetForm);
        out.writeBoolean(true);
        out.writeInt(this.flattenedAttrNum);
        out.writeObject(this.monitorLock);
    }

    private void readObject(ObjectInputStream in) throws ClassNotFoundException, IOException {
        this.statusBits = in.readInt();
        this.tdsVersion = in.readInt();
        this.typeVersion = in.readInt();
        in.readObject();
        in.readObject();
        in.readLong();
        in.readInt();
        this.attrTypes = (OracleType[]) in.readObject();
        this.attrNames = (String[]) in.readObject();
        this.attrTypeNames = (String[]) in.readObject();
        in.readLong();
        this.toid = (byte[]) in.readObject();
        in.readObject();
        this.charSetId = in.readInt();
        this.charSetForm = in.readInt();
        in.readBoolean();
        this.flattenedAttrNum = in.readInt();
        this.monitorLock = (Monitor.CloseableLock) in.readObject();
    }

    @Override // oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        Monitor.CloseableLock lock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.connection = conn;
                for (int i = 0; i < this.attrTypes.length; i++) {
                    this.attrTypes[i].setConnection(this.connection);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void setStatusBits(int bits) {
        this.connection.assertLockHeldByCurrentThread();
        this.statusBits |= bits;
    }

    private void maskAndSetStatusBits(int mask, int bits) {
        this.connection.assertLockHeldByCurrentThread();
        this.statusBits &= mask;
        this.statusBits |= bits;
    }

    private void printUnsignedByteArray(byte[] b, PrintWriter pw) {
        int length = b.length;
        int[] intArray = Util.toJavaUnsignedBytes(b);
        for (int i = 0; i < length; i++) {
            pw.print("0x" + Integer.toHexString(intArray[i]) + " ");
        }
        pw.println();
        for (int i2 = 0; i2 < length; i2++) {
            pw.print(intArray[i2] + " ");
        }
        pw.println();
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void initChildNamesRecursively(Map typesMap) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TypeTreeElement element = (TypeTreeElement) typesMap.get(this.sqlName);
                if (this.attrTypes != null && this.attrTypes.length > 0) {
                    for (int j = 0; j < this.attrTypes.length; j++) {
                        OracleType child = this.attrTypes[j];
                        child.setNames(element.getChildSchemaName(j + 1), element.getChildTypeName(j + 1));
                        child.initChildNamesRecursively(typesMap);
                        child.cacheDescriptor();
                    }
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void cacheDescriptor() throws SQLException {
        this.descriptor = StructDescriptor.createDescriptor(this);
    }

    private void initMetaData1() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            short version = this.connection.getVersionNumber();
            if (version >= 9000) {
                initMetaData1_9_0();
            } else {
                initMetaData1_pre_9_0();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public Boolean isInstanciable() throws SQLException {
        if (this.isInstanciable == null) {
            initMetaData1();
        }
        return this.isInstanciable;
    }

    public String getSuperTypeName() throws SQLException {
        if (this.superTypeName == null) {
            initMetaData1();
        }
        return this.superTypeName;
    }

    public int getNumberOfLocalAttributes() throws SQLException {
        if (this.numberOfLocalAttributes == -1) {
            initMetaData1();
        }
        return this.numberOfLocalAttributes;
    }

    public String[] getSubtypeNames() throws SQLException {
        if (this.subTypeNames == null) {
            initMetaData2();
        }
        return this.subTypeNames;
    }

    private void initMetaData1_9_0() throws SQLException {
        if (getTOID() != null) {
            initMetaData1_9_0UseToid();
        }
    }

    private void initMetaData1_9_0UseToid() throws SQLException {
        if (this.isInstanciable != null && this.numberOfLocalAttributes != -1) {
            return;
        }
        PreparedStatement pstmt1 = null;
        ResultSet rset1 = null;
        if (this.sqlName == null) {
            getFullName();
        }
        this.connection.beginNonRequestCalls();
        try {
            pstmt1 = this.connection.prepareStatement("SELECT INSTANTIABLE, supertype_owner, supertype_name, LOCAL_ATTRIBUTES FROM all_types t WHERE TYPE_OID = :1");
            pstmt1.setBytes(1, getTOID());
            pstmt1.setFetchSize(1);
            rset1 = pstmt1.executeQuery();
            if (rset1.next()) {
                this.isInstanciable = new Boolean(rset1.getString(1).equals("YES"));
                this.superTypeName = rset1.getString(2) + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + rset1.getString(3);
                this.numberOfLocalAttributes = rset1.getInt(4);
            } else {
                SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Inconsistent catalog view").fillInStackTrace();
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initMetaData1_9_0UseToid", sqlException.getMessage(), null, sqlException, new Object[0]);
                throw sqlException;
            }
        } finally {
            if (rset1 != null) {
                rset1.close();
            }
            if (pstmt1 != null) {
                pstmt1.close();
            }
            this.connection.endNonRequestCalls();
        }
    }

    private void initMetaData1_pre_9_0() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.isInstanciable = new Boolean(true);
            this.superTypeName = "";
            this.numberOfLocalAttributes = 0;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private void initMetaData2() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            short version = this.connection.getVersionNumber();
            if (version >= 9000) {
                initMetaData2_9_0();
            } else {
                initMetaData2_pre_9_0();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r10v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r10v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 10, insn: 0x0175: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r10 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:52:0x0175 */
    /* JADX WARN: Not initialized variable reg: 9, insn: 0x0170: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = 
      (r9 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('selfLock' oracle.jdbc.internal.Monitor$CloseableLock)])
     A[TRY_LEAVE], block:B:50:0x0170 */
    /* JADX WARN: Type inference failed for: r10v0, types: [java.lang.Throwable] */
    /* JADX WARN: Type inference failed for: r9v0, names: [selfLock], types: [oracle.jdbc.internal.Monitor$CloseableLock] */
    private void initMetaData2_9_0() throws SQLException {
        ?? r9;
        ?? r10;
        if (getTOID() != null) {
            initMetaData1_9_0UseToid();
        }
        if (this.sqlName == null) {
            getFullName();
        }
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                Monitor.CloseableLock closeableLockAcquireCloseableLock = acquireCloseableLock();
                Throwable th2 = null;
                if (this.subTypeNames == null) {
                    PreparedStatement preparedStatementPrepareStatement = null;
                    ResultSet resultSetExecuteQuery = null;
                    this.connection.beginNonRequestCalls();
                    try {
                        preparedStatementPrepareStatement = this.connection.prepareStatement("SELECT owner, type_name FROM all_types WHERE supertype_name = :1 and supertype_owner = :2");
                        preparedStatementPrepareStatement.setString(1, this.sqlName.getSimpleName());
                        preparedStatementPrepareStatement.setString(2, this.sqlName.getSchema());
                        resultSetExecuteQuery = preparedStatementPrepareStatement.executeQuery();
                        Vector vector = new Vector();
                        while (resultSetExecuteQuery.next()) {
                            vector.addElement(resultSetExecuteQuery.getString(1) + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + resultSetExecuteQuery.getString(2));
                        }
                        String[] strArr = new String[vector.size()];
                        for (int i = 0; i < strArr.length; i++) {
                            strArr[i] = (String) vector.elementAt(i);
                        }
                        vector.removeAllElements();
                        if (resultSetExecuteQuery != null) {
                            resultSetExecuteQuery.close();
                        }
                        if (preparedStatementPrepareStatement != null) {
                            preparedStatementPrepareStatement.close();
                        }
                        this.connection.endNonRequestCalls();
                        this.subTypeNames = strArr;
                    } catch (Throwable th3) {
                        if (resultSetExecuteQuery != null) {
                            resultSetExecuteQuery.close();
                        }
                        if (preparedStatementPrepareStatement != null) {
                            preparedStatementPrepareStatement.close();
                        }
                        this.connection.endNonRequestCalls();
                        throw th3;
                    }
                }
                if (closeableLockAcquireCloseableLock != null) {
                    if (0 != 0) {
                        try {
                            closeableLockAcquireCloseableLock.close();
                        } catch (Throwable th4) {
                            th2.addSuppressed(th4);
                        }
                    } else {
                        closeableLockAcquireCloseableLock.close();
                    }
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th5) {
                            th.addSuppressed(th5);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th6) {
                if (r9 != 0) {
                    if (r10 != 0) {
                        try {
                            r9.close();
                        } catch (Throwable th7) {
                            r10.addSuppressed(th7);
                        }
                    } else {
                        r9.close();
                    }
                }
                throw th6;
            }
        } catch (Throwable th8) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th9) {
                        th.addSuppressed(th9);
                    }
                } else {
                    lock.close();
                }
            }
            throw th8;
        }
    }

    private void initMetaData2_pre_9_0() throws SQLException {
        this.subTypeNames = new String[0];
    }

    @Override // oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent) throws SQLException {
        printXML(pw, indent, false);
    }

    @Override // oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        for (int i = 0; i < indent; i++) {
            try {
                try {
                    pw.print("  ");
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        pw.print("<OracleTypeADT sqlName=\"" + this.sqlName + "\" ");
        pw.print(" typecode=\"" + this.typeCode + "\"");
        if (this.tdsVersion != -9999) {
            pw.print(" tds_version=\"" + this.tdsVersion + "\"");
        }
        pw.println();
        for (int i2 = 0; i2 < indent + 4; i2++) {
            pw.print("  ");
        }
        pw.println(" is_embedded=\"" + isEmbeddedADT() + "\" is_top_level=\"" + isTopADT() + "\" is_upt=\"" + isUptADT() + "\" finalType=\"" + isFinalType() + "\" subtype=\"" + isSubType() + "\">");
        if (this.attrTypes != null && this.attrTypes.length > 0) {
            for (int i3 = 0; i3 < indent + 1; i3++) {
                pw.print("  ");
            }
            pw.println("<attributes>");
            for (int j = 0; j < this.attrTypes.length; j++) {
                for (int i4 = 0; i4 < indent + 2; i4++) {
                    pw.print("  ");
                }
                pw.println("<attribute name=\"" + getAttributeName(j + 1, fetchAllMetaDataAsNeeded) + "\"  type=\"" + getAttributeType(j + 1, false) + "\" >");
                this.attrTypes[j].printXML(pw, indent + 3, fetchAllMetaDataAsNeeded);
                for (int i5 = 0; i5 < indent + 2; i5++) {
                    pw.print("  ");
                }
                pw.println("</attribute> ");
            }
            for (int i6 = 0; i6 < indent + 1; i6++) {
                pw.print("  ");
            }
            pw.println("</attributes>");
        }
        for (int i7 = 0; i7 < indent; i7++) {
            pw.print("  ");
        }
        pw.println("</OracleTypeADT>");
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }
}
