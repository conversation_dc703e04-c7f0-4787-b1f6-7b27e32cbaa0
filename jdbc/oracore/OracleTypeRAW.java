package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.RAW;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeRAW.class */
public class OracleTypeRAW extends OracleType implements Serializable {
    static final long serialVersionUID = -6083664758336974576L;
    int length;

    public OracleTypeRAW() {
    }

    public OracleTypeRAW(int typecode) {
        super(typecode);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        RAW datum = null;
        if (value != null) {
            try {
                if (value instanceof RAW) {
                    datum = (RAW) value;
                } else {
                    datum = new RAW(value);
                }
            } catch (SQLException e) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof char[][]) {
                char[][] strArray = (char[][]) obj;
                int length = (int) (count == -1 ? strArray.length : Math.min((strArray.length - beginIdx) + 1, count));
                datumArray = new Datum[length];
                for (int i = 0; i < length; i++) {
                    datumArray[i] = toDatum(new String(strArray[(((int) beginIdx) + i) - 1]), conn);
                }
            } else {
                if (obj instanceof Object[]) {
                    return super.toDatumArray(obj, conn, beginIdx, count);
                }
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return -2;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        super.parseTDSrec(tdsReader);
        this.length = tdsReader.readUB2();
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext ctx, Datum data) throws SQLException {
        if (data.getLength() > this.length) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 72, this).fillInStackTrace());
        }
        int len = ctx.writeLength((int) data.getLength());
        return len + ctx.writeData(data.shareBytes());
    }

    public int getLength() {
        return this.length;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] val, int style, Map map) throws SQLException {
        if (val == null || val.length == 0) {
            return null;
        }
        switch (style) {
            case 1:
                return new RAW(val);
            case 2:
            case 3:
                return val;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, val).fillInStackTrace());
        }
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.length);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.length = in.readInt();
    }
}
