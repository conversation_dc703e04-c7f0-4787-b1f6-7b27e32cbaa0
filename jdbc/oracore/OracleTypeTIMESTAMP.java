package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMP;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeTIMESTAMP.class */
public class OracleTypeTIMESTAMP extends OracleType implements Serializable {
    static final long serialVersionUID = 3948043338303602796L;
    int precision = 0;

    protected OracleTypeTIMESTAMP() {
    }

    public OracleTypeTIMESTAMP(OracleConnection connection) {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return 93;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        this.precision = tdsReader.readByte();
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getScale() throws SQLException {
        return 0;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getPrecision() throws SQLException {
        return this.precision;
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.precision = in.readByte();
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeByte(this.precision);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int otype, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        switch (otype) {
            case 1:
                return new TIMESTAMP(bytes);
            case 2:
                return TIMESTAMP.toTimestamp(bytes);
            case 3:
                return bytes;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        if (value != null) {
            try {
                if (value instanceof TIMESTAMP) {
                    datum = (TIMESTAMP) value;
                } else if (value instanceof byte[]) {
                    datum = new TIMESTAMP((byte[]) value);
                } else if (value instanceof Timestamp) {
                    datum = new TIMESTAMP((Timestamp) value);
                } else if (value instanceof DATE) {
                    datum = new TIMESTAMP((DATE) value);
                } else if (value instanceof String) {
                    datum = new TIMESTAMP((String) value);
                } else if (value instanceof Date) {
                    datum = new TIMESTAMP((Date) value);
                } else if (value instanceof Time) {
                    datum = new TIMESTAMP((Time) value);
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
                }
            } catch (Exception e) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    protected Object unpickle81rec(UnpickleContext context, int format, int otype, Map map) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 90).fillInStackTrace());
    }
}
