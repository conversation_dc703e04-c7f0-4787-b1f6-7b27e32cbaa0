package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.SQLName;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleNamedType.class */
public abstract class OracleNamedType extends OracleType implements Serializable {
    transient OracleConnection connection;
    SQLName sqlName;
    transient OracleTypeADT parent;
    transient int idx;
    transient TypeDescriptor descriptor;
    String typeNameByUser;
    String sqlHint;
    private static final String CLASS_NAME = OracleNamedType.class.getName();
    static String getUserTypeTreeSql = "select level depth, parent_type, child_type, ATTR_NO, child_type_owner from  (select TYPE_NAME parent_type, ELEM_TYPE_NAME child_type, 0 ATTR_NO,       ELEM_TYPE_OWNER child_type_owner     from USER_COLL_TYPES  union   select TYPE_NAME parent_type, ATTR_TYPE_NAME child_type, ATTR_NO,       ATTR_TYPE_OWNER child_type_owner     from USER_TYPE_ATTRS  ) start with parent_type  = ?  connect by prior  child_type = parent_type";
    static String getAllTypeTreeSql = "select parent_type, parent_type_owner, child_type, ATTR_NO, child_type_owner from ( select TYPE_NAME parent_type,  OWNER parent_type_owner,     ELEM_TYPE_NAME child_type, 0 ATTR_NO,     ELEM_TYPE_OWNER child_type_owner   from ALL_COLL_TYPES union   select TYPE_NAME parent_type, OWNER parent_type_owner,     ATTR_TYPE_NAME child_type, ATTR_NO,     ATTR_TYPE_OWNER child_type_owner   from ALL_TYPE_ATTRS ) start with parent_type  = ?  and parent_type_owner = ? connect by prior child_type = parent_type   and ( child_type_owner = parent_type_owner or child_type_owner is null )";

    protected OracleNamedType() {
        this.sqlName = null;
        this.parent = null;
        this.descriptor = null;
        this.sqlHint = null;
    }

    public OracleNamedType(String name, OracleConnection conn) throws SQLException {
        this.sqlName = null;
        this.parent = null;
        this.descriptor = null;
        this.sqlHint = null;
        setConnectionInternal(conn);
        this.typeNameByUser = name;
        this.sqlName = new SQLName(name, conn);
    }

    protected OracleNamedType(OracleTypeADT parent, int idx, OracleConnection conn) {
        this.sqlName = null;
        this.parent = null;
        this.descriptor = null;
        this.sqlHint = null;
        setConnectionInternal(conn);
        this.parent = parent;
        this.idx = idx;
    }

    public String getFullName() throws SQLException {
        return getFullName(false);
    }

    public String getFullName(boolean force) throws SQLException {
        String temp_fullName;
        if (force || this.sqlName == null) {
            if (this.parent != null && (temp_fullName = this.parent.getAttributeType(this.idx)) != null) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getFullName", "temp_fullName={0}", (String) null, (String) null, temp_fullName);
                this.sqlName = new SQLName(temp_fullName, this.connection);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Unable to resolve name").fillInStackTrace());
            }
        }
        return this.sqlName.getName();
    }

    public String getSchemaName() throws SQLException {
        if (this.sqlName == null) {
            getFullName();
        }
        return this.sqlName.getSchema();
    }

    public String getSimpleName() throws SQLException {
        if (this.sqlName == null) {
            getFullName();
        }
        return this.sqlName.getSimpleName();
    }

    public boolean hasName() throws SQLException {
        return this.sqlName != null;
    }

    public OracleTypeADT getParent() throws SQLException {
        return this.parent;
    }

    public void setParent(OracleTypeADT parent) throws SQLException {
        this.parent = parent;
    }

    public int getOrder() throws SQLException {
        return this.idx;
    }

    public void setOrder(int order) throws SQLException {
        this.idx = order;
    }

    public OracleConnection getConnection() throws SQLException {
        return this.connection;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        setConnectionInternal(conn);
    }

    public void setConnectionInternal(OracleConnection conn) {
        this.connection = conn;
    }

    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, int type, Map objmap) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, long idx, int cnt, int type, Map objmap) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    public byte[] linearize(Datum data) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    public TypeDescriptor getDescriptor() throws SQLException {
        return this.descriptor;
    }

    public void setDescriptor(TypeDescriptor desc) throws SQLException {
        this.descriptor = desc;
    }

    public int getTypeVersion() {
        return 1;
    }

    String getTypeName(String ownerName, String packageName, String sqlName) {
        String typeName = (packageName == null || packageName.isEmpty()) ? sqlName : packageName + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + sqlName;
        return (ownerName == null || ownerName.isEmpty()) ? typeName : SQLName.getTypeName(ownerName, typeName);
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        try {
            out.writeUTF(getFullName());
        } catch (SQLException e) {
            throw ((IOException) DatabaseError.createIOException(e).fillInStackTrace());
        }
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        String name = in.readUTF();
        try {
            this.sqlName = new SQLName(name, null);
        } catch (SQLException e) {
        }
        this.parent = null;
        this.idx = -1;
    }

    public void fixupConnection(OracleConnection fixupConn) throws SQLException {
        if (this.connection == null) {
            setConnection(fixupConn);
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent) throws SQLException {
        printXML(pw, indent, false);
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void printXML(PrintWriter pw, int indent, boolean fetchAllMetaDataAsNeeded) throws SQLException {
        for (int i = 0; i < indent; i++) {
            pw.print("  ");
        }
        pw.println("<OracleNamedType/>");
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void initNamesRecursively() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            Map typesTreeMap = createTypesTreeMap();
            if (typesTreeMap.size() > 0) {
                initChildNamesRecursively(typesTreeMap);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setNames(String schemaName, String typeName) throws SQLException {
        this.sqlName = new SQLName(schemaName, typeName, this.connection);
    }

    public void setSqlName(SQLName x) {
        this.sqlName = x;
    }

    public Map createTypesTreeMap() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                Map nodeMap = null;
                String currentUserName = this.connection.getDefaultSchemaNameForNamedTypes();
                if (this.sqlName.getSchema().equals(currentUserName)) {
                    nodeMap = getNodeMapFromUserTypes();
                }
                if (nodeMap == null) {
                    nodeMap = getNodeMapFromAllTypes();
                }
                Map map = nodeMap;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return map;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    String getSqlHint() throws SQLException {
        if (this.sqlHint == null) {
            if (this.connection.getVersionNumber() >= 11000) {
                this.sqlHint = "";
            } else {
                this.sqlHint = "/*+RULE*/";
            }
        }
        return this.sqlHint;
    }

    Map getNodeMapFromUserTypes() throws SQLException {
        TypeTreeElement node;
        Map nodeMap = new HashMap();
        PreparedStatement pstmt = null;
        ResultSet rst = null;
        this.connection.beginNonRequestCalls();
        try {
            pstmt = this.connection.prepareStatement(getSqlHint() + getUserTypeTreeSql);
            pstmt.setString(1, this.sqlName.getSimpleName());
            rst = pstmt.executeQuery();
            while (true) {
                if (!rst.next()) {
                    break;
                }
                rst.getInt(1);
                String parentTypeName = rst.getString(2);
                String childTypeName = rst.getString(3);
                int attrNumber = rst.getInt(4);
                String childTypeOwner = rst.getString(5);
                if (childTypeOwner != null && !childTypeOwner.equals(this.sqlName.getSchema())) {
                    nodeMap = null;
                    break;
                }
                if (parentTypeName.length() > 0) {
                    SQLName parentSqlName = new SQLName(this.sqlName.getSchema(), parentTypeName, this.connection);
                    if (nodeMap.containsKey(parentSqlName)) {
                        node = (TypeTreeElement) nodeMap.get(parentSqlName);
                    } else {
                        node = new TypeTreeElement(this.sqlName.getSchema(), parentTypeName);
                        nodeMap.put(parentSqlName, node);
                    }
                    node.putChild(this.sqlName.getSchema(), childTypeName, attrNumber);
                }
            }
            if (rst != null) {
                rst.close();
            }
            if (pstmt != null) {
                pstmt.close();
            }
            this.connection.endNonRequestCalls();
            return nodeMap;
        } catch (Throwable th) {
            if (rst != null) {
                rst.close();
            }
            if (pstmt != null) {
                pstmt.close();
            }
            this.connection.endNonRequestCalls();
            throw th;
        }
    }

    Map getNodeMapFromAllTypes() throws SQLException {
        TypeTreeElement node;
        Map nodeMap = new HashMap();
        PreparedStatement pstmt = null;
        ResultSet rst = null;
        this.connection.beginNonRequestCalls();
        try {
            pstmt = this.connection.prepareStatement(getSqlHint() + getAllTypeTreeSql);
            pstmt.setString(1, this.sqlName.getSimpleName());
            pstmt.setString(2, this.sqlName.getSchema());
            rst = pstmt.executeQuery();
            while (rst.next()) {
                String parentTypeName = rst.getString(1);
                String parentOwnerName = rst.getString(2);
                String childTypeName = rst.getString(3);
                int attrNumber = rst.getInt(4);
                String childOwnerName = rst.getString(5);
                if (childOwnerName == null) {
                    childOwnerName = "SYS";
                }
                if (parentTypeName.length() > 0) {
                    SQLName parentSQLName = new SQLName(parentOwnerName, parentTypeName, this.connection);
                    if (nodeMap.containsKey(parentSQLName)) {
                        node = (TypeTreeElement) nodeMap.get(parentSQLName);
                    } else {
                        node = new TypeTreeElement(parentOwnerName, parentTypeName);
                        nodeMap.put(parentSQLName, node);
                    }
                    node.putChild(childOwnerName, childTypeName, attrNumber);
                }
            }
            if (rst != null) {
                rst.close();
            }
            if (pstmt != null) {
                pstmt.close();
            }
            this.connection.endNonRequestCalls();
            return nodeMap;
        } catch (Throwable th) {
            if (rst != null) {
                rst.close();
            }
            if (pstmt != null) {
                pstmt.close();
            }
            this.connection.endNonRequestCalls();
            throw th;
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
