package oracle.jdbc.oracore;

import java.sql.SQLException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/TDSPatch.class */
public class TDSPatch {
    static final int S_NORMAL_PATCH = 0;
    static final int S_SIMPLE_PATCH = 1;
    int typeId;
    OracleType owner;
    long position;
    int uptCode;

    public TDSPatch(int type, OracleType owner, long pos, int uptCode) throws SQLException {
        this.typeId = type;
        this.owner = owner;
        this.position = pos;
        this.uptCode = uptCode;
    }

    int getType() throws SQLException {
        return this.typeId;
    }

    OracleNamedType getOwner() throws SQLException {
        return (OracleNamedType) this.owner;
    }

    long getPosition() throws SQLException {
        return this.position;
    }

    byte getUptTypeCode() throws SQLException {
        return (byte) this.uptCode;
    }

    void apply(OracleType typeValue) throws SQLException {
        apply(typeValue, -1);
    }

    void apply(OracleType typeValue, int opcode) throws SQLException {
        if (this.typeId == 0) {
            OracleTypeUPT patchElem = (OracleTypeUPT) this.owner;
            patchElem.realType = (OracleTypeADT) typeValue;
            if (typeValue instanceof OracleNamedType) {
                OracleNamedType namedType = (OracleNamedType) typeValue;
                namedType.setParent(patchElem.getParent());
                namedType.setOrder(patchElem.getOrder());
                return;
            }
            return;
        }
        if (this.typeId == 1) {
            OracleTypeCOLLECTION patchElem2 = (OracleTypeCOLLECTION) this.owner;
            patchElem2.opcode = opcode;
            patchElem2.elementType = typeValue;
            if (typeValue instanceof OracleNamedType) {
                OracleNamedType namedType2 = (OracleNamedType) typeValue;
                namedType2.setParent(patchElem2);
                namedType2.setOrder(1);
                return;
            }
            return;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
