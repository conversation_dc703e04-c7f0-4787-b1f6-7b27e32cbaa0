package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.time.Duration;
import java.util.Map;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.CHAR;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeINTERVAL.class */
public class OracleTypeINTERVAL extends OracleType implements Serializable {
    static final long serialVersionUID = 1394800182554224957L;
    static final int LDIINTYEARMONTH = 7;
    static final int LDIINTDAYSECOND = 10;
    static final int SIZE_INTERVAL_YM = 5;
    static final int SIZE_INTERVAL_DS = 11;
    byte typeId = 0;
    int scale = 0;
    int precision = 0;

    protected OracleTypeINTERVAL() {
    }

    public OracleTypeINTERVAL(OracleConnection connection) {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        if (this.typeId == 7) {
            return OracleTypes.INTERVALYM;
        }
        if (this.typeId == 10) {
            return OracleTypes.INTERVALDS;
        }
        return OracleTypes.OTHER;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        this.typeId = tdsReader.readByte();
        this.precision = tdsReader.readByte();
        this.scale = tdsReader.readByte();
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getScale() throws SQLException {
        return this.scale;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getPrecision() throws SQLException {
        return this.precision;
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.typeId = in.readByte();
        this.precision = in.readByte();
        this.scale = in.readByte();
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeByte(this.typeId);
        out.writeByte(this.precision);
        out.writeByte(this.scale);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int otype, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        switch (otype) {
            case 1:
            case 2:
                if (bytes.length == 5) {
                    return new INTERVALYM(bytes);
                }
                if (bytes.length == 11) {
                    return new INTERVALDS(bytes);
                }
                return null;
            case 3:
                return bytes;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        if (value != null) {
            if ((value instanceof INTERVALYM) || (value instanceof INTERVALDS)) {
                return (Datum) value;
            }
            if (value instanceof CHAR) {
                ((CHAR) value).toString();
                return null;
            }
            if (value instanceof String) {
                try {
                    return new INTERVALDS((String) value);
                } catch (StringIndexOutOfBoundsException e) {
                    return new INTERVALYM((String) value);
                }
            }
            if (value instanceof Duration) {
                return INTERVALDS.toIntervalds((Duration) value);
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
        }
        return null;
    }

    protected Object unpickle81rec(UnpickleContext context, int format, int otype, Map map) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 90).fillInStackTrace());
    }
}
