package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleClob;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.CLOB;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeCLOB.class */
public class OracleTypeCLOB extends OracleType implements Serializable {
    static final long serialVersionUID = 1122821330765834411L;
    static int fixedDataSize = 86;
    transient OracleConnection connection;
    int form;

    protected OracleTypeCLOB() {
    }

    public OracleTypeCLOB(OracleConnection conn) {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        if (value != null) {
            if ((value instanceof CLOB) || (value instanceof OracleClob)) {
                datum = (Datum) value;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return this.form == 2 ? OracleTypes.NCLOB : OracleTypes.CLOB;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        if (type == 1 || type == 2) {
            return this.connection.createClobWithUnpickledBytes(bytes);
        }
        if (type == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public boolean isNCHAR() throws SQLException {
        return this.form == 2;
    }

    public void setForm(int formOfUse) {
        this.form = formOfUse;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
