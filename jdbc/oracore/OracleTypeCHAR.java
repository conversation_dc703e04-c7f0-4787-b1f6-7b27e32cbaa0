package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.CHAR;
import oracle.sql.CharacterSet;
import oracle.sql.Datum;
import oracle.sql.converter.CharacterSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeCHAR.class */
public class OracleTypeCHAR extends OracleType implements Serializable {
    private static final String CLASS_NAME = OracleTypeCHAR.class.getName();
    static final long serialVersionUID = -6899444518695804629L;
    int form;
    int charset;
    int length;
    int characterSemantic;
    private transient OracleConnection connection;
    private short pickleCharaterSetId;
    private transient CharacterSet pickleCharacterSet;
    private short pickleNcharCharacterSet;

    protected OracleTypeCHAR() {
    }

    public OracleTypeCHAR(OracleConnection conn) {
        this.form = 0;
        this.charset = 0;
        this.length = 0;
        this.connection = conn;
        this.pickleCharaterSetId = (short) 0;
        this.pickleNcharCharacterSet = (short) 0;
        this.pickleCharacterSet = null;
        try {
            this.pickleCharaterSetId = this.connection.getStructAttrCsId();
        } catch (SQLException sqlException) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "OracleTypeCHAR", sqlException.getMessage(), null, sqlException, new Object[0]);
            this.pickleCharaterSetId = (short) -1;
        }
        this.pickleCharacterSet = CharacterSet.make(this.pickleCharaterSetId);
    }

    protected OracleTypeCHAR(OracleConnection conn, int typecode) {
        super(typecode);
        this.form = 0;
        this.charset = 0;
        this.length = 0;
        this.connection = conn;
        this.pickleCharaterSetId = (short) 0;
        this.pickleNcharCharacterSet = (short) 0;
        this.pickleCharacterSet = null;
        try {
            this.pickleCharaterSetId = this.connection.getStructAttrCsId();
        } catch (SQLException sqlException) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "OracleTypeCHAR", sqlException.getMessage(), null, sqlException, new Object[0]);
            this.pickleCharaterSetId = (short) -1;
        }
        this.pickleCharacterSet = CharacterSet.make(this.pickleCharaterSetId);
    }

    private int getLengthInCharacter() {
        int characterSet = this.form == 2 ? this.pickleNcharCharacterSet : this.pickleCharaterSetId;
        if (characterSet != 0) {
            int ratio = CharacterSetMetaData.getRatio(characterSet, 1);
            return this.length / ratio;
        }
        return this.length;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        CHAR datum;
        if (value == null) {
            return null;
        }
        if (value instanceof CHAR) {
            datum = (CHAR) value;
        } else if (this.typeCode == 1 && (value instanceof String)) {
            if (this.characterSemantic != 0) {
                int ratio = CharacterSetMetaData.getRatio(this.pickleCharaterSetId, 1);
                String s = (String) value;
                for (int i = s.length(); i < this.length / ratio; i++) {
                    s = s + " ";
                }
                datum = new CHAR((Object) s, this.pickleCharacterSet);
            } else {
                datum = new CHAR((String) value, this.pickleCharacterSet, this.length);
            }
        } else {
            datum = new CHAR(value, this.pickleCharacterSet);
        }
        if (this.typeCode == 12 || this.typeCode == -9) {
            datum.setVariableLength(true);
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum[] toDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if ((obj instanceof Object[]) && !(obj instanceof char[][])) {
                return super.toDatumArray(obj, conn, beginIdx, count);
            }
            datumArray = cArrayToDatumArray(obj, conn, beginIdx, count);
        }
        return datumArray;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        super.parseTDSrec(tdsReader);
        try {
            this.length = tdsReader.readUB2();
            this.form = tdsReader.readByte();
            this.characterSemantic = this.form & 128;
            this.form &= 127;
            this.charset = tdsReader.readUB2();
            if (this.form == 2) {
                switch (this.typeCode) {
                    case 1:
                        this.typeCode = -15;
                        break;
                    case 12:
                        this.typeCode = -9;
                        break;
                }
            }
            if (this.form != 2 || this.pickleNcharCharacterSet != 0) {
                return;
            }
            try {
                this.pickleNcharCharacterSet = this.connection.getStructAttrNCsId();
            } catch (SQLException sqlException) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "parseTDSrec", sqlException.getMessage(), null, sqlException, new Object[0]);
                this.pickleNcharCharacterSet = (short) 2000;
            }
            this.pickleCharaterSetId = this.pickleNcharCharacterSet;
            this.pickleCharacterSet = CharacterSet.make(this.pickleCharaterSetId);
        } catch (SQLException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 47, "parseTDS").fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext context, Datum data) throws SQLException {
        CHAR dbchar = getDbCHAR(data);
        if (this.characterSemantic == 0 || this.form == 2) {
            if (dbchar.getLength() > this.length) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 72, "\"" + dbchar.getStringWithReplacement() + "\"").fillInStackTrace());
            }
        } else if (dbchar.getStringWithReplacement().length() > this.length) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 72, "\"" + dbchar.getStringWithReplacement() + "\"").fillInStackTrace());
        }
        return super.pickle81(context, dbchar);
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int type, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        CHAR result = null;
        switch (this.form) {
            case 1:
            case 2:
                result = new CHAR(bytes, this.pickleCharacterSet);
                break;
            case 3:
            case 4:
            case 5:
                result = new CHAR(bytes, (CharacterSet) null);
                break;
        }
        if ((this.typeCode == 12 || this.typeCode == -9) && result != null) {
            result.setVariableLength(true);
        }
        if (type == 1) {
            return result;
        }
        if (type == 2) {
            return result != null ? result.stringValue() : result;
        }
        if (type == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }

    private CHAR getDbCHAR(Datum data) {
        CHAR db_char_obj;
        CHAR in_char_obj = (CHAR) data;
        if (in_char_obj.getCharacterSet().getOracleId() == this.pickleCharaterSetId) {
            db_char_obj = in_char_obj;
        } else {
            try {
                db_char_obj = new CHAR(in_char_obj.toString(), this.pickleCharacterSet);
            } catch (SQLException sqlException) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getDbCHAR", sqlException.getMessage(), null, sqlException, new Object[0]);
                db_char_obj = in_char_obj;
            }
        }
        return db_char_obj;
    }

    private Datum[] cArrayToDatumArray(Object obj, OracleConnection conn, long beginIdx, int count) throws SQLException {
        Datum[] datumArray = null;
        if (obj != null) {
            if (obj instanceof char[][]) {
                char[][] parray = (char[][]) obj;
                int len = (int) (count == -1 ? parray.length : Math.min((parray.length - beginIdx) + 1, count));
                datumArray = new Datum[len];
                for (int i = 0; i < len; i++) {
                    datumArray[i] = new CHAR(new String(parray[(((int) beginIdx) + i) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof boolean[]) {
                boolean[] parray2 = (boolean[]) obj;
                int len2 = (int) (count == -1 ? parray2.length : Math.min((parray2.length - beginIdx) + 1, count));
                datumArray = new Datum[len2];
                for (int i2 = 0; i2 < len2; i2++) {
                    datumArray[i2] = new CHAR(Boolean.valueOf(parray2[(((int) beginIdx) + i2) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof short[]) {
                short[] parray3 = (short[]) obj;
                int len3 = (int) (count == -1 ? parray3.length : Math.min((parray3.length - beginIdx) + 1, count));
                datumArray = new Datum[len3];
                for (int i3 = 0; i3 < len3; i3++) {
                    datumArray[i3] = new CHAR(Integer.valueOf(parray3[(((int) beginIdx) + i3) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof int[]) {
                int[] parray4 = (int[]) obj;
                int len4 = (int) (count == -1 ? parray4.length : Math.min((parray4.length - beginIdx) + 1, count));
                datumArray = new Datum[len4];
                for (int i4 = 0; i4 < len4; i4++) {
                    datumArray[i4] = new CHAR(Integer.valueOf(parray4[(((int) beginIdx) + i4) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof long[]) {
                long[] parray5 = (long[]) obj;
                int len5 = (int) (count == -1 ? parray5.length : Math.min((parray5.length - beginIdx) + 1, count));
                datumArray = new Datum[len5];
                for (int i5 = 0; i5 < len5; i5++) {
                    datumArray[i5] = new CHAR(new Long(parray5[(((int) beginIdx) + i5) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof float[]) {
                float[] parray6 = (float[]) obj;
                int len6 = (int) (count == -1 ? parray6.length : Math.min((parray6.length - beginIdx) + 1, count));
                datumArray = new Datum[len6];
                for (int i6 = 0; i6 < len6; i6++) {
                    datumArray[i6] = new CHAR(new Float(parray6[(((int) beginIdx) + i6) - 1]), this.pickleCharacterSet);
                }
            } else if (obj instanceof double[]) {
                double[] parray7 = (double[]) obj;
                int len7 = (int) (count == -1 ? parray7.length : Math.min((parray7.length - beginIdx) + 1, count));
                datumArray = new Datum[len7];
                for (int i7 = 0; i7 < len7; i7++) {
                    datumArray[i7] = new CHAR(new Double(parray7[(((int) beginIdx) + i7) - 1]), this.pickleCharacterSet);
                }
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, obj).fillInStackTrace());
            }
        }
        return datumArray;
    }

    public int getLength() {
        if (this.characterSemantic != 0) {
            return getLengthInCharacter();
        }
        return this.length;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeInt(this.form);
        out.writeInt(this.charset);
        out.writeInt(this.length);
        out.writeInt(this.characterSemantic);
        out.writeShort(this.pickleCharaterSetId);
        out.writeShort(this.pickleNcharCharacterSet);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        this.form = in.readInt();
        this.charset = in.readInt();
        this.length = in.readInt();
        this.characterSemantic = in.readInt();
        this.pickleCharaterSetId = in.readShort();
        this.pickleNcharCharacterSet = in.readShort();
        if (this.pickleNcharCharacterSet != 0) {
            this.pickleCharacterSet = CharacterSet.make(this.pickleNcharCharacterSet);
        } else {
            this.pickleCharacterSet = CharacterSet.make(this.pickleCharaterSetId);
        }
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public boolean isNCHAR() throws SQLException {
        return this.form == 2;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
