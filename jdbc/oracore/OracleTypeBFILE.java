package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleBfile;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.BFILE;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeBFILE.class */
public class OracleTypeBFILE extends OracleType implements Serializable {
    static final long serialVersionUID = -707073491109554687L;
    static int fixedDataSize = 530;
    transient OracleConnection connection;

    public OracleTypeBFILE() {
    }

    public OracleTypeBFILE(OracleConnection conn) {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        Datum datum = null;
        if (value != null) {
            if ((value instanceof BFILE) || (value instanceof OracleBfile)) {
                datum = (Datum) value;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, value).fillInStackTrace());
            }
        }
        return datum;
    }

    @Override // oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return -13;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected Object toObject(byte[] bytes, int style, Map map) throws SQLException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        if (style == 1 || style == 2) {
            return this.connection.createBfile(bytes);
        }
        if (style == 3) {
            return bytes;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 59, bytes).fillInStackTrace());
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }

    @Override // oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
    }

    @Override // oracle.jdbc.oracore.OracleType
    protected OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
