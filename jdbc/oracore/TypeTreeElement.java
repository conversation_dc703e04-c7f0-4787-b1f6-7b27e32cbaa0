package oracle.jdbc.oracore;

import java.io.PrintWriter;
import java.io.StringWriter;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/TypeTreeElement.class */
public class TypeTreeElement {
    String schemaName;
    String typeName;
    String[] childSchemaNames = null;
    String[] childTypeNames = null;
    int size = 0;

    public TypeTreeElement(String s, String t) {
        this.schemaName = null;
        this.typeName = null;
        this.schemaName = s;
        this.typeName = t;
    }

    public void putChild(String schemaName, String typeName, int index) {
        if (this.childTypeNames == null) {
            int newSize = 10;
            if (index > 10) {
                newSize = index * 2;
            }
            this.childSchemaNames = new String[newSize];
            this.childTypeNames = new String[newSize];
        }
        if (index >= this.childTypeNames.length) {
            int newSize2 = (index + 10) * 2;
            String[] temp = new String[newSize2];
            System.arraycopy(this.childSchemaNames, 0, temp, 0, this.childSchemaNames.length);
            this.childSchemaNames = temp;
            String[] temp2 = new String[newSize2];
            System.arraycopy(this.childTypeNames, 0, temp2, 0, this.childTypeNames.length);
            this.childTypeNames = temp2;
        }
        this.childSchemaNames[index] = schemaName;
        this.childTypeNames[index] = typeName;
        if (index > this.size) {
            this.size = index;
        }
    }

    public String getChildSchemaName(int index) {
        return this.childSchemaNames[index];
    }

    public String getChildTypeName(int index) {
        return this.childTypeNames[index];
    }

    public String toString() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        pw.println("schemaName: " + this.schemaName + " typeName: " + this.typeName);
        for (int i = 0; i < this.size; i++) {
            pw.println("index: " + i + " schema name: " + this.childSchemaNames[i] + " type name: " + this.childTypeNames[i]);
        }
        return sw.getBuffer().substring(0);
    }
}
