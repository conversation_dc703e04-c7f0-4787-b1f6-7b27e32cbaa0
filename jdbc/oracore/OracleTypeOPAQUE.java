package oracle.jdbc.oracore;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleTypes;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.sql.Datum;
import oracle.sql.OPAQUE;
import oracle.sql.OpaqueDescriptor;
import oracle.sql.StructDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/oracore/OracleTypeOPAQUE.class */
public class OracleTypeOPAQUE extends OracleTypeADT implements Serializable {
    static final long KOLOFLLB = 1;
    static final long KOLOFLCL = 2;
    static final long KOLOFLUB = 4;
    static final long KOLOFLFX = 8;
    static final long serialVersionUID = -7279638692691669378L;
    private static final String PUBLIC_XMLTYPE_STRING_VALUE = "PUBLIC.XMLTYPE";
    private static final String SYS_XMLTYPE_STRING_VALUE = "SYS.XMLTYPE";
    private static final String XMLTYPE_STRING_VALUE = "XMLTYPE";
    long flagBits;
    long maxLen;
    private static final String CLASS_NAME = OracleTypeOPAQUE.class.getName();
    private static Method XMLTYPE_CREATEXML = null;

    public OracleTypeOPAQUE(byte[] toid, int vsn, int csi, short csfrm, String fullName, long _flagBits) throws SQLException {
        super(toid, vsn, csi, csfrm, fullName);
        this.flagBits = _flagBits;
        this.flattenedAttrNum = 1;
    }

    public OracleTypeOPAQUE(String name, OracleConnection conn) throws SQLException {
        super(name, conn);
    }

    public OracleTypeOPAQUE(OracleTypeADT parent, int idx, OracleConnection conn) throws SQLException {
        super(parent, idx, conn);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public Datum toDatum(Object value, OracleConnection conn) throws SQLException {
        if (value != null) {
            if (value instanceof OPAQUE) {
                return (OPAQUE) value;
            }
            OpaqueDescriptor desc = createOpaqueDescriptor();
            return new OPAQUE(desc, this.connection, value);
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public int getTypeCode() {
        return OracleTypes.OPAQUE;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(OracleType anOracleType) throws SQLException {
        if (anOracleType == null || !(anOracleType instanceof OracleTypeOPAQUE)) {
            return false;
        }
        OpaqueDescriptor theOtherDesc = (OpaqueDescriptor) anOracleType.getTypeDescriptor();
        return this.descriptor.isInHierarchyOf(theOtherDesc.getName());
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isInHierarchyOf(StructDescriptor aStructDescriptor) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public boolean isObjectType() {
        return false;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    public void parseTDSrec(TDSReader tdsReader) throws SQLException {
        tdsReader.skipBytes(5);
        this.flagBits = tdsReader.readLong();
        this.maxLen = tdsReader.readLong();
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType
    public Datum unlinearize(byte[] pickled_bytes, long offset, Datum container, int style, Map objmap) throws SQLException {
        if (pickled_bytes != null && (pickled_bytes[0] & 128) > 0) {
            PickleContext context = new PickleContext(pickled_bytes, offset);
            return unpickle81(context, (OPAQUE) container, style, objmap);
        }
        return null;
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType
    public byte[] linearize(Datum data) throws SQLException {
        return pickle81(data);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected int pickle81(PickleContext ctx, Datum datum) throws SQLException {
        OPAQUE obj = (OPAQUE) datum;
        byte[] data = obj.getBytesValue();
        int imglen = 0 + ctx.writeOpaqueImageHeader(data.length);
        return imglen + ctx.writeData(data);
    }

    OPAQUE unpickle81(PickleContext context, OPAQUE container, int style, Map elemMap) throws SQLException {
        return unpickle81datum(context, container, style);
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleType
    protected Object unpickle81rec(PickleContext context, int style, Map map) throws SQLException {
        byte b = context.readByte();
        Object returnValue = null;
        if (PickleContext.isElementNull(b)) {
            return null;
        }
        context.skipRestOfLength(b);
        switch (style) {
            case 1:
                returnValue = unpickle81datum(context, null);
                break;
            case 2:
                returnValue = unpickle81datum(context, null).toJdbc();
                break;
            case 3:
                returnValue = new OPAQUE(createOpaqueDescriptor(), context.readDataValue(), this.connection);
                break;
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1).fillInStackTrace());
            case 9:
                context.skipDataValue();
                break;
        }
        return returnValue;
    }

    private OPAQUE unpickle81datum(PickleContext context, OPAQUE container) throws SQLException {
        return unpickle81datum(context, container, 1);
    }

    private OPAQUE createXml(OPAQUE opaqueObject) throws SQLException, ClassNotFoundException {
        if (XMLTYPE_CREATEXML == null) {
            try {
                Class xmltype = Class.forName("oracle.xdb.XMLType");
                XMLTYPE_CREATEXML = xmltype.getMethod("createXML", OPAQUE.class);
            } catch (Exception ex) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex).fillInStackTrace());
            }
        }
        try {
            OPAQUE result = (OPAQUE) XMLTYPE_CREATEXML.invoke(null, opaqueObject);
            return result;
        } catch (InvocationTargetException ex2) {
            Throwable cause = ex2.getCause();
            if (cause instanceof SQLException) {
                throw ((SQLException) cause);
            }
            if (cause instanceof Exception) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (Exception) cause).fillInStackTrace());
            }
            throw ((Error) cause);
        } catch (Exception ex3) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ex3).fillInStackTrace());
        }
    }

    private OPAQUE unpickle81datum(PickleContext context, OPAQUE container, int style) throws SQLException {
        context.skipBytes(2);
        long length = context.readLength(true) - 2;
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "unpickle81datum", "byte[] length = {0}", (String) null, (String) null, Long.valueOf(length));
        if (container == null) {
            OPAQUE opaqueObject = new OPAQUE(createOpaqueDescriptor(), this.connection, context.readBytes((int) length));
            if (this.typeNameByUser == null ? !(this.sqlName == null || (this.connection.getVersionNumber() < 12100 ? this.connection.getVersionNumber() <= 10205 ? !this.sqlName.getName().equals(this.connection.getUserName() + '.' + XMLTYPE_STRING_VALUE) : !this.sqlName.getName().equals(PUBLIC_XMLTYPE_STRING_VALUE) : !this.sqlName.getName().equals(SYS_XMLTYPE_STRING_VALUE))) : !(this.connection.getVersionNumber() < 12100 ? this.connection.getVersionNumber() <= 10205 ? !this.typeNameByUser.equals(this.connection.getUserName() + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + XMLTYPE_STRING_VALUE) : !this.typeNameByUser.equals(PUBLIC_XMLTYPE_STRING_VALUE) : !this.typeNameByUser.equals(SYS_XMLTYPE_STRING_VALUE))) {
                return createXml(opaqueObject);
            }
            return opaqueObject;
        }
        container.setValue(context.readBytes((int) length));
        return container;
    }

    OpaqueDescriptor createOpaqueDescriptor() throws SQLException {
        if (this.sqlName == null) {
            return new OpaqueDescriptor(this, this.connection);
        }
        return OpaqueDescriptor.createDescriptor(this.sqlName, this.connection);
    }

    public long getMaxLength() throws SQLException {
        return this.maxLen;
    }

    public boolean isTrustedLibrary() throws SQLException {
        return (this.flagBits & 1) != 0;
    }

    public boolean isModeledInC() throws SQLException {
        return (this.flagBits & KOLOFLCL) != 0;
    }

    public boolean isUnboundedSized() throws SQLException {
        return (this.flagBits & KOLOFLUB) != 0;
    }

    public boolean isFixedSized() throws SQLException {
        return (this.flagBits & KOLOFLFX) != 0;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    }

    @Override // oracle.jdbc.oracore.OracleTypeADT, oracle.jdbc.oracore.OracleNamedType, oracle.jdbc.oracore.OracleType
    public void setConnection(OracleConnection conn) throws SQLException {
        this.connection = conn;
    }
}
