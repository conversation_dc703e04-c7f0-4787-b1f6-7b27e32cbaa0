package oracle.net.mesg;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/net/mesg/NetErrorMessages.class */
public class NetErrorMessages extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"02396", "exceeded maximum idle time, please connect again"}, new Object[]{"03113", "database connection closed by peer (connection_type={0}, source={1}:{2}, target={3}:{4}, protocol={5}, service={6}, detecting_end={7}, server_type={8}, client_program={9}, server_process={10}, session={11}.{12}, user={13}, last_rpc={14}, connection_id={15}{16})"}, new Object[]{"12150", "TNS:unable to send data"}, new Object[]{"12151", "TNS:received bad packet type from network layer"}, new Object[]{"12152", "TNS:unable to send break message"}, new Object[]{"12153", "TNS:not connected"}, new Object[]{"12154", "Cannot connect to database. Could not find alias {0} in {1}."}, new Object[]{"12155", "TNS:received bad datatype in NSWMARKER packet"}, new Object[]{"12156", "TNS:tried to reset line from incorrect state"}, new Object[]{"12157", "TNS:internal network communication error"}, new Object[]{"12158", "TNS:could not initialize parameter subsystem"}, new Object[]{"12159", "TNS:trace file not writeable"}, new Object[]{"12160", "TNS:internal error: Bad error number"}, new Object[]{"12161", "TNS:internal error: partial data received"}, new Object[]{"12162", "TNS:net service name is incorrectly specified"}, new Object[]{"12163", "TNS:connect descriptor is too long"}, new Object[]{"12164", "TNS:Sqlnet.fdf file not present"}, new Object[]{"12165", "TNS:Trying to write trace file into swap space."}, new Object[]{"12166", "TNS:Client can not connect to HO agent."}, new Object[]{"12168", "TNS:Unable to contact LDAP Directory Server"}, new Object[]{"12169", "TNS:Net service name given as connect identifier is too long"}, new Object[]{"12170", "Cannot connect. {0} timeout of {1} for {2}."}, new Object[]{"12171", "TNS:could not resolve connect identifier"}, new Object[]{"12196", "TNS:received an error from TNS"}, new Object[]{"12197", "TNS:keyword-value resolution error"}, new Object[]{"12198", "TNS:could not find path to destination"}, new Object[]{"12200", "TNS:could not allocate memory"}, new Object[]{"12201", "TNS:encountered too small a connection buffer"}, new Object[]{"12202", "TNS:internal navigation error"}, new Object[]{"12203", "TNS:unable to connect to destination"}, new Object[]{"12204", "TNS:received data refused from an application"}, new Object[]{"12205", "TNS:could not get failed addresses"}, new Object[]{"12206", "TNS:received a TNS error during navigation"}, new Object[]{"12207", "TNS:unable to perform navigation"}, new Object[]{"12208", "TNS:could not find the TNSNAV.ORA file"}, new Object[]{"12209", "TNS:encountered uninitialized global"}, new Object[]{"12210", "TNS:error in finding Navigator data"}, new Object[]{"12211", "TNS:needs PREFERRED_CMANAGERS entry in TNSNAV.ORA"}, new Object[]{"12212", "TNS:incomplete PREFERRED_CMANAGERS binding in TNSNAV.ORA"}, new Object[]{"12213", "TNS:incomplete PREFERRED_CMANAGERS binding in TNSNAV.ORA"}, new Object[]{"12214", "TNS:missing local communities entry in TNSNAV.ORA"}, new Object[]{"12215", "TNS:poorly formed PREFERRED_NAVIGATORS Addresses in TNSNAV.ORA"}, new Object[]{"12216", "TNS:poorly formed PREFERRED_CMANAGERS addresses in TNSNAV.ORA"}, new Object[]{"12217", "TNS:could not contact PREFERRED_CMANAGERS in TNSNAV.ORA"}, new Object[]{"12218", "TNS:unacceptable network configuration data"}, new Object[]{"12219", "TNS:missing community name from address in ADDRESS_LIST"}, new Object[]{"12221", "TNS:illegal ADDRESS parameters"}, new Object[]{"12222", "TNS:no support is available for the protocol indicated"}, new Object[]{"12223", "TNS:internal limit restriction exceeded"}, new Object[]{"12224", "TNS:no listener"}, new Object[]{"12225", "TNS:destination host unreachable"}, new Object[]{"12226", "TNS:operating system resource quota exceeded"}, new Object[]{"12227", "TNS:syntax error"}, new Object[]{"12228", "TNS:protocol adapter not loadable"}, new Object[]{"12229", "TNS:Interchange has no more free connections"}, new Object[]{"12230", "TNS:Severe Network error occurred in making this connection"}, new Object[]{"12231", "TNS:No connection possible to destination"}, new Object[]{"12232", "TNS:No path available to destination"}, new Object[]{"12233", "TNS:Failure to accept a connection"}, new Object[]{"12234", "TNS:Redirect to destination"}, new Object[]{"12235", "TNS:Failure to redirect to destination"}, new Object[]{"12236", "TNS:protocol support not loaded"}, new Object[]{"12238", "TNS: NT Operation is aborted"}, new Object[]{"12261", "Cannot connect to database. Syntax error in Easy Connect connection string {0}."}, new Object[]{"12262", "Cannot connect to database. Could not resolve hostname {0} in Easy Connect connection string {1}."}, new Object[]{"12263", "Failed to access tnsnames.ora in the directory configured as TNS admin: {0}. The file does not exist, or is not accessible."}, new Object[]{"12268", "server uses weak encryption/crypto-checksumming version"}, new Object[]{"12269", "client uses weak encryption/crypto-checksumming version"}, new Object[]{"12270", "Client NOT using TCPS protocol to connect to server"}, new Object[]{"12500", "TNS:listener failed to start a dedicated server process"}, new Object[]{"12502", "TNS:listener received no CONNECT_DATA from client"}, new Object[]{"12503", "TNS:listener received an invalid REGION from client"}, new Object[]{"12504", "TNS:listener was not given the SERVICE_NAME in CONNECT_DATA"}, new Object[]{"12505", "Cannot connect to database. SID {0} is not registered with the listener at {1}."}, new Object[]{"12506", "TNS:listener rejected connection based on service ACL filtering"}, new Object[]{"12508", "TNS:listener could not resolve the COMMAND given"}, new Object[]{"12509", "TNS:listener failed to redirect client to service handler"}, new Object[]{"12510", "TNS:database temporarily lacks resources to handle the request"}, new Object[]{"12511", "TNS:service handler found but it is not accepting connections"}, new Object[]{"12513", "TNS:service handler found but it has registered for a different protocol"}, new Object[]{"12514", "Cannot connect to database. Service {0} is not registered with the listener at {1}."}, new Object[]{"12515", "TNS:listener could not find a handler for this presentation"}, new Object[]{"12516", "Cannot connect to database. Listener at {0} does not have a protocol handler for {1} ready or registered for service {2}."}, new Object[]{"12518", "TNS:listener could not hand off client connection"}, new Object[]{"12519", "TNS:no appropriate service handler found"}, new Object[]{"12520", "Cannot connect to database. Listener at {0} does not have handler for {1} server type ready or registered for service {2}."}, new Object[]{"12521", "Cannot connect to database. Instance {0} for service {1} is not registered with the listener at {2}."}, new Object[]{"12522", "TNS:listener could not find available instance with given INSTANCE_ROLE"}, new Object[]{"12523", "TNS:listener could not find instance appropriate for the client connection"}, new Object[]{"12524", "TNS:listener could not resolve HANDLER_NAME given in connect descriptor"}, new Object[]{"12525", "TNS:listener has not received client's request in time allowed"}, new Object[]{"12526", "TNS:listener: all appropriate instances are in restricted mode"}, new Object[]{"12527", "TNS:listener: all instances are in restricted mode or blocking new connections"}, new Object[]{"12528", "TNS:listener: all appropriate instances are blocking new connections"}, new Object[]{"12529", "TNS:connect request rejected based on current filtering rules"}, new Object[]{"12530", "TNS:listener: rate limit reached"}, new Object[]{"12531", "TNS:cannot allocate memory"}, new Object[]{"12532", "TNS:invalid argument"}, new Object[]{"12533", "TNS:illegal ADDRESS parameters"}, new Object[]{"12534", "TNS:operation not supported"}, new Object[]{"12535", "TNS:operation timed out"}, new Object[]{"12536", "TNS:operation would block"}, new Object[]{"12537", "TNS:connection closed"}, new Object[]{"12538", "TNS:no such protocol adapter"}, new Object[]{"12539", "TNS:buffer over- or under-flow"}, new Object[]{"12540", "TNS:internal limit restriction exceeded"}, new Object[]{"12541", "Cannot connect. No listener at {0}."}, new Object[]{"12542", "TNS:address already in use"}, new Object[]{"12543", "TNS:destination host unreachable"}, new Object[]{"12544", "TNS:contexts have different wait/test functions"}, new Object[]{"12545", "Connect failed because target host or object does not exist"}, new Object[]{"12546", "TNS:permission denied"}, new Object[]{"12547", "TNS:lost contact"}, new Object[]{"12548", "TNS:incomplete read or write"}, new Object[]{"12549", "TNS:operating system resource quota exceeded"}, new Object[]{"12550", "TNS:syntax error"}, new Object[]{"12551", "TNS:missing keyword"}, new Object[]{"12552", "TNS:operation was interrupted"}, new Object[]{"12554", "TNS:current operation is still in progress"}, new Object[]{"12555", "TNS:permission denied"}, new Object[]{"12556", "TNS:no caller"}, new Object[]{"12557", "TNS:protocol adapter not loadable"}, new Object[]{"12558", "TNS:protocol adapter not loaded"}, new Object[]{"12560", "Database communication protocol error."}, new Object[]{"12561", "TNS:unknown error"}, new Object[]{"12562", "TNS:bad global handle"}, new Object[]{"12563", "TNS:operation was aborted"}, new Object[]{"12564", "TNS:connection refused"}, new Object[]{"12566", "TNS:protocol error"}, new Object[]{"12569", "TNS:packet checksum failure"}, new Object[]{"12570", "TNS:packet reader failure"}, new Object[]{"12571", "TNS:packet writer failure"}, new Object[]{"12574", "TNS:Application generated error"}, new Object[]{"12575", "TNS:dhctx busy"}, new Object[]{"12576", "TNS:handoff not supported for this session"}, new Object[]{"12578", "TNS:wallet open failed"}, new Object[]{"12579", "TNS:transport connection failed"}, new Object[]{"12582", "TNS:invalid operation"}, new Object[]{"12583", "TNS:no reader"}, new Object[]{"12585", "TNS:data truncation"}, new Object[]{"12589", "TNS:connection not bequeathable"}, new Object[]{"12590", "TNS:no I/O buffer"}, new Object[]{"12591", "TNS:event signal failure"}, new Object[]{"12592", "TNS:bad packet"}, new Object[]{"12593", "TNS:no registered connection"}, new Object[]{"12595", "TNS:no confirmation"}, new Object[]{"12596", "TNS:internal inconsistency"}, new Object[]{"12597", "TNS:connect descriptor already in use"}, new Object[]{"12598", "TNS:banner registration failed"}, new Object[]{"12599", "TNS:cryptographic checksum mismatch"}, new Object[]{"12600", "TNS: string open failed"}, new Object[]{"12601", "TNS:information flags check failed"}, new Object[]{"12602", "TNS: Connection Pooling limit reached"}, new Object[]{"12606", "TNS: Application timeout occurred"}, new Object[]{"12607", "TNS: Connect timeout occurred"}, new Object[]{"12608", "TNS: Send timeout occurred"}, new Object[]{"12609", "TNS: Receive timeout occurred"}, new Object[]{"12611", "TNS:operation is not portable"}, new Object[]{"12612", "TNS:connection is busy"}, new Object[]{"12615", "TNS:preempt error"}, new Object[]{"12616", "TNS:no event signals"}, new Object[]{"12617", "TNS:bad 'what' type"}, new Object[]{"12618", "TNS:versions are incompatible"}, new Object[]{"12619", "TNS:unable to grant requested service"}, new Object[]{"12620", "TNS:requested characteristic not available"}, new Object[]{"12622", "TNS:event notifications are not homogeneous"}, new Object[]{"12623", "TNS:operation is illegal in this state"}, new Object[]{"12624", "TNS:connection is already registered"}, new Object[]{"12625", "TNS:missing argument"}, new Object[]{"12626", "TNS:bad event type"}, new Object[]{"12628", "TNS:no event callbacks"}, new Object[]{"12629", "TNS:no event test"}, new Object[]{"12630", "Native service operation not supported"}, new Object[]{"12631", "Username retrieval failed"}, new Object[]{"12632", "Role fetch failed"}, new Object[]{"12633", "No shared authentication services"}, new Object[]{"12634", "Memory allocation failed"}, new Object[]{"12635", "No authentication adapters available"}, new Object[]{"12636", "Packet send failed"}, new Object[]{"12637", "Packet receive failed"}, new Object[]{"12638", "Credential retrieval failed"}, new Object[]{"12639", "Authentication service negotiation failed"}, new Object[]{"12640", "Authentication adapter initialization failed"}, new Object[]{"12641", "Authentication service failed to initialize"}, new Object[]{"12642", "No session key"}, new Object[]{"12643", "Client received internal error from server"}, new Object[]{"12645", "Parameter does not exist."}, new Object[]{"12646", "Invalid value specified for boolean parameter"}, new Object[]{"12647", "Authentication required"}, new Object[]{"12648", "Encryption or data integrity algorithm list empty"}, new Object[]{"12649", "Unknown encryption or data integrity algorithm"}, new Object[]{"12650", "No common encryption or data integrity algorithm"}, new Object[]{"12651", "Encryption or data integrity algorithm unacceptable"}, new Object[]{"12652", "String truncated"}, new Object[]{"12653", "Authentication control function failed"}, new Object[]{"12654", "Authentication conversion failed"}, new Object[]{"12655", "Password check failed"}, new Object[]{"12656", "Cryptographic checksum mismatch"}, new Object[]{"12657", "No algorithms installed"}, new Object[]{"12658", "ANO service required but TNS version is incompatible"}, new Object[]{"12659", "Error received from other process"}, new Object[]{"12660", "Encryption or crypto-checksumming parameters incompatible"}, new Object[]{"12661", "Protocol authentication to be used"}, new Object[]{"12662", "proxy ticket retrieval failed"}, new Object[]{"12663", "Services required by client not available on the server"}, new Object[]{"12664", "Services required by server not available on the client"}, new Object[]{"12665", "NLS string open failed"}, new Object[]{"12666", "Dedicated server: outbound transport protocol different from inbound"}, new Object[]{"12667", "Shared server: outbound transport protocol different from inbound"}, new Object[]{"12668", "Dedicated server: outbound protocol does not support proxies"}, new Object[]{"12669", "Shared server: outbound protocol does not support proxies"}, new Object[]{"12670", "Incorrect role password"}, new Object[]{"12671", "Shared server: adapter failed to save context"}, new Object[]{"12672", "Database logon failure"}, new Object[]{"12673", "Dedicated server: context not saved"}, new Object[]{"12674", "Shared server: proxy context not saved"}, new Object[]{"12675", "External user name not available yet"}, new Object[]{"12676", "Server received internal error from client"}, new Object[]{"12677", "Authentication service not supported by database link"}, new Object[]{"12678", "Authentication disabled but required"}, new Object[]{"12679", "Native services disabled by other process but required"}, new Object[]{"12680", "Native services disabled but required"}, new Object[]{"12681", "Login failed: the SecurID card does not have a pincode yet"}, new Object[]{"12682", "Login failed: the SecurID card is in next PRN mode"}, new Object[]{"12683", "encryption/crypto-checksumming: no Diffie-Hellman seed"}, new Object[]{"12684", "encryption/crypto-checksumming: Diffie-Hellman seed too small"}, new Object[]{"12685", "Native service required remotely but disabled locally"}, new Object[]{"12686", "Invalid command specified for a service"}, new Object[]{"12687", "Credentials expired."}, new Object[]{"12688", "Login failed: the SecurID server rejected the new pincode"}, new Object[]{"12689", "Server Authentication required, but not supported"}, new Object[]{"12690", "Server Authentication failed, login cancelled"}, new Object[]{"12691", "TTC RPC not supported by Oracle Connection Manager in Traffic Director mode"}, new Object[]{"12692", "functionality not supported by Oracle Connection Manager in Traffic Director mode"}, new Object[]{"12693", "PRCP is not configured in Oracle Connection Manager in Traffic Director mode"}, new Object[]{"12694", "non-PRCP connection requested when PRCP is configured in OracleConnection Manager in Traffic Director mode"}, new Object[]{"12695", "This statement cannot be executed with Oracle Connection Manager in Traffic Director mode with PRCP enabled"}, new Object[]{"12696", "Double Encryption Turned On, login disallowed"}, new Object[]{"12697", "PRCP: Internal error"}, new Object[]{"12699", "Native service internal error"}, new Object[]{"56611", "DRCP: Timeout while waiting for a server"}, new Object[]{"17800", "Got minus one from a read call."}, new Object[]{"17801", "Internal error."}, new Object[]{"17820", "The network adapter could not establish the connection."}, new Object[]{"17821", "The network adapter in use is invalid"}, new Object[]{"17822", "MSGQ adapter got a timeout while connecting the initial socket."}, new Object[]{"17823", "MSGQ adapter got a timeout while exchanging queue names."}, new Object[]{"17824", "MSGQ adapter read unexpected data on socket."}, new Object[]{"17825", "MSGQ adapter can only support one outstanding message."}, new Object[]{"17826", "NTMQ packet received on remote queue is not valid"}, new Object[]{"17827", "The Network Adapter could not disconnect"}, new Object[]{"17828", "Bequeath Network Adapter connection timeout"}, new Object[]{"17829", "Invalid DATA URI format. \";base64,\" is missing from uri."}, new Object[]{"17850", "The protocol value pair is missing."}, new Object[]{"17851", "TNS address string parsing error."}, new Object[]{"17852", "The connect data in the TNS address is not valid."}, new Object[]{"17853", "The hostname in the TNS address was not specified."}, new Object[]{"17854", "The port number in the address was not specified."}, new Object[]{"17855", "The CONNECT_DATA in the TNS address is missing."}, new Object[]{"17856", "The SID or SERVICE_NAME in the TNS address is missing."}, new Object[]{"17857", "An ADDRESS value pair was not defined in the TNS address."}, new Object[]{"17858", "JNDI package failure"}, new Object[]{"17859", "JNDI access package not initialized."}, new Object[]{"17860", "JNDI class not found"}, new Object[]{"17861", "User properties not initialized, JNDI access cannot be used."}, new Object[]{"17862", "Naming factory not defined, JNDI access cannot be completed."}, new Object[]{"17863", "Naming provider not defined, JNDI access cannot be completed."}, new Object[]{"17864", "Profile name not defined, JNDI access cannot be completed."}, new Object[]{"17865", "Invalid connection string format, a valid format is: \"host:port:sid\"."}, new Object[]{"17866", "Invalid number format for port number"}, new Object[]{"17867", "Invalid connection string format, a valid format is: \"//host[:port][/service_name]\""}, new Object[]{"17868", "Unknown host specified."}, new Object[]{"17869", "System property oracle.net.tns_admin was empty."}, new Object[]{"17870", "Connect identifier was empty."}, new Object[]{"17871", "Invalid read path."}, new Object[]{"17872", "Could not resolve the connect identifier."}, new Object[]{"17873", "File error."}, new Object[]{"17874", "Invalid LDAP url specified."}, new Object[]{"17875", "Invalid LDAP configuration {0}={1}"}, new Object[]{"17876", "Unable to get LDAP Authentication details."}, new Object[]{"17877", "Invalid CONNECT_DATA configuration {0}={1}."}, new Object[]{"17900", "Invalid operation, not connected."}, new Object[]{"17901", "Already connected."}, new Object[]{"17902", "End of TNS data channel."}, new Object[]{"17903", "Size Data Unit (SDU) mismatch."}, new Object[]{"17904", "Bad packet type."}, new Object[]{"17905", "Unexpected packet."}, new Object[]{"17906", "Connection refused"}, new Object[]{"17907", "Invalid packet length."}, new Object[]{"17908", "Connection string was null."}, new Object[]{"17909", "Socket channel is closed."}, new Object[]{"17950", "Invalid version of SSL specified."}, new Object[]{"17951", "The ssl protocol specified is not supported."}, new Object[]{"17952", "Invalid cipher suites specified"}, new Object[]{"17953", "The cipher suite specified is not supported."}, new Object[]{"17954", "The configured DN, \"{0}\", does not match the DN, \"{1}\", of the server''s certificate"}, new Object[]{"17956", "Unable to parse the wallet location supplied"}, new Object[]{"17957", "Unable to initialize the key store."}, new Object[]{"17958", "Unable to initialize the trust store."}, new Object[]{"17959", "Unable to initialize ssl context."}, new Object[]{"17960", "The peer is unverified."}, new Object[]{"17961", "The method specified in wallet_location is not supported"}, new Object[]{"17962", "Redirect failure: Protocol downgrade"}, new Object[]{"17963", "Redirect failure: Security parameters not allowed"}, new Object[]{"17964", "Redirect failure: Failed to parse redirect address"}, new Object[]{"17965", "Host name(s): \"{0}\" does not match the CN: \"{1}\" or SAN(s): \"{2}\" of the server''s certificate"}, new Object[]{"17966", "Neither host name(s): \"{0}\" nor service name: \"{1}\" matches the CN: \"{2}\" or SAN(s): \"{3}\" of the server''s certificate"}, new Object[]{"17967", "SSL Handshake failure."}, new Object[]{"17968", "Invalid ssl certificate alias / thumbprint."}, new Object[]{"17969", "Invalid Base64 in wallet_location property."}, new Object[]{"17970", "Could not find the specified private key in the PEM file."}, new Object[]{"17971", "Unable to parse the given PEM file / DataURI."}, new Object[]{"17972", "No certificate found in the given PEM file / DataURI."}, new Object[]{"18900", "Failed to enable encryption."}, new Object[]{"18901", "Wrong byte number in NA packet."}, new Object[]{"18902", "Wrong magic number in NA packet."}, new Object[]{"18903", "Unknown authentication, encryption or data integrity algorithm"}, new Object[]{"18904", "Invalid parameter, use one of ACCEPTED, REJECTED, REQUESTED or REQUIRED."}, new Object[]{"18905", "Wrong number of service subpackets."}, new Object[]{"18906", "Supervisor service received status failure."}, new Object[]{"18907", "Authentication service received status failure."}, new Object[]{"18908", "Service classes not found in oracle.net.ano package."}, new Object[]{"18909", "Invalid ANO driver."}, new Object[]{"18910", "Error in array header received."}, new Object[]{"18911", "Received unexpected length for a variable length NA type."}, new Object[]{"18912", "Invalid length for an NA type."}, new Object[]{"18913", "Invalid NA packet type received."}, new Object[]{"18914", "Unexpected NA packet type received."}, new Object[]{"18915", "Unknown encryption or data integrity algorithm."}, new Object[]{"18916", "Invalid encryption algorithm from the server."}, new Object[]{"18917", "Encryption class not installed."}, new Object[]{"18918", "Data integrity class not installed."}, new Object[]{"18919", "Invalid data integrity algorithm received from server."}, new Object[]{"18920", "Received invalid services from the server"}, new Object[]{"18921", "Received incomplete services from the server"}, new Object[]{"18922", "Level should be one of ACCEPTED, REJECTED, REQUESTED or REQUIRED."}, new Object[]{"18923", "The service in process is not supported"}, new Object[]{"18924", "Kerberos5 adaptor could not retrieve credentials (TGT) from the cache."}, new Object[]{"18925", "Error during Kerberos5 authentication."}, new Object[]{"18926", "Kerberos5 adaptor could not create context."}, new Object[]{"18927", "Mutual authentication failed during Kerberos5 authentication."}, new Object[]{"18950", "Break packet was received."}, new Object[]{"18951", "NL exception was generated"}, new Object[]{"18952", "SO exception was generated"}, new Object[]{"18953", "Socket connect timed out."}, new Object[]{"18954", "Socket read timed out."}, new Object[]{"18955", "Invalid socket connect time out value."}, new Object[]{"18956", "Invalid socket read time out value."}, new Object[]{"18957", "Connection initialization failed with error number: "}, new Object[]{"18958", "Invalid refuse packet data."}, new Object[]{"18997", "Listener refused the connection with the following error"}, new Object[]{"18998", "The Connection descriptor used by the client was"}, new Object[]{"18999", "Oracle error"}, new Object[]{"28725", "Connection initialization failed, probable invalid configuration for CMAN in Traffic Director mode"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
