package oracle.net.ns;

import java.io.IOException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIOHeader.class */
final class NIOHeader implements Diagnosable {
    private static final String CLASS_NAME = NIOHeader.class.getName();
    int length;
    int packetChecksum;
    int flags;
    int headerChecksum;
    int type;
    final SessionAtts session;

    NIOHeader(SessionAtts session) {
        this.session = session;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.session.getDiagnosable();
    }

    final void readHeaderBuffer() throws IOException {
        this.session.headerBufferForRead = this.session.readBuffer.slice();
        this.session.headerBufferForRead.clear();
        this.session.headerBufferForRead.limit(8);
        this.session.headerBufferForRead.rewind();
        readNSHeader();
    }

    final void readNSHeader() throws IOException {
        if (this.session.isLargeSDU && (this.type == 6 || this.type == 12 || this.type == 15 || this.type == 14)) {
            this.length = this.session.headerBufferForRead.getInt() & (-1);
        } else {
            this.length = this.session.headerBufferForRead.getShort() & 65535;
            this.packetChecksum = this.session.headerBufferForRead.getShort() & 65535;
        }
        this.type = this.session.headerBufferForRead.get() & 255;
        this.flags = this.session.headerBufferForRead.get() & 255;
        this.headerChecksum = this.session.headerBufferForRead.getShort() & 65535;
    }

    final void fillHeaderBuffer() throws IOException {
        this.session.headerBufferForWrite.clear();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "fillHeaderBuffer", "HeaderBuffer : {0}", (String) null, (String) null, this.session.headerBufferForWrite);
        if (this.session.isLargeSDU && (this.type == 6 || this.type == 12 || this.type == 15 || this.type == 14)) {
            this.session.headerBufferForWrite.putInt(this.length);
        } else {
            this.session.headerBufferForWrite.putShort((short) this.length);
            this.session.headerBufferForWrite.putShort((short) 0);
        }
        this.session.headerBufferForWrite.put((byte) this.type);
        this.session.headerBufferForWrite.put((byte) this.flags);
        this.session.headerBufferForWrite.putShort((short) 0);
    }
}
