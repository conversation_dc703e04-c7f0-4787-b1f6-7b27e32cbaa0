package oracle.net.ns;

import java.util.Arrays;
import oracle.jdbc.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/net/ns/SQLnetDef.class */
public interface SQLnetDef {
    public static final boolean DEBUG = false;
    public static final boolean ASSERT = false;
    public static final int NSPTCN = 1;
    public static final int NSPTAC = 2;
    public static final int NSPTAK = 3;
    public static final int NSPTRF = 4;
    public static final int NSPTRD = 5;
    public static final int NSPTDA = 6;
    public static final int NSPTNL = 7;
    public static final int NSPTAB = 9;
    public static final int NSPTRS = 11;
    public static final int NSPTMK = 12;
    public static final int NSPTAT = 13;
    public static final int NSPTCNL = 14;
    public static final int NSPTDD = 15;
    public static final int NSPTHI = 19;
    public static final byte NSPHDLEN = 0;
    public static final byte NSPHDPSM = 2;
    public static final byte NSPHDTYP = 4;
    public static final byte NSPHDFLGS = 5;
    public static final byte NSPHDHSM = 6;
    public static final byte NSPSIZHD = 8;
    public static final byte NSPFSID = 1;
    public static final byte NSPFRDS = 2;
    public static final byte NSPFRDR = 4;
    public static final byte NSPFLSD = 32;
    public static final byte NO_HEADER_FLAGS = 0;
    public static final byte NSPFSRN = 8;
    public static final String MAX_NS_VERSION = "319";
    public static final byte NSPCNVSN = 8;
    public static final byte NSPCNLOV = 10;
    public static final byte NSPCNOPT = 12;
    public static final byte NSPCNSDU = 14;
    public static final byte NSPCNTDU = 16;
    public static final byte NSPCNNTC = 18;
    public static final byte NSPCNTNA = 20;
    public static final byte NSPCNONE = 22;
    public static final byte NSPCNLEN = 24;
    public static final byte NSPCNOFF = 26;
    public static final byte NSPCNMXC = 28;
    public static final byte NSPCNFL0 = 32;
    public static final byte NSPCNFL1 = 33;
    public static final byte NSPCNTMO = 50;
    public static final byte NSPCNTCK = 52;
    public static final byte NSPCNADL = 54;
    public static final byte NSPCNAOF = 56;
    public static final byte NSPCNLSD = 58;
    public static final byte NSPCNLTD = 62;
    public static final byte NSPCNCFL = 66;
    public static final byte NSPCNCFL2 = 70;
    public static final byte NSPCNDAT = 74;
    public static final int NSPMXCDATA = 230;
    public static final int NSINAWANTED = 1;
    public static final int NSINAINTCHG = 2;
    public static final int NSINADISABLEFORCONNECTION = 4;
    public static final int NSINANOSERVICES = 8;
    public static final int NSINAREQUIRED = 16;
    public static final int NSINAAUTHWANTED = 32;
    public static final int NSISUPSECRENEG = 128;
    public static final int NSGDONTCARE = 1;
    public static final int NSGHDX = 2;
    public static final int NSGFDX = 4;
    public static final int NSGHDRCHKSUM = 8;
    public static final int NSGPAKCHKSUM = 16;
    public static final int NSGBROKEN = 32;
    public static final int NSGUSEVIO = 64;
    public static final int NSGOSAUTHOK = 128;
    public static final int NSGSENDATTN = 512;
    public static final int NSGRECVATTN = 1024;
    public static final int NSGNOATTNPR = 2048;
    public static final int NSGRAW = 4096;
    public static final int NTBPAKSTR = 0;
    public static final int NTBASYNC = 1;
    public static final int NTBCALLBACK = 2;
    public static final int NTBMOREDATA = 3;
    public static final int NTBSPAWNER = 4;
    public static final int NTBTDU4RD = 5;
    public static final int NTBCNFMRLS = 6;
    public static final int NTBHANGON = 7;
    public static final int NTBTEST = 8;
    public static final int NTBFDX = 9;
    public static final int NTBURG = 10;
    public static final int NTBSIGURG = 11;
    public static final int NTBSIGPIPE = 12;
    public static final int NTBSIGIO = 13;
    public static final int NTBHANDOFF = 14;
    public static final int NTBGRANT = 15;
    public static final int NTCPAKSTR = 1;
    public static final int NTCASYNC = 2;
    public static final int NTCCALLBACK = 4;
    public static final int NTCMOREDATA = 8;
    public static final int NTCSPAWNER = 16;
    public static final int NTCTDU4RD = 32;
    public static final int NTCCNFMRLS = 64;
    public static final int NTCHANGON = 128;
    public static final int NTCTEST = 256;
    public static final int NTCFDX = 512;
    public static final int NTCURG = 1024;
    public static final int NTCSIGURG = 2048;
    public static final int NTCSIGPIPE = 4096;
    public static final int NTCSIGIO = 8192;
    public static final int NTCHANDOFF = 16384;
    public static final int NTCGRANT = 32768;
    public static final int NSPCNCON = Integer.MIN_VALUE;
    public static final int NSPCNCAT = 1073741824;
    public static final int NSPCNCNT = 2;
    public static final int NSPCNOOB = 1;
    public static final int SESSION_ID_SIZE = 16;
    public static final int PROBE_PACKET_SIZE = 26;
    public static final byte NSPACVSN = 8;
    public static final byte NSPACOPT = 10;
    public static final byte NSPACSDU = 12;
    public static final byte NSPACTDU = 14;
    public static final byte NSPACONE = 16;
    public static final byte NSPACLEN = 18;
    public static final byte NSPACOFF = 20;
    public static final byte NSPACFL0 = 22;
    public static final byte NSPACFL1 = 23;
    public static final byte NSPACTMO = 24;
    public static final byte NSPACTCK = 26;
    public static final byte NSPACADL = 28;
    public static final byte NSPACAOF = 30;
    public static final byte NSPACLSD = 32;
    public static final byte NSPACLTD = 36;
    public static final byte NSPACCFL = 40;
    public static final byte NSPACFL2 = 41;
    public static final byte NSPACUUIDLEN = 16;
    public static final byte NSPACUUID = 45;
    public static final byte NSPACV310DAT = 32;
    public static final byte NSPACV315DAT = 41;
    public static final short NSPACCFON = 128;
    public static final byte NSPACCFAT = 64;
    public static final byte NSPACCFNT = 2;
    public static final int NSPACOOB = 1;
    public static final int NSGPCHKSCMD = 16777216;
    public static final int NSGENBFAT = 268435456;
    public static final int NSGPXYCON = 67108864;
    public static final int NSGDISSRN = 134217728;
    public static final byte NSPRFURS = 8;
    public static final byte NSPRFSRS = 9;
    public static final byte NSPRFLEN = 10;
    public static final byte NSPRFDAT = 12;
    public static final byte NSPRDLEN = 8;
    public static final byte NSPRDDAT = 10;
    public static final int NSPDAFLG = 8;
    public static final int NSPDADAT = 10;
    public static final int NSPDAHEADSIZE = 2;
    public static final int NSPDAFZER = 0;
    public static final int NSPDAFTKN = 1;
    public static final int NSPDAFRCF = 2;
    public static final int NSPDAFCFM = 4;
    public static final int NSPDAFRSV = 8;
    public static final int NSPDAFMOR = 32;
    public static final int NSPDAFEOF = 64;
    public static final int NSPDAFIMM = 128;
    public static final int NSPDAFRTS = 256;
    public static final int NSPDAFRNT = 512;
    public static final int NSPDAFCMP = 1024;
    public static final int NSPDAFAPP = 2048;
    public static final int NSPDAFAPP2 = 4096;
    public static final int NSPDAFRTN = 32768;
    public static final int NSPDAFPRTN = 16384;
    public static final int NSPMKTYP = 8;
    public static final int NSPMKODT = 9;
    public static final int NSPMKDAT = 10;
    public static final int NSPMKTD0 = 0;
    public static final int NSPMKTD1 = 1;
    public static final int NSPMKTAT = 2;
    public static final byte NIQBMARK = 1;
    public static final byte NIQRMARK = 2;
    public static final byte NIQIMARK = 3;
    public static final int NSPDDFLG = 8;
    public static final int NSPDDFMRK = 1;
    public static final int NSPDDFSSZ = 2;
    public static final int NSPDDDLN = 12;
    public static final int NSPDDCNT = 16;
    public static final int NSPDD0 = 20;
    public static final int NSPDDCNTMAX = 26;
    public static final int NSPDDSZOFSS = 2;
    public static final int NSPDDSZOFLS = 4;
    public static final int NSPDDSSLMAX = 65535;
    public static final long NSPDDLSLMAX = 4294967295L;
    public static final int NSPDDSMAXTO = 1703910;
    public static final int NSPDDSIZ = 72;
    public static final int NSPCTLCMD = 8;
    public static final int NSPCTLDAT = 10;
    public static final int NSPDFSDULN = 8192;
    public static final int NSPABSSDULN = 2097152;
    public static final int NSPMXSDULN = 65535;
    public static final int NSPMNSDULN = 512;
    public static final int NSPDFTDULN = 2097152;
    public static final int NSPMXTDULN = 2097152;
    public static final int NSPMNTDULN = 255;
    public static final int NSPINSDULN = 255;
    public static final int MQLMXSDULN = 65518;
    public static final int MQLDFSDULN = 65518;
    public static final int MQLMXTDULN = 65518;
    public static final int MQLDFTDULN = 65518;
    public static final String TCP_NODELAY_STR = "TCP.NODELAY";
    public static final String TCP_CONNTIMEOUT_STR = "oracle.net.CONNECT_TIMEOUT";
    public static final String TCP_READTIMEOUT_STR = "oracle.net.READ_TIMEOUT";
    public static final String DISABLE_OOB_STR = "DISABLE_OOB";
    public static final String USE_ZERO_COPY_IO_STR = "USE_ZERO_COPY_IO";
    public static final String FORCE_DNS_LOAD_BALANCING_STR = "FORCE_DNS_LOAD_BALANCING";
    public static final String SSL_SERVER_DN_MATCH = "oracle.net.ssl_server_dn_match";
    public static final String SSL_SERVER_DN_MATCH_DEFAULT = "oracle.net.ssl_server_dn_match_default";
    public static final String SSL_SERVER_CERT_DN = "oracle.net.ssl_server_cert_dn";
    public static final String ORACLE_NET_WALLET_LOCATION = "oracle.net.wallet_location";
    public static final String ORACLE_NET_WALLET_PASSWORD = "oracle.net.wallet_password";
    public static final String SSL_VERSION = "oracle.net.ssl_version";
    public static final String SSL_CIPHER_SUITES = "oracle.net.ssl_cipher_suites";
    public static final String SSL_CERTIFICATE_ALIAS = "oracle.net.ssl_certificate_alias";
    public static final String JAVAX_NET_SSL_KEYSTORE = "javax.net.ssl.keyStore";
    public static final String JAVAX_NET_SSL_KEYSTORETYPE = "javax.net.ssl.keyStoreType";
    public static final String JAVAX_NET_SSL_KEYSTOREPASSWORD = "javax.net.ssl.keyStorePassword";
    public static final String JAVAX_NET_SSL_TRUSTSTORE = "javax.net.ssl.trustStore";
    public static final String JAVAX_NET_SSL_TRUSTSTORETYPE = "javax.net.ssl.trustStoreType";
    public static final String JAVAX_NET_SSL_TRUSTSTOREPASSWORD = "javax.net.ssl.trustStorePassword";
    public static final String SSL_KEYMANAGERFACTORY_ALGORITHM = "ssl.keyManagerFactory.algorithm";
    public static final String SSL_TRUSTMANAGERFACTORY_ALGORITHM = "ssl.trustManagerFactory.algorithm";
    public static final String INSTANCE_NAME_STR = "oracle.jdbc.targetInstanceName";
    public static final String SERVICE_NAME_STR = "oracle.jdbc.targetServiceName";
    public static final String SHARDING_KEY_STR = "oracle.jdbc.targetShardingKey";
    public static final String SUPER_SHARDING_KEY_STR = "oracle.jdbc.targetSuperShardingKey";
    public static final String ALLOW_READ_ONLY_INSTANCE = "oracle.jdbc.readOnlyInstanceAllowed";
    public static final String NEGOTIATED_SDU = "oracle.jdbc.negotiatedSDU";
    public static final String NEGOTIATED_TDU = "oracle.jdbc.negotiatedTDU";
    public static final String NEGOTIATED_COMPRESSION_STATUS = "oracle.jdbc.negotiatedCompressionStatus";
    public static final String NEGOTIATED_ENCRYPTION_ALOGRITHM = "oracle.jdbc.negotiatedEncryptionAlgorithm";
    public static final String NEGOTIATED_CHECKSUM_ALGORITHM = "oracle.jdbc.negotiatedChecksumAlgorithm";
    public static final String NEGOTIATED_CIPHER_SUITE = "oracle.jdbc.negotiatedCipherSuite";
    public static final String NEGOTIATED_AUTHENTICATION_ADAPTOR = "oracle.jdbc.negotiatedAuthenticationAdaptor";
    public static final String CONNECTED_NETWORK_ADDRESS = "oracle.jdbc.connectedNetworkAddress";
    public static final String IS_OOB_ENABLED = "oracle.jdbc.isOOBEnabled";
    public static final String IS_OOB_CHECK_DONE = "oracle.jdbc.isOOBCheckDone";
    public static final String IS_ANO_NEGOTIATION_DONE = "oracle.jdbc.isANONegotiationDone";
    public static final int TCP_NODELAY_OFF = 0;
    public static final int TCP_KEEPALIVE_OFF = 1;
    public static final int TCP_CONNTIMEOUT_OFF = 2;
    public static final int TCP_READTIMEOUT_OFF = 3;
    public static final int SSL_SERVER_DN_MATCH_OFF = 4;
    public static final int ORACLE_NET_WALLET_LOCATION_OFF = 5;
    public static final int SSL_VERSION_OFF = 6;
    public static final int SSL_CIPHER_SUITES_OFF = 7;
    public static final int JAVAX_NET_SSL_KEYSTORE_OFF = 8;
    public static final int JAVAX_NET_SSL_KEYSTORETYPE_OFF = 9;
    public static final int JAVAX_NET_SSL_KEYSTOREPASSWORD_OFF = 10;
    public static final int JAVAX_NET_SSL_TRUSTSTORE_OFF = 11;
    public static final int JAVAX_NET_SSL_TRUSTSTORETYPE_OFF = 12;
    public static final int JAVAX_NET_SSL_TRUSTSTOREPASSWORD_OFF = 13;
    public static final int SSL_KEYMANAGERFACTORY_ALGORITHM_OFF = 14;
    public static final int SSL_TRUSTMANAGERFACTORY_ALGORITHM_OFF = 15;
    public static final int ORACLE_NET_WALLET_PASSWORD_OFF = 16;
    public static final int CONNECT_RETRY_COUNT_OFF = 17;
    public static final int FORCE_DNS_LOAD_BALANCING_OFF = 18;
    public static final int JAVANET_USE_NIO = 20;
    public static final int JAVANET_LOCALIP_MSGQ_OFF = 21;
    public static final int JAVANET_MSGQ_TRANSPORT_OFF = 22;
    public static final int JAVANET_MSGQ_BUSYWAIT_OFF = 23;
    public static final int JAVANET_MSGQ_KERNELWAIT_OFF = 24;
    public static final int JAVANET_OUTBOUND_CONNECT_TIMEOUT_OFF = 25;
    public static final int JAVANET_WEBSOCKET_USER_OFF = 26;
    public static final int JAVANET_WEBSOCKET_PASSWORD_OFF = 27;
    public static final int SSL_SERVER_CERT_DN_OFF = 28;
    public static final int SSL_CERTIFICATE_ALIAS_OFF = 29;
    public static final int JAVANET_HTTPS_PROXY_HOST_OFF = 30;
    public static final int JAVANET_HTTPS_PROXY_PORT_OFF = 31;
    public static final int JAVANET_LOG_BUFFER_ID_OFF = 32;
    public static final int TCP_KEEPIDLE_OFF = 33;
    public static final int TCP_KEEPINTERVAL_OFF = 34;
    public static final int TCP_KEEPCOUNT_OFF = 35;
    public static final int JAVANET_SOCKS_PROXY_HOST_OFF = 36;
    public static final int JAVANET_SOCKS_PROXY_PORT_OFF = 37;
    public static final int SSL_CONTEXT_PROTOCOL_OFF = 38;
    public static final int SSL_SERVER_DN_MATCH_DEFAULT_OFF = 40;
    public static final int SSL_TRUST_CA_CERTS_OFF = 41;
    public static final int PROVIDER_SSL_CONTEXT_OFF = 42;
    public static final int SSL_ALLOW_WEAK_DN_MATCH_OFF = 43;
    public static final int SSL_CERTIFICATE_THUMBPRINT_OFF = 44;
    public static final int JAVANET_PROXY_REMOTE_DNS_OFF = 45;
    public static final int SSL_PEM_PRIVATE_KEY_INDEX_OFF = 46;
    public static final int SSL_USE_SNI_OFF = 47;
    public static final int SSL_SNI_IGNORE_LIST_OFF = 48;
    public static final int SSL_SNI_VALUE_OFF = 49;
    public static final int ORACLE_NET_NTMINOPT = 100;
    public static final int ORACLE_NET_READ_TIMEOUT = 101;
    public static final int ORACLE_NET_SSL_ENCRYPTION_ENABLED = 102;
    public static final int ORACLE_NET_SSL_PEER_CERT_DN = 103;
    public static final int ORACLE_NET_SSL_PEER_CERT_CHAIN = 104;
    public static final int ORACLE_NET_SSL_CIPHER_SUITE = 105;
    public static final int ORACLE_NET_SSL_MATCH_SERVER_DN = 106;
    public static final int ORACLE_NET_SSL_FULL_DN_MATCH = 107;
    public static final int ORACLE_NET_SSL_MATCH_SERVER_DN_WITH = 108;
    public static final int ORACLE_NET_TCP_FAST_OPEN = 109;
    public static final int ORACLE_NET_NTMAXOPT = 110;
    public static final int SEND_ATTN = 33;
    public static final int NET_CONNECTION_ID_PREFIX_LENGTH = 8;
    public static final int NET_CONNECTION_ID_LENGTH = 24;
    public static final String[] TCP_FAST_OPEN_TNS_PARAM_VALID_ENABLE_VALUES = {"true", "yes", OracleConnection.NETWORK_COMPRESSION_ON};
    public static final String[] TCP_FAST_OPEN_TNS_PARAM_VALID_DISABLE_VALUES = {"false", "no", "off"};
    public static final String TCP_FAST_OPEN_PARAM_NAME = "USE_TCP_FAST_OPEN";
    public static final String TCP_FAST_OPEN_PARAM_NAME_2 = "TFO";

    static boolean isValidTcpFastOpenValue(String value) {
        return isTcpFastOpenEnabled(value) || isTcpFastOpenDisabled(value);
    }

    static boolean isTcpFastOpenEnabled(String value) {
        if (value != null && !value.isEmpty()) {
            return Arrays.asList(TCP_FAST_OPEN_TNS_PARAM_VALID_ENABLE_VALUES).contains(value.toLowerCase());
        }
        return false;
    }

    static boolean isTcpFastOpenDisabled(String value) {
        if (value != null && !value.isEmpty()) {
            return Arrays.asList(TCP_FAST_OPEN_TNS_PARAM_VALID_DISABLE_VALUES).contains(value.toLowerCase());
        }
        return false;
    }
}
