package oracle.net.ns;

import java.io.IOException;
import java.nio.charset.Charset;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIORedirectPacket.class */
final class NIORedirectPacket extends NIOPacket {
    String redirectData;

    NIORedirectPacket(NIOHeader header, SessionAtts session) throws IOException {
        super(header, session);
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        byte[] data;
        int rdlen = this.session.payloadBufferForRead.getShort();
        if (rdlen > 0) {
            if (this.session.payloadBufferForRead.hasRemaining()) {
                data = new byte[rdlen];
                this.session.payloadBufferForRead.get(data);
            } else {
                data = this.session.dataChannel.readPayloadDataFromSocketChannel(rdlen);
            }
            this.redirectData = new String(data, Charset.forName("US-ASCII"));
        }
    }
}
