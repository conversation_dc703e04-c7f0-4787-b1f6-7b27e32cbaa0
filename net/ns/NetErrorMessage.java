package oracle.net.ns;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/* loaded from: ojdbc8.jar:oracle/net/ns/NetErrorMessage.class */
public class NetErrorMessage implements Message, Serializable {
    private static final boolean DEBUG = false;
    private String msg;
    private transient ResourceBundle rBundle;
    private static final String messageFile = "oracle.net.mesg.NetErrorMessages";

    @Override // oracle.net.ns.Message
    public String getMessage(int errorNumber, String userMsg) {
        try {
            this.rBundle = ResourceBundle.getBundle(messageFile);
            try {
                String key = String.format("%05d", Integer.valueOf(errorNumber));
                if (userMsg != null && !userMsg.trim().isEmpty() && userMsg.contains("ORA-")) {
                    this.msg = userMsg;
                } else {
                    String _userMsg = (userMsg == null || userMsg.trim().isEmpty()) ? "" : ": " + userMsg;
                    this.msg = "ORA-" + key + ": " + this.rBundle.getString(key) + _userMsg;
                }
            } catch (MissingResourceException e) {
                if (userMsg == null || userMsg.trim().isEmpty()) {
                    this.msg = "ORA-18957: " + this.rBundle.getString(String.valueOf(NetException.NO_ERROR_MESSAGE)) + String.format("%05d", Integer.valueOf(errorNumber));
                } else if (userMsg.contains(errorNumber + "")) {
                    this.msg = userMsg;
                } else {
                    this.msg = "ORA-" + String.format("%05d", Integer.valueOf(errorNumber)) + ": " + userMsg;
                }
            }
            return this.msg;
        } catch (Exception e2) {
            return "Message file 'oracle.net.mesg.NetErrorMessages' is missing.";
        }
    }

    @Override // oracle.net.ns.Message
    public String addArgs(String errMsg, Object... args) {
        return MessageFormat.format(errMsg, args);
    }
}
