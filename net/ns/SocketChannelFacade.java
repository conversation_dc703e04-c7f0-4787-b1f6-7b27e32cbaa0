package oracle.net.ns;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ProtocolFamily;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketOption;
import java.nio.ByteBuffer;
import java.nio.channels.DatagramChannel;
import java.nio.channels.NetworkChannel;
import java.nio.channels.Pipe;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.nio.channels.spi.AbstractSelector;
import java.nio.channels.spi.SelectorProvider;
import java.security.InvalidParameterException;
import java.util.Objects;
import java.util.Set;
import oracle.jdbc.diagnostics.Diagnosable;

/* loaded from: ojdbc8.jar:oracle/net/ns/SocketChannelFacade.class */
public class SocketChannelFacade extends SocketChannel implements Diagnosable {
    private OutputStream underlyingOutStream;
    private InputStream underlyingInStream;

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public /* bridge */ /* synthetic */ NetworkChannel setOption(SocketOption socketOption, Object obj) throws IOException {
        return setOption((SocketOption<SocketOption>) socketOption, (SocketOption) obj);
    }

    public SocketChannelFacade(InputStream in, OutputStream out) {
        super(new SelectorProvider() { // from class: oracle.net.ns.SocketChannelFacade.1
            @Override // java.nio.channels.spi.SelectorProvider
            public DatagramChannel openDatagramChannel() throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override // java.nio.channels.spi.SelectorProvider
            public DatagramChannel openDatagramChannel(ProtocolFamily family) throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override // java.nio.channels.spi.SelectorProvider
            public Pipe openPipe() throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override // java.nio.channels.spi.SelectorProvider
            public AbstractSelector openSelector() throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override // java.nio.channels.spi.SelectorProvider
            public ServerSocketChannel openServerSocketChannel() throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override // java.nio.channels.spi.SelectorProvider
            public SocketChannel openSocketChannel() throws IOException {
                throw new UnsupportedOperationException();
            }
        });
        Objects.requireNonNull(out);
        Objects.requireNonNull(in);
        this.underlyingOutStream = out;
        this.underlyingInStream = in;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public SocketChannel bind(SocketAddress local) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public <T> SocketChannel setOption(SocketOption<T> name, T value) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.NetworkChannel
    public <T> T getOption(SocketOption<T> name) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.NetworkChannel
    public Set<SocketOption<?>> supportedOptions() {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public SocketChannel shutdownInput() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public SocketChannel shutdownOutput() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public Socket socket() {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean isConnected() {
        return false;
    }

    @Override // java.nio.channels.SocketChannel
    public boolean isConnectionPending() {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean connect(SocketAddress remote) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean finishConnect() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public SocketAddress getRemoteAddress() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ReadableByteChannel
    public int read(ByteBuffer dst) throws IOException {
        if (dst == null) {
            throw new InvalidParameterException("cannot be null");
        }
        if (!dst.hasRemaining()) {
            throw new NetException(NetException.ASSERTION_FAILED, "no space left in read destination buffer");
        }
        byte[] buffer = new byte[dst.remaining()];
        int byteRead = this.underlyingInStream.read(buffer);
        if (byteRead > 0) {
            ByteBuffer newBytes = ByteBuffer.wrap(buffer, 0, byteRead);
            dst.put(newBytes);
        }
        return byteRead;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ScatteringByteChannel
    public long read(ByteBuffer[] dsts, int offset, int length) throws IOException {
        throw new UnsupportedOperationException("unsupported read type of operation");
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.WritableByteChannel
    public int write(ByteBuffer src) throws IOException {
        int _size = src.limit();
        byte[] buffer = new byte[_size];
        src.get(buffer, 0, _size);
        this.underlyingOutStream.write(buffer);
        return _size;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.GatheringByteChannel
    public long write(ByteBuffer[] srcs, int offset, int length) throws IOException {
        throw new UnsupportedOperationException("unsupported write type of operation");
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public SocketAddress getLocalAddress() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.spi.AbstractSelectableChannel
    protected void implCloseSelectableChannel() throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.spi.AbstractSelectableChannel
    protected void implConfigureBlocking(boolean block) throws IOException {
        throw new UnsupportedOperationException();
    }
}
