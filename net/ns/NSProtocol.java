package oracle.net.ns;

import java.io.IOException;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.logging.Level;
import javax.net.ssl.SSLContext;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.util.CommaListBuilder;
import oracle.net.ano.Ano;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.nt.AsyncOutboundTimeoutHandler;
import oracle.net.nt.ConnOption;
import oracle.net.nt.ConnectDescription;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.TcpsNTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;
import oracle.net.resolver.AddrResolution;
import org.ietf.jgss.GSSCredential;

/* loaded from: ojdbc8.jar:oracle/net/ns/NSProtocol.class */
public abstract class NSProtocol implements Communication, SQLnetDef, Diagnosable {
    private static final String CLASS_NAME = NSProtocol.class.getName();
    protected final AddrResolution addrRes;
    protected final SessionAtts sAtts;
    DMSFactory.DMSNoun dmsParent = null;
    private Throwable lastConnectException;

    abstract void negotiateConnection(NVFactory nVFactory, NVNavigator nVNavigator, boolean z, boolean z2, DMSFactory.DMSNoun dMSNoun) throws IOException;

    abstract CompletionStage<Void> negotiateConnectionAsync(NVFactory nVFactory, NVNavigator nVNavigator, boolean z, boolean z2, DMSFactory.DMSNoun dMSNoun, AsyncOutboundTimeoutHandler asyncOutboundTimeoutHandler, Executor executor);

    protected abstract void sendMarker(int i, byte b) throws IOException;

    abstract void initializeSessionAttributes() throws IOException;

    abstract void sendProbePacket() throws IOException;

    protected NSProtocol(String connection, @Blind(PropertiesBlinder.class) Properties userProperties, SSLContext sslContext, OracleHostnameResolver hostnameResolver, boolean useDirectBuffers, Diagnosable diagnosable, TraceEventListener traceEventListener) throws NetException {
        if (connection == null) {
            throw new NetException(NetException.CONNECTION_STRING_NULL);
        }
        this.addrRes = new AddrResolution(connection, userProperties, sslContext, hostnameResolver, diagnosable, traceEventListener);
        this.sAtts = new SessionAtts(this, 2097152, 2097152, true, useDirectBuffers, diagnosable);
    }

    @Override // oracle.net.ns.Communication
    public SessionAtts getSessionAttributes() {
        return this.sAtts;
    }

    @Override // oracle.net.ns.Communication
    public void connect(GSSCredential gssCredential, DMSFactory.DMSNoun _dmsParent) throws IOException {
        CommaListBuilder listBuilder;
        if (this.sAtts.connected) {
            throw new NetException(NetException.CONNECTED_ALREADY);
        }
        this.dmsParent = _dmsParent;
        Properties userProperties = this.addrRes.getUp();
        boolean disableOOB = getDisableOOBProperty(userProperties);
        boolean useZeroCopyIO = getZeroCopyIOProperty(userProperties);
        this.sAtts.traceId = (String) userProperties.get("T4CConnection.hashCode");
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "traceId={0}. ", null, null, this.sAtts.traceId);
        configureSessionAttsCompression(userProperties);
        try {
            establishConnection(null, true, this.dmsParent);
            if (NTAdapter.NetworkAdapterType.BEQ.equals(this.sAtts.getNTAdapter().getNetworkAdapterType())) {
                String services = userProperties.getProperty("oracle.net.authentication_services");
                if (services != null && services.trim().length() > 0) {
                    listBuilder = CommaListBuilder.from(services);
                } else {
                    listBuilder = new CommaListBuilder();
                }
                listBuilder.add("BEQ");
                userProperties.setProperty("oracle.net.authentication_services", listBuilder.format());
            }
            configureSessionAttsAno(userProperties);
            negotiateConnection(new NVFactory(), new NVNavigator(), disableOOB, useZeroCopyIO, this.dmsParent);
            if (isTLSEnabled()) {
                ((TcpsNTAdapter) this.sAtts.getNTAdapter()).verifyDN();
            }
            configureSessionAttsAfterNegotiation(userProperties);
            initializeAno(gssCredential);
            cancelOutboundTimeoutInterrupt();
            this.addrRes.connection_redirected = false;
            this.sAtts.initializeNetProperties(userProperties);
        } catch (Throwable th) {
            cancelOutboundTimeoutInterrupt();
            throw th;
        }
    }

    @Override // oracle.net.ns.Communication
    public CompletionStage<Void> connectAsync(GSSCredential gssCredential, DMSFactory.DMSNoun _dmsParent, AsyncOutboundTimeoutHandler loginTimeoutHandler, Executor asyncExecutor) {
        String tNSAddress;
        if (this.sAtts.connected) {
            return CompletionStageUtil.failedStage(new NetException(NetException.CONNECTED_ALREADY));
        }
        this.dmsParent = _dmsParent;
        Properties userProperties = this.addrRes.getUp();
        boolean disableOOB = getDisableOOBProperty(userProperties);
        boolean useZeroCopyIO = getZeroCopyIOProperty(userProperties);
        this.sAtts.traceId = (String) userProperties.get("T4CConnection.hashCode");
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "connectAsync", "traceId={0}. ", null, null, this.sAtts.traceId);
        if (this.addrRes.connection_revised) {
            tNSAddress = this.addrRes.getTNSAddressInUpperCase();
        } else {
            tNSAddress = this.addrRes.getTNSAddress();
        }
        String connection = tNSAddress;
        configureSessionAttsCompression(userProperties);
        AsyncOutboundTimeoutHandler outboundTimeoutHandler = AsyncOutboundTimeoutHandler.newInstance(loginTimeoutHandler);
        return establishConnectionAsync(connection, true, this.dmsParent, outboundTimeoutHandler, asyncExecutor).thenCompose(CompletionStageUtil.normalCompletionHandler(initializedSessionAtts -> {
            configureSessionAttsAno(userProperties);
            return negotiateConnectionAsync(new NVFactory(), new NVNavigator(), disableOOB, useZeroCopyIO, this.dmsParent, outboundTimeoutHandler, asyncExecutor);
        })).thenApply(CompletionStageUtil.normalCompletionHandler(nil -> {
            if (isTLSEnabled()) {
                ((TcpsNTAdapter) this.sAtts.getNTAdapter()).verifyDN();
            }
            configureSessionAttsAfterNegotiation(userProperties);
            outboundTimeoutHandler.setInterruptThread(Thread.currentThread());
            try {
                initializeAno(gssCredential);
                outboundTimeoutHandler.setInterruptThread(null);
                return nil;
            } catch (Throwable th) {
                outboundTimeoutHandler.setInterruptThread(null);
                throw th;
            }
        })).whenComplete((nil2, err) -> {
            outboundTimeoutHandler.cancelTimeout();
            if (err == null) {
                this.addrRes.connection_redirected = false;
                try {
                    this.sAtts.initializeNetProperties(userProperties);
                } catch (IOException initializationFailure) {
                    throw new CompletionException(initializationFailure);
                }
            }
        });
    }

    private final boolean getDisableOOBProperty(Properties userProperties) {
        String disableOOBValue = (String) userProperties.get(SQLnetDef.DISABLE_OOB_STR);
        return "true".equals(disableOOBValue);
    }

    private final boolean getZeroCopyIOProperty(Properties userProperties) {
        if (this.sAtts.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            return false;
        }
        String zeroCopyIOValue = (String) userProperties.get(SQLnetDef.USE_ZERO_COPY_IO_STR);
        if (zeroCopyIOValue != null && "false".equals(zeroCopyIOValue)) {
            return false;
        }
        return true;
    }

    private final void configureSessionAttsCompression(Properties userProperties) {
        this.sAtts.networkCompression = userProperties.getProperty(OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION, "off").toLowerCase();
        this.sAtts.networkCompressionThreshold = Integer.parseInt(userProperties.getProperty(OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_THRESHOLD, OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_THRESHOLD_DEFAULT));
        String levels_string = userProperties.getProperty(OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_LEVELS, OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_LEVELS_DEFAULT);
        this.sAtts.networkCompressionLevelsArray = new ArrayList<>();
        if (levels_string.equals(OracleConnection.CONNECTION_PROPERTY_NETWORK_COMPRESSION_LEVELS_DEFAULT)) {
            this.sAtts.networkCompressionLevelsArray.add(OracleConnection.NETWORK_COMPRESSION_LEVEL_HIGH);
            return;
        }
        String levels_string2 = levels_string.trim();
        String[] levels_tokens = levels_string2.substring(1, levels_string2.length() - 1).split("\\s+|,");
        for (String level : levels_tokens) {
            if (!level.equals("")) {
                this.sAtts.networkCompressionLevelsArray.add(level.toLowerCase());
            }
        }
    }

    private final void configureSessionAttsAno(Properties userProperties) throws NetException {
        this.sAtts.profile = new ClientProfile(userProperties);
        this.sAtts.ano = new Ano();
        this.sAtts.ano.init(this.sAtts, this.sAtts.isJavaNetNIO);
        this.sAtts.anoEnabled = true;
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "configureSessionAttsAno", "traceId={0}, anoEnabled={1}. ", null, null, this.sAtts.traceId, Boolean.valueOf(this.sAtts.anoEnabled));
    }

    private final void configureSessionAttsAfterNegotiation(Properties userProperties) throws IOException {
        this.sAtts.connected = true;
        this.sAtts.nt.setReadTimeoutIfRequired(userProperties);
    }

    private final void initializeAno(GSSCredential gssCredential) throws IOException {
        if (this.sAtts.noAnoServices) {
            if (this.sAtts.profile.getEncryptionLevelNum() == 3 || this.sAtts.profile.getDataIntegrityLevelNum() == 3) {
                throw new NetException(NetException.INVALID_SERVICES_FROM_SERVER, "(encryption / checksumming required by client but disabled by server)");
            }
            this.sAtts.setNetProperty(SQLnetDef.IS_ANO_NEGOTIATION_DONE, "false");
            return;
        }
        begin(Metrics.ConnectionEvent.ASO_NEGOTIATION);
        this.sAtts.ano.negotiation(this.addrRes.connection_redirected, this.sAtts.isJavaNetNIO, gssCredential);
        end(Metrics.ConnectionEvent.ASO_NEGOTIATION);
        this.sAtts.setNetProperty(SQLnetDef.IS_ANO_NEGOTIATION_DONE, "true");
    }

    private final void cancelOutboundTimeoutInterrupt() throws NetException {
        TimeoutInterruptHandler.InterruptTask interruptTask = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, Thread.currentThread());
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cancelOutboundTimeoutInterrupt", "Cancelling  the outbound interrupt timer {0}. ", (String) null, (String) null, interruptTask);
        if (interruptTask != null && interruptTask.isInterrupted()) {
            Thread.interrupted();
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "cancelOutboundTimeoutInterrupt", "Outbound timeout happened, throwing socket timeout exception. ", null, null, new Object[0]);
            throw new NetException(NetException.SO_CONNECTTIMEDOUT);
        }
    }

    @Override // oracle.net.ns.Communication
    public void sendBreak() throws IOException {
        if (this.sAtts.isExpediatedAttentionEnabled()) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendBreak", "Sending urgent marker.  sessionTraceId={0}. ", null, null, this.sAtts.traceId);
            this.sAtts.nt.sendUrgentByte(33);
            if (this.sAtts.isAttentionProcessingEnabled()) {
                sendMarker(2, (byte) 1);
                return;
            }
            return;
        }
        sendMarker(1, (byte) 1);
    }

    @Override // oracle.net.ns.Communication
    public void sendInterrupt() throws IOException {
        if (this.sAtts.isExpediatedAttentionEnabled()) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendInterrupt", "Sending urgent marker.  SessionTraceId={0}. ", null, null, this.sAtts.traceId);
            this.sAtts.nt.sendUrgentByte(33);
            if (this.sAtts.isAttentionProcessingEnabled()) {
                sendMarker(2, (byte) 3);
                return;
            }
            return;
        }
        sendMarker(1, (byte) 3);
    }

    @Override // oracle.net.ns.Communication
    public void setOption(int option, Object value) throws IOException {
        if (option > 100 && option < 110) {
            NTAdapter nt = this.sAtts.getNTAdapter();
            nt.setOption(option, value);
        }
    }

    @Override // oracle.net.ns.Communication
    public Object getOption(int option) throws IOException {
        if (option > 100 && option < 110) {
            NTAdapter nt = this.sAtts.getNTAdapter();
            return nt.getOption(option);
        }
        return null;
    }

    @Override // oracle.net.ns.Communication
    public void abort() throws IOException {
        NTAdapter nt = this.sAtts.getNTAdapter();
        if (nt != null) {
            nt.abort();
        }
    }

    @Override // oracle.net.ns.Communication
    public String getEncryptionName() {
        String ret = null;
        NTAdapter ntadapter = this.sAtts.getNTAdapter();
        try {
            ret = (String) ntadapter.getOption(105);
        } catch (Exception e) {
        }
        if (ret == null && this.sAtts.ano != null) {
            ret = this.sAtts.ano.getEncryptionName();
        }
        if (ret == null) {
            ret = "";
        }
        return ret;
    }

    @Override // oracle.net.ns.Communication
    public String getAccessBanner() {
        return this.sAtts.getConnectData();
    }

    @Override // oracle.net.ns.Communication
    public String getDataIntegrityName() {
        String ret = "";
        if (this.sAtts.ano != null) {
            ret = this.sAtts.ano.getDataIntegrityName();
        }
        return ret;
    }

    @Override // oracle.net.ns.Communication
    public String getAuthenticationAdaptorName() {
        String ret = "";
        if (this.sAtts.ano != null) {
            ret = this.sAtts.ano.getAuthenticationAdaptorName();
        }
        return ret;
    }

    @Override // oracle.net.ns.Communication
    public void cancelTimeout() {
    }

    public void reconnectIfRequired(boolean probe) throws IOException {
        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - this.sAtts.timestampLastIO;
        Level level = Level.INFO;
        SecurityLabel securityLabel = SecurityLabel.UNKNOWN;
        String str = CLASS_NAME;
        Object[] objArr = new Object[4];
        objArr[0] = Long.valueOf(elapsedTime);
        objArr[1] = Integer.valueOf(this.sAtts.timeout);
        objArr[2] = Boolean.valueOf(elapsedTime > ((long) this.sAtts.timeout));
        objArr[3] = this.sAtts.traceId;
        trace(level, securityLabel, str, "reconnectIfRequired", "Elapsed Time={0}, Timeout={1}, Reconnect={2}, SessionTraceId={3}. ", null, null, objArr);
        if (elapsedTime > this.sAtts.timeout) {
            reconnect(probe);
        }
    }

    @Override // oracle.net.ns.Communication
    public void setAuthSessionKey(byte[] sessionKey) throws NetException {
        if (sessionKey != null) {
            if (this.sAtts.isEncryptionActive || this.sAtts.isChecksumActive) {
                this.sAtts.ano.setAuthSessionKey(sessionKey);
            }
        }
    }

    @Override // oracle.net.ns.Communication
    public void doKeyFoldinForExternalAuth() {
        if ((this.sAtts.isEncryptionActive || this.sAtts.isChecksumActive) && !this.sAtts.profile.useWeakCrypto()) {
            this.sAtts.ano.setAuthSessionKey(this.sAtts.ano.getExternalAuthSessionKey());
        }
    }

    public void reconnect(boolean probe) throws IOException {
        try {
            String reconnectAddress = new String(this.sAtts.reconnectAddress);
            this.sAtts.attemptingReconnect = true;
            ConnOption origConnOption = this.sAtts.cOption.getOriginalConnOption();
            this.addrRes.connection_redirected = true;
            this.sAtts.cOption.nt.disconnect();
            establishConnection(reconnectAddress, false, this.dmsParent);
            this.sAtts.cOption.setOriginalConnOption(origConnOption);
            if (probe) {
                sendProbePacket();
            }
        } finally {
            this.sAtts.attemptingReconnect = false;
        }
    }

    protected void validateRedirectResponse(String rdAddress) throws IOException {
        if (!"TCPS".equalsIgnoreCase(this.sAtts.cOption.getOriginalConnOption().protocol)) {
            return;
        }
        try {
            NVNavigator nav = new NVNavigator();
            NVPair rootNVPair = new NVFactory().createNVPair(rdAddress);
            NVPair protocolNV = nav.findNVPairRecurse(rootNVPair, "PROTOCOL");
            if (protocolNV != null && !"TCPS".equalsIgnoreCase(protocolNV.getAtom())) {
                throw new NetException(NetException.REDIRECT_FAILURE_PROTOCOL_DOWNGRADE);
            }
            NVPair securityNV = nav.findNVPairRecurse(rootNVPair, "SECURITY");
            if (securityNV != null) {
                throw new NetException(NetException.REDIRECT_FAILURE_SECURITY_PARAM);
            }
        } catch (NLException e) {
            throw new NetException(NetException.REDIRECT_FAILURE_INVALID_DATA);
        }
    }

    SessionAtts establishConnection(String connection, DMSFactory.DMSNoun dmsParent) throws IOException {
        return establishConnection(connection, false, dmsParent);
    }

    SessionAtts establishConnection(String connection, boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent) throws IOException {
        this.sAtts.cOption = this.addrRes.resolveAndExecute(connection, startNewOCTOInterruptTask, dmsParent);
        if (this.sAtts.cOption == null) {
            return null;
        }
        this.sAtts.nt = this.sAtts.cOption.nt;
        this.sAtts.networkType = this.sAtts.nt.getNetworkAdapterType();
        this.sAtts.setTDU(this.sAtts.cOption.tdu);
        this.sAtts.setSDU(this.sAtts.cOption.sdu);
        this.sAtts.setNetConnectionIdPrefix(this.addrRes.getConnStrategy().getConnectionIdPrefix());
        initializeSessionAttributes();
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "establishConnection", "{0}", null, null, this.sAtts);
        return this.sAtts;
    }

    final CompletionStage<SessionAtts> establishConnectionAsync(String connection, boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        return this.addrRes.resolveAndExecuteAsync(connection, startNewOCTOInterruptTask, dmsParent, outboundTimeoutHandler, asyncExecutor).thenApply(executedConnOption -> {
            this.sAtts.cOption = executedConnOption;
            if (this.sAtts.cOption == null) {
                return null;
            }
            this.sAtts.nt = this.sAtts.cOption.nt;
            this.sAtts.networkType = this.sAtts.nt.getNetworkAdapterType();
            this.sAtts.setTDU(this.sAtts.cOption.tdu);
            this.sAtts.setSDU(this.sAtts.cOption.sdu);
            try {
                initializeSessionAttributes();
                return this.sAtts;
            } catch (IOException initializationFailure) {
                throw new CompletionException(initializationFailure);
            }
        });
    }

    @Override // oracle.net.ns.Communication
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.addrRes.isConnectionSocketKeepAlive();
    }

    @Override // oracle.net.ns.Communication
    public int getSocketReadTimeout() throws IOException {
        String millis = (String) getOption(101);
        int milliseconds = (millis == null || "".equals(millis)) ? 0 : Integer.parseInt(millis);
        return milliseconds;
    }

    @Override // oracle.net.ns.Communication
    public void setSocketReadTimeout(int milliseconds) throws IOException {
        String millis = Integer.toString(milliseconds);
        setOption(101, millis);
    }

    @Override // oracle.net.ns.Communication
    public String getConnectionString() {
        return this.addrRes.getTNSAddress();
    }

    @Blind(PropertiesBlinder.class)
    public Properties getSocketOptions() {
        return this.addrRes.getSocketOptions();
    }

    @Override // oracle.net.ns.Communication
    public int getNegotiatedSDU() throws NetException {
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        return this.sAtts.negotiatedSDU;
    }

    @Override // oracle.net.ns.Communication
    public NetStat getNetworkStat() {
        return this.sAtts.nt.getNetStat();
    }

    @Override // oracle.net.ns.Communication
    public boolean isNetworkCompressionEnabled() {
        return this.sAtts.networkCompressionEnabled;
    }

    @Override // oracle.net.ns.Communication
    public int getOutboundConnectTimeout() {
        int retVal = 0;
        if (this.addrRes != null) {
            retVal = this.addrRes.getConnStrategy().getOutboundConnectTimeout();
        }
        return retVal;
    }

    @Override // oracle.net.ns.Communication
    public boolean isUsingCustomHostnameResolver() {
        return this.addrRes.isUsingCustomHostnameResolver();
    }

    void tryUrgentByte() {
        try {
            this.sAtts.nt.sendUrgentByte(33);
        } catch (IOException e) {
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return getSessionAttributes().getDiagnosable();
    }

    @Override // oracle.net.ns.Communication
    public final List<ConnectDescription> getConnectDescriptions() {
        return this.addrRes.getResolvedDescriptions();
    }

    @Override // oracle.net.ns.Communication
    public final ConnectDescription getConnectedDescription() {
        return this.addrRes.getConnectedDescription();
    }

    @Override // oracle.net.ns.Communication
    public final void setDriverResources(DriverResources driverResources) {
        this.addrRes.setDriverResources(driverResources);
    }

    void lastConnectException(Throwable thr) {
        this.lastConnectException = thr;
    }

    @Override // oracle.net.ns.Communication
    public Throwable lastConnectException() {
        return this.lastConnectException;
    }
}
