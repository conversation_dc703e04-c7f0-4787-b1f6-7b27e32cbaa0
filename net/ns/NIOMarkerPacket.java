package oracle.net.ns;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.nt.MQLNTAdapter;
import oracle.net.nt.NTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIOMarkerPacket.class */
final class NIOMarkerPacket extends NIOPacket {
    private static final String CLASS_NAME = NIOMarkerPacket.class.getName();
    private boolean isReset;
    private boolean isBreak;
    private ByteBuffer markerBuffer;

    NIOMarkerPacket(SessionAtts session) {
        super(session);
        this.header.type = 12;
        this.markerBuffer = ByteBuffer.allocate(11);
    }

    NIOMarkerPacket(NIOHeader header, SessionAtts session) throws IOException {
        super(header, session);
        header.type = 12;
    }

    void writeToSocketChannel(int type, byte markerData) throws IOException {
        this.markerBuffer.clear();
        if (this.session.isLargeSDU) {
            this.markerBuffer.putInt(this.markerBuffer.capacity());
        } else {
            this.markerBuffer.putShort((short) this.markerBuffer.capacity());
            this.markerBuffer.putShort((short) 0);
        }
        this.markerBuffer.put((byte) this.header.type);
        this.markerBuffer.put((byte) this.header.flags);
        this.markerBuffer.putShort((short) 0);
        this.markerBuffer.put((byte) type);
        this.markerBuffer.put((byte) 0);
        this.markerBuffer.put(markerData);
        this.markerBuffer.flip();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "writeToSocketChannel", "Type = {0}, Length = {1}, flags = {2}, MarkerBuffer = {3}, SessionTraceID = {4}", null, null, Integer.valueOf(this.header.type), Integer.valueOf(this.header.length), Integer.valueOf(this.header.flags), this.markerBuffer, this.session.traceId);
        if (this.session.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            ((MQLNTAdapter) this.session.nt).writeToRemoteQueue(this.markerBuffer, false);
            return;
        }
        if ((this.session.networkType == NTAdapter.NetworkAdapterType.TCP || this.session.networkType == NTAdapter.NetworkAdapterType.TCPS) && !this.session.isExpediatedAttentionEnabled()) {
            try {
                this.session.nt.enqueueBlockedWrites(true);
                this.session.socketChannel.write(this.markerBuffer);
                this.session.nt.completeBlockedWrites();
                return;
            } finally {
                this.session.nt.enqueueBlockedWrites(false);
            }
        }
        while (this.markerBuffer.hasRemaining()) {
            this.session.socketChannel.write(this.markerBuffer);
        }
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        int mktype = this.session.payloadBufferForRead.get();
        this.session.payloadBufferForRead.get();
        switch (mktype) {
            case 0:
                this.isBreak = true;
                return;
            case 1:
                int mkdat = this.session.payloadBufferForRead.get();
                if (mkdat == 2) {
                    this.isBreak = false;
                    this.isReset = true;
                    return;
                } else {
                    this.isReset = false;
                    this.isBreak = true;
                    return;
                }
            default:
                throw new NetException(NetException.UNEXPECTED_PKT);
        }
    }

    boolean isBreakPkt() {
        return this.isBreak;
    }

    boolean isResetPkt() {
        return this.isReset;
    }
}
