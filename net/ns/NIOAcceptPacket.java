package oracle.net.ns;

import java.io.IOException;
import java.nio.charset.Charset;
import oracle.jdbc.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIOAcceptPacket.class */
final class NIOAcceptPacket extends NIOPacket {
    protected int version;
    protected int options;
    protected int sduSize;
    protected int tduSize;
    protected int myHWByteOrder;
    protected int flag0;
    protected int flag1;
    String connectData;
    boolean isOOBCheckEnabled;

    NIOAcceptPacket(NIOHeader header, SessionAtts session) throws IOException {
        super(header, session);
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        byte[] data;
        this.version = this.session.payloadBufferForRead.getShort();
        this.options = this.session.payloadBufferForRead.getShort();
        this.sduSize = this.session.payloadBufferForRead.getShort() & 65535;
        this.tduSize = this.session.payloadBufferForRead.getShort() & 65535;
        this.myHWByteOrder = this.session.payloadBufferForRead.getShort();
        int connectDataLen = this.session.payloadBufferForRead.getShort();
        int offsetToConnectData = this.session.payloadBufferForRead.getShort();
        this.flag0 = this.session.payloadBufferForRead.get();
        this.flag1 = this.session.payloadBufferForRead.get();
        this.session.timeout = this.session.payloadBufferForRead.getShort();
        this.session.tick = this.session.payloadBufferForRead.getShort();
        int reconnectAddressLength = this.session.payloadBufferForRead.getShort();
        int reconnectAddressOffset = this.session.payloadBufferForRead.getShort();
        this.session.noAnoServices = (this.flag1 & 8) == 8;
        if (!this.session.noAnoServices) {
            this.session.noAnoServices = (this.flag0 & 4) == 4;
        }
        this.session.timeout -= this.session.tick;
        this.session.timeout *= 10;
        if (reconnectAddressLength > 0) {
            this.session.reconnectAddress = new byte[reconnectAddressLength];
            this.session.payloadBufferForRead.position(reconnectAddressOffset - 8);
            this.session.payloadBufferForRead.get(this.session.reconnectAddress, 0, reconnectAddressLength);
        }
        if (this.version >= 315) {
            this.sduSize = this.session.payloadBufferForRead.getInt(24);
            this.tduSize = this.session.payloadBufferForRead.getInt(28);
            this.session.isLargeSDU = true;
            this.session.dataChannel.setLargeSDU(true);
            this.session.markerPacket.setLargeSDU(true);
            byte compressionFlag = this.session.payloadBufferForRead.get(32);
            boolean isCompressionCompatible = true;
            if (!this.session.cOption.protocol.equalsIgnoreCase("tcp") && (compressionFlag & 2) == 0) {
                isCompressionCompatible = false;
            }
            if (isCompressionCompatible && (compressionFlag & 128) != 0) {
                if ((compressionFlag & 64) != 0) {
                    this.session.negotiatedNetworkCompression = OracleConnection.NETWORK_COMPRESSION_AUTO;
                } else {
                    this.session.negotiatedNetworkCompression = OracleConnection.NETWORK_COMPRESSION_ON;
                }
                this.session.negotiatedNetworkCompressionScheme = (compressionFlag & 60) >> 2;
                this.session.networkCompressionEnabled = true;
                if (this.session.negotiatedNetworkCompressionScheme == 1) {
                    throw new IOException("Unsupported Compression Scheme");
                }
                if (this.session.negotiatedNetworkCompressionScheme == 2) {
                    this.session.compressionCodec = new ZLIBCodec();
                }
            } else {
                this.session.negotiatedNetworkCompression = "off";
                this.session.networkCompressionEnabled = false;
            }
            int flag2 = 0;
            if (this.version >= 318) {
                flag2 = this.session.payloadBufferForRead.getInt(33);
                if ((flag2 & 1) > 0) {
                    this.isOOBCheckEnabled = true;
                } else {
                    this.isOOBCheckEnabled = false;
                }
                if ((flag2 & 16777216) != 0) {
                    this.session.isPollAndCheckEnabled = true;
                } else {
                    this.session.isPollAndCheckEnabled = false;
                }
            }
            if (this.version >= 319) {
                if ((flag2 & SQLnetDef.NSGENBFAT) != 0) {
                    this.session.fastAuthenticationEnabled = true;
                }
                byte[] _buf = new byte[16];
                this.session.payloadBufferForRead.position(37);
                this.session.payloadBufferForRead.get(_buf);
                this.session.setDatabaseUUID(_buf);
                this.session.isConnectedViaProxy = (flag2 & 67108864) != 0;
                this.session.disableSecurityRenegotiation = (flag2 & 134217728) != 0;
            }
        }
        if (this.session.timeout > 0) {
            int bufferOffset = (connectDataLen + offsetToConnectData) - 8;
            this.session.poolEnabled = true;
            this.session.sessionId = new byte[16];
            this.session.payloadBufferForRead.position(bufferOffset);
            this.session.payloadBufferForRead.get(this.session.sessionId, 0, 16);
            this.session.dataChannel.setPoolEnabled(true);
            this.session.timestampLastIO = System.currentTimeMillis();
        }
        if (connectDataLen > 0) {
            if (this.session.payloadBufferForRead.hasRemaining() && this.session.payloadBufferForRead.position(offsetToConnectData - 8).remaining() > 0) {
                data = new byte[connectDataLen];
                this.session.payloadBufferForRead.get(data);
            } else {
                data = this.session.dataChannel.readPayloadDataFromSocketChannel(connectDataLen);
            }
            this.connectData = new String(data, Charset.forName("US-ASCII"));
        } else {
            this.connectData = "";
        }
        this.session.setNegotiatedSDUAndTDU(this.sduSize, this.tduSize);
        this.session.setNegotiatedOptions(this.options);
        if (this.session.networkCompressionEnabled) {
            this.session.dataChannel.initializeNetworkCompressionBuffers();
        }
    }
}
