package oracle.net.ns;

import java.io.IOException;
import java.net.InetAddress;
import java.nio.ByteOrder;
import java.util.Iterator;
import oracle.jdbc.OracleConnection;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.nt.ConnOption;
import oracle.net.nt.NTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIOConnectPacket.class */
final class NIOConnectPacket extends NIOPacket {
    static final boolean DEBUG = true;
    private static final String TUNNEL_CONNECT_DATA_FORMAT = "(DESCRIPTION=(ADDRESS=(PROTOCOL=%s)(HOST=%s)(PORT=%s))(CONNECT_DATA=(TUNNEL_ID=%s)(CONNECTION_ID=%s)(SERVICE_NAME=tunnel)(CMAN_ID=jdbc)))";
    static final int ntCharacteristics = 20376;

    NIOConnectPacket(SessionAtts session) {
        super(session);
    }

    final void writeToSocketChannel(String connectData, boolean sendAttention, boolean useVIO, boolean isUrgentSupported, int sdu, int tdu, int anoFlags) throws IOException {
        if (sdu < 512) {
            sdu = 512;
        }
        if (tdu < 255) {
            tdu = 255;
        }
        if (sdu > 2097152) {
            sdu = 2097152;
        }
        if (tdu > 2097152) {
            tdu = 2097152;
        }
        if (tdu < sdu) {
            tdu = sdu;
        }
        if (this.session.isTunnelConnection) {
            ConnOption co = this.session.cOption.getOriginalConnOption();
            connectData = String.format(TUNNEL_CONNECT_DATA_FORMAT, co.protocol, co.host, Integer.valueOf(co.port), co.service_name, this.session.getNetConnectionId());
        } else if (connectData != null) {
            connectData = prepareConnectData(connectData, this.session);
        }
        int dataLen = connectData == null ? 0 : connectData.length();
        boolean connDataOflow = dataLen > 230;
        this.session.profile.isANOEnabled();
        this.session.prepareWriteBuffer();
        this.session.payloadBufferForWrite.order(ByteOrder.BIG_ENDIAN);
        this.session.payloadBufferForWrite.putShort((short) 319);
        this.session.payloadBufferForWrite.putShort((short) 300);
        int connFlag2 = 0;
        int opt = 1;
        if (sendAttention && isUrgentSupported) {
            opt = 1 | 3072;
            if (this.session.networkType != NTAdapter.NetworkAdapterType.MSGQ && this.session.networkType != NTAdapter.NetworkAdapterType.BEQ) {
                connFlag2 = 1;
            }
        }
        if (useVIO) {
            opt |= 64;
        }
        this.session.payloadBufferForWrite.putShort((short) opt);
        if (sdu < 65535) {
            this.session.payloadBufferForWrite.putShort((short) sdu);
        } else {
            this.session.payloadBufferForWrite.putShort((short) -1);
        }
        if (tdu < 65535) {
            this.session.payloadBufferForWrite.putShort((short) tdu);
        } else {
            this.session.payloadBufferForWrite.putShort((short) -1);
        }
        this.session.payloadBufferForWrite.putShort((short) 20376);
        this.session.payloadBufferForWrite.putShort((short) 0);
        this.session.payloadBufferForWrite.putShort((short) 1);
        this.session.payloadBufferForWrite.putShort((short) dataLen);
        this.session.payloadBufferForWrite.putShort((short) 74);
        this.session.payloadBufferForWrite.putInt(0);
        byte naFlags = (byte) (anoFlags | 128);
        this.session.payloadBufferForWrite.put(naFlags);
        this.session.payloadBufferForWrite.put(naFlags);
        this.session.payloadBufferForWrite.position(42);
        this.session.payloadBufferForWrite.putShort((short) 0);
        this.session.payloadBufferForWrite.putShort((short) 0);
        this.session.payloadBufferForWrite.putShort((short) 0);
        this.session.payloadBufferForWrite.putShort((short) 0);
        this.session.payloadBufferForWrite.putInt(sdu);
        this.session.payloadBufferForWrite.putInt(tdu);
        int compressionFieldBuilder = 0;
        if (this.session.networkCompression.equals(OracleConnection.NETWORK_COMPRESSION_ON) || this.session.networkCompression.equals(OracleConnection.NETWORK_COMPRESSION_AUTO)) {
            compressionFieldBuilder = Integer.MIN_VALUE;
            if (this.session.networkCompression.equals(OracleConnection.NETWORK_COMPRESSION_AUTO)) {
                compressionFieldBuilder = Integer.MIN_VALUE | 1073741824;
            }
            if (!this.session.cOption.protocol.equalsIgnoreCase("tcp")) {
                compressionFieldBuilder |= 2;
            }
            int schemeShiftCounter = 26;
            Iterator<String> it = this.session.networkCompressionLevelsArray.iterator();
            while (it.hasNext()) {
                String level = it.next();
                if (level.equals(OracleConnection.NETWORK_COMPRESSION_LEVEL_LOW)) {
                    compressionFieldBuilder |= 1 << schemeShiftCounter;
                } else if (level.equals(OracleConnection.NETWORK_COMPRESSION_LEVEL_HIGH)) {
                    compressionFieldBuilder |= 2 << schemeShiftCounter;
                }
                schemeShiftCounter -= 4;
            }
        }
        this.session.payloadBufferForWrite.putInt(compressionFieldBuilder);
        this.session.payloadBufferForWrite.putInt(connFlag2);
        this.session.payloadBufferForWrite.position(66);
        if (!connDataOflow && dataLen > 0) {
            this.session.payloadBufferForWrite.put(connectData.getBytes("ASCII"));
        }
        this.header.type = 1;
        this.header.flags = this.session.redirecting ? 4 : 0;
        if (connDataOflow) {
            this.session.nt.enqueueAllWrites(true);
            writeToSocketChannel();
            byte[] connectDataBytes = connectData.getBytes("ASCII");
            this.session.dataChannel.writeDataToSocketChannel(connectDataBytes);
            this.session.nt.completeWrites();
        } else {
            writeToSocketChannel();
        }
        this.session.redirecting = false;
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        throw new UnsupportedOperationException("Attempting to read from a one way packet sent by client");
    }

    static final String prepareConnectData(String cdata, SessionAtts sessionAttributes) {
        try {
            NVNavigator navigator = new NVNavigator();
            NVPair rootNVPair = new NVFactory().createNVPair(cdata);
            changeHostNameToIPAddress(navigator, rootNVPair, sessionAttributes);
            addConnectionId(navigator, rootNVPair, sessionAttributes);
            return rootNVPair.toString();
        } catch (NLException e) {
            return cdata;
        }
    }

    private static final void changeHostNameToIPAddress(NVNavigator navigator, NVPair rootNVPair, SessionAtts sessionAttributes) throws NLException {
        NVPair nvpAddr;
        NVPair nvpHost;
        String configuredHostName;
        NVPair nvpAddrList = navigator.findNVPair(rootNVPair, "ADDRESS_LIST");
        if (nvpAddrList != null) {
            nvpAddr = navigator.findNVPair(nvpAddrList, "ADDRESS");
        } else {
            nvpAddr = navigator.findNVPair(rootNVPair, "ADDRESS");
        }
        if (nvpAddr == null || (nvpHost = navigator.findNVPair(nvpAddr, "HOST")) == null || (configuredHostName = nvpHost.getAtom()) == null) {
            return;
        }
        boolean changeToIPAddress = (configuredHostName.matches("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}") || configuredHostName.matches("[\\[[\\w:]*\\]]")) ? false : true;
        InetAddress connectedSocketAddr = sessionAttributes.nt.getInetAddress();
        if (changeToIPAddress && connectedSocketAddr != null) {
            nvpHost.setAtom(connectedSocketAddr.getHostAddress());
        }
    }

    private static final void addConnectionId(NVNavigator navigator, NVPair rootNVPair, SessionAtts sessionAttributes) throws NLException {
        NVPair connectDataPair = navigator.findNVPair(rootNVPair, "CONNECT_DATA");
        if (connectDataPair != null && sessionAttributes.getNetConnectionId() != null) {
            connectDataPair.addListElement(new NVPair("CONNECTION_ID", sessionAttributes.getNetConnectionId()));
        }
    }
}
