package oracle.net.ns;

import java.io.IOException;
import java.nio.ByteOrder;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIODataDescriptorPacket.class */
public final class NIODataDescriptorPacket extends NIOPacket implements SQLnetDef {
    int totalDataLength;
    int descriptorFLaG;
    int[] sdd;
    boolean useLongDescriptor;
    private static final String CLASS_NAME = NIODataDescriptorPacket.class.getName();
    private static final byte[] STANDARD_SDD_MAX_DD_HEADER_SHORT_SDU = {0, 72, 0, 0, 15, 0, 0, 0};
    private static final byte[] STANDARD_SDD_MAX_DD_HEADER_LARGE_SDU = {0, 0, 0, 72, 15, 0, 0, 0};
    private static final byte[] STANDARD_SDD_MAX_DD_PAYLOAD = {0, 0, 0, 2, 0, 25, -1, -26, 0, 0, 0, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1};

    @Override // oracle.net.ns.NIOPacket, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    NIODataDescriptorPacket(SessionAtts sAtts) {
        super(sAtts);
        this.sdd = new int[26];
        this.useLongDescriptor = false;
        this.header.type = 15;
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        ByteOrder originalOrder = this.session.readBuffer.order();
        this.session.readBuffer.order(ByteOrder.BIG_ENDIAN);
        this.descriptorFLaG = this.session.payloadBufferForRead.getInt();
        if ((this.descriptorFLaG & 2) != 0) {
            this.useLongDescriptor = false;
        } else {
            this.useLongDescriptor = true;
        }
        this.totalDataLength = this.session.payloadBufferForRead.getInt();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readPayloadBuffer", "DescriptorFlag = {0}, UseLongDescriptor = {1}, TotalDataLength = {2}, SessionTraceId = {3}", null, null, Integer.valueOf(this.descriptorFLaG), Boolean.valueOf(this.useLongDescriptor), Integer.valueOf(this.totalDataLength), this.session.traceId);
        this.session.readBuffer.order(originalOrder);
    }

    final void writeToSocketChannel(int totalLength, boolean marker) throws IOException {
        if (totalLength == 1703910 && !marker) {
            this.session.writeBuffer.clear();
            if (this.session.isLargeSDU) {
                this.session.writeBuffer.put(STANDARD_SDD_MAX_DD_HEADER_LARGE_SDU, 0, STANDARD_SDD_MAX_DD_HEADER_LARGE_SDU.length);
            } else {
                this.session.writeBuffer.put(STANDARD_SDD_MAX_DD_HEADER_SHORT_SDU, 0, STANDARD_SDD_MAX_DD_HEADER_SHORT_SDU.length);
            }
            this.session.writeBuffer.put(STANDARD_SDD_MAX_DD_PAYLOAD, 0, STANDARD_SDD_MAX_DD_PAYLOAD.length);
            this.session.writeBuffer.flip();
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "writeToSocketChannel", "SessionWriteBuffer = {0}, TotalDataLength = {1}, SessionTraceId = {2}", null, null, this.session.writeBuffer, Integer.valueOf(totalLength), this.session.traceId);
            while (this.session.writeBuffer.hasRemaining()) {
                this.session.socketChannel.write(this.session.writeBuffer);
            }
            return;
        }
        this.useLongDescriptor = false;
        this.descriptorFLaG = 2;
        if (marker) {
            this.descriptorFLaG |= 1;
        }
        int nbOfDD = 0;
        int remainingBytes = totalLength;
        while (remainingBytes > 0) {
            if (remainingBytes > 65535) {
                this.sdd[nbOfDD] = 65535;
            } else {
                this.sdd[nbOfDD] = remainingBytes;
            }
            remainingBytes -= this.sdd[nbOfDD];
            nbOfDD++;
        }
        this.session.payloadBufferForWrite.clear();
        this.session.payloadBufferForWrite.putInt(this.descriptorFLaG);
        this.session.payloadBufferForWrite.putInt(totalLength);
        this.session.payloadBufferForWrite.putInt(nbOfDD);
        for (int i = 0; i < nbOfDD; i++) {
            this.session.payloadBufferForWrite.putShort((short) this.sdd[i]);
        }
        for (int i2 = nbOfDD; i2 < 26; i2++) {
            this.session.payloadBufferForWrite.putShort((short) 0);
        }
        writeToSocketChannel();
    }
}
