package oracle.net.ns;

import java.util.Enumeration;
import java.util.Properties;
import java.util.StringTokenizer;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/net/ns/ClientProfile.class */
public class ClientProfile extends Properties {
    private static final long serialVersionUID = -4472014940429606620L;
    private static final String profile_name = "ora-net-profile";
    private static final String shared_profile_name = "ora-shared-profile";
    private final boolean isANOEnabled;
    private boolean isWeakCryptoEnabled;
    private boolean isServerUsingWeakCrypto;
    private long anoVersion = 0;

    public ClientProfile(@Blind(PropertiesBlinder.class) Properties up) {
        this.isWeakCryptoEnabled = false;
        this.isServerUsingWeakCrypto = false;
        if (up.containsKey(OracleConnection.CONNECTION_PROPERTY_THIN_NET_PROFILE)) {
            put(OracleConnection.CONNECTION_PROPERTY_THIN_NET_PROFILE, up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_PROFILE));
        }
        this.isWeakCryptoEnabled = Boolean.valueOf(up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_ALLOW_WEAK_CRYPTO, "true")).booleanValue();
        this.isServerUsingWeakCrypto = this.isWeakCryptoEnabled;
        String authServices = up.getProperty("oracle.net.authentication_services");
        String encryptionLevel = up.getProperty("oracle.net.encryption_client");
        String checkSumLevel = up.getProperty("oracle.net.crypto_checksum_client");
        this.isANOEnabled = ((encryptionLevel == null || encryptionLevel.equalsIgnoreCase(AnoServices.ANO_REJECTED)) && (checkSumLevel == null || checkSumLevel.equalsIgnoreCase(AnoServices.ANO_REJECTED)) && (authServices == null || authServices.equals(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_SERVICES_DEFAULT))) ? false : true;
        put("oracle.net.authentication_services", authServices == null ? "()" : authServices);
        put("oracle.net.encryption_client", encryptionLevel == null ? AnoServices.ANO_ACCEPTED : encryptionLevel);
        put("oracle.net.crypto_checksum_client", checkSumLevel == null ? AnoServices.ANO_ACCEPTED : checkSumLevel);
        put("oracle.net.encryption_types_client", up.getProperty("oracle.net.encryption_types_client", "()"));
        put("oracle.net.crypto_checksum_types_client", up.getProperty("oracle.net.crypto_checksum_types_client", "()"));
        setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_SET_FIPS_MODE, up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_SET_FIPS_MODE, "false"));
        String jaasConfigModuleName = up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_JAAS_LOGIN_MODULE);
        if (jaasConfigModuleName != null) {
            setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_JAAS_LOGIN_MODULE, jaasConfigModuleName);
        }
        put(OracleConnection.CONNECTION_PROPERTY_THIN_NET_CRYPTO_SEED, up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_CRYPTO_SEED, ""));
        String tmp = up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_REALM);
        if (tmp != null) {
            put(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_REALM, tmp);
        }
        put("oracle.net.kerberos5_mutual_authentication", up.getProperty("oracle.net.kerberos5_mutual_authentication", "false"));
        if (up.getProperty("oracle.net.kerberos5_cc_name") != null) {
            put("oracle.net.kerberos5_cc_name", up.getProperty("oracle.net.kerberos5_cc_name"));
        }
        String passwordAuthType = up.getProperty(OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION);
        if (passwordAuthType != null && passwordAuthType.equalsIgnoreCase(AnoServices.AUTHENTICATION_KERBEROS5)) {
            if (up.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER)) {
                put(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER, up.getProperty(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER));
            }
            if (up.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD)) {
                put(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD, up.get(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD));
            }
        }
        if (up.containsKey(OracleConnection.CONNECTION_PROPERTY_THIN_NET_RADIUS_CHALLENGE_RESPONSE_HANDLER)) {
            put(OracleConnection.CONNECTION_PROPERTY_THIN_NET_RADIUS_CHALLENGE_RESPONSE_HANDLER, up.get(OracleConnection.CONNECTION_PROPERTY_THIN_NET_RADIUS_CHALLENGE_RESPONSE_HANDLER));
        }
        if (up.containsKey(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_USER)) {
            put(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_USER, up.get(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_USER));
        }
        if (up.containsKey(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_PWD)) {
            put(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_PWD, up.get(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_PWD));
        }
    }

    public boolean isANOEnabled() {
        return this.isANOEnabled;
    }

    public boolean useWeakCrypto() {
        return this.isWeakCryptoEnabled && this.isServerUsingWeakCrypto;
    }

    public boolean isWeakCryptoEnabled() {
        return this.isWeakCryptoEnabled;
    }

    public void setANOVersion(long anoVersion) {
        if (this.anoVersion != 0) {
            return;
        }
        this.anoVersion = anoVersion;
        int majorVersion = ((int) (anoVersion >> 24)) & 255;
        int releaseUpdateRevision = ((int) (anoVersion >> 12)) & 255;
        this.isServerUsingWeakCrypto = majorVersion < 23 && releaseUpdateRevision < 1;
    }

    public boolean isServerUsingWeakCrypto() {
        return this.isServerUsingWeakCrypto;
    }

    public String[] getAuthenticationServices() {
        return getServices((String) get("oracle.net.authentication_services"));
    }

    public String[] getEncryptionServices() {
        return getServices((String) get("oracle.net.encryption_types_client"));
    }

    public String[] getDataIntegrityServices() {
        return getServices((String) get("oracle.net.crypto_checksum_types_client"));
    }

    public String getEncryptionLevel() {
        return (String) get("oracle.net.encryption_client");
    }

    public int getEncryptionLevelNum() {
        return translateAnoValue(getEncryptionLevel());
    }

    public String getDataIntegrityLevel() {
        return (String) get("oracle.net.crypto_checksum_client");
    }

    public int getDataIntegrityLevelNum() {
        return translateAnoValue(getDataIntegrityLevel());
    }

    public boolean isFIPSMode() {
        return Boolean.valueOf(getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_SET_FIPS_MODE, "false")).booleanValue();
    }

    public void print() {
        System.out.println(" ----------------------------------------");
        System.out.println(" Displaying the content of ClientProfile ");
        System.out.println(" List:");
        list(System.out);
        Enumeration en = propertyNames();
        System.out.println("Enumeration has elements ? " + en.hasMoreElements());
        int i = 0;
        while (en.hasMoreElements()) {
            String key = (String) en.nextElement();
            System.out.println("Key " + i + " = " + key);
            System.out.println("Value = " + getProperty(key));
            i++;
        }
        System.out.println(" ----------------------------------------");
    }

    private String[] getServices(String services) {
        String mainString = removeParenths(services);
        StringTokenizer st = new StringTokenizer(mainString, ",");
        int tokens = st.countTokens();
        String[] items = new String[tokens];
        for (int i = 0; i < tokens; i++) {
            items[i] = st.nextToken().trim();
        }
        return items;
    }

    private String removeParenths(String str) {
        int beginParenth = str.indexOf(40);
        int beginOffset = beginParenth == -1 ? 0 : beginParenth + 1;
        int endParenth = str.lastIndexOf(41);
        int endOffset = endParenth == -1 ? str.length() : endParenth;
        String result = str.substring(beginOffset, endOffset);
        return result.trim();
    }

    private int translateAnoValue(String level) {
        int levelNum = 0;
        if (level != null) {
            if (level.equalsIgnoreCase(AnoServices.ANO_ACCEPTED)) {
                levelNum = 0;
            } else if (level.equalsIgnoreCase(AnoServices.ANO_REQUESTED)) {
                levelNum = 2;
            } else if (level.equalsIgnoreCase(AnoServices.ANO_REQUIRED)) {
                levelNum = 3;
            } else if (level.equalsIgnoreCase(AnoServices.ANO_REJECTED)) {
                levelNum = 1;
            } else {
                levelNum = -1;
            }
        }
        return levelNum;
    }
}
