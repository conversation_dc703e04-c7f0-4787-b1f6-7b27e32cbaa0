package oracle.net.ns;

import java.io.IOException;
import java.sql.SQLException;
import java.sql.SQLTransientException;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/net/ns/NetException.class */
public class NetException extends IOException {
    private static final int NET_ERROR_INTERNAL_BASE = 17800;
    public static final int GOT_MINUS_ONE = 17800;
    public static final int ASSERTION_FAILED = 17801;
    private static final int NET_ERROR_NT_BASE = 17820;
    public static final int NT_CONNECTION_FAILED = 17820;
    public static final int INVALID_NT_ADAPTER = 17821;
    public static final int NT_MSGQ_CONNECT_TIMEOUT = 17822;
    public static final int NT_MSGQ_TIMEOUT_WHILE_EXCHANGING_QUEUE_NAME = 17823;
    public static final int NT_MSGQ_UNEXPECTED_READ_ON_SOCKET = 17824;
    public static final int NT_MSGQ_MORE_THAN_ONE_MESSAGE = 17825;
    public static final int NT_NTMQ_INVALID_PACKET = 17826;
    public static final int NT_DISCONNECT_FAILED = 17827;
    public static final int NT_BEQ_CONNECT_TIMEOUT = 17828;
    public static final int NT_INVALID_DATA_URI_FORMAT = 17829;
    private static final int NET_ERROR_NAMING_BASE = 17850;
    public static final int PROTOCOL_NOT_SPECIFIED = 17850;
    public static final int CSTRING_PARSING = 17851;
    public static final int INVALID_CONNECT_DATA = 17852;
    public static final int HOSTNAME_NOT_SPECIFIED = 17853;
    public static final int PORT_NOT_SPECIFIED = 17854;
    public static final int CONNECT_DATA_MISSING = 17855;
    public static final int SID_INFORMATION_MISSING = 17856;
    public static final int ADDRESS_NOT_DEFINED = 17857;
    public static final int JNDI_THREW_EXCEPTION = 17858;
    public static final int JNDI_NOT_INITIALIZED = 17859;
    public static final int JNDI_CLASSES_NOT_FOUND = 17860;
    public static final int USER_PROPERTIES_NOT_DEFINED = 17861;
    public static final int NAMING_FACTORY_NOT_DEFINED = 17862;
    public static final int NAMING_PROVIDER_NOT_DEFINED = 17863;
    public static final int PROFILE_NAME_NOT_DEFINED = 17864;
    public static final int HOST_PORT_SID_EXPECTED = 17865;
    public static final int PORT_NUMBER_ERROR = 17866;
    public static final int EZ_CONNECT_FORMAT_EXPECTED = 17867;
    public static final int UNKNOWN_HOST = 17868;
    public static final int TNS_ADMIN_EMPTY = 17869;
    public static final int CONNECT_STRING_EMPTY = 17870;
    public static final int INVALID_READ_PATH = 17871;
    public static final int NAMELOOKUP_FAILED = 17872;
    public static final int NAMELOOKUP_FILE_ERROR = 17873;
    public static final int INVALID_LDAP_URL = 17874;
    public static final int INVALID_LDAP_CONFIG = 17875;
    public static final int LDAP_AUTH_NOT_AVAILABLE = 17876;
    public static final int INVALID_CONNECT_DATA_CONFIG = 17877;
    private static final int NET_ERROR_NS_BASE = 17900;
    public static final int NOT_CONNECTED = 17900;
    public static final int CONNECTED_ALREADY = 17901;
    public static final int DATA_EOF = 17902;
    public static final int SDU_MISMATCH = 17903;
    public static final int BAD_PKT_TYPE = 17904;
    public static final int UNEXPECTED_PKT = 17905;
    public static final int REFUSED_CONNECT = 17906;
    public static final int INVALID_PKT_LENGTH = 17907;
    public static final int CONNECTION_STRING_NULL = 17908;
    public static final int SOCKET_CLOSED_ERR = 17909;
    private static final int NET_ERROR_SSL_BASE = 17950;
    public static final int INVALID_SSL_VERSION = 17950;
    public static final int UNSUPPORTED_SSL_PROTOCOL = 17951;
    public static final int INVALID_SSL_CIPHER_SUITES = 17952;
    public static final int UNSUPPORTED_SSL_CIPHER_SUITE = 17953;
    public static final int MISMATCH_SERVER_CERT_DN = 17954;
    public static final int DOUBLE_ENCRYPTION_NOT_ALLOWED = 17955;
    public static final int UNABLE_TO_PARSE_WALLET_LOCATION = 17956;
    public static final int UNABLE_TO_INIT_KEY_STORE = 17957;
    public static final int UNABLE_TO_INIT_TRUST_STORE = 17958;
    public static final int UNABLE_TO_INIT_SSL_CONTEXT = 17959;
    public static final int SSL_UNVERIFIED_PEER = 17960;
    public static final int UNSUPPORTED_METHOD_IN_WALLET_LOCATION = 17961;
    public static final int REDIRECT_FAILURE_PROTOCOL_DOWNGRADE = 17962;
    public static final int REDIRECT_FAILURE_SECURITY_PARAM = 17963;
    public static final int REDIRECT_FAILURE_INVALID_DATA = 17964;
    public static final int MISMATCH_SERVER_CERT_DN_HOSTNAME = 17965;
    public static final int MISMATCH_SERVER_CERT_DN_SERVICE_NAME = 17966;
    public static final int SSL_HANDSHAKE_FAILURE = 17967;
    public static final int SSL_CERT_ALIAS_NOTFOUND = 17968;
    public static final int INVALID_BASE64_WALLET_LOCATION = 17969;
    public static final int INVALID_PEM_PRIVATE_KEY_INDEX = 17970;
    public static final int PEM_PARSE_FAILURE = 17971;
    public static final int PEM_NO_CERTIFICATE_FOUND = 17972;
    private static final int NET_ERROR_ANO_BASE = 18900;
    public static final int FAILED_TO_TURN_ENCRYPTION_ON = 18900;
    public static final int WRONG_BYTES_IN_NAPACKET = 18901;
    public static final int WRONG_MAGIC_NUMBER = 18902;
    public static final int UNKNOWN_ALGORITHM_12649 = 18903;
    public static final int INVALID_ENCRYPTION_PARAMETER = 18904;
    public static final int WRONG_SERVICE_SUBPACKETS = 18905;
    public static final int SUPERVISOR_STATUS_FAILURE = 18906;
    public static final int AUTHENTICATION_STATUS_FAILURE = 18907;
    public static final int SERVICE_CLASSES_NOT_INSTALLED = 18908;
    public static final int INVALID_DRIVER = 18909;
    public static final int ARRAY_HEADER_ERROR = 18910;
    public static final int RECEIVED_UNEXPECTED_LENGTH_FOR_TYPE = 18911;
    public static final int INVALID_NA_PACKET_TYPE_LENGTH = 18912;
    public static final int INVALID_NA_PACKET_TYPE = 18913;
    public static final int UNEXPECTED_NA_PACKET_TYPE_RECEIVED = 18914;
    public static final int UNKNOWN_ENC_OR_DATAINT_ALGORITHM = 18915;
    public static final int INVALID_ENCRYPTION_ALGORITHM_FROM_SERVER = 18916;
    public static final int ENCRYPTION_CLASS_NOT_INSTALLED = 18917;
    public static final int DATAINTEGRITY_CLASS_NOT_INSTALLED = 18918;
    public static final int INVALID_DATAINTEGRITY_ALGORITHM_FROM_SERVER = 18919;
    public static final int INVALID_SERVICES_FROM_SERVER = 18920;
    public static final int INCOMPLETE_SERVICES_FROM_SERVER = 18921;
    public static final int INVALID_LEVEL = 18922;
    public static final int INVALID_SERVICE = 18923;
    public static final int AUTHENTICATION_KERBEROS5_NO_TGT = 18924;
    public static final int AUTHENTICATION_KERBEROS5_FAILURE = 18925;
    public static final int AUTHENTICATION_KERBEROS5_NO_CONTEXT = 18926;
    public static final int AUTHENTICATION_KERBEROS5_MUTUAL_AUTH_FAILED = 18927;
    private static final int NET_ERROR_OTHER_ERRORS_BASE = 18950;
    public static final int NS_BREAK = 18950;
    public static final int NL_EXCEPTION = 18951;
    public static final int SO_EXCEPTION = 18952;
    public static final int SO_CONNECTTIMEDOUT = 18953;
    public static final int SO_READTIMEDOUT = 18954;
    public static final int INVALID_CONNECTTIMEOUT = 18955;
    public static final int INVALID_READTIMEOUT = 18956;
    public static final int NO_ERROR_MESSAGE = 18957;
    public static final int INVALID_REFUSE_PACKET_DATA = 18958;
    public static final int LISTENER_REFUSES_CONNECTION = 18997;
    public static final int CONNECT_DESCRIPTOR_USED = 18998;
    public static final int ORACLE_ERROR = 18999;
    public static final int SERVER_CRYPTO_VERSION_MISMATCH = 12268;
    public static final int LISTENER_NOT_AVAILABLE = 12541;
    public static final int TIMEOUT = 12170;
    public static final int CONNECT_ALIAS_NOTFOUND = 12154;
    public static final int EZ_CONNECT_SYNTAX_ERROR = 12261;
    public static final int EZ_CONNECT_UNKNOWN_HOST = 12262;
    public static final int CONNECTION_REFUSED = 12564;
    public static final int DATABASE_CONNECTION_LOST = 3113;
    public static final int TNS_NAMES_INACCESSIBLE = 12263;
    private final int errorNumber;
    private final String userMsg;
    private final Object[] exceptionParams;
    private final NetErrorMessage messageHandler;
    private final boolean isNSControlCommandError;
    private String netConnectionId;
    private final boolean isTransient;

    public NetException(SQLException sqlException) {
        this(sqlException.getErrorCode(), sqlException.getMessage(), false, null, sqlException instanceof SQLTransientException);
        initCause(sqlException);
    }

    public NetException(int error) {
        this(error, null);
    }

    public NetException(int error, String userMsg) {
        this(error, userMsg, false);
    }

    public NetException(int error, String userMsg, Throwable throwable) {
        this(error, userMsg, false);
        initCause(throwable);
    }

    NetException(int error, String userMsg, boolean isNSControlCommandError) {
        this(error, userMsg, isNSControlCommandError, null);
    }

    public NetException(int error, String userMsg, boolean isNSControlCommandError, Object... args) {
        this(error, userMsg, isNSControlCommandError, (args == null || args.length <= 0) ? null : args, isTransient(error));
    }

    public NetException(int errorNumber, String userMsg, boolean isNSControlCommandError, Object[] exceptionParams, boolean isTransient) {
        this.errorNumber = errorNumber;
        this.userMsg = userMsg;
        this.isNSControlCommandError = isNSControlCommandError;
        this.exceptionParams = exceptionParams;
        this.isTransient = isTransient;
        this.messageHandler = new NetErrorMessage();
    }

    public NetException setNetConnectionId(String netConnectionId) {
        this.netConnectionId = netConnectionId;
        return this;
    }

    public String getNetConnectionId() {
        return this.netConnectionId;
    }

    public boolean isNSControlCommandError() {
        return this.isNSControlCommandError;
    }

    public int getErrorNumber() {
        return this.errorNumber;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        String errMsg;
        if (this.messageHandler == null) {
            errMsg = Integer.toString(this.errorNumber);
        } else {
            errMsg = this.messageHandler.getMessage(this.errorNumber, this.userMsg);
        }
        if (this.exceptionParams != null && errMsg != null) {
            errMsg = this.messageHandler.addArgs(errMsg, this.exceptionParams);
        }
        if (this.netConnectionId != null && errMsg != null) {
            errMsg = errMsg + " (CONNECTION_ID=" + this.netConnectionId + ")";
        }
        String url = "";
        if (DatabaseError.isErrorUrlEnabled() && !errMsg.contains(DatabaseError.ERROR_URL_PREFIX)) {
            url = System.lineSeparator() + DatabaseError.ERROR_URL_PREFIX + String.format("%05d", Integer.valueOf(this.errorNumber)) + "/";
        }
        return errMsg + url;
    }

    public boolean isTransient() {
        return this.isTransient;
    }

    private static boolean isTransient(int errorNumber) {
        switch (errorNumber) {
            case UNABLE_TO_PARSE_WALLET_LOCATION /* 17956 */:
            case UNABLE_TO_INIT_KEY_STORE /* 17957 */:
            case UNABLE_TO_INIT_TRUST_STORE /* 17958 */:
            case UNABLE_TO_INIT_SSL_CONTEXT /* 17959 */:
            case INVALID_PEM_PRIVATE_KEY_INDEX /* 17970 */:
            case PEM_PARSE_FAILURE /* 17971 */:
            case PEM_NO_CERTIFICATE_FOUND /* 17972 */:
                return false;
            case SSL_UNVERIFIED_PEER /* 17960 */:
            case UNSUPPORTED_METHOD_IN_WALLET_LOCATION /* 17961 */:
            case REDIRECT_FAILURE_PROTOCOL_DOWNGRADE /* 17962 */:
            case REDIRECT_FAILURE_SECURITY_PARAM /* 17963 */:
            case REDIRECT_FAILURE_INVALID_DATA /* 17964 */:
            case MISMATCH_SERVER_CERT_DN_HOSTNAME /* 17965 */:
            case MISMATCH_SERVER_CERT_DN_SERVICE_NAME /* 17966 */:
            case SSL_HANDSHAKE_FAILURE /* 17967 */:
            case SSL_CERT_ALIAS_NOTFOUND /* 17968 */:
            case INVALID_BASE64_WALLET_LOCATION /* 17969 */:
            default:
                return true;
        }
    }
}
