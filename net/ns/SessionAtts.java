package oracle.net.ns;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.channels.SocketChannel;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Properties;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.regex.Pattern;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.ano.Ano;
import oracle.net.nt.ConnOption;
import oracle.net.nt.MQLNTAdapter;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.SecurityInformationImpl;
import oracle.net.nt.SocketChannelWrapper;
import oracle.net.nt.TcpsNTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/ns/SessionAtts.class */
public class SessionAtts implements SQLnetDef, Diagnosable {
    private static final String CLASS_NAME;
    private static final Predicate<String> IS_PASSWORD;
    private static final Predicate<String> IS_VALID_CONNECTION_ID_PREFIX;
    private static final boolean DISABLE_TLS_DRCP_RENEGO = true;
    protected NSProtocol ns;
    protected NTAdapter nt;
    protected InputStream ntInputStream;
    protected OutputStream ntOutputStream;
    private int sdu;
    private int tdu;
    protected ConnOption cOption;
    protected boolean dataEOF;
    public boolean onBreakReset;
    public ClientProfile profile;
    public Ano ano;
    public boolean anoEnabled;
    public boolean isEncryptionActive;
    public boolean isChecksumActive;
    public boolean areEncryptionAndChecksumActive;
    boolean noAnoServices;
    int negotiatedOptions;
    protected byte[] sessionId;
    protected int timeout;
    protected int tick;
    protected byte[] reconnectAddress;
    protected long timestampLastIO;
    protected String connectData;
    protected boolean isJavaNetNIO;
    private final Diagnosable diagnosable;
    SocketChannel socketChannel;
    public NIONSDataChannel dataChannel;
    NIOMarkerPacket markerPacket;
    NIODataDescriptorPacket ddPacket;
    NIOControlPacket controlPacket;
    NTAdapter.NetworkAdapterType networkType;
    boolean useNativeBuffers;
    String networkCompression;
    ArrayList<String> networkCompressionLevelsArray;
    int networkCompressionThreshold;
    String negotiatedNetworkCompression;
    boolean networkCompressionEnabled;
    int negotiatedNetworkCompressionScheme;
    NetworkCompressionCodec compressionCodec;
    boolean needsToBeClosed;
    boolean isPollAndCheckEnabled;
    static int DEFAULT_POLL_AND_CHECK_TIME_MILLIS;
    private final String uniqueConnectionId;
    private String netConnectionId;
    private String netConnectionIdPrefix;
    boolean isTunnelConnection;
    boolean fastAuthenticationEnabled;
    String traceId;
    static final /* synthetic */ boolean $assertionsDisabled;
    public boolean poolEnabled = false;
    protected boolean attemptingReconnect = false;
    protected boolean isLargeSDU = false;
    protected int negotiatedSDU = -1;
    protected boolean redirecting = false;
    private String databaseUUID = null;
    public ByteBuffer readBuffer = null;
    public ByteBuffer payloadDataBufferForRead = null;
    ByteBuffer payloadBufferForRead = null;
    ByteBuffer headerBufferForRead = null;
    public ByteBuffer writeBuffer = null;
    public ByteBuffer payloadDataBufferForWrite = null;
    ByteBuffer payloadBufferForWrite = null;
    ByteBuffer headerBufferForWrite = null;
    boolean needToReleaseMSGQBuffer = false;
    private final Properties netProperties = new Properties();
    private ByteOrder byteOrder = ByteOrder.BIG_ENDIAN;
    final Monitor ntOutputStreamMonitor = Monitor.newInstance();
    boolean isConnectedViaProxy = false;
    boolean disableSecurityRenegotiation = false;
    private SecurityInformation securityInformation = new SecurityInformationImpl();
    public boolean anoActive = false;
    protected boolean connected = false;

    static {
        $assertionsDisabled = !SessionAtts.class.desiredAssertionStatus();
        CLASS_NAME = SessionAtts.class.getName();
        IS_PASSWORD = Pattern.compile(".*password.*", 2).asPredicate().and(string -> {
            return !OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION.equalsIgnoreCase(string);
        });
        IS_VALID_CONNECTION_ID_PREFIX = Pattern.compile(String.format("[A-z0-9,_]{%d}", 8)).asPredicate();
        DEFAULT_POLL_AND_CHECK_TIME_MILLIS = 60000;
    }

    public SessionAtts(NSProtocol nsp, int sdu, int tdu, boolean useNIO, boolean _useNativeBuffers, Diagnosable diagnosable) {
        this.isJavaNetNIO = false;
        this.useNativeBuffers = false;
        this.sdu = sdu;
        this.tdu = tdu;
        this.ns = nsp;
        this.diagnosable = diagnosable;
        String strCreateUniqueConnectionId = createUniqueConnectionId();
        this.uniqueConnectionId = strCreateUniqueConnectionId;
        this.netConnectionId = strCreateUniqueConnectionId;
        if (useNIO) {
            this.dataChannel = new NIONSDataChannel(this);
            this.markerPacket = new NIOMarkerPacket(this);
            this.ddPacket = new NIODataDescriptorPacket(this);
            this.controlPacket = new NIOControlPacket(this);
            this.useNativeBuffers = _useNativeBuffers;
            this.isJavaNetNIO = true;
        }
    }

    public boolean isConnected() {
        return this.connected;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    public void initTLSRenegotiation() throws IOException {
    }

    void setNetProperty(String propertyKey, String value) {
        this.netProperties.setProperty(propertyKey, value);
    }

    public String getNetProperty(String propertyKey) {
        return this.netProperties.getProperty(propertyKey);
    }

    public String getNetConnectionId() {
        return this.netConnectionId;
    }

    private String createUniqueConnectionId() {
        try {
            UUID uuid = UUID.randomUUID();
            ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
            bb.putLong(uuid.getMostSignificantBits());
            bb.putLong(uuid.getLeastSignificantBits());
            return Base64.getEncoder().encodeToString(bb.array());
        } catch (Exception e) {
            throw new RuntimeException("Unable to generate ConnectionID to establish Connection", e);
        }
    }

    void setNetConnectionIdPrefix(String prefix) {
        if (!isValidNetConnectionIdPrefix(prefix)) {
            throw new IllegalArgumentException("Invalid ConnectionId prefix : " + prefix);
        }
        this.netConnectionIdPrefix = prefix;
        if (prefix == null) {
            this.netConnectionId = this.uniqueConnectionId;
        } else {
            this.netConnectionId = this.netConnectionIdPrefix + this.uniqueConnectionId;
        }
    }

    public boolean isTTCCookieEnabled() {
        return this.databaseUUID != null && this.databaseUUID.trim().length() > 0;
    }

    public String getDatabaseUUID() {
        if ($assertionsDisabled || this.databaseUUID != null) {
            return this.databaseUUID;
        }
        throw new AssertionError("setDatabaseUUID must be called first");
    }

    public void setDatabaseUUID(byte[] uuid) {
        if (!$assertionsDisabled && this.databaseUUID != null) {
            throw new AssertionError("Cannot set database UUID twice");
        }
        this.databaseUUID = new String(uuid);
    }

    public boolean isFastAuthenticationOptimizationEnabled() {
        return this.fastAuthenticationEnabled;
    }

    private boolean isValidNetConnectionIdPrefix(String prefix) {
        return prefix == null || IS_VALID_CONNECTION_ID_PREFIX.test(prefix);
    }

    public boolean isTwoFactorAuthenticationDone() {
        return this.ano != null && this.ano.isTwoFactorAuthenticationDone();
    }

    @Blind(PropertiesBlinder.class)
    public Properties getNetProperties() {
        return (Properties) this.netProperties.clone();
    }

    public SecurityInformation getSecurityInformation() {
        return this.securityInformation;
    }

    public boolean isNetworkCompressionEnabled() {
        return this.networkCompressionEnabled;
    }

    void initializeNetProperties(@Blind(PropertiesBlinder.class) Properties nsProperties) throws IOException {
        nsProperties.stringPropertyNames().stream().filter(IS_PASSWORD.negate()).forEach(key -> {
            this.netProperties.put(key, nsProperties.get(key));
        });
        setNetProperty(SQLnetDef.NEGOTIATED_COMPRESSION_STATUS, this.networkCompressionEnabled + "");
        setNetProperty(SQLnetDef.NEGOTIATED_SDU, getSDU() + "");
        setNetProperty(SQLnetDef.NEGOTIATED_TDU, getTDU() + "");
        setNetProperty(SQLnetDef.CONNECTED_NETWORK_ADDRESS, this.cOption.addr);
        setNetProperty("oracle.net.CONNECT_TIMEOUT", this.cOption.transportConnectTimeout + "");
        setNetProperty(OracleConnection.CONNECTION_PROPERTY_THIN_OUTBOUND_CONNECT_TIMEOUT, this.cOption.connectTimeout + "");
        setNetProperty(SQLnetDef.IS_OOB_ENABLED, isExpediatedAttentionEnabled() + "");
        if (this.nt.getNetworkAdapterType() == NTAdapter.NetworkAdapterType.TCPS) {
            setNetProperty(SQLnetDef.NEGOTIATED_CIPHER_SUITE, (String) this.nt.getOption(105));
        }
        if (this.ano != null) {
            if (this.isChecksumActive) {
                setNetProperty(SQLnetDef.NEGOTIATED_CHECKSUM_ALGORITHM, this.ano.getDataIntegrityName());
            }
            if (this.isEncryptionActive) {
                setNetProperty(SQLnetDef.NEGOTIATED_ENCRYPTION_ALOGRITHM, this.ano.getEncryptionName());
            }
            if (!this.ano.getAuthenticationAdaptorName().equals("")) {
                setNetProperty(SQLnetDef.NEGOTIATED_AUTHENTICATION_ADAPTOR, this.ano.getAuthenticationAdaptorName());
            }
        }
        SecurityInformationImpl securityInfoImpl = (SecurityInformationImpl) this.securityInformation;
        if (getNTAdapter().getNetworkAdapterType().equals(NTAdapter.NetworkAdapterType.TCPS)) {
            TcpsNTAdapter tcpsNTAdapter = (TcpsNTAdapter) getNTAdapter();
            securityInfoImpl.setDNMatchStatus(tcpsNTAdapter.getDNMatchStatus());
            securityInfoImpl.setServerDN(tcpsNTAdapter.getOption(103).toString());
            securityInfoImpl.setTLSCipherSuite((String) tcpsNTAdapter.getOption(105));
            securityInfoImpl.setTLSVersion(tcpsNTAdapter.getNegotiatedTLSVersion());
            securityInfoImpl.setSNI(tcpsNTAdapter.getSNI());
        }
        if (this.isChecksumActive || this.isEncryptionActive) {
            securityInfoImpl.setEncryptionLevel(this.profile.getEncryptionLevel());
            securityInfoImpl.setChecksumLevel(this.profile.getDataIntegrityLevel());
            securityInfoImpl.setEncryptionAlgorithm(this.ano.getEncryptionName());
            securityInfoImpl.setChecksummingAlgorithm(this.ano.getDataIntegrityName());
            securityInfoImpl.setNativeEncryptionEnabled(true);
            securityInfoImpl.setStrongCryptoUsed(!this.profile.useWeakCrypto());
        }
        if (this.ano.getAuthenticationAdaptorName() != null) {
            switch (this.ano.getAuthenticationAdaptorName()) {
                case "KERBEROS5":
                    securityInfoImpl.setAuthenticationAdaptor(SecurityInformation.AuthenticationAdaptorType.KERBEROS);
                    break;
                case "RADIUS":
                    securityInfoImpl.setAuthenticationAdaptor(SecurityInformation.AuthenticationAdaptorType.RADIUS);
                    break;
                case "TCPS":
                    securityInfoImpl.setAuthenticationAdaptor(SecurityInformation.AuthenticationAdaptorType.TCPS);
                    break;
            }
        }
        if (this.netProperties.containsKey(OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION) || "OCI_TOKEN".equalsIgnoreCase(this.netProperties.getProperty(OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION)) || "AZURE_TOKEN".equalsIgnoreCase(this.netProperties.getProperty(OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION))) {
            securityInfoImpl.setAuthenticationAdaptor(SecurityInformation.AuthenticationAdaptorType.OAUTH);
        }
    }

    protected void initializeBuffer() throws IOException {
        if (this.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            initializeBufferForMSGQ();
        } else {
            if (this.socketChannel instanceof SocketChannelWrapper) {
                ((SocketChannelWrapper) this.socketChannel).setBufferSize(getSDU());
            }
            if (this.useNativeBuffers) {
                setWriteBuffer(ByteBuffer.allocateDirect(this.sdu));
                setReadBuffer(ByteBuffer.allocateDirect(this.sdu));
            } else {
                setWriteBuffer(ByteBuffer.allocate(this.sdu));
                setReadBuffer(ByteBuffer.allocate(this.sdu));
            }
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeBuffer", "Buffers are initialized. WriteBuffer={0}, ReadBuffer={1}, NetworkType={2}. ", null, null, this.readBuffer, this.writeBuffer, this.networkType);
    }

    private void initializeBufferForMSGQ() {
        if (this.writeBuffer != null) {
            MQLNTAdapter.getBufferManager().release(this.writeBuffer);
        }
        setWriteBuffer(MQLNTAdapter.getBufferManager().acquire(this.sdu));
        ((MQLNTAdapter) this.nt).setNegotiatedSDUAndTDU(this.sdu, this.tdu);
    }

    void resetWriteBuffersForMSGQ() {
        this.writeBuffer = null;
    }

    void setWriteBuffer(ByteBuffer buffer) {
        this.writeBuffer = buffer;
        sliceWriteBuffers();
    }

    void setReadBuffer(ByteBuffer buffer) {
        this.readBuffer = buffer;
        sliceReadBuffers();
    }

    void releaseWriteBuffer() {
        if (this.writeBuffer == null) {
            return;
        }
        if (this.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            MQLNTAdapter.getBufferManager().release(this.writeBuffer);
        }
        this.writeBuffer = null;
    }

    void sliceWriteBuffers() {
        this.writeBuffer.clear();
        this.writeBuffer.limit(8);
        this.headerBufferForWrite = this.writeBuffer.slice();
        this.writeBuffer.position(8);
        this.writeBuffer.limit(this.sdu);
        this.payloadBufferForWrite = this.writeBuffer.slice();
        this.writeBuffer.position(10);
        this.payloadDataBufferForWrite = this.writeBuffer.slice();
        this.payloadDataBufferForWrite.limit(this.payloadDataBufferForWrite.capacity() - this.dataChannel.getDataExpansionByteSize());
        this.payloadDataBufferForWrite.order(this.byteOrder);
        this.writeBuffer.rewind();
    }

    void sliceReadBuffers() {
        this.readBuffer.position(0);
        this.headerBufferForRead = this.readBuffer.slice();
        if (this.readBuffer.limit() >= 8) {
            this.readBuffer.position(8);
            this.payloadBufferForRead = this.readBuffer.slice();
        }
        if (this.readBuffer.limit() >= 10) {
            this.readBuffer.position(10);
            this.payloadDataBufferForRead = this.readBuffer.slice();
            this.payloadDataBufferForRead.order(this.byteOrder);
            this.payloadBufferForRead.rewind();
        }
        this.readBuffer.rewind();
    }

    public void prepareWriteBuffer() {
        if (this.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            if (this.writeBuffer != null) {
                sliceWriteBuffers();
                return;
            } else {
                setWriteBuffer(MQLNTAdapter.getBufferManager().acquire(this.sdu));
                return;
            }
        }
        this.payloadDataBufferForWrite.clear();
        this.payloadBufferForWrite.clear();
        this.payloadDataBufferForWrite.limit(this.payloadDataBufferForWrite.capacity() - this.dataChannel.getDataExpansionByteSize());
        this.payloadBufferForWrite.limit(this.payloadBufferForWrite.capacity() - this.dataChannel.getDataExpansionByteSize());
    }

    public ByteOrder getByteOrder() {
        return this.byteOrder;
    }

    public void setByteOrder(ByteOrder newByteOrder) {
        this.byteOrder = newByteOrder;
    }

    public void setSDU(int sdu) {
        if (this.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            if (sdu <= 0) {
                this.sdu = 65518;
                return;
            }
            if (sdu > 65518) {
                this.sdu = 65518;
                return;
            } else if (sdu < 512) {
                this.sdu = 512;
                return;
            } else {
                this.sdu = sdu;
                return;
            }
        }
        if (sdu <= 0) {
            this.sdu = 8192;
            return;
        }
        if (sdu > 2097152) {
            this.sdu = 2097152;
        } else if (sdu < 512) {
            this.sdu = 512;
        } else {
            this.sdu = sdu;
        }
    }

    public int getSDU() {
        return this.sdu;
    }

    public void setTDU(int tdu) {
        if (this.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            if (tdu <= 0) {
                this.tdu = 65518;
                return;
            }
            if (tdu > 65518) {
                this.tdu = 65518;
                return;
            } else if (tdu < 255) {
                this.tdu = 255;
                return;
            } else {
                this.tdu = tdu;
                return;
            }
        }
        if (tdu <= 0) {
            this.tdu = 2097152;
            return;
        }
        if (tdu > 2097152) {
            this.tdu = 2097152;
        } else if (tdu < 255) {
            this.tdu = 255;
        } else {
            this.tdu = tdu;
        }
    }

    public int getTDU() {
        return this.tdu;
    }

    void setNegotiatedSDUAndTDU(int sdu, int tdu) throws IOException {
        setTDU(tdu);
        setSDU(sdu);
        this.negotiatedSDU = this.sdu;
        initializeBuffer();
    }

    public NTAdapter getNTAdapter() {
        return this.nt;
    }

    void renegotiateSSLSession() throws IOException {
        ((TcpsNTAdapter) this.nt).renegotiateSession();
        if (this.isJavaNetNIO) {
            this.socketChannel = this.nt.getSocketChannel();
        } else {
            this.ntInputStream = this.nt.getInputStream();
            this.ntOutputStream = this.nt.getOutputStream();
        }
    }

    public String toString() {
        String ls = System.lineSeparator();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Session Attributes: ").append(ls);
        stringBuilder.append("sdu=").append(this.sdu).append(", tdu=").append(this.tdu).append(ls);
        if (this.nt != null) {
            stringBuilder.append("nt: ").append(this.nt).append(ls);
        }
        if (this.ntInputStream != null) {
            stringBuilder.append("ntInputStream=").append(this.ntInputStream).append(ls);
        }
        if (this.ntOutputStream != null) {
            stringBuilder.append("ntOutputStream=").append(this.ntOutputStream).append(ls);
        }
        if (this.profile != null) {
            stringBuilder.append("client profile=").append(this.profile).append(ls);
        }
        if (this.cOption != null) {
            stringBuilder.append("connection options=").append(this.cOption).append(ls);
        }
        stringBuilder.append("onBreakReset=").append(this.onBreakReset);
        stringBuilder.append(", dataEOF=").append(this.dataEOF);
        stringBuilder.append(", negotiatedOptions=0x").append(Integer.toHexString(this.negotiatedOptions));
        stringBuilder.append(", connected=").append(this.connected).append(ls);
        stringBuilder.append("TTIINIT enabled=").append(isFastAuthenticationOptimizationEnabled()).append(", ");
        stringBuilder.append("TTC cookie enabled=").append(isTTCCookieEnabled()).append(ls);
        return stringBuilder.toString();
    }

    public void turnEncryptionOn(NIONSDataChannel dataChannel) throws NetException {
        if (dataChannel != null) {
            this.dataChannel = dataChannel;
            this.anoActive = true;
            return;
        }
        throw new NetException(NetException.FAILED_TO_TURN_ENCRYPTION_ON);
    }

    public int getANOFlags() {
        int flags = 1;
        if (this.ano != null) {
            flags = this.ano.getNAFlags();
        }
        return flags;
    }

    public void setNegotiatedOptions(int value) {
        this.negotiatedOptions = value;
    }

    public int getNegotiatedOptions() {
        return this.negotiatedOptions;
    }

    public ConnOption getcOption() {
        return this.cOption;
    }

    void setConnectData(String value) {
        this.connectData = value;
    }

    String getConnectData() {
        return this.connectData;
    }

    final boolean isExpediatedAttentionEnabled() {
        return (this.negotiatedOptions & 1024) == 1024;
    }

    final boolean isAttentionProcessingEnabled() {
        return (this.negotiatedOptions & 2048) != 2048;
    }
}
