package oracle.net.ns;

import java.io.IOException;
import java.nio.charset.Charset;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIORefusePacket.class */
final class NIORefusePacket extends NIOPacket {
    int userReason;
    int systemReason;
    String refuseData;

    NIORefusePacket(NIOHeader header, SessionAtts session) throws IOException {
        super(header, session);
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        byte[] data;
        this.userReason = this.session.payloadBufferForRead.get();
        this.systemReason = this.session.payloadBufferForRead.get();
        int refuseDataLen = this.session.payloadBufferForRead.getShort();
        if (refuseDataLen > 0) {
            if (this.session.payloadBufferForRead.limit() > this.session.payloadBufferForRead.position()) {
                data = new byte[refuseDataLen];
                this.session.payloadBufferForRead.get(data);
            } else {
                data = this.session.dataChannel.readPayloadDataFromSocketChannel(refuseDataLen);
            }
            this.refuseData = new String(data, Charset.forName("US-ASCII"));
            return;
        }
        this.refuseData = "";
    }
}
