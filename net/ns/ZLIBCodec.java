package oracle.net.ns;

import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

/* loaded from: ojdbc8.jar:oracle/net/ns/ZLIBCodec.class */
class ZLIBCodec implements NetworkCompressionCodec {
    private final Inflater decompresser = new Inflater();
    private final Deflater compressor = new Deflater();

    @Override // oracle.net.ns.NetworkCompressionCodec
    public int compress(byte[] decompressed, int offset, int length, byte[] compressed) throws DataFormatException {
        this.compressor.setInput(decompressed, offset, length);
        int resultLength = this.compressor.deflate(compressed, 0, compressed.length, 3);
        return resultLength;
    }

    @Override // oracle.net.ns.NetworkCompressionCodec
    public int decompress(byte[] compressed, int offset, int length, byte[] decompressed) throws DataFormatException {
        this.decompresser.setInput(compressed, offset, length);
        int resultLength = this.decompresser.inflate(decompressed);
        return resultLength;
    }
}
