package oracle.net.ns;

import java.io.IOException;
import java.net.SocketException;
import java.util.List;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.NetStat;
import oracle.net.nt.AsyncOutboundTimeoutHandler;
import oracle.net.nt.ConnectDescription;
import oracle.net.nt.NTAdapter;
import org.ietf.jgss.GSSCredential;

/* loaded from: ojdbc8.jar:oracle/net/ns/Communication.class */
public interface Communication {
    void connect(GSSCredential gSSCredential, DMSFactory.DMSNoun dMSNoun) throws IOException;

    SessionAtts getSessionAttributes();

    void cancelTimeout();

    void disconnect() throws IOException;

    void sendBreak() throws IOException;

    void sendInterrupt() throws IOException;

    void sendReset() throws IOException;

    void setAuthSessionKey(byte[] bArr) throws NetException;

    void doKeyFoldinForExternalAuth();

    void setOption(int i, Object obj) throws IOException;

    Object getOption(int i) throws IOException;

    void abort() throws IOException;

    String getEncryptionName();

    String getDataIntegrityName();

    String getAuthenticationAdaptorName();

    boolean readZeroCopyIO(byte[] bArr, int i, int[] iArr) throws IOException;

    void writeZeroCopyIO(byte[] bArr, int i, int i2) throws IOException;

    void writeZeroCopyIOHeader(boolean z, int i, boolean z2) throws IOException;

    void writeZeroCopyIOData(byte[] bArr, int i, int i2) throws IOException;

    boolean isConnectionSocketKeepAlive() throws SocketException;

    int getSocketReadTimeout() throws IOException;

    void setSocketReadTimeout(int i) throws IOException;

    String getConnectionString();

    int getNegotiatedSDU() throws NetException;

    String getAccessBanner();

    NetStat getNetworkStat();

    boolean isNetworkCompressionEnabled();

    int getOutboundConnectTimeout();

    boolean isUsingCustomHostnameResolver();

    void sendZDP() throws IOException;

    boolean needsToBeClosed();

    void readInbandNotification();

    List<ConnectDescription> getConnectDescriptions();

    ConnectDescription getConnectedDescription();

    void setDriverResources(DriverResources driverResources);

    Throwable lastConnectException();

    default CompletionStage<Void> connectAsync(GSSCredential gssCredential, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler loginTimeoutHandler, Executor asyncExecutor) {
        return CompletionStageUtil.failedStage(new UnsupportedOperationException());
    }

    default boolean isTLSEnabled() {
        NTAdapter ntAdapter;
        SessionAtts sessionAtts = getSessionAttributes();
        return (sessionAtts == null || (ntAdapter = sessionAtts.getNTAdapter()) == null || ntAdapter.getNetworkAdapterType() != NTAdapter.NetworkAdapterType.TCPS) ? false : true;
    }

    default void onWriteReady(Consumer<Throwable> callback) {
        try {
            getConnectedSessionAttributes().getNTAdapter().registerForNonBlockingWrite(callback);
        } catch (Exception exception) {
            callback.accept(exception);
        }
    }

    default void onReadReady(Consumer<Throwable> callback) {
        try {
            SessionAtts sessionAtts = getConnectedSessionAttributes();
            if (sessionAtts.readBuffer.hasRemaining()) {
                callback.accept(null);
            } else {
                sessionAtts.getNTAdapter().registerForNonBlockingRead(callback);
            }
        } catch (Exception exception) {
            callback.accept(exception);
        }
    }

    default void restoreBlockingMode() throws IOException {
        getConnectedSessionAttributes().getNTAdapter().restoreBlockingMode();
    }

    default void enqueueBlockedWrites(boolean isEnabled) throws IOException {
        getConnectedSessionAttributes().getNTAdapter().enqueueBlockedWrites(isEnabled);
    }

    default boolean completeBlockedWrites() throws IOException {
        return getConnectedSessionAttributes().getNTAdapter().completeBlockedWrites();
    }

    default boolean isOutOfBandDataEnabled() throws IOException {
        return getConnectedSessionAttributes().isExpediatedAttentionEnabled();
    }

    default SessionAtts getConnectedSessionAttributes() throws IOException {
        SessionAtts sessionAtts = getSessionAttributes();
        if (sessionAtts == null || !sessionAtts.isConnected()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        return sessionAtts;
    }
}
