package oracle.net.ns;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.zip.DataFormatException;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.net.nt.Clock;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIONSDataChannel.class */
public class NIONSDataChannel extends NIOPacket {
    int sessionIdSize;
    protected byte[] compressedDataBuffer;
    protected byte[] decompressedDataBuffer;
    private boolean isPipelineStarting;
    private boolean remoteTLSNegotiation;

    @Override // oracle.net.ns.NIOPacket, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    public NIONSDataChannel(SessionAtts session) {
        super(session);
        this.header.type = 6;
        this.sessionIdSize = session.poolEnabled ? 16 : 0;
    }

    public int getDataExpansionByteSize() {
        return 0;
    }

    public void readDataFromSocketChannel() throws IOException {
        if (this.session.dataEOF) {
            throw new NetException(NetException.DATA_EOF);
        }
        this.session.payloadDataBufferForRead.position(this.session.payloadDataBufferForRead.limit());
        readFromSocketChannel(true);
        short dataFlags = this.session.payloadBufferForRead.getShort();
        this.session.payloadDataBufferForRead = this.session.payloadBufferForRead.slice();
        this.session.payloadDataBufferForRead.order(this.session.getByteOrder());
        this.session.payloadDataBufferForRead.limit(this.session.payloadBufferForRead.limit() - 2);
        if ((dataFlags & 64) != 0) {
            this.session.dataEOF = true;
            if (!this.session.payloadDataBufferForRead.hasRemaining()) {
                throw new NetException(NetException.DATA_EOF);
            }
        }
        if ((dataFlags & 32768) == 32768) {
            this.session.renegotiateSSLSession();
        }
        if ((dataFlags & 1024) != 0) {
            decompress();
        }
    }

    public void sendEOF() throws IOException {
        this.session.prepareWriteBuffer();
        this.session.payloadBufferForWrite.put((byte) 0);
        this.session.payloadBufferForWrite.put((byte) 64);
        writeToSocketChannel();
    }

    public final void writeDataToSocketChannel() throws IOException {
        writeDataToSocketChannel(0);
    }

    public void writeDataToSocketChannel(int dataFlags) throws IOException {
        if (isPollAndCheckRequired()) {
            this.session.dataChannel.readInbandNotificationCtlPacket();
        }
        if (this.session.payloadDataBufferForWrite.position() == 0) {
            return;
        }
        this.header.type = 6;
        this.session.payloadBufferForWrite.clear();
        if (compress()) {
            dataFlags |= 1024;
        }
        if (this.isPipelineStarting) {
            dataFlags |= 4096;
            this.isPipelineStarting = false;
        }
        if (this.remoteTLSNegotiation) {
            dataFlags |= 16384;
            this.remoteTLSNegotiation = false;
        }
        this.session.payloadBufferForWrite.position(this.session.payloadDataBufferForWrite.position() + 2);
        this.session.payloadBufferForWrite.put(0, (byte) (dataFlags / 256));
        this.session.payloadBufferForWrite.put(1, (byte) (dataFlags % 256));
        writeToSocketChannel();
    }

    private boolean isPollAndCheckRequired() {
        return this.session.isPollAndCheckEnabled && Clock.currentTimeMillis() - this.session.nt.getNetStat().getLastNetworkAccessTime() > ((long) SessionAtts.DEFAULT_POLL_AND_CHECK_TIME_MILLIS);
    }

    private boolean compress() throws IOException {
        int resultLength;
        if (!this.session.networkCompressionEnabled || this.session.payloadDataBufferForWrite.position() + 10 <= this.session.networkCompressionThreshold) {
            return false;
        }
        try {
            int uncompressedLength = this.session.payloadDataBufferForWrite.position();
            if (this.session.writeBuffer.hasArray()) {
                resultLength = this.session.compressionCodec.compress(this.session.writeBuffer.array(), 10, uncompressedLength, this.compressedDataBuffer);
            } else {
                this.session.payloadDataBufferForWrite.rewind();
                this.session.payloadDataBufferForWrite.get(this.decompressedDataBuffer, 0, uncompressedLength);
                resultLength = this.session.compressionCodec.compress(this.decompressedDataBuffer, 0, uncompressedLength, this.compressedDataBuffer);
            }
            if (resultLength >= uncompressedLength) {
                return false;
            }
            this.session.payloadDataBufferForWrite.rewind();
            this.session.payloadDataBufferForWrite.put(this.compressedDataBuffer, 0, resultLength);
            return true;
        } catch (DataFormatException ex) {
            throw ((IOException) new IOException("Network Compression failure").initCause(ex));
        }
    }

    private void decompress() throws IOException {
        try {
            this.session.payloadDataBufferForRead.get(this.compressedDataBuffer, 0, this.session.payloadDataBufferForRead.limit());
            int resultLength = this.session.compressionCodec.decompress(this.compressedDataBuffer, 0, this.session.payloadDataBufferForRead.limit(), this.decompressedDataBuffer);
            this.session.payloadDataBufferForRead = ByteBuffer.wrap(this.decompressedDataBuffer, 0, resultLength);
            this.session.payloadDataBufferForRead.order(this.session.getByteOrder());
            this.session.payloadDataBufferForRead.limit(resultLength);
        } catch (DataFormatException ex) {
            throw ((IOException) new IOException("Network Compression failure").initCause(ex));
        }
    }

    public ByteBuffer getDataBuffer() {
        return this.session.payloadDataBufferForRead;
    }

    protected void initializeNetworkCompressionBuffers() {
        this.compressedDataBuffer = new byte[this.session.getSDU()];
        this.decompressedDataBuffer = new byte[this.session.getSDU()];
    }

    byte[] readPayloadDataFromSocketChannel(int len) throws IOException {
        byte[] data = new byte[len];
        int bytesReadSoFar = 0;
        while (bytesReadSoFar < len) {
            readDataFromSocketChannel();
            int bytesToReadThisTime = this.session.payloadDataBufferForRead.limit();
            this.session.payloadDataBufferForRead.get(data, bytesReadSoFar, bytesToReadThisTime);
            bytesReadSoFar += bytesToReadThisTime;
        }
        return data;
    }

    void writeDataToSocketChannel(byte[] data) throws IOException {
        int bytesToWriteThisTime;
        for (int bytesWrittenSoFar = 0; bytesWrittenSoFar < data.length; bytesWrittenSoFar += bytesToWriteThisTime) {
            this.session.prepareWriteBuffer();
            bytesToWriteThisTime = this.session.payloadDataBufferForWrite.limit() - this.session.payloadDataBufferForWrite.position();
            if (bytesToWriteThisTime > data.length - bytesWrittenSoFar) {
                bytesToWriteThisTime = data.length - bytesWrittenSoFar;
            }
            int dataFlags = data.length > bytesToWriteThisTime ? 32 : 0;
            this.session.payloadDataBufferForWrite.put(data, bytesWrittenSoFar, bytesToWriteThisTime);
            writeDataToSocketChannel(dataFlags);
        }
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        throw new UnsupportedOperationException("Data Byte buffers are not duplicated and accessed directly by the upper layer");
    }

    public void startPipeline() {
        this.isPipelineStarting = true;
    }

    public void doRemoteTLSRenegotiation() {
        this.remoteTLSNegotiation = true;
    }
}
