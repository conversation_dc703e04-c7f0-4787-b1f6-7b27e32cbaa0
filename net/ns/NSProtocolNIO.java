package oracle.net.ns;

import java.io.IOException;
import java.io.InterruptedIOException;
import java.nio.ByteBuffer;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.logging.Level;
import javax.net.ssl.SSLContext;
import oracle.jdbc.OracleConnectionStringBuilder;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.nt.AsyncOutboundTimeoutHandler;
import oracle.net.nt.ConnOption;
import oracle.net.nt.TimeoutInterruptHandler;

/* loaded from: ojdbc8.jar:oracle/net/ns/NSProtocolNIO.class */
public class NSProtocolNIO extends NSProtocol {
    private static final String CLASS_NAME;
    private static final long SEND_BREAK_TIMEOUT_MS = 30;
    private final AtomicBoolean isWriting;
    private final AtomicBoolean isBreakPending;
    private NIONSDataChannel probePacket;
    static final int MAX_RETRIES = 10;
    DMSFactory.DMSNoun dmsParent;
    private boolean isResending;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !NSProtocolNIO.class.desiredAssertionStatus();
        CLASS_NAME = NSProtocolNIO.class.getName();
    }

    public NSProtocolNIO(String connection, @Blind(PropertiesBlinder.class) Properties userProperties, SSLContext sslContext, OracleHostnameResolver hostnameResolver, boolean useDirectBuffers, Diagnosable diagnosable, TraceEventListener traceEventListener) throws NetException {
        super(connection, userProperties, sslContext, hostnameResolver, useDirectBuffers, diagnosable, traceEventListener);
        this.isWriting = new AtomicBoolean(false);
        this.isBreakPending = new AtomicBoolean(false);
        this.dmsParent = null;
        this.isResending = false;
    }

    @Override // oracle.net.ns.NSProtocol
    void negotiateConnection(NVFactory nvf, NVNavigator nvn, boolean disableOOB, boolean useZeroCopyIO, DMSFactory.DMSNoun _dmsParent) throws IOException {
        boolean isConnectionAccepted;
        NIOConnectPacket cnPkt = new NIOConnectPacket(this.sAtts);
        this.dmsParent = _dmsParent;
        do {
            IOException stashedException = null;
            NIOPacket packet = null;
            long connectStartTime = System.currentTimeMillis();
            int responseType = 0;
            try {
                begin(this.isResending ? Metrics.ConnectionEvent.NS_CONNECT_SEND2 : Metrics.ConnectionEvent.NS_CONNECT_SEND1);
                cnPkt.writeToSocketChannel(this.sAtts.cOption.conn_data.toString(), !disableOOB, useZeroCopyIO, this.sAtts.nt.isCharacteristicUrgentSupported(), this.sAtts.getSDU(), this.sAtts.getTDU(), this.sAtts.getANOFlags());
                if (this.isResending) {
                    end(Metrics.ConnectionEvent.NS_CONNECT_SEND2);
                    begin(Metrics.ConnectionEvent.NS_CONNECT_RECEIVE2);
                } else {
                    end(Metrics.ConnectionEvent.NS_CONNECT_SEND1);
                    begin(Metrics.ConnectionEvent.NS_CONNECT_RECEIVE1);
                }
                packet = NIOPacket.readNIOPacket(this.sAtts);
                responseType = packet.header.type;
                if (this.isResending) {
                    end(Metrics.ConnectionEvent.NS_CONNECT_RECEIVE2);
                    this.isResending = false;
                } else {
                    end(Metrics.ConnectionEvent.NS_CONNECT_RECEIVE1);
                }
            } catch (TimeoutInterruptHandler.IOReadTimeoutException ioTimeoutInterrupt) {
                handleIOTimeoutInterrupt();
                stashedException = ioTimeoutInterrupt;
            } catch (InterruptedIOException ioInterrupt) {
                if (handleOutboundTimeoutInterrupt(ioInterrupt)) {
                    String port = Integer.toString(this.sAtts.cOption.port);
                    stashedException = new NetException(NetException.TIMEOUT, null, false, "Outbound connect", this.sAtts.cOption.getOriginalConnOption().connectTimeout + "ms", "host " + this.sAtts.cOption.host + " port " + port);
                    lastConnectException(stashedException);
                } else {
                    throw ioInterrupt;
                }
            } catch (IOException ioException) {
                handleIOException();
                stashedException = ioException;
                lastConnectException(stashedException);
            }
            if (stashedException != null) {
                if (!(stashedException instanceof NetException)) {
                    String stashedExceptionMessage = String.format("%s, connect lapse %d ms.", stashedException.getMessage(), Long.valueOf(System.currentTimeMillis() - connectStartTime));
                    stashedException = new IOException(stashedExceptionMessage, stashedException);
                }
                responseType = 4;
            }
            isConnectionAccepted = handleConnectPacketResponse(nvf, nvn, packet, responseType, stashedException);
        } while (!isConnectionAccepted);
    }

    private void handleIOTimeoutInterrupt() {
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleIOTimeoutInterrupt", "Connection establishment interrupted by IO Read Timout mechanism, will be trying with next available connect option.", null, null, new Object[0]);
        Thread.interrupted();
    }

    private boolean handleOutboundTimeoutInterrupt(InterruptedIOException interruptException) {
        if (!$assertionsDisabled && (interruptException instanceof TimeoutInterruptHandler.IOReadTimeoutException)) {
            throw new AssertionError("IO timeout is being handled as an outbound timeout");
        }
        TimeoutInterruptHandler.InterruptTask outBoundInterruptTask = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, Thread.currentThread());
        if (outBoundInterruptTask != null && outBoundInterruptTask.isInterrupted()) {
            Thread.interrupted();
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleOutboundTimeoutInterrupt", "Connection establishment interrupted by Outbound Timout mechanism, will be trying with next available connect option.", null, null, new Object[0]);
            return true;
        }
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleOutboundTimeoutInterrupt", "Connection establishment interrupted externally, exiting.", null, null, new Object[0]);
        return false;
    }

    private void handleIOException() {
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleIOException", "Connection establishment failed due to IOException, will be trying with next available connect option.", null, null, new Object[0]);
        TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, Thread.currentThread());
    }

    private final boolean handleConnectPacketResponse(NVFactory nvf, NVNavigator nvn, NIOPacket packet, int responseType, IOException stashedException) throws IOException {
        switch (responseType) {
            case 2:
                handleAcceptPacket((NIOAcceptPacket) packet);
                return true;
            case 3:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            default:
                this.sAtts.cOption.nt.disconnect();
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleConnectPacketResponse", "Got Unexpected packet from server. Packet Type = {0}, SessionTraceId = {1}", null, null, Integer.valueOf(responseType), this.sAtts.traceId);
                throw new NetException(NetException.UNEXPECTED_PKT);
            case 4:
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleConnectPacketResponse", "Got Refused, SessionTraceId = {0}", null, null, this.sAtts.traceId);
                ConnOption origConnOption = this.sAtts.cOption.getOriginalConnOption();
                if (establishConnectionAfterRefusePacket()) {
                    lastConnectException(createRefusePacketException(nvf, nvn, (NIORefusePacket) packet, origConnOption));
                    return false;
                }
                if (stashedException != null) {
                    throw stashedException;
                }
                throw createRefusePacketException(nvf, nvn, packet, origConnOption);
            case 5:
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleConnectPacketResponse", "Got Redirect, SessionTraceId = {0}", null, null, this.sAtts.traceId);
                ConnOption origConnOption2 = this.sAtts.cOption.getOriginalConnOption();
                handleRedirectPacket((NIORedirectPacket) packet);
                redirectConnection((NIORedirectPacket) packet, origConnOption2);
                return false;
            case 11:
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleConnectPacketResponse", "Got Resend, SessionTraceId = {0}", null, null, this.sAtts.traceId);
                handleResendPacket((NIOResendPacket) packet);
                return false;
        }
    }

    private final void redirectConnection(NIORedirectPacket rdPkt, ConnOption origConnOption) throws IOException {
        begin(Metrics.ConnectionEvent.NS_REDIRECT);
        String rdAddress = rdPkt.redirectData;
        String redirectConnectData = null;
        if ((rdPkt.header.flags & 2) == 2 && rdPkt.redirectData.indexOf(0) != -1) {
            rdAddress = rdPkt.redirectData.substring(0, rdPkt.redirectData.indexOf(0));
            this.sAtts.redirecting = true;
            redirectConnectData = rdPkt.redirectData.substring(rdPkt.redirectData.indexOf(0) + 1, rdPkt.redirectData.length());
        }
        validateRedirectResponse(rdAddress);
        if (OracleConnectionStringBuilder.PROTOCOL_WSS.equalsIgnoreCase(this.sAtts.cOption.protocol)) {
            rdAddress = getWSSRedirectAddress(rdAddress, this.sAtts.cOption.addr);
        }
        this.addrRes.setRedirectConnectData(redirectConnectData == null ? this.sAtts.cOption.conn_data.toString() : redirectConnectData);
        establishConnection(rdAddress, this.dmsParent);
        this.addrRes.setRedirectConnectData(null);
        this.sAtts.cOption.setOriginalConnOption(origConnOption);
        end(Metrics.ConnectionEvent.NS_REDIRECT);
    }

    private final boolean establishConnectionAfterRefusePacket() throws IOException {
        this.sAtts.cOption.nt.disconnect();
        this.sAtts.cOption = null;
        TimeoutInterruptHandler.InterruptTask it = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, Thread.currentThread());
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "establishConnectionAfterRefusePacket", "Outbound interrupt timer cancelled {0}", null, null, it);
        if (it != null && it.isInterrupted()) {
            Thread.interrupted();
        }
        try {
            establishConnection(null, true, this.dmsParent);
        } catch (NetException e) {
        }
        return this.sAtts.cOption != null;
    }

    @Override // oracle.net.ns.NSProtocol
    final CompletionStage<Void> negotiateConnectionAsync(NVFactory nvf, NVNavigator nvn, boolean disableOOB, boolean useZeroCopyIO, DMSFactory.DMSNoun _dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        return chainAsyncNegotiationIO(nvf, nvn, disableOOB, useZeroCopyIO, _dmsParent, new NIOConnectPacket(this.sAtts), outboundTimeoutHandler, asyncExecutor);
    }

    /* renamed from: oracle.net.ns.NSProtocolNIO$1ConnectResponse, reason: invalid class name */
    /* loaded from: ojdbc8.jar:oracle/net/ns/NSProtocolNIO$1ConnectResponse.class */
    class C1ConnectResponse {
        final int packetType;
        final NIOPacket packet;
        final IOException failure;

        C1ConnectResponse(int packetType, NIOPacket packet, IOException failure) {
            this.packetType = packetType;
            this.packet = packet;
            this.failure = failure;
        }
    }

    private final CompletionStage<Void> chainAsyncNegotiationIO(NVFactory nvf, NVNavigator nvn, boolean disableOOB, boolean useZeroCopyIO, DMSFactory.DMSNoun dmsParent, NIOConnectPacket cnPkt, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        CompletableFuture<Void> ioFuture = new CompletableFuture<>();
        try {
            cnPkt.writeToSocketChannel(this.sAtts.cOption.conn_data.toString(), !disableOOB, useZeroCopyIO, this.sAtts.nt.isCharacteristicUrgentSupported(), this.sAtts.getSDU(), this.sAtts.getTDU(), this.sAtts.getANOFlags());
            this.sAtts.cOption.nt.registerForNonBlockingRead(ioFailure -> {
                asyncExecutor.execute(() -> {
                    if (ioFailure == null) {
                        ioFuture.complete(null);
                    } else {
                        ioFuture.completeExceptionally(ioFailure);
                    }
                });
            });
        } catch (IOException ea) {
            ioFuture.completeExceptionally(ea);
        }
        return ioFuture.thenApply((Function<? super Void, ? extends U>) CompletionStageUtil.normalCompletionHandler(nil -> {
            NIOPacket packet = NIOPacket.readNIOPacket(this.sAtts);
            int packetType = packet.header.type;
            return new C1ConnectResponse(packetType, packet, null);
        })).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, ea2 -> {
            outboundTimeoutHandler.cancelTimeout();
            return new C1ConnectResponse(4, null, ea2);
        })).thenCompose(response -> {
            return handleConnectPacketResponseAsync(nvf, nvn, response.packet, response.packetType, response.failure, outboundTimeoutHandler, asyncExecutor);
        }).thenCompose(isConnectionAccepted -> {
            if (isConnectionAccepted.booleanValue()) {
                return CompletionStageUtil.VOID_COMPLETED_FUTURE;
            }
            return chainAsyncNegotiationIO(nvf, nvn, disableOOB, useZeroCopyIO, dmsParent, cnPkt, outboundTimeoutHandler, asyncExecutor);
        });
    }

    private final CompletionStage<Boolean> handleConnectPacketResponseAsync(NVFactory nvf, NVNavigator nvn, NIOPacket packet, int responseType, IOException stashedException, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        try {
            switch (responseType) {
                case 2:
                    handleAcceptPacket((NIOAcceptPacket) packet);
                    return CompletionStageUtil.completedStage(Boolean.TRUE);
                case 3:
                case 6:
                case 7:
                case 8:
                case 9:
                case 10:
                default:
                    this.sAtts.cOption.nt.disconnect();
                    return CompletionStageUtil.failedStage(new NetException(NetException.UNEXPECTED_PKT));
                case 4:
                    ConnOption origConnOption = this.sAtts.cOption.getOriginalConnOption();
                    return establishConnectionAfterRefusePacketAsync(outboundTimeoutHandler, asyncExecutor).thenApply(CompletionStageUtil.normalCompletionHandler(isConnected -> {
                        if (isConnected.booleanValue()) {
                            return Boolean.FALSE;
                        }
                        if (stashedException != null) {
                            throw stashedException;
                        }
                        if (packet != null) {
                            throw createRefusePacketException(nvf, nvn, (NIORefusePacket) packet, origConnOption);
                        }
                        throw new NetException(NetException.CONNECTION_REFUSED);
                    }));
                case 5:
                    ConnOption origConnOption2 = this.sAtts.cOption.getOriginalConnOption();
                    handleRedirectPacket((NIORedirectPacket) packet);
                    return redirectConnectionAsync((NIORedirectPacket) packet, origConnOption2, outboundTimeoutHandler, asyncExecutor).thenApply(nil -> {
                        return Boolean.FALSE;
                    });
                case 11:
                    handleResendPacket((NIOResendPacket) packet);
                    return CompletionStageUtil.completedStage(Boolean.FALSE);
            }
        } catch (IOException handlingFailure) {
            return CompletionStageUtil.failedStage(handlingFailure);
        }
    }

    private final CompletionStage<Void> redirectConnectionAsync(NIORedirectPacket rdPkt, ConnOption origConnOption, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        String rdAddress;
        String redirectConnectData;
        if ((rdPkt.header.flags & 2) == 2 && rdPkt.redirectData.indexOf(0) != -1) {
            rdAddress = rdPkt.redirectData.substring(0, rdPkt.redirectData.indexOf(0));
            this.sAtts.redirecting = true;
            redirectConnectData = rdPkt.redirectData.substring(rdPkt.redirectData.indexOf(0) + 1, rdPkt.redirectData.length());
        } else {
            rdAddress = rdPkt.redirectData;
            redirectConnectData = null;
        }
        try {
            validateRedirectResponse(rdAddress);
            if (OracleConnectionStringBuilder.PROTOCOL_WSS.equalsIgnoreCase(this.sAtts.cOption.protocol)) {
                rdAddress = getWSSRedirectAddress(rdAddress, this.sAtts.cOption.addr);
            }
            String str = redirectConnectData;
            return establishConnectionAsync(rdAddress, false, this.dmsParent, outboundTimeoutHandler, asyncExecutor).thenAccept(initializedSessionAtts -> {
                this.sAtts.cOption.setOriginalConnOption(origConnOption);
                if (this.sAtts.redirecting) {
                    this.sAtts.cOption.conn_data.setLength(0);
                    this.sAtts.cOption.conn_data.append(str);
                } else {
                    this.sAtts.cOption.conn_data = origConnOption.conn_data;
                }
            });
        } catch (IOException getAddressFailure) {
            return CompletionStageUtil.failedStage(getAddressFailure);
        }
    }

    private final CompletionStage<Boolean> establishConnectionAfterRefusePacketAsync(AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        try {
            this.sAtts.cOption.nt.disconnect();
            this.sAtts.cOption = null;
            outboundTimeoutHandler.cancelTimeout();
            return establishConnectionAsync(null, true, this.dmsParent, outboundTimeoutHandler, asyncExecutor).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(NetException.class, connectFailure -> {
                return null;
            })).thenApply(initializedSessionAttsOrNull -> {
                return Boolean.valueOf(this.sAtts.cOption != null);
            });
        } catch (IOException disconnectFailure) {
            return CompletionStageUtil.failedStage(disconnectFailure);
        }
    }

    private final void handleAcceptPacket(NIOAcceptPacket acPkt) throws IOException {
        begin(Metrics.ConnectionEvent.NS_ACCEPT);
        this.sAtts.setNegotiatedSDUAndTDU(acPkt.sduSize, acPkt.tduSize);
        this.sAtts.setNegotiatedOptions(acPkt.options);
        this.sAtts.setConnectData(acPkt.connectData);
        this.addrRes.clearConnStrategyStack();
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleAcceptPacket", "Connection established. Cleared conn strategy stack", null, null, new Object[0]);
        this.sAtts.payloadDataBufferForRead.position(this.sAtts.payloadDataBufferForRead.limit());
        this.sAtts.connected = true;
        end(Metrics.ConnectionEvent.NS_ACCEPT);
        if (acPkt.isOOBCheckEnabled) {
            tryUrgentByte();
            sendMarker(2, (byte) 3);
            this.sAtts.setNetProperty(SQLnetDef.IS_OOB_CHECK_DONE, "true");
            return;
        }
        this.sAtts.setNetProperty(SQLnetDef.IS_OOB_CHECK_DONE, "false");
    }

    private final void handleRedirectPacket(NIORedirectPacket rdPkt) throws IOException {
        this.addrRes.connection_redirected = true;
        this.sAtts.cOption.nt.disconnect();
    }

    private final NetException createRefusePacketException(NVFactory nvf, NVNavigator nvn, NIOPacket pkt, ConnOption cOption) {
        NVPair nvPair;
        if (pkt == null || pkt.header == null || pkt.header.type != 4) {
            return new NetException(NetException.CONNECTION_REFUSED, "");
        }
        NIORefusePacket rfPkt = (NIORefusePacket) pkt;
        String errCode = null;
        try {
            NVPair errvp = nvn.findNVPairRecurse(nvf.createNVPair(rfPkt.refuseData), "ERROR");
            if (errvp != null && (nvPair = nvn.findNVPairRecurse(errvp, "CODE")) != null) {
                errCode = nvPair.valueToString();
            }
        } catch (NLException exp) {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "createRefusePacketException", "Failed to parse refuse data.", null, exp, new Object[0]);
        }
        int errNumber = errCode == null ? NetException.CONNECTION_REFUSED : Integer.parseInt(errCode);
        String serverType = null;
        try {
            NVPair nvpConData = nvf.createNVPair(cOption.conn_data.toString());
            NVPair connDataPairs = nvn.findNVPair(nvpConData, "CONNECT_DATA");
            for (int i = 0; i < connDataPairs.getListSize(); i++) {
                if (connDataPairs.getListElement(i).getName().equals("SERVER")) {
                    serverType = connDataPairs.getListElement(i).getAtom();
                }
            }
            if (errNumber == 12514) {
                return new NetException(errNumber, null, false, cOption.service_name, "host " + cOption.host + " port " + cOption.port);
            }
            if (errNumber == 12505) {
                return new NetException(errNumber, null, false, cOption.sid, "host " + cOption.host + " port " + cOption.port);
            }
            if (errNumber == 12521) {
                return new NetException(errNumber, null, false, cOption.instance_name, cOption.service_name, "host " + cOption.host + " port " + cOption.port);
            }
            if (errNumber == 12520) {
                return new NetException(errNumber, null, false, "host " + cOption.host + " port " + cOption.port, serverType, cOption.service_name);
            }
            if (errNumber == 12516) {
                return new NetException(errNumber, null, false, "host " + cOption.host + " port " + cOption.port, cOption.protocol, cOption.service_name);
            }
            if (errNumber >= 12000) {
                return new NetException(errNumber);
            }
            return new NetException(NetException.CONNECTION_REFUSED, errCode == null ? "" : "(Oracle Error - " + errCode + ")");
        } catch (Exception e) {
            return new NetException(NetException.INVALID_REFUSE_PACKET_DATA);
        }
    }

    private final void handleResendPacket(NIOResendPacket rsPkt) throws IOException {
        this.isResending = true;
        if ((rsPkt.header.flags & 8) == 8) {
            this.sAtts.renegotiateSSLSession();
        }
    }

    private String getWSSRedirectAddress(String redirectAddr, String originalAddr) throws IOException {
        try {
            NVNavigator redirectNavigator = new NVNavigator();
            NVPair redirectNVPair = new NVFactory().createNVPair(redirectAddr);
            String redirectHost = redirectNavigator.findNVPair(redirectNVPair, "HOST").getAtom();
            String redirectPort = redirectNavigator.findNVPair(redirectNVPair, "PORT").getAtom();
            NVNavigator origNav = new NVNavigator();
            NVPair originalNVPair = new NVFactory().createNVPair(originalAddr);
            String originalHost = redirectNavigator.findNVPair(originalNVPair, "HOST").getAtom();
            String originalPort = redirectNavigator.findNVPair(originalNVPair, "PORT").getAtom();
            NVPair nvpWebSockUri = origNav.findNVPair(originalNVPair, "WEBSOCK_URI");
            String websocketURI = nvpWebSockUri == null ? "/sqlnet" : nvpWebSockUri.getAtom();
            String redirectWebSocketUri = websocketURI + "/" + redirectHost + ":" + redirectPort;
            return String.format("(ADDRESS=(PROTOCOL=WSS)(HOST=%s)(PORT=%s)(WEBSOCK_URI=%s))", originalHost, originalPort, redirectWebSocketUri);
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    @Override // oracle.net.ns.Communication
    public void writeZeroCopyIO(byte[] userBuffer, int offset, int length) throws IOException {
        int lengthInDD;
        int nbOfRemainingBytes = length;
        boolean isLastDD = false;
        while (nbOfRemainingBytes > 0) {
            if (nbOfRemainingBytes >= 1703910) {
                lengthInDD = 1703910;
            } else {
                lengthInDD = nbOfRemainingBytes;
                isLastDD = true;
            }
            this.sAtts.prepareWriteBuffer();
            this.sAtts.ddPacket.writeToSocketChannel(lengthInDD, isLastDD);
            ByteBuffer tmpByteBuffer = ByteBuffer.wrap(userBuffer, offset, lengthInDD);
            while (tmpByteBuffer.hasRemaining()) {
                this.sAtts.socketChannel.write(tmpByteBuffer);
            }
            offset += lengthInDD;
            nbOfRemainingBytes -= lengthInDD;
        }
    }

    @Override // oracle.net.ns.Communication
    public void writeZeroCopyIOHeader(boolean flushBuffer, int lengthInDD, boolean isLastDD) throws IOException {
        this.sAtts.prepareWriteBuffer();
        this.sAtts.ddPacket.writeToSocketChannel(lengthInDD, isLastDD);
    }

    @Override // oracle.net.ns.Communication
    public void writeZeroCopyIOData(byte[] userBuffer, int offset, int length) throws IOException {
        ByteBuffer tmpByteBuffer = ByteBuffer.wrap(userBuffer, offset, length);
        while (tmpByteBuffer.hasRemaining()) {
            this.sAtts.socketChannel.write(tmpByteBuffer);
        }
    }

    @Override // oracle.net.ns.Communication
    public boolean readZeroCopyIO(byte[] userBuffer, int offset, int[] bytesRead) throws IOException {
        boolean markIsPresent = false;
        this.sAtts.ddPacket.readFromSocketChannel(true);
        this.sAtts.ddPacket.readPayloadBuffer();
        int nbOfBytes = this.sAtts.ddPacket.totalDataLength;
        if ((this.sAtts.ddPacket.descriptorFLaG & 1) != 0) {
            markIsPresent = true;
        }
        if (userBuffer.length < offset + nbOfBytes) {
            throw new IOException("Assertion Failed");
        }
        int bytesReadSoFar = 0;
        ByteBuffer buffer = this.sAtts.readBuffer;
        if (buffer.hasRemaining()) {
            int copySize = Math.min(buffer.remaining(), nbOfBytes);
            buffer.get(userBuffer, offset, copySize);
            bytesReadSoFar = 0 + copySize;
        }
        while (bytesReadSoFar < nbOfBytes) {
            buffer.clear();
            buffer.limit(Math.min(buffer.capacity(), nbOfBytes - bytesReadSoFar));
            while (buffer.hasRemaining()) {
                this.sAtts.socketChannel.read(buffer);
            }
            buffer.rewind();
            buffer.get(userBuffer, offset + bytesReadSoFar, buffer.limit());
            bytesReadSoFar += buffer.limit();
        }
        bytesRead[0] = bytesReadSoFar;
        return markIsPresent;
    }

    @Override // oracle.net.ns.NSProtocol, oracle.net.ns.Communication
    public void cancelTimeout() {
        TimeoutInterruptHandler.InterruptTask it = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.SO_TIMEOUT, Thread.currentThread());
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cancelTimeout", "SO_TIMEOUT interrupt timer cancelled {0}", (String) null, (String) null, it);
    }

    @Override // oracle.net.ns.Communication
    public void disconnect() throws IOException {
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        IOException ioException = null;
        try {
            this.sAtts.dataChannel.sendEOF();
        } catch (IOException ioe) {
            ioException = ioe;
        }
        this.sAtts.connected = false;
        this.dmsParent = null;
        this.sAtts.cOption.nt.disconnect();
        this.sAtts.releaseWriteBuffer();
        if (ioException != null) {
            throw ioException;
        }
    }

    @Override // oracle.net.ns.Communication
    public void sendReset() throws IOException {
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        TimeoutInterruptHandler.InterruptTask it = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.SO_TIMEOUT, Thread.currentThread());
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendReset", "SO_TIMEOUT interrupt timer cancelled {0}", null, null, it);
        sendMarker(1, (byte) 2);
        while (this.sAtts.onBreakReset) {
            this.sAtts.markerPacket.readFromSocketChannel(true, false);
            this.sAtts.markerPacket.readPayloadBuffer();
            if (this.sAtts.markerPacket.isResetPkt()) {
                this.sAtts.onBreakReset = false;
            }
        }
    }

    @Override // oracle.net.ns.NSProtocol, oracle.net.ns.Communication
    public void sendInterrupt() throws IOException {
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        TimeoutInterruptHandler.InterruptTask it = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.SO_TIMEOUT, this.sAtts.socketChannel);
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendInterrupt", "SO_TIMEOUT interrupt timer cancelled {0}", null, null, it);
        super.sendInterrupt();
    }

    @Override // oracle.net.ns.NSProtocol
    void initializeSessionAttributes() throws IOException {
        this.sAtts.socketChannel = this.sAtts.nt.getSocketChannel();
        this.sAtts.initializeBuffer();
        this.sAtts.dataEOF = false;
    }

    @Override // oracle.net.ns.NSProtocol
    protected void sendMarker(int type, byte markerData) throws IOException {
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        this.sAtts.markerPacket.writeToSocketChannel(type, markerData);
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendMarker", "Sending break marker, SessionTraceId = {0}", null, null, this.sAtts.traceId);
    }

    @Override // oracle.net.ns.NSProtocol
    void sendProbePacket() throws IOException {
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendMarker", "Sending a probe packet, SessionTraceId = {0}", null, null, this.sAtts.traceId);
        if (this.probePacket == null) {
            this.probePacket = new NIONSDataChannel(this.sAtts);
        } else {
            this.probePacket.reinitialize(this.sAtts);
        }
        byte[] connectDataBytes = new byte[26];
        this.probePacket.writeDataToSocketChannel(connectDataBytes);
    }

    void doSocketRead(int minBytesRequired) throws IOException {
        int initialPosition = this.sAtts.readBuffer.position();
        int numberOfRetries = 0;
        while (this.sAtts.readBuffer.position() < minBytesRequired) {
            int numberOfBytesRead = this.sAtts.socketChannel.read(this.sAtts.readBuffer);
            if (numberOfBytesRead < 0) {
                throw new NetException(NetException.GOT_MINUS_ONE);
            }
            if (numberOfBytesRead == 0) {
                numberOfRetries++;
                if (numberOfRetries >= 10) {
                    throw new NetException(NetException.GOT_MINUS_ONE);
                }
            } else {
                numberOfRetries = 0;
            }
        }
        this.sAtts.readBuffer.flip();
        this.sAtts.readBuffer.position(initialPosition);
    }

    @Override // oracle.net.ns.Communication
    public void sendZDP() throws IOException {
        this.sAtts.prepareWriteBuffer();
        this.sAtts.dataChannel.header.type = 6;
        this.sAtts.payloadBufferForWrite.clear();
        this.sAtts.payloadBufferForWrite.limit(2);
        this.sAtts.payloadBufferForWrite.put((byte) 0);
        this.sAtts.payloadBufferForWrite.put((byte) 0);
        this.sAtts.dataChannel.writeToSocketChannel();
    }

    @Override // oracle.net.ns.Communication
    public boolean needsToBeClosed() {
        return this.sAtts.needsToBeClosed;
    }

    @Override // oracle.net.ns.Communication
    public void readInbandNotification() {
        try {
            if (this.sAtts.needsToBeClosed) {
                return;
            }
            this.sAtts.dataChannel.readInbandNotificationCtlPacket();
        } catch (IOException ioEx) {
            this.sAtts.needsToBeClosed = true;
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "readInbandNotification", "Received IOException while reading in-band notification, SessionTraceId = {0}", null, ioEx, this.sAtts.traceId);
        }
    }

    @Override // oracle.net.ns.NSProtocol, oracle.net.ns.Communication
    public final void sendBreak() throws IOException {
        this.isBreakPending.set(true);
        if (this.isWriting.compareAndSet(false, true)) {
            try {
                sendPendingBreak();
            } finally {
                this.isWriting.set(false);
            }
        }
    }

    final void beginWrite() throws IOException {
        while (!this.isWriting.compareAndSet(false, true)) {
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedIOException("Socket write interrupted");
            }
        }
        try {
            sendPendingBreak();
        } catch (Throwable sendBreakError) {
            this.isWriting.set(false);
            if (sendBreakError instanceof IOException) {
                throw ((IOException) sendBreakError);
            }
            throw new IOException(sendBreakError);
        }
    }

    final void endWrite(Throwable writeException) throws IOException {
        if (writeException != null) {
            this.isWriting.set(false);
            if (writeException instanceof IOException) {
                throw ((IOException) writeException);
            }
            throw new IOException(writeException);
        }
        try {
            sendPendingBreak();
            while (this.isBreakPending.get()) {
                if (this.isWriting.compareAndSet(false, true)) {
                    try {
                        sendPendingBreak();
                    } finally {
                    }
                } else if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedIOException("Socket write interrupted");
                }
            }
        } finally {
        }
    }

    private void sendPendingBreak() throws IOException {
        if (!$assertionsDisabled && !this.isWriting.get()) {
            throw new AssertionError("Sending break without the write lock");
        }
        if (!this.isBreakPending.get()) {
            return;
        }
        if (!this.sAtts.connected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        if (this.sAtts.isExpediatedAttentionEnabled() && !this.sAtts.nt.awaitWriteReadiness(SEND_BREAK_TIMEOUT_MS)) {
            throw new IOException("Unable to send break without blocking");
        }
        TimeoutInterruptHandler.InterruptTask it = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, this.sAtts.socketChannel);
        trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "sendPendingBreak", "OUTBOUND interrupt timer cancelled {0}", null, null, it);
        int originalTimeout = getSocketReadTimeout();
        setSocketReadTimeout(30);
        try {
            super.sendBreak();
            this.isBreakPending.set(false);
        } finally {
            setSocketReadTimeout(originalTimeout);
        }
    }
}
