package oracle.net.ns;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/net/ns/NIOControlPacket.class */
class NIOControlPacket extends NIOPacket {
    short nsControlCommand;
    static final short NSPCTL_SERR = 8;
    static final short NSPCTL_CLRATTN = 9;
    static final int ORA_ERROR_EMFI_NUMBER = 22;
    static final int NSECMANSHUT = 12572;
    static final int NSESENDMESG = 12573;

    NIOControlPacket(SessionAtts session) {
        super(session);
        this.header.type = 10;
    }

    @Override // oracle.net.ns.NIOPacket
    void readPayloadBuffer() throws IOException {
        this.nsControlCommand = this.session.payloadBufferForRead.getShort();
        if (this.nsControlCommand == 8) {
            processNSError();
        } else if (this.nsControlCommand == 9) {
            disableAttentionProcessing();
        }
    }

    private void processNSError() throws IOException {
        int emfiNumber = this.session.payloadBufferForRead.getInt();
        int primaryErrorNumber = this.session.payloadBufferForRead.getInt();
        this.session.payloadBufferForRead.getInt();
        if (primaryErrorNumber == NSECMANSHUT || primaryErrorNumber == NSESENDMESG) {
            this.session.needsToBeClosed = true;
        } else {
            if (emfiNumber == 22) {
                throw new NetException(primaryErrorNumber, (String) null, true);
            }
            throw new NetException(primaryErrorNumber, "TNS-" + primaryErrorNumber, true);
        }
    }

    private void disableAttentionProcessing() {
        this.session.negotiatedOptions &= -513;
        this.session.negotiatedOptions &= -1025;
        this.session.setNetProperty(SQLnetDef.IS_OOB_ENABLED, "false");
    }
}
