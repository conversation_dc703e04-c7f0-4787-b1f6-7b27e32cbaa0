package oracle.net.ns;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.nt.MQLNTAdapter;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.SocketChannelWrapper;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: ojdbc8.jar:oracle/net/ns/NIOPacket.class */
public abstract class NIOPacket implements Diagnosable {
    final NIOHeader header;
    public SessionAtts session;
    private static final String CLASS_NAME = NIOPacket.class.getName();
    static final String[] toHex = {" 00", " 01", " 02", " 03", " 04", " 05", " 06", " 07", " 08", " 09", " 0A", " 0B", " 0C", " 0D", " 0E", " 0F", " 10", " 11", " 12", " 13", " 14", " 15", " 16", " 17", " 18", " 19", " 1A", " 1B", " 1C", " 1D", " 1E", " 1F", " 20", " 21", " 22", " 23", " 24", " 25", " 26", " 27", " 28", " 29", " 2A", " 2B", " 2C", " 2D", " 2E", " 2F", " 30", " 31", " 32", " 33", " 34", " 35", " 36", " 37", " 38", " 39", " 3A", " 3B", " 3C", " 3D", " 3E", " 3F", " 40", " 41", " 42", " 43", " 44", " 45", " 46", " 47", " 48", " 49", " 4A", " 4B", " 4C", " 4D", " 4E", " 4F", " 50", " 51", " 52", " 53", " 54", " 55", " 56", " 57", " 58", " 59", " 5A", " 5B", " 5C", " 5D", " 5E", " 5F", " 60", " 61", " 62", " 63", " 64", " 65", " 66", " 67", " 68", " 69", " 6A", " 6B", " 6C", " 6D", " 6E", " 6F", " 70", " 71", " 72", " 73", " 74", " 75", " 76", " 77", " 78", " 79", " 7A", " 7B", " 7C", " 7D", " 7E", " 7F", " 80", " 81", " 82", " 83", " 84", " 85", " 86", " 87", " 88", " 89", " 8A", " 8B", " 8C", " 8D", " 8E", " 8F", " 90", " 91", " 92", " 93", " 94", " 95", " 96", " 97", " 98", " 99", " 9A", " 9B", " 9C", " 9D", " 9E", " 9F", " A0", " A1", " A2", " A3", " A4", " A5", " A6", " A7", " A8", " A9", " AA", " AB", " AC", " AD", " AE", " AF", " B0", " B1", " B2", " B3", " B4", " B5", " B6", " B7", " B8", " B9", " BA", " BB", " BC", " BD", " BE", " BF", " C0", " C1", " C2", " C3", " C4", " C5", " C6", " C7", " C8", " C9", " CA", " CB", " CC", " CD", " CE", " CF", " D0", " D1", " D2", " D3", " D4", " D5", " D6", " D7", " D8", " D9", " DA", " DB", " DC", " DD", " DE", " DF", " E0", " E1", " E2", " E3", " E4", " E5", " E6", " E7", " E8", " E9", " EA", " EB", " EC", " ED", " EE", " EF", " F0", " F1", " F2", " F3", " F4", " F5", " F6", " F7", " F8", " F9", " FA", " FB", " FC", " FD", " FE", " FF"};
    static final char[] toChar = {'.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '!', '\"', '#', '$', '%', '&', '\'', '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.'};

    abstract void readPayloadBuffer() throws IOException;

    NIOPacket(SessionAtts session) {
        this.session = session;
        this.header = new NIOHeader(session);
    }

    NIOPacket(NIOHeader header, SessionAtts session) throws IOException {
        this.header = header;
        this.session = session;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.session.getDiagnosable();
    }

    final void readFromSocketChannel(boolean needToReadHeader) throws IOException {
        readFromSocketChannel(needToReadHeader, true);
    }

    final void readFromSocketChannel(boolean needToReadHeader, boolean handleMarker) throws IOException {
        if (this.session.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            readPacketFromMSGQ();
        } else {
            readPacketFromSocketChannel(needToReadHeader);
        }
        if (this.header.type == 14) {
            this.session.controlPacket.readPayloadBuffer();
            readFromSocketChannel(needToReadHeader, handleMarker);
        }
        if (handleMarker && this.header.type == 12) {
            this.session.markerPacket.readPayloadBuffer();
            processMarker();
            this.session.onBreakReset = this.session.markerPacket.isBreakPkt();
            throw new BreakNetException(NetException.NS_BREAK);
        }
    }

    private void readPacketFromMSGQ() throws IOException {
        ByteOrder byteOrder = this.session.readBuffer.order();
        ByteBuffer readBuffer = ((MQLNTAdapter) this.session.nt).readFromLocalQueue();
        int read = readBuffer.limit();
        readBuffer.order(byteOrder);
        this.session.setReadBuffer(readBuffer);
        this.session.payloadDataBufferForRead.position(this.session.payloadDataBufferForRead.limit());
        if (read >= 8) {
            this.session.headerBufferForRead.position(0);
            this.session.headerBufferForRead.limit(8);
            this.header.readNSHeader();
        }
        this.session.payloadBufferForRead.clear();
        this.session.payloadBufferForRead.limit(this.header.length - 8);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readPacketFromMSGQ", "Type = {0}, Length = {1}, flags = {2}, SessionTraceID = {3}", null, null, Integer.valueOf(this.header.type), Integer.valueOf(this.header.length), Integer.valueOf(this.header.flags), this.session.traceId);
    }

    private void readPacketFromSocketChannel(boolean needToReadHeader) throws IOException {
        int packetStartPosition;
        if (needToReadHeader) {
            packetStartPosition = readHeader();
        } else {
            packetStartPosition = this.session.readBuffer.position() - 8;
        }
        int payloadLength = this.header.length - 8;
        if (payloadLength <= 0) {
            return;
        }
        if (this.session.readBuffer.remaining() < payloadLength) {
            packetStartPosition = readPayload(packetStartPosition, payloadLength);
        }
        this.session.readBuffer.position(packetStartPosition + 8);
        this.session.payloadBufferForRead = this.session.readBuffer.slice();
        this.session.payloadBufferForRead.limit(this.header.length - 8);
        this.session.readBuffer.position(packetStartPosition + this.header.length);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readPacketFromSocketChannel", "Type = {0}, Length = {1}, flags = {2}, SessionTraceID = {3}", null, null, Integer.valueOf(this.header.type), Integer.valueOf(this.header.length), Integer.valueOf(this.header.flags), this.session.traceId);
    }

    private int readHeader() throws IOException {
        int packetStartPosition;
        if (this.session.readBuffer.hasRemaining() && this.session.readBuffer.position() > 0) {
            if (this.session.readBuffer.remaining() < 8) {
                this.session.readBuffer.compact();
                packetStartPosition = 0;
                ((NSProtocolNIO) this.session.ns).doSocketRead(8);
            } else {
                packetStartPosition = this.session.readBuffer.position();
            }
        } else {
            this.session.readBuffer.clear();
            packetStartPosition = 0;
            ((NSProtocolNIO) this.session.ns).doSocketRead(8);
        }
        this.session.readBuffer.position(packetStartPosition);
        this.header.readHeaderBuffer();
        this.session.readBuffer.position(packetStartPosition + 8);
        return packetStartPosition;
    }

    private int readPayload(int packetStartPosition, int payloadLength) throws IOException {
        int remainingBytesToBeRead = Math.max(payloadLength - this.session.readBuffer.remaining(), 0);
        int spaceAvailableInBuffer = this.session.readBuffer.capacity() - this.session.readBuffer.limit();
        if (spaceAvailableInBuffer < remainingBytesToBeRead) {
            this.session.readBuffer.position(packetStartPosition);
            this.session.readBuffer.compact();
            packetStartPosition = 0;
        } else {
            this.session.readBuffer.position(this.session.readBuffer.limit());
            this.session.readBuffer.limit(this.session.readBuffer.capacity());
        }
        ((NSProtocolNIO) this.session.ns).doSocketRead(packetStartPosition + this.header.length);
        return packetStartPosition;
    }

    protected void processMarker() throws IOException {
    }

    final void writeToSocketChannel() throws IOException {
        this.header.length = this.session.payloadBufferForWrite.position() + 8;
        this.header.fillHeaderBuffer();
        this.session.writeBuffer.clear();
        this.session.writeBuffer.limit(this.header.length);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "writeToSocketChannel", "Type = {0}, Length = {1}, flags = {2}, HeaderBuffer = {3}, PayloadBuffer = {4}, SessionTraceID = {5}", null, null, Integer.valueOf(this.header.type), Integer.valueOf(this.header.length), Integer.valueOf(this.header.flags), this.session.headerBufferForWrite, this.session.payloadBufferForWrite, this.session.traceId);
        ((NSProtocolNIO) this.session.ns).beginWrite();
        try {
            if (this.session.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
                ((MQLNTAdapter) this.session.nt).writeToRemoteQueue(this.session.writeBuffer, true);
                this.session.writeBuffer = null;
            } else {
                while (this.session.writeBuffer.hasRemaining()) {
                    this.session.socketChannel.write(this.session.writeBuffer);
                }
            }
            ((NSProtocolNIO) this.session.ns).endWrite(null);
        } catch (Throwable throwable) {
            ((NSProtocolNIO) this.session.ns).endWrite(throwable);
        }
        this.session.payloadBufferForWrite.position(this.session.payloadBufferForWrite.limit());
        this.session.payloadDataBufferForWrite.position(this.session.payloadDataBufferForWrite.limit());
    }

    static NIOPacket readNIOPacket(SessionAtts session) throws IOException {
        NIOPacket packet;
        NIOHeader header = new NIOHeader(session);
        if (session.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            ByteBuffer readBuffer = ((MQLNTAdapter) session.nt).readFromLocalQueue();
            int read = readBuffer.limit();
            session.setReadBuffer(readBuffer);
            if (read >= 8) {
                session.headerBufferForRead.position(0);
                session.headerBufferForRead.limit(8);
                header.readNSHeader();
            }
            session.payloadBufferForRead.clear();
            session.payloadBufferForRead.limit(header.length - 8);
            session.readBuffer.position(header.length);
        } else {
            if (session.readBuffer.hasRemaining() && session.readBuffer.position() != 0) {
                session.readBuffer.compact();
            } else {
                session.readBuffer.clear();
            }
            ((NSProtocolNIO) session.ns).doSocketRead(8);
            session.readBuffer.rewind();
            header.readHeaderBuffer();
            session.readBuffer.position(8);
        }
        switch (header.type) {
            case 2:
                packet = new NIOAcceptPacket(header, session);
                break;
            case 3:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            default:
                throw new NetException(NetException.BAD_PKT_TYPE);
            case 4:
                packet = new NIORefusePacket(header, session);
                break;
            case 5:
                packet = new NIORedirectPacket(header, session);
                break;
            case 11:
                packet = new NIOResendPacket(header, session);
                break;
            case 12:
                packet = new NIOMarkerPacket(header, session);
                break;
        }
        if (session.networkType != NTAdapter.NetworkAdapterType.MSGQ) {
            packet.readFromSocketChannel(false);
        }
        packet.readPayloadBuffer();
        return packet;
    }

    void readInbandNotificationCtlPacket() throws IOException {
        if (this.session.networkType == NTAdapter.NetworkAdapterType.MSGQ) {
            ByteBuffer readBuffer = ((MQLNTAdapter) this.session.nt).readFromLocalQueue(false);
            if (readBuffer == null) {
                return;
            }
            int read = readBuffer.limit();
            this.session.setReadBuffer(readBuffer);
            if (read >= 8) {
                this.session.headerBufferForRead.position(0);
                this.session.headerBufferForRead.limit(8);
                this.header.readNSHeader();
            }
            this.session.payloadBufferForRead.clear();
            this.session.payloadBufferForRead.limit(this.header.length - 8);
            this.session.readBuffer.position(this.header.length);
            return;
        }
        try {
            if (!this.session.socketChannel.isConnected()) {
                this.session.needsToBeClosed = true;
                this.session.readBuffer.position(this.session.readBuffer.limit());
                return;
            }
            this.session.readBuffer.clear();
            this.session.readBuffer.limit(8);
            int numberOfRetries = 0;
            int lastBufferPosition = 0;
            int bytesRead = ((SocketChannelWrapper) this.session.socketChannel).readNow(this.session.readBuffer);
            if (bytesRead == 0) {
                return;
            }
            if (bytesRead == -1) {
                this.session.needsToBeClosed = true;
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readInbandNotificationCtlPacket", "Found socket closed while reading in-band notification control packet. SessionTraceId : {0}", (String) null, (String) null, this.session.traceId);
                this.session.readBuffer.position(this.session.readBuffer.limit());
                return;
            }
            while (this.session.readBuffer.hasRemaining()) {
                this.session.socketChannel.read(this.session.readBuffer);
                numberOfRetries = lastBufferPosition == this.session.readBuffer.position() ? numberOfRetries + 1 : 0;
                lastBufferPosition = this.session.readBuffer.position();
                if (numberOfRetries >= 10) {
                    throw new NetException(NetException.GOT_MINUS_ONE);
                }
            }
            this.session.readBuffer.rewind();
            this.session.headerBufferForRead = this.session.readBuffer.slice();
            this.header.readNSHeader();
            this.session.readBuffer.limit(this.header.length);
            this.session.readBuffer.position(8);
            while (this.session.readBuffer.hasRemaining()) {
                this.session.socketChannel.read(this.session.readBuffer);
            }
            this.session.readBuffer.position(8);
            this.session.payloadBufferForRead = this.session.readBuffer.slice();
            this.session.readBuffer.position(this.session.readBuffer.limit());
            if (this.header.type == 14) {
                this.session.controlPacket.readPayloadBuffer();
                return;
            }
            if (this.header.type != 6) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readInbandNotificationCtlPacket", "Received a different NS packet type while reading in-band notification control packet. SessionTraceId : {0}", (String) null, (String) null, this.session.traceId);
                return;
            }
            short dataFlags = this.session.payloadBufferForRead.getShort();
            if ((dataFlags & 32768) == 32768) {
                this.session.renegotiateSSLSession();
            }
        } finally {
            this.session.readBuffer.position(this.session.readBuffer.limit());
        }
    }

    void reinitialize(SessionAtts _session) throws NetException {
        this.session = _session;
    }

    void addFlags(int _flags) throws NetException {
        this.header.flags |= _flags;
    }

    void setPoolEnabled(boolean value) throws IOException {
        if (value) {
            addFlags(1);
        }
    }

    void setLargeSDU(boolean value) throws IOException {
        if (value) {
            addFlags(32);
        }
    }

    String dumpBytes(ByteBuffer buffer, int offset, int packetLength) {
        StringBuilder sb = new StringBuilder(16384);
        StringBuilder tmpBuf = new StringBuilder(80);
        if (buffer == null) {
            return "NULL";
        }
        int position = buffer.position();
        int limit = buffer.limit();
        buffer.position(offset);
        buffer.limit(packetLength);
        sb.delete(0, sb.length());
        tmpBuf.delete(0, tmpBuf.length());
        int bytesThisLine = 0;
        while (buffer.hasRemaining()) {
            int x = buffer.get() & 255;
            sb.append(toHex[x]);
            tmpBuf.append(toChar[x]);
            bytesThisLine++;
            if (bytesThisLine == 8) {
                sb.append("     |");
                sb.append(tmpBuf.substring(0, tmpBuf.length()));
                sb.append("|\n");
                tmpBuf.delete(0, tmpBuf.length());
                bytesThisLine = 0;
            }
        }
        if (bytesThisLine > 0) {
            int spaces = (8 - bytesThisLine) - 1;
            for (int j = 0; j <= spaces; j++) {
                sb.append("   ");
            }
            sb.append("     |");
            sb.append(tmpBuf.substring(0, tmpBuf.length()));
            for (int j2 = 0; j2 <= spaces; j2++) {
                sb.append(" ");
            }
            sb.append("|\n");
            tmpBuf.delete(0, tmpBuf.length());
        }
        buffer.rewind();
        if (limit >= 0) {
            buffer.limit(limit);
        }
        if (position >= 0 && position <= limit) {
            buffer.position(position);
        }
        return sb.substring(0, sb.length());
    }
}
