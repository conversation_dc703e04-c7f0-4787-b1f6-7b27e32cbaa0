package oracle.net.aso;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import oracle.net.aso.AESEncryption;

/* loaded from: ojdbc8.jar:oracle/net/aso/AESDataIntegrity.class */
class AESDataIntegrity implements EncryptionAlgorithm {
    private static final byte DATA_INTEGRITY_RENEWAL_KEY_PAD = -1;
    private static final int DATA_INTEGRITY_SERVER_TO_CLIENT_KEY_PAD = 180;
    private static final int DATA_INTEGRITY_CLIENT_TO_SERVER_KEY_PAD = 90;
    private static final int DATA_INTEGRITY_AES_KEY_SIZE = 16;
    private static final int DATA_INTEGRITY_KEY_SIZE = 5;
    protected static final int AES_INCOMING = 1;
    protected static final int AES_OUTGOING = 2;
    private byte[] feederBytes;
    private PaddedCipher incomingCipher;
    private PaddedCipher outgoingCipher;
    private PaddedCipher sessionCipher;
    private byte[] encryptSequence;
    private byte[] decryptSequence;
    private final boolean allowWeakCrypto;

    AESDataIntegrity(byte[] key, byte[] iv, boolean allowWeakCrypto) throws AsoException {
        this.allowWeakCrypto = allowWeakCrypto;
        init(key, iv);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void init(byte[] dhKey, byte[] iv) throws AsoException {
        byte[] sessionCipherKey = new byte[16];
        byte[] sessionCipherIV = new byte[16];
        if (this.allowWeakCrypto) {
            System.arraycopy(dhKey, 0, sessionCipherKey, 0, 5);
            sessionCipherKey[5] = -1;
            System.arraycopy(iv, 0, sessionCipherIV, 0, 16);
        } else {
            System.arraycopy(dhKey, 0, sessionCipherKey, 0, 16);
            sessionCipherKey[15] = -1;
            System.arraycopy(dhKey, 32, sessionCipherIV, 0, 16);
        }
        this.feederBytes = new byte[32];
        this.sessionCipher = createCipher(sessionCipherKey, sessionCipherIV);
        renewKey();
        this.encryptSequence = null;
        this.decryptSequence = null;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void renewKey() throws AsoException {
        byte[] newSessionKey = this.sessionCipher.encrypt(this.feederBytes);
        this.feederBytes = newSessionKey;
        byte[] newKeyForSessionCipher = new byte[16];
        byte[] newIVForSessionCipher = new byte[16];
        System.arraycopy(newSessionKey, 0, newKeyForSessionCipher, 0, 16);
        System.arraycopy(newSessionKey, 16, newIVForSessionCipher, 0, 16);
        this.sessionCipher = createCipher(newKeyForSessionCipher, newIVForSessionCipher);
        int paddingIndex = this.allowWeakCrypto ? 5 : 15;
        byte[] incomingCipherKey = new byte[16];
        System.arraycopy(newSessionKey, 0, incomingCipherKey, 0, 16);
        incomingCipherKey[paddingIndex] = -76;
        byte[] incomingCipherIV = new byte[16];
        System.arraycopy(newSessionKey, 16, incomingCipherIV, 0, 16);
        this.incomingCipher = createCipher(incomingCipherKey, incomingCipherIV);
        byte[] outgoingCipherKey = new byte[16];
        System.arraycopy(newSessionKey, 0, outgoingCipherKey, 0, 16);
        outgoingCipherKey[paddingIndex] = 90;
        byte[] outgoingCipherIV = new byte[16];
        System.arraycopy(newSessionKey, 16, outgoingCipherIV, 0, 16);
        this.outgoingCipher = createCipher(outgoingCipherKey, outgoingCipherIV);
    }

    private PaddedCipher createCipher(byte[] key, byte[] iv) throws InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
        AESEncryption.AESJCE coreEngine = new AESEncryption.AESJCE(0, true, true);
        coreEngine.init(key, iv);
        CipherBlockProcessor cbp = CipherBlockProcessor.newInstance(0, iv, coreEngine, 16);
        return PaddedCipher.newInstance(0, coreEngine, 16, cbp);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void setSessionKey(byte[] key, byte[] iv) throws AsoException {
        if (key == null || iv == null) {
            return;
        }
        init(key, iv);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] decrypt(byte[] ebuf) throws AsoException {
        if (this.decryptSequence == null) {
            this.decryptSequence = ebuf;
        }
        byte[] output = this.incomingCipher.encrypt(this.decryptSequence);
        this.decryptSequence = (byte[]) output.clone();
        return output;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] encrypt(byte[] buffer) throws AsoException {
        if (this.encryptSequence == null) {
            this.encryptSequence = buffer;
        }
        byte[] output = this.outgoingCipher.encrypt(this.encryptSequence);
        this.encryptSequence = (byte[]) output.clone();
        return output;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public int maxDelta() {
        return 16;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public String getProviderName() {
        return this.sessionCipher.getEncryptionEngine().getProviderName();
    }
}
