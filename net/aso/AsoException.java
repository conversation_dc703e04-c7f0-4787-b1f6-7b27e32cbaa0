package oracle.net.aso;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/net/aso/AsoException.class */
public class AsoException extends IOException {
    private int errorNum;
    static final int ASO_FIRST_ERROR = 100;
    static final int ASO_LAST_ERROR = 300;
    public static final int UNKNOWN_CRYPTO_ALGORITHM = 100;
    public static final int EXPORT_CRYPTO_VIOLATION = 101;
    public static final int KEY_IS_TOO_SMALL = 102;
    public static final int INVALID_ENCRYPTED_BUFFER_LENGTH = 103;
    public static final int INVALID_DECRYPTED_LENGTH = 104;
    public static final int MD5_CHECKSUM_MISMATCH = 105;
    public static final int INVALID_PADDING_FROM_CIPHERTEXT = 106;
    public static final int JCE_EXCEPTION = 107;

    public AsoException(int error) {
        this.errorNum = error;
    }

    public AsoException(int error, Throwable cause) {
        super(cause);
        this.errorNum = error;
    }

    public int getErrorNumber() {
        return this.errorNum;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return Integer.toString(this.errorNum);
    }
}
