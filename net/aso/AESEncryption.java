package oracle.net.aso;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: ojdbc8.jar:oracle/net/aso/AESEncryption.class */
public class AESEncryption implements EncryptionAlgorithm {
    public static final int AES_UNIT_SIZE_BYTES = 16;
    public static final int AES128_KEY_SIZE_BYTES = 16;
    public static final int AES192_KEY_SIZE_BYTES = 24;
    public static final int AES256_KEY_SIZE_BYTES = 32;
    public static final int AES_128 = 1;
    public static final int AES_192 = 2;
    public static final int AES_256 = 3;
    private int blockMode;
    private int algId;
    private boolean isCBCEnabled;
    private int paddingMode;
    private int keySize;
    protected byte[] key;
    protected byte[] iv;
    private PaddedCipher cipher;
    private final boolean allowWeakCrypto;

    public AESEncryption(int cipherBlockMode, int type, int padding, boolean weakCryptoEnabled) {
        this.blockMode = 1;
        this.algId = 1;
        this.isCBCEnabled = true;
        this.paddingMode = 1;
        this.blockMode = cipherBlockMode;
        this.isCBCEnabled = this.blockMode == 1;
        this.algId = type;
        this.allowWeakCrypto = weakCryptoEnabled;
        this.paddingMode = padding;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void init(byte[] dhKey, byte[] iv) throws InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
        if (this.algId == 1) {
            this.keySize = 16;
        } else if (this.algId == 2) {
            this.keySize = 24;
        } else if (this.algId == 3) {
            this.keySize = 32;
        }
        if (dhKey.length < this.keySize) {
            throw new AsoException(102);
        }
        this.key = new byte[this.keySize];
        this.iv = new byte[16];
        System.arraycopy(dhKey, 0, this.key, 0, this.keySize);
        if (!this.allowWeakCrypto) {
            if (dhKey.length < 48) {
                throw new AsoException(102);
            }
            System.arraycopy(dhKey, 32, this.iv, 0, 16);
        }
        initJCEEngine();
    }

    private void initJCEEngine() throws InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
        AESJCE coreEngine = new AESJCE(this.isCBCEnabled);
        coreEngine.init(this.key, this.iv);
        CipherBlockProcessor cipherBlockProcessor = CipherBlockProcessor.newInstance(0, this.iv, coreEngine, 16);
        this.cipher = PaddedCipher.newInstance(this.paddingMode, coreEngine, 16, cipherBlockProcessor);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] decrypt(byte[] ebuf) throws AsoException {
        this.cipher.getBlockProcessor().resetIV(this.iv);
        return this.cipher.decrypt(ebuf);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] encrypt(byte[] buffer) throws AsoException {
        this.cipher.getBlockProcessor().resetIV(this.iv);
        return this.cipher.encrypt(buffer);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public int maxDelta() {
        return 16;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void setSessionKey(byte[] key, byte[] iv) throws InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
        if (key == null || iv == null) {
            return;
        }
        init(key, iv);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public String getProviderName() {
        if (this.cipher == null) {
            return null;
        }
        return this.cipher.getEncryptionEngine().getProviderName();
    }

    /* loaded from: ojdbc8.jar:oracle/net/aso/AESEncryption$AESJCE.class */
    static class AESJCE implements EncryptionEngine {
        private Cipher encryptionCipher;
        private Cipher decryptionCipher;
        private final boolean isCBCEnabled;
        private SecretKeySpec secretKeySpec;
        private IvParameterSpec ivParamSpec;
        private boolean isDataIntegrityMode;
        private int keySize;

        AESJCE(boolean isCBCEnabled) {
            this.isDataIntegrityMode = false;
            this.keySize = 0;
            this.isCBCEnabled = isCBCEnabled;
        }

        AESJCE(int paddingAlgorithm, boolean isCBCEnabled, boolean isDataIntegrityMode) {
            this.isDataIntegrityMode = false;
            this.keySize = 0;
            this.isCBCEnabled = isCBCEnabled;
            this.isDataIntegrityMode = isDataIntegrityMode;
        }

        @Override // oracle.net.aso.EncryptionEngine
        public void init(byte[] key, byte[] iv) throws InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
            try {
                this.keySize = key.length;
                String cipherMode = this.isCBCEnabled ? "CBC" : "ECB";
                String cipherStr = "AES/" + cipherMode + "/NoPadding";
                this.encryptionCipher = Cipher.getInstance(cipherStr);
                this.decryptionCipher = Cipher.getInstance(cipherStr);
                this.secretKeySpec = new SecretKeySpec(key, "AES");
                if (this.isCBCEnabled) {
                    this.ivParamSpec = new IvParameterSpec(iv);
                    this.encryptionCipher.init(1, this.secretKeySpec, this.ivParamSpec);
                    this.decryptionCipher.init(2, this.secretKeySpec, this.ivParamSpec);
                } else {
                    this.encryptionCipher.init(1, this.secretKeySpec);
                    this.decryptionCipher.init(2, this.secretKeySpec);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public byte[] encrypt(byte[] plainData) throws BadPaddingException, IllegalBlockSizeException, InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
            try {
                if (this.isDataIntegrityMode) {
                    return this.encryptionCipher.update(plainData);
                }
                byte[] retBytes = this.encryptionCipher.doFinal(plainData);
                this.encryptionCipher.init(1, this.secretKeySpec, this.ivParamSpec);
                return retBytes;
            } catch (Exception e) {
                throw new AsoException(107, e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public byte[] decrypt(byte[] encryptedData) throws BadPaddingException, IllegalBlockSizeException, InvalidKeyException, AsoException, InvalidAlgorithmParameterException {
            try {
                if (this.isDataIntegrityMode) {
                    return this.decryptionCipher.update(encryptedData);
                }
                byte[] retBytes = this.decryptionCipher.doFinal(encryptedData);
                this.decryptionCipher.init(2, this.secretKeySpec, this.ivParamSpec);
                return retBytes;
            } catch (Exception e) {
                throw new AsoException(107, e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int encrypt(byte[] plainData, int offset, int length, byte[] resultBuffer) throws BadPaddingException, IllegalBlockSizeException, InvalidKeyException, AsoException, ShortBufferException, InvalidAlgorithmParameterException {
            try {
                if (this.isDataIntegrityMode) {
                    return this.encryptionCipher.update(plainData, offset, length, resultBuffer);
                }
                int retVal = this.encryptionCipher.doFinal(plainData, offset, length, resultBuffer);
                this.encryptionCipher.init(1, this.secretKeySpec, this.ivParamSpec);
                return retVal;
            } catch (Exception e) {
                throw new AsoException(107, e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int decrypt(byte[] encryptedData, int offset, int length, byte[] resultBuffer) throws BadPaddingException, IllegalBlockSizeException, InvalidKeyException, AsoException, ShortBufferException, InvalidAlgorithmParameterException {
            try {
                if (this.isDataIntegrityMode) {
                    return this.decryptionCipher.update(encryptedData, offset, length, resultBuffer);
                }
                int retVal = this.decryptionCipher.doFinal(encryptedData, offset, length, resultBuffer);
                this.decryptionCipher.init(2, this.secretKeySpec, this.ivParamSpec);
                return retVal;
            } catch (Exception e) {
                throw new AsoException(107, e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public boolean canProcessBulk(int srcLength) {
            if (srcLength >= this.keySize) {
                return true;
            }
            return false;
        }

        @Override // oracle.net.aso.EncryptionEngine
        public String getProviderName() {
            if (this.encryptionCipher != null) {
                return this.encryptionCipher.getProvider().getName();
            }
            return null;
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int getKeySize() {
            return this.keySize;
        }
    }

    static String bytesToHex(byte[] bytes) {
        char[] hexArray = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = hexArray[v & 15];
        }
        return new String(hexChars);
    }
}
