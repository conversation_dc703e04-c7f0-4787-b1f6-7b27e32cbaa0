package oracle.net.aso;

import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/aso/EncryptionAlgorithm.class */
public interface EncryptionAlgorithm {
    public static final int NAE_40_KEY_SIZE_BITS = 40;
    public static final int NAE_56_KEY_SIZE_BITS = 56;
    public static final int NAE_128_KEY_SIZE_BITS = 128;
    public static final int NAE_256_KEY_SIZE_BITS = 256;
    public static final int NAE_CBC_0 = 1;
    public static final int NAE_CBC_8 = 2;
    public static final int NAE_RAW_0 = 3;
    public static final int NAE_RAW_8 = 4;
    public static final int ENC_NULL_ALG = 0;
    public static final int RC4_40 = 140;
    public static final int RC4_56 = 141;
    public static final int RC4_128 = 142;
    public static final int RC4_256 = 143;
    public static final int DES_40_RAW_0 = 210;
    public static final int DES_56_RAW_0 = 211;
    public static final int DES_40_RAW_8 = 212;
    public static final int DES_56_RAW_8 = 213;
    public static final int DES_40_CBC_0 = 220;
    public static final int DES_56_CBC_0 = 221;
    public static final int DES_40_CBC_8 = 222;
    public static final int DES_56_CBC_8 = 223;
    public static final int BYTE_OPER_AND = 1;
    public static final int BYTE_OPER_XOR = 2;
    public static final int BYTE_OPER_MOV = 3;
    public static final int STRONG_KEY_SIZE = 32;
    public static final int STRONG_KEY_DH_OFFSET = 0;
    public static final int STRONG_IV_SIZE = 32;
    public static final int STRONG_IV_DH_OFFSET = 32;

    void init(byte[] bArr, byte[] bArr2) throws AsoException;

    byte[] decrypt(byte[] bArr) throws AsoException;

    byte[] encrypt(byte[] bArr) throws AsoException;

    int maxDelta();

    void setSessionKey(byte[] bArr, byte[] bArr2) throws AsoException;

    String getProviderName();

    static EncryptionAlgorithm newInstance(String algorithmName, byte[] key, byte[] iv, boolean allowWeakCrypto) throws AsoException, NetException {
        EncryptionAlgorithm algortihmInstance;
        switch (algorithmName) {
            case "AES128":
                algortihmInstance = new AESEncryption(1, 1, 1, allowWeakCrypto);
                break;
            case "AES192":
                algortihmInstance = new AESEncryption(1, 2, 1, allowWeakCrypto);
                break;
            case "AES256":
                algortihmInstance = new AESEncryption(1, 3, 1, allowWeakCrypto);
                break;
            default:
                throw new NetException(NetException.ENCRYPTION_CLASS_NOT_INSTALLED);
        }
        algortihmInstance.init(key, iv);
        return algortihmInstance;
    }

    static void byteOperation(byte[] dest, byte[] oper1, byte[] oper2, int opertype, int length) {
        if (opertype == 1) {
            for (int i = 0; i < length; i++) {
                dest[i] = (byte) (oper1[i] & oper2[i]);
            }
            return;
        }
        if (opertype == 2) {
            for (int i2 = 0; i2 < length; i2++) {
                dest[i2] = (byte) (oper1[i2] ^ oper2[i2]);
            }
            return;
        }
        if (opertype == 3) {
            System.arraycopy(oper1, 0, dest, 0, length);
        }
    }

    default void renewKey() throws AsoException {
        throw new RuntimeException("Unsupported Operation");
    }
}
