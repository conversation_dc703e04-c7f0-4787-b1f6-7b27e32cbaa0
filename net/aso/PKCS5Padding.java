package oracle.net.aso;

import java.util.Arrays;

/* compiled from: PaddedCipher.java */
/* loaded from: ojdbc8.jar:oracle/net/aso/PKCS5Padding.class */
class PKCS5Padding extends PaddedCipher {
    private final byte[] tempBuffer;

    PKCS5Padding(int blockSize, EncryptionEngine engine, CipherBlockProcessor blockProcessor) {
        super(blockSize, engine, blockProcessor);
        this.tempBuffer = new byte[blockSize];
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] encrypt(byte[] buffer) throws AsoException {
        int length = buffer.length;
        byte padding = (byte) (length % this.blockSize == 0 ? this.blockSize : this.blockSize - (length % this.blockSize));
        int elength = length + padding;
        byte[] result = new byte[elength];
        if (this.coreEngine.canProcessBulk(elength)) {
            byte[] input = new byte[elength];
            System.arraycopy(buffer, 0, input, 0, length);
            Arrays.fill(input, length, elength, padding);
            this.coreEngine.encrypt(input, 0, elength, result);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= elength) {
                    break;
                }
                if (i2 <= length - this.blockSize) {
                    System.arraycopy(buffer, i2, this.tempBuffer, 0, this.blockSize);
                } else {
                    System.arraycopy(buffer, i2, this.tempBuffer, 0, buffer.length - i2);
                    Arrays.fill(this.tempBuffer, buffer.length - i2, this.blockSize, padding);
                }
                byte[] output = this.blockProcessor.encrypt(this.tempBuffer);
                System.arraycopy(output, 0, result, i2, this.blockSize);
                i = i2 + this.blockSize;
            }
        }
        return result;
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] decrypt(byte[] ebuffer) throws AsoException {
        int elen = ebuffer.length;
        byte[] temp = new byte[elen];
        if (this.coreEngine.canProcessBulk(elen)) {
            this.coreEngine.decrypt(ebuffer, 0, elen, temp);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= elen - 1) {
                    break;
                }
                System.arraycopy(ebuffer, i2, this.tempBuffer, 0, this.blockSize);
                byte[] output = this.blockProcessor.decrypt(this.tempBuffer);
                System.arraycopy(output, 0, temp, i2, this.blockSize);
                i = i2 + this.blockSize;
            }
        }
        byte[] result = Arrays.copyOfRange(temp, 0, elen - temp[elen - 1]);
        return result;
    }
}
