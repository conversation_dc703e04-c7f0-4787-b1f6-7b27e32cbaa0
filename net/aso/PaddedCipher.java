package oracle.net.aso;

/* loaded from: ojdbc8.jar:oracle/net/aso/PaddedCipher.class */
public abstract class PaddedCipher {
    public static final int NO_PADDING = 0;
    public static final int ORACLE_PADDING = 1;
    public static final int PKCS5_PADDING = 2;
    public static final int ZEROS_PADDING = 3;
    final int blockSize;
    final EncryptionEngine coreEngine;
    final CipherBlockProcessor blockProcessor;

    abstract byte[] encrypt(byte[] bArr) throws AsoException;

    abstract byte[] decrypt(byte[] bArr) throws AsoException;

    PaddedCipher(int blockSize, EncryptionEngine engine, CipherBlockProcessor blockProcessor) {
        this.blockSize = blockSize;
        this.coreEngine = engine;
        this.blockProcessor = blockProcessor;
    }

    static PaddedCipher newInstance(int type, EncryptionEngine engine, int blockSize, CipherBlockProcessor blockProcessor) {
        if (type == 1) {
            return new OraclePadding(blockSize, engine, blockProcessor);
        }
        if (type == 2) {
            return new PKCS5Padding(blockSize, engine, blockProcessor);
        }
        if (type == 3) {
            return new ZerosPadding(blockSize, engine, blockProcessor);
        }
        return new NoPadding(blockSize, engine, blockProcessor);
    }

    public final int getBlockSize() {
        return this.blockSize;
    }

    public final EncryptionEngine getEncryptionEngine() {
        return this.coreEngine;
    }

    public final CipherBlockProcessor getBlockProcessor() {
        return this.blockProcessor;
    }
}
