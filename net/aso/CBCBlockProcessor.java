package oracle.net.aso;

/* compiled from: CipherBlockProcessor.java */
/* loaded from: ojdbc8.jar:oracle/net/aso/CBCBlockProcessor.class */
class CBCBlockProcessor implements CipherBlockProcessor {
    private byte[] fbreg;
    private final EncryptionEngine engine;
    private final int blockSize;
    private final byte[] tempBuffer;

    CBCBlockProcessor(byte[] iv, EncryptionEngine engine, int blockSize) {
        this.fbreg = iv == null ? iv : (byte[]) iv.clone();
        this.engine = engine;
        this.blockSize = blockSize;
        this.tempBuffer = new byte[blockSize];
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public byte[] encrypt(byte[] plainData) throws AsoException {
        EncryptionAlgorithm.byteOperation(this.tempBuffer, this.fbreg, plainData, 2, this.blockSize);
        byte[] result = this.engine.encrypt(this.tempBuffer);
        EncryptionAlgorithm.byteOperation(this.fbreg, result, null, 3, this.blockSize);
        return result;
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public byte[] decrypt(byte[] encryptedData) throws AsoException {
        EncryptionAlgorithm.byteOperation(this.tempBuffer, encryptedData, null, 3, this.blockSize);
        byte[] result = this.engine.decrypt(encryptedData);
        EncryptionAlgorithm.byteOperation(result, this.fbreg, result, 2, this.blockSize);
        EncryptionAlgorithm.byteOperation(this.fbreg, this.tempBuffer, null, 3, this.blockSize);
        return result;
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public int encrypt(byte[] plainData, int offset, int length, byte[] resultBuffer) throws AsoException {
        throw new RuntimeException("Unsupported Operation");
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public int decrypt(byte[] encryptedData, int offset, int length, byte[] resultBuffer) throws AsoException {
        throw new RuntimeException("Unsupported Operation");
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public void resetIV(byte[] iv) {
        this.fbreg = iv == null ? iv : (byte[]) iv.clone();
    }
}
