package oracle.net.aso;

import java.util.Arrays;

/* compiled from: PaddedCipher.java */
/* loaded from: ojdbc8.jar:oracle/net/aso/OraclePadding.class */
class OraclePadding extends PaddedCipher {
    private final byte[] tempBuffer;

    OraclePadding(int blockSize, EncryptionEngine engine, CipherBlockProcessor blockProcessor) {
        super(blockSize, engine, blockProcessor);
        this.tempBuffer = new byte[blockSize];
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] encrypt(byte[] buffer) throws AsoException {
        int length = buffer.length;
        byte padding = (byte) (length % this.blockSize == 0 ? 0 : this.blockSize - (length % this.blockSize));
        int elength = length + padding;
        byte[] result = new byte[elength + 1];
        if (this.coreEngine.canProcessBulk(elength)) {
            byte[] input = new byte[elength];
            System.arraycopy(buffer, 0, input, 0, length);
            this.coreEngine.encrypt(input, 0, elength, result);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= length) {
                    break;
                }
                if (i2 <= length - this.blockSize) {
                    System.arraycopy(buffer, i2, this.tempBuffer, 0, this.blockSize);
                } else {
                    System.arraycopy(buffer, i2, this.tempBuffer, 0, buffer.length - i2);
                    Arrays.fill(this.tempBuffer, buffer.length - i2, this.blockSize, (byte) 0);
                }
                byte[] output = this.blockProcessor.encrypt(this.tempBuffer);
                System.arraycopy(output, 0, result, i2, this.blockSize);
                i = i2 + this.blockSize;
            }
        }
        result[elength] = (byte) (padding + 1);
        return result;
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] decrypt(byte[] ebuffer) throws AsoException {
        int encryptedDataLength = ebuffer.length;
        byte padding = ebuffer[encryptedDataLength - 1];
        if (padding < 0 || padding > this.blockSize) {
            throw new RuntimeException("Invalid padding value");
        }
        int actualDataLength = encryptedDataLength - padding;
        byte[] result = new byte[actualDataLength];
        if (this.coreEngine.canProcessBulk(encryptedDataLength - 1)) {
            byte[] temp = new byte[encryptedDataLength - 1];
            this.coreEngine.decrypt(ebuffer, 0, encryptedDataLength - 1, temp);
            System.arraycopy(temp, 0, result, 0, actualDataLength);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= encryptedDataLength - 1) {
                    break;
                }
                System.arraycopy(ebuffer, i2, this.tempBuffer, 0, this.blockSize);
                byte[] output = this.blockProcessor.decrypt(this.tempBuffer);
                System.arraycopy(output, 0, result, i2, Math.min(this.blockSize, actualDataLength - i2));
                i = i2 + this.blockSize;
            }
        }
        return result;
    }
}
