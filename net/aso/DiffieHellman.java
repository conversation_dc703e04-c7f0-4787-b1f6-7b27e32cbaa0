package oracle.net.aso;

import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.DSAParameterSpec;
import java.security.spec.InvalidKeySpecException;
import javax.crypto.KeyAgreement;
import javax.crypto.interfaces.DHPublicKey;
import javax.crypto.spec.DHParameterSpec;
import javax.crypto.spec.DHPublicKeySpec;

/* loaded from: ojdbc8.jar:oracle/net/aso/DiffieHellman.class */
public abstract class DiffieHellman {
    private static final int MIN_PK_SIZE_IN_BYTES = 256;

    public abstract byte[] getSessionKey(byte[] bArr, int i);

    public abstract byte[] getPublicKey(boolean z);

    public static final DiffieHellman newInstance(byte[] base, byte[] modulus, short ebits, short mbits, boolean isFipsMode) {
        return new DHJCEWrapper(base, modulus, ebits, mbits, isFipsMode);
    }

    /* loaded from: ojdbc8.jar:oracle/net/aso/DiffieHellman$DHJCEWrapper.class */
    private static class DHJCEWrapper extends DiffieHellman {
        private final boolean isFipsMode;
        private BigInteger baseValue;
        private BigInteger modulusValue;
        private short exponentSizeInBits;
        private short modulusSizeInBits;
        private KeyPair keyPair;

        DHJCEWrapper(byte[] base, byte[] modulus, short ebitsSize, short mbitsSize, boolean isFipsMode) {
            this.baseValue = null;
            this.modulusValue = null;
            this.isFipsMode = isFipsMode;
            this.baseValue = new BigInteger(1, base);
            this.modulusValue = new BigInteger(1, modulus);
            this.modulusSizeInBits = mbitsSize;
            this.exponentSizeInBits = ebitsSize;
        }

        @Override // oracle.net.aso.DiffieHellman
        public byte[] getPublicKey(boolean isWeakCryptoAllowed) throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
            try {
                KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("DiffieHellman");
                AlgorithmParameterSpec paramSpec = this.isFipsMode ? createDSAParameterSpec() : createDHParameterSpec();
                keyPairGenerator.initialize(paramSpec);
                this.keyPair = keyPairGenerator.generateKeyPair();
                byte[] publicKey = ((DHPublicKey) this.keyPair.getPublic()).getY().toByteArray();
                if (!isWeakCryptoAllowed && publicKey.length < 256) {
                    byte[] paddedPkey = new byte[256];
                    System.arraycopy(publicKey, 0, paddedPkey, 256 - publicKey.length, publicKey.length);
                    return paddedPkey;
                }
                return publicKey;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.DiffieHellman
        public byte[] getSessionKey(byte[] serPKey, int serPKeyLen) throws IllegalStateException, InvalidKeySpecException, NoSuchAlgorithmException, InvalidKeyException {
            try {
                DHPublicKeySpec publicKey = new DHPublicKeySpec(new BigInteger(1, serPKey), this.modulusValue, this.baseValue);
                KeyFactory keyFactory = KeyFactory.getInstance("DiffieHellman");
                PublicKey peersPublicKey = keyFactory.generatePublic(publicKey);
                KeyAgreement keyAgreement = KeyAgreement.getInstance("DiffieHellman");
                keyAgreement.init(this.keyPair.getPrivate());
                keyAgreement.doPhase(peersPublicKey, true);
                byte[] sessionKey = keyAgreement.generateSecret();
                if (sessionKey.length < serPKeyLen) {
                    byte[] paddedSessionKey = new byte[serPKeyLen];
                    System.arraycopy(sessionKey, 0, paddedSessionKey, serPKeyLen - sessionKey.length, sessionKey.length);
                    return paddedSessionKey;
                }
                return sessionKey;
            } catch (IllegalStateException | InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException e) {
                throw new RuntimeException(e);
            }
        }

        private AlgorithmParameterSpec createDHParameterSpec() {
            return new DHParameterSpec(this.modulusValue, this.baseValue, this.exponentSizeInBits);
        }

        private AlgorithmParameterSpec createDSAParameterSpec() {
            BigInteger subPrime = this.modulusValue.subtract(BigInteger.ONE).divide(BigInteger.valueOf(2L));
            return new DSAParameterSpec(this.modulusValue, subPrime, this.baseValue);
        }
    }
}
