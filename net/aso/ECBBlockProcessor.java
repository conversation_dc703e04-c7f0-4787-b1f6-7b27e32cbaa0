package oracle.net.aso;

/* compiled from: CipherBlockProcessor.java */
/* loaded from: ojdbc8.jar:oracle/net/aso/ECBBlockProcessor.class */
class ECBBlockProcessor implements CipherBlockProcessor {
    private final EncryptionEngine engine;

    ECBBlockProcessor(EncryptionEngine engine) {
        this.engine = engine;
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public byte[] encrypt(byte[] plainData) throws AsoException {
        return this.engine.encrypt(plainData);
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public byte[] decrypt(byte[] encryptedData) throws AsoException {
        return this.engine.decrypt(encryptedData);
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public int encrypt(byte[] plainData, int offset, int length, byte[] resultBuffer) throws AsoException {
        return this.engine.encrypt(plainData, offset, length, resultBuffer);
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public int decrypt(byte[] encryptedData, int offset, int length, byte[] resultBuffer) throws AsoException {
        return this.engine.decrypt(encryptedData, offset, length, resultBuffer);
    }

    @Override // oracle.net.aso.CipherBlockProcessor
    public void resetIV(byte[] iv) {
    }
}
