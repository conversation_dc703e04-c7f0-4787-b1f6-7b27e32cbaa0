package oracle.net.aso;

import java.security.DigestException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/net/aso/Radius.class */
public class Radius {
    public static final byte[] obfuscatePassword(byte[] paddedPwd) throws NoSuchAlgorithmException, DigestException {
        byte[] R1 = new byte[8];
        byte[] R2 = new byte[8];
        Random rdm = new Random();
        rdm.nextBytes(R1);
        rdm.nextBytes(R2);
        byte[] sharedKey = {121, 111, 114, -123, -82, -107, -109, 0};
        byte[] key = new byte[8];
        for (int i = 0; i < R1.length; i++) {
            key[i] = (byte) ((R1[i] ^ R2[i]) ^ sharedKey[i]);
        }
        DESEncryption desEngine = new DESEncryption();
        byte[] iv = new byte[8];
        try {
            desEngine.init(key, iv);
            byte[] encpwd = desEngine.encrypt(paddedPwd);
            int encpwdLength = encpwd.length - 1;
            byte[] ipwd = new byte[R1.length + R2.length + encpwdLength];
            System.arraycopy(R1, 0, ipwd, 0, R1.length);
            System.arraycopy(encpwd, 0, ipwd, R1.length, encpwdLength / 2);
            System.arraycopy(R2, 0, ipwd, R1.length + (encpwdLength / 2), R2.length);
            System.arraycopy(encpwd, encpwdLength / 2, ipwd, R1.length + (encpwdLength / 2) + R2.length, encpwdLength - (encpwdLength / 2));
            try {
                MessageDigest md5 = MessageDigest.getInstance(AnoServices.CHECKSUM_MD5);
                md5.update(ipwd, 0, ipwd.length);
                byte[] digest = new byte[md5.getDigestLength()];
                md5.digest(digest, 0, digest.length);
                byte[] opwd = new byte[ipwd.length + digest.length];
                System.arraycopy(ipwd, 0, opwd, 0, ipwd.length);
                System.arraycopy(digest, 0, opwd, ipwd.length, digest.length);
                return opwd;
            } catch (DigestException | NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e2) {
            throw new RuntimeException(e2);
        }
    }
}
