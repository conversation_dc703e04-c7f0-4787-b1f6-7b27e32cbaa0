package oracle.net.aso;

/* loaded from: ojdbc8.jar:oracle/net/aso/CipherBlockProcessor.class */
public interface CipherBlockProcessor {
    public static final int ECB = 0;
    public static final int CBC = 1;

    byte[] encrypt(byte[] bArr) throws AsoException;

    byte[] decrypt(byte[] bArr) throws AsoException;

    int encrypt(byte[] bArr, int i, int i2, byte[] bArr2) throws AsoException;

    int decrypt(byte[] bArr, int i, int i2, byte[] bArr2) throws AsoException;

    void resetIV(byte[] bArr);

    static CipherBlockProcessor newInstance(int type, byte[] iv, EncryptionEngine engine, int blockSize) {
        if (type == 1) {
            return new CBCBlockProcessor(iv, engine, blockSize);
        }
        return new ECBBlockProcessor(engine);
    }
}
