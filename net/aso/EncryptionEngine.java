package oracle.net.aso;

/* loaded from: ojdbc8.jar:oracle/net/aso/EncryptionEngine.class */
interface EncryptionEngine {
    void init(byte[] bArr, byte[] bArr2) throws AsoException;

    byte[] encrypt(byte[] bArr) throws AsoException;

    byte[] decrypt(byte[] bArr) throws AsoException;

    int encrypt(byte[] bArr, int i, int i2, byte[] bArr2) throws AsoException;

    int decrypt(byte[] bArr, int i, int i2, byte[] bArr2) throws AsoException;

    boolean canProcessBulk(int i);

    String getProviderName();

    int getKeySize();
}
