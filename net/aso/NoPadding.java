package oracle.net.aso;

/* compiled from: PaddedCipher.java */
/* loaded from: ojdbc8.jar:oracle/net/aso/NoPadding.class */
class NoPadding extends PaddedCipher {
    private final byte[] tempBuffer;

    NoPadding(int blockSize, EncryptionEngine engine, CipherBlockProcessor blockProcessor) {
        super(blockSize, engine, blockProcessor);
        this.tempBuffer = new byte[blockSize];
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] encrypt(byte[] buffer) throws AsoException {
        int length = buffer.length;
        byte[] result = new byte[length];
        if (this.coreEngine.canProcessBulk(length)) {
            this.coreEngine.encrypt(buffer, 0, length, result);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= length) {
                    break;
                }
                System.arraycopy(buffer, i2, this.tempBuffer, 0, this.blockSize);
                byte[] output = this.blockProcessor.encrypt(this.tempBuffer);
                System.arraycopy(output, 0, result, i2, this.blockSize);
                i = i2 + this.blockSize;
            }
        }
        return result;
    }

    @Override // oracle.net.aso.PaddedCipher
    public byte[] decrypt(byte[] ebuffer) throws AsoException {
        int elen = ebuffer.length;
        byte[] result = new byte[elen];
        if (this.coreEngine.canProcessBulk(elen)) {
            this.coreEngine.decrypt(ebuffer, 0, elen, result);
        } else {
            int i = 0;
            while (true) {
                int i2 = i;
                if (i2 >= elen) {
                    break;
                }
                System.arraycopy(ebuffer, i2, this.tempBuffer, 0, this.blockSize);
                byte[] output = this.blockProcessor.decrypt(this.tempBuffer);
                System.arraycopy(output, 0, result, i2, this.blockSize);
                i = i2 + this.blockSize;
            }
        }
        return result;
    }
}
