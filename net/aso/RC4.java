package oracle.net.aso;

import java.security.InvalidKeyException;
import javax.crypto.Cipher;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: ojdbc8.jar:oracle/net/aso/RC4.class */
class RC4 implements EncryptionAlgorithm {
    private static final byte NAEREN_RENEWAL_KEY_PAD = 123;
    private static final byte DATA_INTEGRITY_RENEWAL_KEY_PAD = -1;
    private static final int NAEREN_SERVER_TO_CLIENT_KEY_PAD = 170;
    private static final int NAEREN_CLIENT_TO_SERVER_KEY_PAD = 85;
    private static final int DATA_INTEGRITY_SERVER_TO_CLIENT_KEY_PAD = 180;
    private static final int DATA_INTEGRITY_CLIENT_TO_SERVER_KEY_PAD = 90;
    private static final byte[] fixed = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32};
    private RC4Algorithm rc4Session;
    private RC4Algorithm rc4Outgoing;
    private RC4Algorithm rc4Incoming;
    private boolean oraclePadding;
    private boolean dataIntegrityMode;
    private int rc4KeySize;
    private boolean allowWeakCrypto;
    private byte[] originalDHKey;

    /* loaded from: ojdbc8.jar:oracle/net/aso/RC4$RC4Algorithm.class */
    interface RC4Algorithm {
        void init(int i, byte[] bArr, int i2);

        void encrypt(byte[] bArr, byte[] bArr2, int i);

        String getProviderName();
    }

    RC4(int key_size) throws AsoException {
        this.oraclePadding = true;
        this.dataIntegrityMode = false;
        this.rc4KeySize = 40;
        this.allowWeakCrypto = true;
        this.originalDHKey = null;
        switch (key_size) {
            case 40:
            case 56:
            case 128:
            case 256:
                this.rc4KeySize = key_size;
                return;
            default:
                throw new AsoException(100);
        }
    }

    RC4(byte[] key, byte[] iv, boolean allowWeakCrypto) {
        this.oraclePadding = true;
        this.dataIntegrityMode = false;
        this.rc4KeySize = 40;
        this.allowWeakCrypto = true;
        this.originalDHKey = null;
        this.allowWeakCrypto = allowWeakCrypto;
        this.rc4KeySize = allowWeakCrypto ? 40 : 128;
        this.dataIntegrityMode = true;
        this.oraclePadding = false;
        try {
            init(key, iv);
        } catch (AsoException e) {
            throw new RuntimeException(e);
        }
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void init(byte[] key, byte[] iv) throws AsoException {
        this.rc4Session = new RC4JCEWrapper();
        this.rc4Outgoing = new RC4JCEWrapper();
        this.rc4Incoming = new RC4JCEWrapper();
        setSessionKey(key, iv);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] decrypt(byte[] ciphertext) {
        byte[] result;
        if (this.oraclePadding) {
            result = new byte[ciphertext.length - 1];
            this.rc4Incoming.encrypt(result, ciphertext, ciphertext.length - 1);
        } else {
            result = new byte[ciphertext.length];
            this.rc4Incoming.encrypt(result, ciphertext, ciphertext.length);
        }
        return result;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] encrypt(byte[] plaintext) {
        byte[] result;
        if (this.oraclePadding) {
            result = new byte[plaintext.length + 1];
            this.rc4Outgoing.encrypt(result, plaintext, plaintext.length);
            result[plaintext.length] = 0;
        } else {
            result = new byte[plaintext.length];
            this.rc4Outgoing.encrypt(result, plaintext, plaintext.length);
        }
        return result;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public String getProviderName() {
        if (this.rc4Outgoing != null) {
            return this.rc4Outgoing.getProviderName();
        }
        return null;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public int maxDelta() {
        return 1;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void setSessionKey(byte[] dhKey, byte[] iv) throws AsoException {
        byte[] workspace;
        if (dhKey == null && iv == null) {
            renewKey();
            return;
        }
        if (this.allowWeakCrypto) {
            if (this.originalDHKey == null) {
                this.originalDHKey = (byte[]) dhKey.clone();
            } else {
                dhKey = this.originalDHKey;
            }
        }
        int keyLength = this.rc4KeySize / 8;
        if (dhKey.length < keyLength) {
            throw new AsoException(102);
        }
        byte paddingByte = this.dataIntegrityMode ? (byte) -1 : (byte) 123;
        if (!this.dataIntegrityMode || this.allowWeakCrypto) {
            int ivLength = iv == null ? 0 : iv.length;
            workspace = new byte[keyLength + 1 + ivLength];
            System.arraycopy(dhKey, dhKey.length - keyLength, workspace, 0, keyLength);
            workspace[keyLength] = paddingByte;
            if (iv != null) {
                System.arraycopy(iv, 0, workspace, keyLength + 1, iv.length);
            }
        } else {
            workspace = new byte[keyLength + 32];
            System.arraycopy(dhKey, 0, workspace, 0, keyLength);
            workspace[keyLength - 1] = paddingByte;
            System.arraycopy(dhKey, 32, workspace, keyLength, 32);
        }
        this.rc4Session.init(1, workspace, workspace.length);
        renewKey();
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void renewKey() throws AsoException {
        int keyLength = this.rc4KeySize / 8;
        if (!this.dataIntegrityMode) {
            byte[] workspace = new byte[keyLength];
            this.rc4Session.encrypt(workspace, fixed, keyLength);
            if (this.oraclePadding) {
                int i = keyLength - 1;
                workspace[i] = (byte) (workspace[i] ^ 170);
            }
            this.rc4Incoming.init(2, workspace, keyLength);
            int i2 = keyLength - 1;
            workspace[i2] = (byte) (workspace[i2] ^ 170);
            this.rc4Outgoing.init(1, workspace, keyLength);
            return;
        }
        byte[] workspace2 = this.allowWeakCrypto ? new byte[keyLength + 1] : new byte[keyLength];
        this.rc4Session.encrypt(workspace2, fixed, keyLength);
        workspace2[workspace2.length - 1] = -76;
        this.rc4Incoming.init(2, workspace2, workspace2.length);
        workspace2[workspace2.length - 1] = 90;
        this.rc4Outgoing.init(1, workspace2, workspace2.length);
    }

    /* loaded from: ojdbc8.jar:oracle/net/aso/RC4$RC4JCEWrapper.class */
    private class RC4JCEWrapper implements RC4Algorithm {
        private Cipher rc4Cipher;

        private RC4JCEWrapper() {
        }

        @Override // oracle.net.aso.RC4.RC4Algorithm
        public void init(int mode, byte[] key_data, int key_size) throws InvalidKeyException {
            try {
                this.rc4Cipher = Cipher.getInstance("RC4");
                SecretKeySpec keySpec = new SecretKeySpec(key_data, "RC4");
                this.rc4Cipher.init(mode, keySpec);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.RC4.RC4Algorithm
        public void encrypt(byte[] output, byte[] input, int input_size) throws ShortBufferException {
            try {
                this.rc4Cipher.update(input, 0, input_size, output);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.RC4.RC4Algorithm
        public String getProviderName() {
            if (this.rc4Cipher != null) {
                return this.rc4Cipher.getProvider().getName();
            }
            return null;
        }
    }
}
