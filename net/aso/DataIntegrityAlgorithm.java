package oracle.net.aso;

import java.security.DigestException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/net/aso/DataIntegrityAlgorithm.class */
public final class DataIntegrityAlgorithm {
    private final EncryptionAlgorithm encryptionEngine;
    private byte[] feederBytes;
    private final String dataIntegrityAlgName;
    protected MessageDigestAlgorithm digestEngine;

    public DataIntegrityAlgorithm(byte[] key, byte[] iv, String algName, boolean allowWeakCrypto) throws AsoException {
        this.dataIntegrityAlgName = algName;
        this.digestEngine = new JCEDigestWrapper(algName);
        this.encryptionEngine = useAES() ? new AESDataIntegrity(key, iv, allowWeakCrypto) : new RC4(key, iv, allowWeakCrypto);
        this.feederBytes = new byte[size()];
    }

    public boolean compare(byte[] buffer, byte[] xsum) throws AsoException {
        byte[] output = this.encryptionEngine.decrypt(this.feederBytes);
        this.digestEngine.reset();
        this.digestEngine.update(buffer, 0, buffer.length);
        this.digestEngine.update(output, 0, output.length);
        byte[] tempBuff = new byte[size()];
        this.digestEngine.digest(tempBuff, 0);
        boolean areDifferent = false;
        int i = 0;
        while (true) {
            if (i >= size()) {
                break;
            }
            if (tempBuff[i] == xsum[i]) {
                i++;
            } else {
                areDifferent = true;
                break;
            }
        }
        return areDifferent;
    }

    public byte[] compute(byte[] source, int length) throws AsoException {
        if (source.length < length) {
            return null;
        }
        byte[] output = this.encryptionEngine.encrypt(this.feederBytes);
        this.digestEngine.reset();
        this.digestEngine.update(source, 0, length);
        this.digestEngine.update(output, 0, output.length);
        this.digestEngine.digest(output, 0);
        return output;
    }

    public int takeSessionKey(byte[] key, byte[] iv) {
        try {
            this.encryptionEngine.setSessionKey(key, iv);
            return 0;
        } catch (AsoException e) {
            throw new RuntimeException(e);
        }
    }

    public void renew() throws AsoException {
        this.encryptionEngine.renewKey();
    }

    public int size() {
        return this.digestEngine.getDigestLength();
    }

    public String getProviderName() {
        return this.digestEngine.getProviderName();
    }

    private boolean useAES() {
        return this.dataIntegrityAlgName == AnoServices.CHECKSUM_SHA256 || this.dataIntegrityAlgName == AnoServices.CHECKSUM_SHA384 || this.dataIntegrityAlgName == AnoServices.CHECKSUM_SHA512;
    }

    /* loaded from: ojdbc8.jar:oracle/net/aso/DataIntegrityAlgorithm$JCEDigestWrapper.class */
    class JCEDigestWrapper implements MessageDigestAlgorithm {
        private MessageDigest jceEngine = null;

        JCEDigestWrapper(String algorithmName) {
            try {
                initializeEngine(algorithmName);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        }

        private void initializeEngine(String algorithmName) throws NoSuchAlgorithmException {
            switch (algorithmName) {
                case "SHA1":
                    this.jceEngine = MessageDigest.getInstance("SHA-1");
                    return;
                case "SHA256":
                    this.jceEngine = MessageDigest.getInstance("SHA-256");
                    return;
                case "SHA384":
                    this.jceEngine = MessageDigest.getInstance("SHA-384");
                    return;
                case "SHA512":
                    this.jceEngine = MessageDigest.getInstance("SHA-512");
                    return;
                default:
                    throw new RuntimeException("Unsupported Algorithm : " + algorithmName);
            }
        }

        @Override // oracle.net.aso.MessageDigestAlgorithm
        public int getDigestLength() {
            return this.jceEngine.getDigestLength();
        }

        @Override // oracle.net.aso.MessageDigestAlgorithm
        public void reset() {
            this.jceEngine.reset();
        }

        @Override // oracle.net.aso.MessageDigestAlgorithm
        public void update(byte[] input, int off, int len) {
            this.jceEngine.update(input, off, len);
        }

        @Override // oracle.net.aso.MessageDigestAlgorithm
        public int digest(byte[] output, int offset) {
            try {
                return this.jceEngine.digest(output, offset, output.length);
            } catch (DigestException e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.MessageDigestAlgorithm
        public String getProviderName() {
            return this.jceEngine.getProvider().getName();
        }
    }
}
