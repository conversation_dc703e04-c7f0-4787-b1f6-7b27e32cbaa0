package oracle.net.aso;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.DESKeySpec;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/net/aso/DESEncryption.class */
public class DESEncryption implements EncryptionAlgorithm {
    static int DES_KEY_SIZE = 8;
    static final byte[] naedmfb = {1, 35, 69, 103, -119, -85, -51, -17};
    static final byte[] naedmm1 = {-2, -2, -2, -2, -2, -2, -2, -2};
    static final byte[] naedmk1 = {88, -46, 26, -119, 7, 0, -59, -68};
    static final byte[] naedmk2 = {103, 98, -82, -38, 116, -21, -92, -87};
    static final byte[] naedmm2 = {14, -2, 14, -2, 14, -2, 14, -2};
    public static final int DES_UNIT_SIZE = 8;
    protected int paddingMode;
    protected byte[] key;
    protected byte[] iv;
    private String algId;
    private PaddedCipher cipher;
    private boolean isCBCEnabled;
    private int blockMode;

    public DESEncryption(String algoID, int cipherBlockMode, int padding) {
        this.paddingMode = 1;
        this.algId = AnoServices.ENCRYPTION_DES40C;
        this.isCBCEnabled = true;
        this.blockMode = 1;
        this.algId = algoID;
        this.blockMode = cipherBlockMode;
        this.paddingMode = padding;
    }

    DESEncryption() {
        this.paddingMode = 1;
        this.algId = AnoServices.ENCRYPTION_DES40C;
        this.isCBCEnabled = true;
        this.blockMode = 1;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void init(byte[] key, byte[] iv) throws AsoException {
        this.isCBCEnabled = this.blockMode == 1;
        this.key = new byte[8];
        System.arraycopy(key, 0, this.key, 0, 8);
        initializeCiphers();
    }

    private void initializeCiphers() throws BadPaddingException, NoSuchPaddingException, InvalidKeySpecException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, AsoException, ShortBufferException {
        if (this.algId == AnoServices.ENCRYPTION_DES40C) {
            shrinkKey(this.key);
        }
        this.iv = (byte[]) naedmfb.clone();
        EncryptionEngine coreEngine = new DESJCEEngine();
        coreEngine.init(this.key, this.iv);
        CipherBlockProcessor cipherBlockProcessor = CipherBlockProcessor.newInstance(this.isCBCEnabled ? 1 : 0, this.iv, coreEngine, 8);
        this.cipher = PaddedCipher.newInstance(this.paddingMode, coreEngine, 8, cipherBlockProcessor);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] decrypt(byte[] ebuf) throws AsoException {
        return this.cipher.decrypt(ebuf);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public byte[] encrypt(byte[] buffer) throws AsoException {
        return this.cipher.encrypt(buffer);
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public int maxDelta() {
        return 8;
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public void setSessionKey(byte[] key, byte[] iv) throws BadPaddingException, NoSuchPaddingException, InvalidKeySpecException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, AsoException, ShortBufferException {
        if (key != null && iv != null) {
            init(key, iv);
        } else {
            initializeCiphers();
        }
    }

    @Override // oracle.net.aso.EncryptionAlgorithm
    public String getProviderName() {
        return this.cipher.getEncryptionEngine().getProviderName();
    }

    private static void shrinkKey(byte[] desKey) throws BadPaddingException, NoSuchPaddingException, InvalidKeySpecException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, ShortBufferException {
        byte[] i0 = new byte[8];
        byte[] di1 = new byte[8];
        byte[] di2 = new byte[8];
        int[] iArr = new int[32];
        EncryptionAlgorithm.byteOperation(i0, desKey, naedmm1, 1, DES_KEY_SIZE);
        applyDES(naedmk2, i0, di2);
        applyDES(naedmk1, i0, di1);
        EncryptionAlgorithm.byteOperation(i0, di2, di1, 2, DES_KEY_SIZE);
        EncryptionAlgorithm.byteOperation(desKey, i0, naedmm2, 1, DES_KEY_SIZE);
    }

    private static void applyDES(byte[] key, byte[] in, byte[] out) throws BadPaddingException, NoSuchPaddingException, InvalidKeySpecException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, ShortBufferException {
        try {
            Cipher cipher = Cipher.getInstance("DES/ECB/NoPadding");
            DESKeySpec dks = new DESKeySpec(key);
            SecretKeyFactory skf = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = skf.generateSecret(dks);
            cipher.init(1, secretKey);
            cipher.doFinal(in, 0, in.length, out, 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/aso/DESEncryption$DESJCEEngine.class */
    class DESJCEEngine implements EncryptionEngine {
        private byte[] desKey = null;
        private Cipher cipher = null;
        private int keySize;

        DESJCEEngine() {
        }

        @Override // oracle.net.aso.EncryptionEngine
        public void init(byte[] key, byte[] iv) throws AsoException {
            this.keySize = key.length;
            this.desKey = key;
            this.cipher = null;
        }

        private void initializeCipher(boolean isEncryptionMode) throws InvalidKeySpecException, NoSuchAlgorithmException, InvalidKeyException {
            try {
                DESKeySpec dks = new DESKeySpec(this.desKey);
                SecretKeyFactory skf = SecretKeyFactory.getInstance("DES");
                SecretKey secretKey = skf.generateSecret(dks);
                this.cipher = Cipher.getInstance("DES/ECB/NoPadding");
                if (isEncryptionMode) {
                    this.cipher.init(1, secretKey);
                } else {
                    this.cipher.init(2, secretKey);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public byte[] decrypt(byte[] ebuf) throws InvalidKeySpecException, NoSuchAlgorithmException, InvalidKeyException, AsoException {
            if (this.cipher == null) {
                initializeCipher(false);
            }
            try {
                return this.cipher.doFinal(ebuf);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public byte[] encrypt(byte[] buffer) throws InvalidKeySpecException, NoSuchAlgorithmException, InvalidKeyException, AsoException {
            if (this.cipher == null) {
                initializeCipher(true);
            }
            try {
                return this.cipher.doFinal(buffer);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int encrypt(byte[] plainData, int offset, int length, byte[] resultBuffer) throws AsoException {
            throw new RuntimeException("Unsupported Operation");
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int decrypt(byte[] encryptedData, int offset, int length, byte[] resultBuffer) throws AsoException {
            throw new RuntimeException("Unsupported Operation");
        }

        @Override // oracle.net.aso.EncryptionEngine
        public boolean canProcessBulk(int srcLength) {
            return false;
        }

        @Override // oracle.net.aso.EncryptionEngine
        public String getProviderName() {
            if (this.cipher != null) {
                return this.cipher.getProvider().getName();
            }
            return null;
        }

        @Override // oracle.net.aso.EncryptionEngine
        public int getKeySize() {
            return this.keySize;
        }
    }
}
