package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketAddress;
import oracle.jdbc.driver.DMSFactory;

/* compiled from: TcpNTAdapter.java */
/* loaded from: ojdbc8.jar:oracle/net/nt/MetricsEnabledSocket.class */
class MetricsEnabledSocket extends Socket {
    DMSFactory.DMSPhase waitEvent;
    MetricsEnabledInputStream meinput;

    private void createStreams() throws IOException {
        this.meinput = new MetricsEnabledInputStream(super.getInputStream(), this.waitEvent);
    }

    MetricsEnabledSocket(DMSFactory.DMSPhase pe) {
        this.waitEvent = pe;
    }

    MetricsEnabledSocket(DMSFactory.DMSPhase pe, String s, int i) throws IOException {
        super(s, i);
        this.waitEvent = pe;
        createStreams();
    }

    MetricsEnabledSocket(DMSFactory.DMSPhase pe, String host, int port, InetAddress localAddr, int localPort) throws IOException {
        super(host, port, localAddr, localPort);
        this.waitEvent = pe;
        createStreams();
    }

    MetricsEnabledSocket(DMSFactory.DMSPhase pe, InetAddress address, int port) throws IOException {
        super(address, port);
        this.waitEvent = pe;
        createStreams();
    }

    MetricsEnabledSocket(DMSFactory.DMSPhase pe, InetAddress address, int port, InetAddress localAddr, int localPort) throws IOException {
        super(address, port, localAddr, localPort);
        this.waitEvent = pe;
        createStreams();
    }

    @Override // java.net.Socket
    public void connect(SocketAddress endpoint, int timeout) throws IOException {
        super.connect(endpoint, timeout);
        createStreams();
    }

    @Override // java.net.Socket
    public InputStream getInputStream() {
        return this.meinput;
    }
}
