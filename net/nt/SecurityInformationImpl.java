package oracle.net.nt;

import oracle.jdbc.SecurityInformation;

/* loaded from: ojdbc8.jar:oracle/net/nt/SecurityInformationImpl.class */
public class SecurityInformationImpl implements SecurityInformation {
    private SecurityInformation.DNMatchStatus dnMatchStatus;
    private String serverDN;
    private String tlsCipherSuite;
    private String tlsVersion;
    private boolean isNativeEncryptionEnabled;
    private String encryptionAlgorithm;
    private String checksummingAlgorithm;
    private String encryptionLevel;
    private String checksumLevel;
    private boolean isStrongCryptoUsed;
    private SecurityInformation.AuthenticationAdaptorType authenticationAdapterType = SecurityInformation.AuthenticationAdaptorType.O5LOGON;
    private String sni;

    @Override // oracle.jdbc.SecurityInformation
    public SecurityInformation.DNMatchStatus getDNMatchStatus() {
        return this.dnMatchStatus;
    }

    public SecurityInformationImpl setDNMatchStatus(SecurityInformation.DNMatchStatus dnMatchStatus) {
        this.dnMatchStatus = dnMatchStatus;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getServerDN() {
        return this.serverDN;
    }

    public SecurityInformationImpl setServerDN(String serverDN) {
        this.serverDN = serverDN;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getTLSCipherSuite() {
        return this.tlsCipherSuite;
    }

    public SecurityInformationImpl setTLSCipherSuite(String tlsCipherSuite) {
        this.tlsCipherSuite = tlsCipherSuite;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getTLSVersion() {
        return this.tlsVersion;
    }

    public SecurityInformationImpl setTLSVersion(String tlsVersion) {
        this.tlsVersion = tlsVersion;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public boolean isNativeEncryptionEnabled() {
        return this.isNativeEncryptionEnabled;
    }

    public SecurityInformationImpl setNativeEncryptionEnabled(boolean nativeEncryptionEnabled) {
        this.isNativeEncryptionEnabled = nativeEncryptionEnabled;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getEncryptionAlgorithm() {
        return this.encryptionAlgorithm;
    }

    public SecurityInformationImpl setEncryptionAlgorithm(String encryptionAlgorithm) {
        this.encryptionAlgorithm = encryptionAlgorithm;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getChecksummingAlgorithm() {
        return this.checksummingAlgorithm;
    }

    public SecurityInformationImpl setChecksummingAlgorithm(String checksummingAlgorithm) {
        this.checksummingAlgorithm = checksummingAlgorithm;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getEncryptionLevel() {
        return this.encryptionLevel;
    }

    public SecurityInformationImpl setEncryptionLevel(String encryptionLevel) {
        this.encryptionLevel = encryptionLevel;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getChecksumLevel() {
        return this.checksumLevel;
    }

    public SecurityInformationImpl setChecksumLevel(String checksumLevel) {
        this.checksumLevel = checksumLevel;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public boolean isStrongCryptoUsed() {
        return this.isStrongCryptoUsed;
    }

    public SecurityInformationImpl setStrongCryptoUsed(boolean strongCryptoUsed) {
        this.isStrongCryptoUsed = strongCryptoUsed;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public SecurityInformation.AuthenticationAdaptorType getAuthenticationAdaptor() {
        return this.authenticationAdapterType;
    }

    public SecurityInformationImpl setAuthenticationAdaptor(SecurityInformation.AuthenticationAdaptorType authType) {
        this.authenticationAdapterType = authType;
        return this;
    }

    @Override // oracle.jdbc.SecurityInformation
    public String getSNI() {
        return this.sni;
    }

    public void setSNI(String sni) {
        this.sni = sni;
    }

    public String toString() {
        return "SecurityInformationImpl{dnMatchStatus=" + this.dnMatchStatus + ", serverDN='" + this.serverDN + "', isNativeEncryptionEnabled=" + this.isNativeEncryptionEnabled + ", encryptionAlgorithm='" + this.encryptionAlgorithm + "', checksummingAlgorithm='" + this.checksummingAlgorithm + "', encryptionLevel='" + this.encryptionLevel + "', checksumLevel='" + this.checksumLevel + "', isStrongCryptoUsed=" + this.isStrongCryptoUsed + ", AuthenticationAdapterType=" + this.authenticationAdapterType + ", tlsCipherSuite='" + this.tlsCipherSuite + "', tlsVersion=" + this.tlsVersion + ", sni='" + this.sni + "'}";
    }
}
