package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.SocketException;
import java.nio.channels.SocketChannel;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.logging.annotations.Blind;

/* loaded from: ojdbc8.jar:oracle/net/nt/NTAdapter.class */
public interface NTAdapter {

    /* loaded from: ojdbc8.jar:oracle/net/nt/NTAdapter$NetworkAdapterType.class */
    public enum NetworkAdapterType {
        TCP,
        TCPS,
        SDP,
        MSGQ,
        BEQ
    }

    NetworkAdapterType getNetworkAdapterType();

    void connect(DMSFactory.DMSNoun dMSNoun) throws IOException;

    void disconnect() throws IOException;

    SocketChannel getSocketChannel();

    InputStream getInputStream() throws IOException;

    OutputStream getOutputStream() throws IOException;

    void setOption(int i, Object obj) throws IOException;

    Object getOption(int i) throws IOException;

    void abort() throws IOException;

    void sendUrgentByte(int i) throws IOException;

    boolean isCharacteristicUrgentSupported() throws IOException;

    void setReadTimeoutIfRequired(Properties properties) throws IOException;

    boolean isConnectionSocketKeepAlive() throws SocketException;

    InetAddress getInetAddress();

    default NetStat getNetStat() {
        return null;
    }

    default CompletionStage<Void> connectAsync(DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        throw new UnsupportedOperationException("This NT Adapter does not support non-blocking connects");
    }

    default void registerForNonBlockingRead(Consumer<Throwable> onReady) throws IOException {
        throw new UnsupportedOperationException("This NT Adapter does not support non-blocking reads");
    }

    default void registerForNonBlockingWrite(Consumer<Throwable> onReady) throws IOException {
        throw new UnsupportedOperationException("This NT Adapter does not support non-blocking writes");
    }

    default void restoreBlockingMode() throws IOException {
    }

    default void forceCallback(Throwable onReadyError) throws IOException {
        throw new UnsupportedOperationException("This NT Adapter does not support non-blocking IO");
    }

    default boolean awaitWriteReadiness(long timeoutMillis) throws IOException {
        return true;
    }

    @Blind
    default Properties getSqlNetOptions() {
        throw new UnsupportedOperationException();
    }

    default void enqueueBlockedWrites(boolean isEnabled) throws IOException {
        throw new UnsupportedOperationException();
    }

    default boolean completeBlockedWrites() throws IOException {
        throw new UnsupportedOperationException();
    }

    default void enqueueAllWrites(boolean isEnabled) {
    }

    default boolean getEnqueueAllWrites() {
        return false;
    }

    default void completeWrites() throws IOException {
    }
}
