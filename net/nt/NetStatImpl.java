package oracle.net.nt;

import oracle.jdbc.internal.NetStat;

/* loaded from: ojdbc8.jar:oracle/net/nt/NetStatImpl.class */
class NetStatImpl implements NetStat {
    private long bytesSent;
    private long bytesReceived;
    private long lastNetworkAccessTimeInMillis;
    private long lastNetworkAccessTimeForReadInMillis;

    NetStatImpl() {
    }

    @Override // oracle.jdbc.internal.NetStat
    public long getTotalBytesSent() {
        return this.bytesSent;
    }

    @Override // oracle.jdbc.internal.NetStat
    public long getTotalBytesReceived() {
        return this.bytesReceived;
    }

    @Override // oracle.jdbc.internal.NetStat
    public void reset() {
        this.bytesReceived = 0L;
        this.bytesSent = 0L;
    }

    void incrementBytesSent(int bytesSent) {
        this.bytesSent += bytesSent;
        this.lastNetworkAccessTimeInMillis = Clock.currentTimeMillis();
    }

    void incrementBytesReceived(int bytesReceived) {
        this.bytesReceived += bytesReceived;
        this.lastNetworkAccessTimeInMillis = Clock.currentTimeMillis();
        this.lastNetworkAccessTimeForReadInMillis = this.lastNetworkAccessTimeInMillis;
    }

    @Override // oracle.jdbc.internal.NetStat
    public long getLastNetworkAccessTime() {
        return this.lastNetworkAccessTimeInMillis;
    }

    @Override // oracle.jdbc.internal.NetStat
    public long getLastNetworkAccessTimeForRead() {
        return this.lastNetworkAccessTimeForReadInMillis;
    }
}
