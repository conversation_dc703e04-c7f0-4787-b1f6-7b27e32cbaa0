package oracle.net.nt;

import com.oracle.common.base.Disposable;
import com.oracle.common.internal.Platform;
import com.oracle.common.internal.net.ipclw.mql.Context;
import com.oracle.common.internal.net.ipclw.mql.KeyRegistry;
import com.oracle.common.internal.net.ipclw.mql.KeyedBufferSequence;
import com.oracle.common.internal.net.ipclw.mql.KeyedSingleBufferSequence;
import com.oracle.common.internal.net.ipclw.mql.LocalQueue;
import com.oracle.common.internal.net.ipclw.mql.MessageQueue;
import com.oracle.common.internal.net.ipclw.mql.MultiInterfaceKeyRegistry;
import com.oracle.common.internal.net.ipclw.mql.RegistrationKey;
import com.oracle.common.internal.net.ipclw.mql.RemoteQueue;
import com.oracle.common.io.BufferManager;
import com.oracle.common.io.BufferManagers;
import com.oracle.common.io.BufferSequence;
import com.oracle.common.net.InetAddresses;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.channels.ClosedByInterruptException;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Properties;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;

/* loaded from: ojdbc8.jar:oracle/net/nt/MQLNTAdapter.class */
public class MQLNTAdapter extends AbstractAdapter implements LocalQueue.ReadCallback {
    private SocketChannel socketChannel;
    private Selector selector;
    private SelectionKey selectionKey;
    private long connectTimeout;
    protected Socket socket;
    protected int readTimeout;
    protected Properties socketOptions;
    private InetSocketAddress inetSocketAddress;
    InetAddress localInetAddress;
    private Context mqContext;
    private LocalQueue localQueue;
    private RemoteQueue remoteQueue;
    Context.Dependencies.Transport transport;
    int busyWait;
    int kernelWait;
    NTMQProtocolHandler ntmqProtocolHandler;
    private int headerSizeSend;
    private MQLFlowControl flowControl;
    private static final boolean FLOW_CONTROL_ENABLED = true;
    private KeyRegistry keyRegistry;
    private TimeoutInterruptHandler.InterruptTask interruptTask;
    private int pendingSends;
    private boolean connectResponsePending;
    private static final byte MQL_RC_TRANS = 1;
    private static final int MQL_DEFAULT_BUFFER_SPACE = 8;
    private static final int MQL_MAX_MSGSIZE = 65536;
    private static final int IMD_MSG_BUFFER_SPACE = 2;
    private static final int IMD_MAX_MSGSIZE = 65536;
    private static final int MAX_PENDING = 4;
    private static final int USR_WAIT_WORK = 10000;
    private static final int USR_WAIT_SEND = 10000;
    private static final int RDMA_CONNECT_WAIT = 2000;
    private static final int RCV_BUF_COUNT = 1;
    private static final int SEND_BUF_COUNT = 2;
    private static final int HDR_OFFSET_SEND = 0;
    private static final int NS_OFFSET_SEND = 1;
    private static final String FMW_COMMONS_IP_PROP = "com.oracle.common.internal.net.ipclw.mql.Context.defaultAddress";
    private static final String MSGQ_ERR_STATE_MSG = "Message Queue is in an error state.";
    private static final String CLASS_NAME = MQLNTAdapter.class.getName();
    private static BufferManager bufferManager = null;
    private static final Monitor BUFFER_MANAGER_INIT_MONITOR = Monitor.newInstance();
    protected static final char[] hexArray = "0123456789ABCDEF".toCharArray();
    private final AtomicInteger numberOfMessagesReceived = new AtomicInteger(0);
    private final ByteBuffer wakeupBuffer = ByteBuffer.allocateDirect(1);
    IOException ioExceptionWhileMSGQOp = null;
    Queue<BufferSequence> onMessageBufferList = new LinkedList();
    BufferSequence dequedRcvBuf = null;
    private int sdu = 65518;
    private int tdu = 65536;
    private boolean drainBuffers = false;
    private boolean flowControlEnabled = false;
    private int kernelWaitSend = Integer.MAX_VALUE;
    private int kernelWaitWork = Integer.MAX_VALUE;
    private byte[] sessionId = null;
    private boolean isConnected = false;
    private BufferSequence sendOnInterrupt = null;
    private int postCount = 0;
    ByteBuffer bufferForDeathDetection = ByteBuffer.allocate(1);

    @Override // oracle.net.nt.AbstractAdapter
    public /* bridge */ /* synthetic */ String toString() {
        return super.toString();
    }

    static /* synthetic */ int access$010(MQLNTAdapter x0) {
        int i = x0.pendingSends;
        x0.pendingSends = i - 1;
        return i;
    }

    public static BufferManager getBufferManager() {
        return bufferManager;
    }

    public MQLNTAdapter(String address, ConnOption connOption, @Blind(PropertiesBlinder.class) Properties socketOptions) throws NLException {
        this.transport = null;
        this.socketOptions = socketOptions;
        this.inetSocketAddress = connOption.inetSocketAddress;
        this.host = connOption.host;
        this.port = connOption.port;
        this.ntmqProtocolHandler = new NTMQProtocolHandler((byte) 1, false, false);
        String msgqTransport = (String) socketOptions.get(22);
        if (msgqTransport != null) {
            this.transport = Context.Dependencies.Transport.valueOf(msgqTransport);
        }
        this.busyWait = Integer.parseInt((String) socketOptions.get(23));
        this.kernelWait = Integer.parseInt((String) socketOptions.get(24));
        CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.CONFIG, CLASS_NAME, "MQLNTAdapter", "host={0}, port={1}, transport={2}, socketOptions={3}. ", null, null, this.host, Integer.valueOf(this.port), this.transport, socketOptions);
    }

    private void handleConnectPacket() throws IOException {
        BufferSequence interruptMsg;
        if (!this.connectResponsePending) {
            throw new NetException(NetException.NT_NTMQ_INVALID_PACKET, "Received unexpected packet type: 1");
        }
        this.connectResponsePending = false;
        if (this.ntmqProtocolHandler.isSIDRequiredForRqMsg()) {
            this.sessionId = this.ntmqProtocolHandler.getSID();
            this.headerSizeSend = 18;
        } else {
            this.sessionId = null;
            this.headerSizeSend = 2;
        }
        ByteBuffer nameBuffer = this.ntmqProtocolHandler.getRemoteQueueNameBuffer();
        MessageQueue.Name remoteQueueName = new MessageQueue.Name(nameBuffer);
        initRemoteQueue();
        this.remoteQueue.connect(remoteQueueName);
        this.flowControlEnabled = this.ntmqProtocolHandler.isFlowControlEnabled();
        if (this.flowControlEnabled) {
            if (this.ntmqProtocolHandler.isSIDRequiredForRdmaMsg()) {
                interruptMsg = createMqlMessage((byte) 6, (byte) 4, this.ntmqProtocolHandler.getSID(), true);
            } else {
                interruptMsg = createMqlMessage((byte) 6, (byte) 0, null, true);
            }
            short initialBufferCount = this.ntmqProtocolHandler.getFcPostCount();
            this.flowControl.onFlowControlEnabled(initialBufferCount, this.ntmqProtocolHandler.getFcKey(), this.ntmqProtocolHandler.getFcAddr(), this.remoteQueue, interruptMsg);
            this.flowControl.onMessageReceived();
            this.flowControl.onBufferPosted(this.postCount);
            BufferSequence msg = createMqlMessage((byte) 6, (byte) 0);
            writeToRemoteQueue(msg);
            while (this.flowControl.getAvailableBufferCount() != initialBufferCount) {
                await(this.localQueue.getContext(), 0, 2000);
            }
        }
    }

    private void writeLocalQueueNameOnSocket(LocalQueue localQueue) throws IOException {
        ByteBuffer headerPacket = bufferManager.acquire(this.ntmqProtocolHandler.getHeaderPacketSize());
        this.ntmqProtocolHandler.prepareHeaderPacket(headerPacket, (byte) 1, (byte) 1, this.sessionId, false);
        int connectPacketSize = 28 + this.mqContext.getNameSize() + 12 + this.mqContext.getKeySize();
        ByteBuffer connectPacket = bufferManager.acquire(connectPacketSize);
        this.ntmqProtocolHandler.prepareConnectPacket(connectPacket, null, this.sdu, ByteOrder.LITTLE_ENDIAN, localQueue);
        prepareFlowControlPacket(connectPacket);
        ByteBuffer ntmqPacket = bufferManager.acquire(headerPacket.limit() + connectPacket.limit());
        ntmqPacket.order(ByteOrder.LITTLE_ENDIAN);
        ntmqPacket.put(headerPacket);
        ntmqPacket.put(connectPacket);
        ntmqPacket.flip();
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "writeLocalQueueNameOnSocket", "ntmqPacket.hasRemaining()={0}. ", (String) null, (String) null, Boolean.valueOf(ntmqPacket.hasRemaining()));
        try {
            try {
                this.selectionKey.interestOps(4);
                while (ntmqPacket.hasRemaining()) {
                    if (this.selector.select(this.readTimeout) == 0) {
                        throw new NetException(NetException.NT_MSGQ_TIMEOUT_WHILE_EXCHANGING_QUEUE_NAME);
                    }
                    Set<SelectionKey> selectedKeys = this.selector.selectedKeys();
                    Iterator<SelectionKey> keyIterator = selectedKeys.iterator();
                    while (keyIterator.hasNext()) {
                        SelectionKey key = keyIterator.next();
                        if (key.isWritable()) {
                            int writtenBytes = this.socketChannel.write(ntmqPacket);
                            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "writeLocalQueueNameOnSocket", "writtenBytes={0}. ", (String) null, (String) null, Integer.valueOf(writtenBytes));
                        }
                        keyIterator.remove();
                    }
                }
                bufferManager.release(ntmqPacket);
                bufferManager.release(connectPacket);
                bufferManager.release(headerPacket);
            } catch (ClosedByInterruptException e) {
                handleInterrupt();
                bufferManager.release(ntmqPacket);
                bufferManager.release(connectPacket);
                bufferManager.release(headerPacket);
            }
            this.connectResponsePending = true;
        } catch (Throwable th) {
            bufferManager.release(ntmqPacket);
            bufferManager.release(connectPacket);
            bufferManager.release(headerPacket);
            throw th;
        }
    }

    public ByteBuffer readFromLocalQueue() throws IOException {
        return readFromLocalQueue(true);
    }

    public ByteBuffer readFromLocalQueue(boolean blocking) throws IOException {
        ByteBuffer nsBuffer;
        ensureConnection(false);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "readFromLocalQueue", "reading from local queue. blocking={0}. ", (String) null, (String) null, Boolean.valueOf(blocking));
        do {
            BufferSequence nextMsg = getNextMessage(blocking);
            if (this.dequedRcvBuf != null) {
                this.dequedRcvBuf.dispose();
            }
            this.dequedRcvBuf = nextMsg;
            if (nextMsg == null) {
                return null;
            }
            nsBuffer = nextMsg.getBuffer(0);
            processNTMQLayer(nsBuffer);
        } while (!this.ntmqProtocolHandler.isDataPacket());
        int dataLen = nsBuffer.remaining();
        nsBuffer.limit(nsBuffer.position() + this.sdu);
        ByteBuffer nsBuffer2 = nsBuffer.slice();
        nsBuffer2.limit(dataLen);
        return nsBuffer2;
    }

    private BufferSequence getNextMessage(boolean blocking) throws IOException {
        while (this.onMessageBufferList.size() <= 0) {
            readNTMQPacketFromLocalQueue(blocking);
            if (!blocking) {
                break;
            }
        }
        return this.onMessageBufferList.poll();
    }

    /* JADX WARN: Code restructure failed: missing block: B:34:0x00ec, code lost:
    
        r10.numberOfMessagesReceived.set(0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x00f8, code lost:
    
        if (r10.flowControlEnabled == false) goto L48;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x00fb, code lost:
    
        r10.flowControl.sendCounterUpdate();
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0102, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:?, code lost:
    
        return;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void readNTMQPacketFromLocalQueue(boolean r11) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 259
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.nt.MQLNTAdapter.readNTMQPacketFromLocalQueue(boolean):void");
    }

    private void processNTMQLayer(ByteBuffer ntmqPacket) throws IOException {
        this.ntmqProtocolHandler.processNTMQPacket(ntmqPacket);
        if (this.ntmqProtocolHandler.isDisconnectPacket()) {
            writeLocalQueueNameOnSocket(this.localQueue);
        } else if (this.ntmqProtocolHandler.isConnectPacket()) {
            handleConnectPacket();
        } else if (this.ntmqProtocolHandler.isDataIRPacket()) {
            this.flowControl.onIRMessage(this.ntmqProtocolHandler.getPacketFlag());
        }
    }

    public int writeToRemoteQueue(ByteBuffer sendBuffer, boolean releaseBuffer) throws IOException {
        ensureConnection(true);
        int pos = sendBuffer.limit();
        KeyedBufferSequence bufseqMsgToWrite = initSendBuffer((byte) 4, (byte) 0, sendBuffer, releaseBuffer);
        if (CommonDiagnosable.getInstance().isLoggable(Level.FINEST)) {
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "writeToRemoteQueue", "bufseqMsgToWrite initialized", "bufseqMsgToWrite initialized, HeaderBuffer:\n{0} \nPacketBuffer:\n{1}", null, dump(bufseqMsgToWrite.getBuffer(0)), dump(sendBuffer));
        }
        writeToRemoteQueue(bufseqMsgToWrite);
        return pos;
    }

    private void writeToRemoteQueue(BufferSequence bufseqMsgToWrite) throws IOException {
        writeToRemoteQueue(bufseqMsgToWrite, true);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean writeToRemoteQueue(BufferSequence bufseqMsgToWrite, boolean canAwait) throws IOException {
        if (this.flowControlEnabled) {
            if (!ensureAvailableReceiveSpace(bufseqMsgToWrite, canAwait)) {
                return canAwait;
            }
            this.flowControl.onMessageSent();
        }
        if (this.pendingSends >= 4) {
            if (canAwait) {
                while (this.pendingSends >= 4) {
                    await(this.remoteQueue.getContext(), 10000, this.kernelWaitWork);
                }
            } else {
                return false;
            }
        }
        try {
            scheduleInterrupt(this.readTimeout);
            this.pendingSends++;
            if (canAwait) {
                while (!this.remoteQueue.send(bufseqMsgToWrite, bufseqMsgToWrite, 1)) {
                    try {
                        await(this.remoteQueue.getContext(), 10000, this.kernelWaitSend);
                    } catch (IOException ioex) {
                        this.pendingSends--;
                        throw ioex;
                    }
                }
            } else if (!this.remoteQueue.send(bufseqMsgToWrite, bufseqMsgToWrite, 1)) {
                this.pendingSends--;
                cancelTimeout();
                return false;
            }
            return true;
        } finally {
            cancelTimeout();
        }
    }

    private boolean ensureAvailableReceiveSpace(BufferSequence nextMsgToSend, boolean canAwait) throws IOException {
        int availableBufferCount = this.flowControl.getAvailableBufferCount();
        if (availableBufferCount == 0) {
            if (this.sendOnInterrupt == null && canAwait) {
                this.sendOnInterrupt = nextMsgToSend;
                while (availableBufferCount == 0) {
                    try {
                        await(this.localQueue.getContext(), 0, this.kernelWait);
                        if (this.sendOnInterrupt == null) {
                            return false;
                        }
                        availableBufferCount = this.flowControl.getAvailableBufferCount();
                    } finally {
                        this.sendOnInterrupt = null;
                    }
                }
                this.sendOnInterrupt = null;
            } else {
                return false;
            }
        }
        if (availableBufferCount == 1) {
            ByteBuffer nextMsgHeader = nextMsgToSend.getBuffer(0);
            if (nextMsgHeader.get(0) == 4) {
                this.wakeupBuffer.put(0, (byte) 0);
                if (!this.flowControl.sendInterruptRequest(canAwait)) {
                    return false;
                }
                if (this.ioExceptionWhileMSGQOp != null) {
                    throw this.ioExceptionWhileMSGQOp;
                }
                this.ntmqProtocolHandler.prepareHeaderPacket(nextMsgHeader, (byte) 7, this.flowControl.getInterruptRequestCount(), this.sessionId, false);
                return true;
            }
            if (!canAwait) {
                return false;
            }
            while (availableBufferCount <= 1) {
                await(this.remoteQueue.getContext(), 0, this.kernelWait);
                availableBufferCount = this.flowControl.getAvailableBufferCount();
            }
            return true;
        }
        return true;
    }

    private void await(Context context, int usrWaitMicro, int sysWaitMilli) throws IOException {
        if (this.ioExceptionWhileMSGQOp != null) {
            throw ((IOException) new IOException(MSGQ_ERR_STATE_MSG).initCause(this.ioExceptionWhileMSGQOp));
        }
        this.wakeupBuffer.put(0, (byte) 0);
        context.await(usrWaitMicro, sysWaitMilli);
        if (this.ioExceptionWhileMSGQOp != null) {
            throw this.ioExceptionWhileMSGQOp;
        }
        if (Thread.currentThread().isInterrupted()) {
            handleInterrupt();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x007e  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private com.oracle.common.internal.net.ipclw.mql.KeyedBufferSequence initSendBuffer(byte r10, byte r11, java.nio.ByteBuffer r12, boolean r13) throws java.io.IOException {
        /*
            r9 = this;
            r0 = 2
            java.nio.ByteBuffer[] r0 = new java.nio.ByteBuffer[r0]
            r14 = r0
            r0 = 2
            com.oracle.common.internal.net.ipclw.mql.RegistrationKey[] r0 = new com.oracle.common.internal.net.ipclw.mql.RegistrationKey[r0]
            r15 = r0
            r0 = 2
            java.nio.ByteBuffer[] r0 = new java.nio.ByteBuffer[r0]
            r16 = r0
            com.oracle.common.io.BufferManager r0 = oracle.net.nt.MQLNTAdapter.bufferManager
            r1 = r9
            int r1 = r1.headerSizeSend
            java.nio.ByteBuffer r0 = r0.acquire(r1)
            r17 = r0
            r0 = r9
            com.oracle.common.internal.net.ipclw.mql.KeyRegistry r0 = r0.keyRegistry
            r1 = r17
            com.oracle.common.internal.net.ipclw.mql.RegistrationKey r0 = r0.getKey(r1)
            r18 = r0
            r0 = r9
            oracle.net.nt.NTMQProtocolHandler r0 = r0.ntmqProtocolHandler
            r1 = r17
            r2 = r10
            r3 = r11
            r4 = r9
            byte[] r4 = r4.sessionId
            r5 = 0
            int r0 = r0.prepareHeaderPacket(r1, r2, r3, r4, r5)
            r0 = r14
            r1 = 0
            r2 = r17
            r0[r1] = r2
            r0 = r15
            r1 = 0
            r2 = r18
            r0[r1] = r2
            r0 = r16
            r1 = 0
            r2 = r17
            r0[r1] = r2
            r0 = r12
            boolean r0 = r0.isDirect()
            if (r0 == 0) goto L7e
            r0 = r15
            r1 = 1
            r2 = r9
            com.oracle.common.internal.net.ipclw.mql.KeyRegistry r2 = r2.keyRegistry
            r3 = r12
            com.oracle.common.internal.net.ipclw.mql.RegistrationKey r2 = r2.getKey(r3)
            r3 = r2; r2 = r1; r1 = r0; r0 = r3; 
            r1[r2] = r3
            if (r0 == 0) goto L7e
            r0 = r14
            r1 = 1
            r2 = r12
            r0[r1] = r2
            r0 = r13
            if (r0 == 0) goto Lb4
            r0 = r16
            r1 = 1
            r2 = r14
            r3 = 1
            r2 = r2[r3]
            r0[r1] = r2
            goto Lb4
        L7e:
            com.oracle.common.io.BufferManager r0 = oracle.net.nt.MQLNTAdapter.bufferManager
            r1 = r12
            int r1 = r1.remaining()
            java.nio.ByteBuffer r0 = r0.acquire(r1)
            r19 = r0
            r0 = r19
            r1 = r12
            java.nio.ByteBuffer r0 = r0.put(r1)
            r0 = r19
            java.nio.Buffer r0 = r0.flip()
            r0 = r14
            r1 = 1
            r2 = r19
            r0[r1] = r2
            r0 = r15
            r1 = 1
            r2 = r9
            com.oracle.common.internal.net.ipclw.mql.KeyRegistry r2 = r2.keyRegistry
            r3 = r19
            com.oracle.common.internal.net.ipclw.mql.RegistrationKey r2 = r2.getKey(r3)
            r0[r1] = r2
            r0 = r16
            r1 = 1
            r2 = r19
            r0[r1] = r2
        Lb4:
            oracle.net.nt.MQLNTAdapter$1 r0 = new oracle.net.nt.MQLNTAdapter$1
            r1 = r0
            r2 = r9
            r3 = 0
            r4 = r14
            r5 = 0
            r6 = r15
            r7 = r16
            r1.<init>(r3, r4, r5, r6)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.nt.MQLNTAdapter.initSendBuffer(byte, byte, java.nio.ByteBuffer, boolean):com.oracle.common.internal.net.ipclw.mql.KeyedBufferSequence");
    }

    private void initLocalQueue(Context.DefaultDependencies dependencies) throws IOException {
        this.mqContext.setWakeupBuffer(this.wakeupBuffer);
        LocalQueue.DefaultDependencies localQueueDep = new LocalQueue.DefaultDependencies(dependencies, (LocalQueue.Dependencies) null).setMaximumReceiveMessageCount(8).setMaximumMessageSizeBytes(65536).setMaximumMessageBufferCount(1);
        localQueueDep.setInitialReceiveMessageCount(0);
        this.localQueue = this.mqContext.openLocalQueue(localQueueDep);
        this.localQueue.setReadCallback(this);
        this.localQueue.getContext().setRdmaImmediateCallback(new LocalQueue.ReadCallback() { // from class: oracle.net.nt.MQLNTAdapter.2
            public void onMessage(BufferSequence immediateMsg, IOException ioex) throws IOException {
                immediateMsg.dispose();
                if (ioex == null) {
                    if (MQLNTAdapter.this.sendOnInterrupt == null || !MQLNTAdapter.this.writeToRemoteQueue(MQLNTAdapter.this.sendOnInterrupt, false)) {
                        MQLNTAdapter.this.wakeupBuffer.put(0, (byte) 1);
                        return;
                    } else {
                        MQLNTAdapter.this.sendOnInterrupt = null;
                        return;
                    }
                }
                if (MQLNTAdapter.this.ioExceptionWhileMSGQOp != null) {
                    ioex.initCause(MQLNTAdapter.this.ioExceptionWhileMSGQOp);
                }
                MQLNTAdapter.this.ioExceptionWhileMSGQOp = ioex;
                MQLNTAdapter.this.wakeupBuffer.put(0, (byte) 1);
            }
        });
        this.localQueue.bind();
        replenish();
    }

    private BufferSequence initReceiveBuffer() throws IOException {
        ByteBuffer buffer = bufferManager.acquire(this.tdu);
        RegistrationKey key = this.keyRegistry.getKey(buffer);
        return new KeyedSingleBufferSequence(bufferManager, buffer, (Context) null, key);
    }

    private BufferSequence createMqlMessage(byte type, byte flags) throws IOException {
        return createMqlMessage(type, flags, null, false);
    }

    private BufferSequence createMqlMessage(byte type, byte flags, byte[] sid, final boolean immediateMsg) throws IOException {
        int headerSize;
        if (immediateMsg && this.ntmqProtocolHandler.isSIDRequiredForRdmaMsg()) {
            headerSize = 18;
        } else {
            headerSize = this.headerSizeSend;
        }
        ByteBuffer mqlHeader = bufferManager.acquire(headerSize);
        this.ntmqProtocolHandler.prepareHeaderPacket(mqlHeader, type, flags, sid, immediateMsg);
        RegistrationKey key = this.keyRegistry.getKey(mqlHeader);
        return new KeyedSingleBufferSequence(bufferManager, mqlHeader, null, key) { // from class: oracle.net.nt.MQLNTAdapter.3
            boolean isImmediateMsg;

            {
                this.isImmediateMsg = immediateMsg;
            }

            public void dispose() {
                if (!this.isImmediateMsg) {
                    MQLNTAdapter.access$010(MQLNTAdapter.this);
                }
                super.dispose();
            }
        };
    }

    @Override // oracle.net.nt.NTAdapter
    public void connect(DMSFactory.DMSNoun dmsParent) throws IOException {
        try {
            connectSocket();
            this.isConnected = true;
        } catch (ClosedByInterruptException e) {
            handleInterrupt();
        }
        setSocketOptions();
        String localIpStr = (String) this.socketOptions.get(21);
        if (localIpStr != null) {
            this.localInetAddress = InetAddress.getByName(localIpStr);
        } else {
            this.localInetAddress = InetAddresses.getLocalHost();
        }
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.CONFIG, CLASS_NAME, "connect", "using localInetAddress={0}. ", (String) null, (String) null, this.localInetAddress);
        initBufferManager(this.localInetAddress);
        this.keyRegistry = bufferManager.getRegistry(this.localInetAddress);
        Context.DefaultDependencies dependencies = new Context.DefaultDependencies().setInetAddress(this.localInetAddress).setBufferManager(bufferManager).setMaximumOutstandingMessageCount(4).setMaximumMessageSizeBytes(65536).setParentContext(this.keyRegistry.getContext()).setTransport(Context.Dependencies.Transport.RC).setFlags(5).setMaximumImmediateReceiveMessageCount(2).setMaximumImmediateReceiveMessageSizeBytes(65536);
        if (this.transport != null) {
            dependencies.setTransport(this.transport);
        }
        this.mqContext = new Context(dependencies);
        this.mqContext.open();
        initLocalQueue(dependencies);
        connectToRemoteQueue();
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "connect", "remote queue connected. ", null, null);
    }

    private void connectSocket() throws IOException {
        Boolean useNio = Boolean.valueOf(Boolean.parseBoolean((String) this.socketOptions.get(20)));
        if (!useNio.booleanValue()) {
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "connectSocket", "NIO is required. ", null, null);
            throw new NetException(NetException.NT_CONNECTION_FAILED);
        }
        String c_timeout = (String) this.socketOptions.get(2);
        this.connectTimeout = Integer.parseInt(c_timeout);
        try {
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.CONFIG, CLASS_NAME, "connectSocket", "Inet = {0}, Port = {1}, Timeout = {2}", null, null, this.inetSocketAddress.getHostString(), Integer.valueOf(this.port), c_timeout);
            this.socketChannel = SocketChannel.open();
            this.socketChannel.configureBlocking(false);
            this.selector = Selector.open();
            this.selectionKey = this.socketChannel.register(this.selector, 8);
            this.socketChannel.connect(this.inetSocketAddress);
            if (this.selector.select(this.connectTimeout) == 0) {
                throw new NetException(NetException.NT_MSGQ_CONNECT_TIMEOUT);
            }
            Set<SelectionKey> selectedKeys = this.selector.selectedKeys();
            Iterator<SelectionKey> keyIterator = selectedKeys.iterator();
            while (keyIterator.hasNext()) {
                SelectionKey key = keyIterator.next();
                if (key.isConnectable()) {
                    while (!this.socketChannel.finishConnect()) {
                    }
                    keyIterator.remove();
                } else {
                    throw new NetException(NetException.NT_MSGQ_UNEXPECTED_READ_ON_SOCKET);
                }
            }
            this.socket = this.socketChannel.socket();
        } catch (IOException ea) {
            DownHostsCache.getInstance().markDownHost(this.inetSocketAddress.getAddress(), this.port);
            CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "connectSocket", "Connection failed {0}", (String) null, (String) null, ea);
            if (this.selectionKey != null) {
                this.selectionKey.cancel();
            }
            try {
                if (this.socketChannel != null) {
                    this.socketChannel.close();
                }
            } catch (Exception e2) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connectSocket", "Ignoring SocketChannel Close Exception {0}", (String) null, (String) null, e2);
            }
            try {
                if (this.socket != null) {
                    this.socket.close();
                }
            } catch (Exception e22) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connectSocket", "Ignoring Socket Close Exception {0}", (String) null, (String) null, e22);
            }
            try {
                if (this.selector != null) {
                    this.selector.close();
                }
            } catch (Exception e23) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connectSocket", "Ignoring Selectore Exception {0}", (String) null, (String) null, e23);
            }
            throw ea;
        }
    }

    private void connectToRemoteQueue() throws IOException {
        try {
            if (!this.connectResponsePending) {
                writeLocalQueueNameOnSocket(this.localQueue);
            }
            do {
                BufferSequence nextMessage = getNextMessage(true);
                ByteBuffer packet = nextMessage.getBuffer(0);
                processNTMQLayer(packet);
                nextMessage.dispose();
                if (this.ntmqProtocolHandler.isDataPacket()) {
                    throw new NetException(NetException.NT_NTMQ_INVALID_PACKET, "ConnectPacket was expected");
                }
            } while (!this.ntmqProtocolHandler.isConnectPacket());
        } catch (InterruptedIOException e) {
            throw new NetException(NetException.NT_MSGQ_TIMEOUT_WHILE_EXCHANGING_QUEUE_NAME);
        }
    }

    public void setSocketOptions() throws IOException {
        String temp = (String) this.socketOptions.get(0);
        if (temp != null) {
            setOption(0, temp);
        }
        String temp2 = (String) this.socketOptions.get(1);
        if (temp2 != null) {
            setOption(1, temp2);
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void disconnect() throws IOException {
        if (this.remoteQueue != null) {
            while (this.remoteQueue.isWorkPending()) {
                try {
                    await(this.remoteQueue.getContext(), 10000, this.kernelWaitWork);
                } catch (IOException ioex) {
                    CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error flushing sends on disconnect: {0}. ", (String) null, (String) null, ioex);
                }
            }
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "disconnect", "Attempting to close remoteQueue: start. ", null, null);
            try {
                this.remoteQueue.close();
            } catch (IOException e) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the remote queue: {0}. ", (String) null, (String) null, e);
            } finally {
                this.remoteQueue = null;
            }
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempting to close remoteQueue: complete. ", null, null);
        }
        if (this.localQueue != null) {
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempting to close localQueue: start. ", null, null);
            try {
                this.localQueue.close();
            } catch (IOException ioex2) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the local queue: {0}. ", (String) null, (String) null, ioex2);
            } finally {
                this.localQueue = null;
            }
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempting to close localQueue: complete. ", null, null);
        }
        if (this.flowControl != null) {
            this.flowControl.onDisconnect(this.mqContext);
            this.flowControl = null;
        }
        if (this.dequedRcvBuf != null) {
            this.dequedRcvBuf.dispose();
            this.dequedRcvBuf = null;
        }
        if (this.socketChannel != null) {
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close socketChannel: start. ", null, null);
            try {
                try {
                    this.socketChannel.close();
                    this.socketChannel = null;
                } catch (IOException e2) {
                    CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the socket channel: {0}. ", (String) null, (String) null, e2);
                    this.socketChannel = null;
                }
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close socketChannel: complete. ", null, null);
            } catch (Throwable th) {
                this.socketChannel = null;
                throw th;
            }
        }
        try {
            if (this.socket != null) {
                try {
                    if (!this.socket.isClosed()) {
                        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close socket: start. ", null, null);
                        this.socket.close();
                        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close socket: complete. ", null, null);
                    }
                    this.socket = null;
                } catch (IOException e3) {
                    CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the socket: {0}. ", (String) null, (String) null, e3);
                    this.socket = null;
                }
            }
            if (this.selector != null) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close selector: start. ", null, null);
                try {
                    try {
                        this.selector.close();
                        this.selector = null;
                    } catch (Throwable th2) {
                        this.selector = null;
                        throw th2;
                    }
                } catch (IOException e4) {
                    CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the selector. ", null, e4);
                    this.selector = null;
                }
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Attempt to close selector: complete. ", null, null);
            }
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "NT TCP connection terminated. ", null, null);
            if (this.mqContext != null) {
                try {
                    this.mqContext.close();
                } catch (IOException ioex3) {
                    CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "disconnect", "Error closing the MQL Context. ", null, ioex3);
                }
            }
            this.isConnected = false;
        } catch (Throwable th3) {
            this.socket = null;
            throw th3;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public InputStream getInputStream() throws IOException {
        return null;
    }

    @Override // oracle.net.nt.NTAdapter
    public OutputStream getOutputStream() throws IOException {
        return null;
    }

    @Override // oracle.net.nt.NTAdapter
    public void setOption(int option, Object value) throws IOException {
        if (isClosed()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        switch (option) {
            case 0:
                String tmp = (String) value;
                this.socket.setTcpNoDelay(tmp.equals("YES"));
                return;
            case 1:
                String tmp2 = (String) value;
                if (tmp2.equals("YES")) {
                    this.socket.setKeepAlive(true);
                    return;
                }
                return;
            case 3:
            case 101:
                this.readTimeout = Integer.parseInt((String) value);
                this.socket.setSoTimeout(this.readTimeout);
                int i = this.readTimeout == 0 ? Integer.MAX_VALUE : this.readTimeout;
                this.kernelWaitWork = i;
                this.kernelWaitSend = i;
                return;
            default:
                return;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public Object getOption(int option) throws IOException {
        if (isClosed()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        switch (option) {
            case 3:
                return Integer.toString(this.socket.getSoTimeout());
            case 101:
                return "" + this.readTimeout;
            default:
                return null;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void abort() throws IOException {
        if (this.socket == null) {
            return;
        }
        try {
            this.socket.setSoLinger(true, 0);
        } catch (Exception e) {
        }
        this.socket.close();
    }

    @Override // oracle.net.nt.NTAdapter
    public void sendUrgentByte(int urgentData) throws IOException {
        this.socket.sendUrgentData(urgentData);
        ByteBuffer echoPacket = ByteBuffer.allocate(2);
        this.ntmqProtocolHandler.prepareHeaderPacket(echoPacket, (byte) 5, (byte) 0, null, false);
        this.socketChannel.write(echoPacket);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isCharacteristicUrgentSupported() throws IOException {
        try {
            return !this.socket.getOOBInline();
        } catch (IOException e) {
            return false;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void setReadTimeoutIfRequired(@Blind(PropertiesBlinder.class) Properties prop) throws IOException {
        String tmp = (String) prop.get(SQLnetDef.TCP_READTIMEOUT_STR);
        if (tmp == null) {
            tmp = "0";
        }
        setOption(3, tmp);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.socket.getKeepAlive();
    }

    @Override // oracle.net.nt.NTAdapter
    public InetAddress getInetAddress() {
        return this.socket.getInetAddress();
    }

    @Override // oracle.net.nt.NTAdapter
    public SocketChannel getSocketChannel() {
        return null;
    }

    @Override // oracle.net.nt.NTAdapter
    public NTAdapter.NetworkAdapterType getNetworkAdapterType() {
        return NTAdapter.NetworkAdapterType.MSGQ;
    }

    public void onMessage(BufferSequence bufseq, IOException ioex) throws IOException {
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "onMessage", "LocalQueue onMessage callback. ", null, null);
        if (ioex != null) {
            if (this.ioExceptionWhileMSGQOp != null) {
                ioex.initCause(this.ioExceptionWhileMSGQOp);
            }
            this.ioExceptionWhileMSGQOp = ioex;
        }
        this.onMessageBufferList.add(bufseq);
        this.numberOfMessagesReceived.incrementAndGet();
        if (this.flowControlEnabled) {
            this.flowControl.onMessageReceived();
        } else {
            this.wakeupBuffer.put(0, (byte) 1);
        }
        replenish();
    }

    private boolean isConnectionDead() throws IOException {
        if (!this.socketChannel.isOpen() || this.socket.isClosed() || this.socket.isInputShutdown() || this.socket.isOutputShutdown()) {
            return true;
        }
        this.selectionKey.interestOps(1);
        if (this.selector.selectNow() > 0) {
            Set<SelectionKey> selectedKeys = this.selector.selectedKeys();
            Iterator<SelectionKey> keyIterator = selectedKeys.iterator();
            while (keyIterator.hasNext()) {
                SelectionKey key = keyIterator.next();
                if (key.isReadable()) {
                    int bytesRead = this.socketChannel.read(this.bufferForDeathDetection);
                    if (bytesRead == -1) {
                        return true;
                    }
                    if (bytesRead > 0) {
                        throw new NetException(NetException.NT_MSGQ_UNEXPECTED_READ_ON_SOCKET);
                    }
                }
                keyIterator.remove();
            }
            return false;
        }
        return false;
    }

    public void setNegotiatedSDUAndTDU(int sdu, int tdu) {
        this.sdu = sdu;
        int adjustedTDU = Math.max(tdu, sdu + 18);
        if (this.tdu != adjustedTDU) {
            this.tdu = adjustedTDU;
            this.drainBuffers = true;
        }
    }

    private int replenish() throws IOException {
        if (this.onMessageBufferList.size() >= 8) {
            return 0;
        }
        int bufCount = this.localQueue.getAvailableReceiveSpaceMessageCount();
        if (this.drainBuffers) {
            if (bufCount > 0) {
                return 0;
            }
            this.drainBuffers = false;
        }
        int more = 8 - bufCount;
        int added = 0;
        if (more > 0) {
            BufferSequence[] receiveBuffers = new BufferSequence[more];
            for (int i = 0; i < more; i++) {
                receiveBuffers[i] = initReceiveBuffer();
            }
            added = this.localQueue.addMessageBuffers(receiveBuffers, 0, more);
            if (added < more) {
                for (BufferSequence failedToAdd : receiveBuffers) {
                    if (failedToAdd != null) {
                        failedToAdd.dispose();
                    }
                }
            }
            if (added > 0) {
                if (this.flowControlEnabled) {
                    this.flowControl.onBufferPosted(added);
                }
                this.postCount += added;
            }
        }
        return added;
    }

    private final boolean isClosed() {
        if (this.socket == null) {
            return true;
        }
        return this.socket.isClosed();
    }

    private void prepareFlowControlPacket(ByteBuffer connectPacket) throws IOException {
        if (this.flowControl == null) {
            this.flowControl = new MQLFlowControl(this.localQueue.getContext(), this.keyRegistry.getContext());
        }
        short availableBuffers = (short) this.localQueue.getAvailableReceiveSpaceMessageCount();
        if (this.ntmqProtocolHandler.getPacketType() == 2 && this.ntmqProtocolHandler.getPacketFlag() == 8) {
            availableBuffers = (short) (availableBuffers - 1);
        }
        this.flowControl.setLocalPostCount(availableBuffers);
        this.flowControl.resetLocalFCB();
        RegistrationKey fcbKey = this.flowControl.getLocalFCBKey();
        this.ntmqProtocolHandler.appendFlowControlPacket(connectPacket, true, availableBuffers, fcbKey.getRemoteVirtualAddress(), fcbKey.getKeyBuffer());
        this.postCount = 0;
    }

    private void scheduleInterrupt(int timeout) {
        if (timeout > 0) {
            this.interruptTask = TimeoutInterruptHandler.scheduleInterrupt(TimeoutInterruptHandler.InterruptTaskType.SO_TIMEOUT, timeout, Thread.currentThread());
        }
    }

    private void handleInterrupt() throws IOException {
        Thread.interrupted();
        try {
            disconnect();
        } catch (IOException e) {
        }
        if (this.interruptTask != null && this.interruptTask.isInterrupted()) {
            throw new TimeoutInterruptHandler.IOReadTimeoutException("MSGQ read timed out");
        }
        throw new InterruptedIOException("Operation interrupted");
    }

    private void cancelTimeout() {
        if (this.interruptTask != null) {
            TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.SO_TIMEOUT, Thread.currentThread());
            if (this.interruptTask.isInterrupted()) {
                Thread.interrupted();
            }
            this.interruptTask = null;
        }
    }

    private void initBufferManager(InetAddress ipOfIB) throws IOException {
        if (bufferManager != null) {
            return;
        }
        Monitor.CloseableLock lock = BUFFER_MANAGER_INIT_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            if (bufferManager == null) {
                if (null == System.getProperty(FMW_COMMONS_IP_PROP)) {
                    System.setProperty(FMW_COMMONS_IP_PROP, ipOfIB.getHostAddress());
                }
                BufferManager tmpBufferManager = BufferManagers.getNetworkDirectManager();
                if (tmpBufferManager instanceof MultiInterfaceKeyRegistry) {
                    bufferManager = tmpBufferManager;
                } else {
                    if (!Platform.getPlatform().isExaEnabled()) {
                        throw new IOException("This system is not recognized as an Exadirect enabled platform.");
                    }
                    throw new IOException("IP: " + ipOfIB.getHostAddress() + " is not recognized as an RDMA enabled adapter.");
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private void ensureConnection(boolean writeOp) throws IOException {
        if (this.ioExceptionWhileMSGQOp != null) {
            throw ((IOException) new IOException(MSGQ_ERR_STATE_MSG).initCause(this.ioExceptionWhileMSGQOp));
        }
        if (writeOp && this.connectResponsePending) {
            connectToRemoteQueue();
        }
        if (!this.isConnected) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
    }

    private void initRemoteQueue() throws IOException {
        if (this.remoteQueue != null) {
            this.remoteQueue.close();
        }
        this.remoteQueue = this.mqContext.openRemoteQueue();
        this.remoteQueue.setWriteCallback(new RemoteQueue.WriteCallback() { // from class: oracle.net.nt.MQLNTAdapter.4
            public void onCompletion(Object oCookie, IOException e) {
                if (e != null) {
                    if (MQLNTAdapter.this.ioExceptionWhileMSGQOp != null) {
                        e.initCause(MQLNTAdapter.this.ioExceptionWhileMSGQOp);
                    }
                    MQLNTAdapter.this.ioExceptionWhileMSGQOp = e;
                    MQLNTAdapter.this.wakeupBuffer.put(0, (byte) 1);
                }
                if (oCookie != null) {
                    ((Disposable) oCookie).dispose();
                }
                if (MQLNTAdapter.this.pendingSends == 0) {
                    MQLNTAdapter.this.wakeupBuffer.put(0, (byte) 1);
                }
            }
        });
    }

    public static final String packetToString(ByteBuffer buff) {
        StringBuffer strbuff = new StringBuffer();
        int offset = 0;
        char[] byteArr = new char[8];
        int initialPosition = buff.position();
        while (buff.hasRemaining()) {
            byte b = buff.get();
            String hexRep = Integer.toHexString(b & 255).toUpperCase();
            if (hexRep.length() == 1) {
                hexRep = "0" + hexRep;
            }
            strbuff.append(hexRep);
            strbuff.append(' ');
            if (b > 32 && b < Byte.MAX_VALUE) {
                byteArr[offset] = (char) b;
            } else {
                byteArr[offset] = '.';
            }
            offset++;
            if (offset == 8) {
                strbuff.append('|');
                strbuff.append(byteArr);
                strbuff.append('|');
                strbuff.append('\n');
                offset = 0;
            }
        }
        if (offset != 0) {
            int nbSpacesMissing = 8 - offset;
            for (int i = 0; i < nbSpacesMissing * 3; i++) {
                strbuff.append(' ');
            }
            strbuff.append('|');
            strbuff.append(byteArr, 0, offset);
            for (int i2 = 0; i2 < nbSpacesMissing; i2++) {
                strbuff.append(' ');
            }
            strbuff.append('|');
            strbuff.append('\n');
        }
        buff.position(initialPosition);
        return strbuff.toString();
    }

    private static String dump(ByteBuffer byteBuffer) {
        ByteBuffer duplicateBuffer = byteBuffer.duplicate();
        char[] hexChars = new char[duplicateBuffer.limit() * 3];
        int counter = 0;
        System.getProperty("line.separator");
        for (int j = 0; j < duplicateBuffer.limit(); j++) {
            int v = duplicateBuffer.get(j) & 255;
            hexChars[j * 3] = hexArray[v >>> 4];
            hexChars[(j * 3) + 1] = hexArray[v & 15];
            counter++;
            if (counter % 8 == 0) {
                hexChars[(j * 3) + 2 + 0] = '\n';
            } else {
                hexChars[(j * 3) + 2] = ' ';
            }
        }
        return new String(hexChars);
    }
}
