package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.AccessController;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.Provider;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Properties;
import java.util.logging.Level;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.ns.NetException;
import oracle.security.jps.service.keystore.KeyStoreServiceLoadStoreParameter;

/* loaded from: ojdbc8.jar:oracle/net/nt/CustomSSLSocketFactory.class */
public class CustomSSLSocketFactory {
    private static final String CLASS_NAME = CustomSSLSocketFactory.class.getName();
    DMSFactory.DMSNoun dmsParent = null;

    private CustomSSLSocketFactory() {
    }

    public static SSLEngine getSSLSocketEngine(String host, int port, @Blind(PropertiesBlinder.class) Properties sslSocketProperties, Diagnosable diagnosable) throws IOException {
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSSLSocketEngine", "Creating SSLSocketEngine..", null, null);
        SSLConfig config = SSLConfig.newInstance(sslSocketProperties);
        SSLEngine sslEngine = SSLContextCache.instance().get(config, diagnosable).context().createSSLEngine(host, port);
        TcpsConfigure.configure(sslEngine, sslSocketProperties, diagnosable);
        return sslEngine;
    }

    public static SSLSocketFactory getSSLSocketFactory(Properties sslSocketProperties, DMSFactory.DMSNoun dmsParent, Diagnosable diagnosable) throws IOException {
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSSLSocketFactory", "Creating SSLSocketFactory..", null, null);
        SSLConfig config = SSLConfig.newInstance(sslSocketProperties);
        return SSLContextCache.instance().get(config, diagnosable).context().getSocketFactory();
    }

    static ExtendedSSLContext getSSLContext(SSLConfig config, Diagnosable diagnosable) throws IOException {
        if (config.useSystemKeystore()) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSSLContext", "Keystore and Truststore is set to 'NONE'. Using the default SSLContext.", null, null);
            try {
                return ExtendedSSLContext.wrap(SSLContext.getDefault());
            } catch (NoSuchAlgorithmException nae) {
                NetException netException = new NetException(NetException.UNABLE_TO_INIT_SSL_CONTEXT);
                netException.initCause(nae);
                throw netException;
            }
        }
        return newSSLContext(config, diagnosable);
    }

    private static ExtendedSSLContext newSSLContext(SSLConfig config, Diagnosable diagnosable) throws NetException {
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "newSSLContext", "Creating SSLContext for the config = {0}", (String) null, (String) null, config);
        try {
            return ExtendedSSLContext.newInstance(config);
        } catch (Exception ex) {
            diagnosable.debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "newSSLContext", "Error in initializing ssl context {0}", (String) null, (String) null, ex.toString());
            if (ex instanceof NetException) {
                throw ((NetException) ex);
            }
            throw ((NetException) new NetException(NetException.UNABLE_TO_INIT_SSL_CONTEXT).initCause(ex));
        }
    }

    static KeyStore mergeCaCerts(KeyStore trustStore) throws GeneralSecurityException, IOException {
        TrustManagerFactory caCertsTrustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        try {
            caCertsTrustManagerFactory.init((KeyStore) null);
        } catch (NullPointerException | KeyStoreException e) {
            caCertsTrustManagerFactory.init(loadCaCerts());
        }
        for (TrustManager trustManager : caCertsTrustManagerFactory.getTrustManagers()) {
            if (trustManager instanceof X509TrustManager) {
                for (X509Certificate certificate : ((X509TrustManager) trustManager).getAcceptedIssuers()) {
                    String alias = certificate.getIssuerX500Principal().getName();
                    if (!trustStore.containsAlias(alias)) {
                        trustStore.setCertificateEntry(alias, certificate);
                    }
                }
            }
        }
        return trustStore;
    }

    private static KeyStore loadCaCerts() throws Exception {
        try {
            return loadCaCerts(SSLConfig.PKCS12_WALLET_TYPE, "SUN");
        } catch (IOException | GeneralSecurityException exception0) {
            try {
                return loadCaCerts(SSLConfig.JKS_TYPE, "SUN");
            } catch (IOException | GeneralSecurityException exception1) {
                exception1.addSuppressed(exception0);
                throw exception1;
            }
        }
    }

    private static KeyStore loadCaCerts(String type, String provider) throws GeneralSecurityException, IOException {
        InputStream inputStream = Files.newInputStream(Paths.get(System.getProperty("java.home"), "lib", "security", "cacerts"), new OpenOption[0]);
        Throwable th = null;
        try {
            try {
                KeyStore cacerts = KeyStore.getInstance(type, provider);
                cacerts.load(inputStream, null);
                if (inputStream != null) {
                    if (0 != 0) {
                        try {
                            inputStream.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        inputStream.close();
                    }
                }
                return cacerts;
            } finally {
            }
        } catch (Throwable th3) {
            if (inputStream != null) {
                if (th != null) {
                    try {
                        inputStream.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    inputStream.close();
                }
            }
            throw th3;
        }
    }

    static KeyStore getKeyStoreInstance(String type, Diagnosable diagnosable) throws KeyStoreException {
        if (SSLConfig.PEM_WALLET_TYPE.equals(type)) {
            type = SSLConfig.PKCS12_WALLET_TYPE;
        }
        try {
            return KeyStore.getInstance(type);
        } catch (KeyStoreException err) {
            try {
                Provider provider = loadKnownProvider(type);
                if (provider == null) {
                    throw err;
                }
                return KeyStore.getInstance(type, provider);
            } catch (Exception loadProviderError) {
                diagnosable.debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getKeyStoreInstance", "Failed to load a known provider for keystore type {0}, exception {1}", null, null, type, loadProviderError);
                throw err;
            }
        }
    }

    private static Provider loadKnownProvider(String type) throws Exception {
        String providerClass;
        Class<?> clazz;
        Provider providerObject = null;
        switch (type.toUpperCase()) {
            case "SSO":
                providerClass = SSLConfig.ORACLE_PKI_PROVIDER_CLASS;
                break;
            case "KSS":
                providerClass = SSLConfig.KSS_PROVIDER_CLASS;
                break;
            default:
                providerClass = null;
                break;
        }
        if (providerClass != null && (clazz = Class.forName(providerClass)) != null && Provider.class.isAssignableFrom(clazz)) {
            providerObject = (Provider) AccessController.doPrivileged(() -> {
                return (Provider) clazz.newInstance();
            });
        }
        return providerObject;
    }

    static void loadFileBasedKeyStore(KeyStore keyStore, String path, OpaqueString password) throws NoSuchAlgorithmException, IOException, CertificateException {
        InputStream fileStream = Channels.newInputStream(FileChannel.open(Paths.get(path, new String[0]), StandardOpenOption.READ));
        Throwable th = null;
        try {
            try {
                loadKeyStore(keyStore, fileStream, password);
                if (fileStream != null) {
                    if (0 != 0) {
                        try {
                            fileStream.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    fileStream.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (fileStream != null) {
                if (th != null) {
                    try {
                        fileStream.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    fileStream.close();
                }
            }
            throw th4;
        }
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [oracle.net.nt.CustomSSLSocketFactory$1KSSLoader] */
    static void loadKSSKeyStore(KeyStore keyStore, String uri, OpaqueString password) throws NoSuchAlgorithmException, IOException, CertificateException {
        new Object() { // from class: oracle.net.nt.CustomSSLSocketFactory.1KSSLoader
            /* JADX INFO: Access modifiers changed from: private */
            /* JADX WARN: Finally extract failed */
            public final void load(KeyStore ks, String uri2, OpaqueString password2) throws NoSuchAlgorithmException, IOException, CertificateException {
                KeyStoreServiceLoadStoreParameter param = new KeyStoreServiceLoadStoreParameter();
                if (!OpaqueString.isNull(password2)) {
                    char[] pw = password2.getChars();
                    try {
                        param.setProtectionParameter(new KeyStore.PasswordProtection(pw));
                        for (int i = 0; i < pw.length; i++) {
                            pw[i] = 0;
                        }
                    } catch (Throwable th) {
                        for (int i2 = 0; i2 < pw.length; i2++) {
                            pw[i2] = 0;
                        }
                        throw th;
                    }
                }
                param.setKssUri(uri2);
                ks.load(param);
            }
        }.load(keyStore, uri, password);
    }

    static void loadKeyStore(KeyStore keyStore, InputStream inputStream, OpaqueString password) throws NoSuchAlgorithmException, IOException, CertificateException {
        char[] pw = password.getChars();
        try {
            keyStore.load(inputStream, pw);
            clearPwd(pw);
        } catch (Throwable th) {
            clearPwd(pw);
            throw th;
        }
    }

    static void clearPwd(char[] pwd) {
        if (pwd != null) {
            Arrays.fill(pwd, (char) 0);
        }
    }
}
