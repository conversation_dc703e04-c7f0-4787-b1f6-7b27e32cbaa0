package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.SocketException;
import java.net.SocketOption;
import java.nio.channels.ClosedChannelException;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.logging.Level;
import javax.net.SocketFactory;
import jdk.net.Sockets;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;

/* loaded from: ojdbc8.jar:oracle/net/nt/TcpNTAdapter.class */
public class TcpNTAdapter extends AbstractAdapter implements Diagnosable {
    private static final String CLASS_NAME = TcpNTAdapter.class.getName();
    private static final int[] SUPPORTED_SOCKET_OPTIONS = {0, 1, 33, 34, 35};
    private static Proxy DEFAULT_SOCKS_PROXY;
    private final String addressInfo;
    private final Diagnosable diagnosable;
    NetStatImpl netStat;
    Boolean useNio;
    String protocol;
    String uri;
    protected SocketChannelWrapper socketChannel;
    private String httpProxy;
    private int httpProxyPort;
    protected InetSocketAddress inetSocketAddress;
    protected boolean isRemoteDNS;
    protected int connectTimeout;
    final ConnOption connOption;
    SocketFactory sockFactory;
    protected Proxy proxy = null;
    private volatile boolean isRegisteredEver = false;

    static {
        DEFAULT_SOCKS_PROXY = null;
        try {
            String globalProxyHost = System.getProperty("socksProxyHost", null);
            if (globalProxyHost != null) {
                DEFAULT_SOCKS_PROXY = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(globalProxyHost, Integer.parseInt(System.getProperty("socksProxyPort", OracleConnection.CONNECTION_PROPERTY_SOCKS_PROXY_PORT_DEFAULT))));
            }
        } catch (Exception e) {
        }
    }

    public TcpNTAdapter(String address, @Blind(PropertiesBlinder.class) Properties socketOptions, Diagnosable diagnosable, ConnOption connOption) throws NumberFormatException, NLException {
        this.netStat = null;
        this.isRemoteDNS = true;
        this.connectTimeout = 0;
        this.connOption = connOption;
        this.diagnosable = diagnosable;
        this.socketOptions = socketOptions;
        this.addressInfo = address;
        this.inetSocketAddress = connOption.inetSocketAddress;
        this.useNio = true;
        this.isRemoteDNS = Boolean.parseBoolean((String) socketOptions.getOrDefault(45, "true"));
        int i = Integer.parseInt((String) socketOptions.getOrDefault(2, OracleConnection.CONNECTION_PROPERTY_THIN_NET_CONNECT_TIMEOUT_DEFAULT));
        connOption.transportConnectTimeout = i;
        this.connectTimeout = i;
        this.netStat = new NetStatImpl();
        this.host = connOption.host;
        this.port = this.inetSocketAddress.getPort();
        this.protocol = connOption.protocol;
        initializeProxy(connOption);
    }

    protected void initializeProxy(ConnOption connOption) {
        readHttpsProxyConfig(connOption);
        if (this.httpProxy != null) {
            this.proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(this.httpProxy, this.httpProxyPort));
        } else if (this.socketOptions.containsKey(36) && this.socketOptions.containsKey(37)) {
            this.proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress((String) this.socketOptions.get(36), Integer.parseInt((String) this.socketOptions.get(37))));
        } else {
            this.proxy = DEFAULT_SOCKS_PROXY;
        }
    }

    private void readHttpsProxyConfig(ConnOption connOption) {
        String proxyPortStr;
        this.httpProxy = connOption.httpsProxy;
        this.httpProxyPort = connOption.httpsProxyPort;
        if (this.httpProxy == null) {
            this.httpProxy = (String) this.socketOptions.get(30);
            if (this.httpProxy != null && (proxyPortStr = (String) this.socketOptions.get(31)) != null) {
                this.httpProxyPort = Integer.parseInt(proxyPortStr);
            }
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void connect(DMSFactory.DMSNoun dmsParent) throws IOException {
        this.sockFactory = new MetricsEnabledSocketFactory(dmsParent);
        long startTime = System.currentTimeMillis();
        try {
            establishSocket(this.inetSocketAddress, dmsParent);
            long connectTime = System.currentTimeMillis() - startTime;
            setSocketOptions();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "Connect succeeded. {0}, protocol={1}, proxy={2}, isRemoteDNS={3},connectTimeout={4}ms, connectTime={5} ms. ", null, null, this, this.protocol, this.proxy == null ? "null" : this.proxy, Boolean.valueOf(this.isRemoteDNS), Integer.valueOf(this.connectTimeout), Long.valueOf(connectTime));
        } catch (IOException ea) {
            if (!this.isRemoteDNS || this.proxy == null) {
                DownHostsCache.getInstance().markDownHost(this.inetSocketAddress.getAddress(), this.port);
            }
            throw ea;
        }
    }

    private byte[] prepareTcpFastOpenDataAndGet() throws NetException, UnsupportedEncodingException {
        byte[] tfoBytes = null;
        if (SQLnetDef.isTcpFastOpenEnabled((String) this.socketOptions.get(109))) {
            tfoBytes = getTFOBytes(this.connOption.getConnectData());
            if (tfoBytes == null) {
                tfoBytes = getTFOBytes(this.connOption.getOriginalConnOption().getConnectData());
            }
            if (tfoBytes != null) {
                this.connOption.setConnectData(new String(tfoBytes));
            }
        }
        return tfoBytes;
    }

    private byte[] getTFOBytes(String url) throws NetException {
        NVPair nVPairFindNVPair;
        if (url == null || url.isEmpty()) {
            return null;
        }
        try {
            NVPair descriptionNVPair = new NVFactory().createNVPair(url);
            NVNavigator navigator = new NVNavigator();
            NVPair connectDataNVPair = navigator.findNVPair(descriptionNVPair, "CONNECT_DATA");
            if (connectDataNVPair == null) {
                return null;
            }
            if (navigator.findNVPair(connectDataNVPair, SQLnetDef.TCP_FAST_OPEN_PARAM_NAME) != null) {
                nVPairFindNVPair = navigator.findNVPair(connectDataNVPair, SQLnetDef.TCP_FAST_OPEN_PARAM_NAME);
            } else {
                nVPairFindNVPair = navigator.findNVPair(connectDataNVPair, SQLnetDef.TCP_FAST_OPEN_PARAM_NAME_2);
            }
            NVPair tfoNVPair = nVPairFindNVPair;
            if (tfoNVPair == null) {
                connectDataNVPair.addListElement(new NVPair(SQLnetDef.TCP_FAST_OPEN_PARAM_NAME, "yes"));
            } else {
                tfoNVPair.setAtom("yes");
            }
            String newUrl = descriptionNVPair.toString();
            return newUrl.getBytes("ASCII");
        } catch (Exception e) {
            throw new NetException(NetException.INVALID_CONNECT_DATA);
        }
    }

    protected void establishSocket(InetSocketAddress inetAddr, DMSFactory.DMSNoun dmsParent) throws IOException {
        long socketConnectStartTime = System.currentTimeMillis();
        try {
            begin(Metrics.ConnectionEvent.SOCKET_ESTABLISHMENT);
            if (this.useNio.booleanValue()) {
                this.socketChannel = new TimeoutSocketChannel(inetAddr, this.connectTimeout, this.netStat, this.proxy, this.diagnosable, prepareTcpFastOpenDataAndGet());
                this.socket = this.socketChannel.socket();
            } else {
                this.socket = this.sockFactory.createSocket();
                this.socket.connect(inetAddr, this.connectTimeout);
            }
            setReadTimeoutIfRequired(this.socketOptions);
            end(Metrics.ConnectionEvent.SOCKET_ESTABLISHMENT);
        } catch (IOException ioException) {
            throw handleEstablishSocketException(ioException, socketConnectStartTime, inetAddr);
        }
    }

    private IOException handleEstablishSocketException(IOException ioException, long socketConnectStartTime, InetSocketAddress inetAddr) throws IOException {
        NetException netException;
        if (ioException instanceof TimeoutInterruptHandler.IOReadTimeoutException) {
            trySocketClose();
            if (getNetworkAdapterType() == NTAdapter.NetworkAdapterType.TCP || getNetworkAdapterType() == NTAdapter.NetworkAdapterType.TCPS) {
                return new NetException(NetException.TIMEOUT, describeConnectionFailure(ioException, socketConnectStartTime, inetAddr), false, getNetworkAdapterType().toString() + " connect", this.connectTimeout + "ms", "host " + this.host + " port " + this.port);
            }
            return new IOException(describeConnectionFailure(ioException, socketConnectStartTime, inetAddr), ioException);
        }
        if (ioException instanceof InterruptedIOException) {
            trySocketClose();
            InterruptedIOException describedInterruptException = new InterruptedIOException(describeConnectionFailure(ioException, socketConnectStartTime, inetAddr));
            describedInterruptException.initCause(ioException);
            return describedInterruptException;
        }
        if (ioException instanceof ConnectException) {
            if (this.protocol.equalsIgnoreCase("tcp") || this.protocol.equalsIgnoreCase("tcps") || this.protocol.equalsIgnoreCase("wss")) {
                String message = "host " + this.host + " port " + this.port;
                netException = new NetException(NetException.LISTENER_NOT_AVAILABLE, null, false, message);
            } else if (this.protocol.equalsIgnoreCase("ipc")) {
                String message2 = "key " + this.socketOptions.keySet();
                netException = new NetException(NetException.LISTENER_NOT_AVAILABLE, null, false, message2);
            } else {
                netException = new NetException(NetException.LISTENER_NOT_AVAILABLE, "at " + inetAddr.toString() + (this.proxy == null ? "" : ", connecting via proxy [" + this.proxy.toString() + "]"));
            }
            netException.initCause(ioException);
            return netException;
        }
        trySocketClose();
        return new IOException(describeConnectionFailure(ioException, socketConnectStartTime, inetAddr), ioException);
    }

    private void trySocketClose() throws IOException {
        try {
            if (this.socket != null) {
                this.socket.close();
            }
        } catch (Exception e) {
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    private String describeConnectionFailure(IOException connectionError, long connectStartTime, InetSocketAddress inetAddr) {
        Level level = Level.INFO;
        SecurityLabel securityLabel = SecurityLabel.UNKNOWN;
        String str = this.protocol;
        Object[] objArr = new Object[8];
        objArr[0] = connectionError.getMessage();
        objArr[1] = Long.valueOf(System.currentTimeMillis() - connectStartTime);
        objArr[2] = inetAddr.getHostString();
        objArr[3] = Integer.valueOf(this.port);
        objArr[4] = this.proxy == null ? "" : "Proxy = " + this.proxy.toString();
        objArr[5] = Integer.valueOf(this.connectTimeout);
        objArr[6] = this.host;
        objArr[7] = this.useNio;
        debug(level, securityLabel, str, "describeConnectionFailure", String.format("%s, socket connect lapse %d ms. %s %d %s %s %s %s", objArr), null, null);
        return this.proxy == null ? "" : "Proxy = " + this.proxy.toString();
    }

    @Override // oracle.net.nt.NTAdapter
    public CompletionStage<Void> connectAsync(DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) {
        if (!this.useNio.booleanValue()) {
            return CompletionStageUtil.failedStage(new IOException("Asynchronous connection is not supported when oracle.jdbc.javaNetNio=false"));
        }
        if (this.proxy != null) {
            return CompletionStageUtil.failedStage(new IOException("Asynchronous connection is not supported with proxies"));
        }
        this.sockFactory = new MetricsEnabledSocketFactory(dmsParent);
        long startTime = System.currentTimeMillis();
        return establishSocketAsync(this.inetSocketAddress, dmsParent, outboundTimeout, asyncExecutor).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, ioException -> {
            if (!this.isRemoteDNS || this.proxy == null) {
                DownHostsCache.getInstance().markDownHost(this.inetSocketAddress.getAddress(), this.port);
            }
            throw ioException;
        })).thenApply(CompletionStageUtil.normalCompletionHandler(nil -> {
            long connectTime = System.currentTimeMillis() - startTime;
            setSocketOptions();
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "connectAsync", "Connect succeeded. {0}, protocol={1}, proxy={2}, isRemoteDNS={3}, connectTimeout={4}ms, connectTime={5} ms. ", null, null, this, this.protocol, this.proxy == null ? "null" : this.proxy, Boolean.valueOf(this.isRemoteDNS), Integer.valueOf(this.connectTimeout), Long.valueOf(connectTime));
            return nil;
        }));
    }

    protected CompletionStage<Void> establishSocketAsync(InetSocketAddress inetAddr, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) {
        long socketConnectStartTime = System.currentTimeMillis();
        begin(Metrics.ConnectionEvent.SOCKET_ESTABLISHMENT);
        return TimeoutSocketChannel.openAsync(inetAddr, this.connectTimeout, this.netStat, this.diagnosable, outboundTimeout, asyncExecutor).thenAccept(connectedChannel -> {
            this.socketChannel = connectedChannel;
            this.socket = this.socketChannel.socket();
            try {
                setReadTimeoutIfRequired(this.socketOptions);
                end(Metrics.ConnectionEvent.SOCKET_ESTABLISHMENT);
            } catch (IOException exception) {
                throw new CompletionException(exception);
            }
        }).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, ioException -> {
            throw handleEstablishSocketException(ioException, socketConnectStartTime, inetAddr);
        }));
    }

    @Override // oracle.net.nt.NTAdapter
    public NetStat getNetStat() {
        return this.netStat;
    }

    final void setSocketOptions() throws IOException {
        for (int option : SUPPORTED_SOCKET_OPTIONS) {
            Integer key = Integer.valueOf(option);
            String value = (String) this.socketOptions.get(key);
            if (value != null) {
                setOption(option, value);
            }
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void disconnect() throws IOException {
        try {
            if (this.useNio.booleanValue()) {
                this.socketChannel.disconnect();
            } else if (this.socket != null && !this.socket.isClosed()) {
                this.socket.close();
            }
        } finally {
            this.socket = null;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public InputStream getInputStream() throws IOException {
        return this.socket.getInputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public OutputStream getOutputStream() throws IOException {
        return this.socket.getOutputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public void setOption(int option, Object value) throws IOException {
        if (isClosed()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        switch (option) {
            case 0:
                setTcpNoDelay((String) value);
                return;
            case 1:
                setTcpKeepAlive((String) value);
                return;
            case 3:
            case 101:
                setTcpReadTimeout((String) value);
                return;
            case 33:
                setTcpKeepAliveIdleTime((String) value);
                return;
            case 34:
                setTcpKeepAliveProbeInterval((String) value);
                return;
            case 35:
                setTcpKeepAliveProbeCount((String) value);
                return;
            default:
                return;
        }
    }

    private final void setTcpNoDelay(String setting) throws IOException {
        this.socket.setTcpNoDelay(setting.equals("YES"));
    }

    private final void setTcpKeepAlive(String setting) throws IOException {
        if (setting.equals("YES")) {
            try {
                this.socket.setKeepAlive(true);
            } catch (IOException ioEx) {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "setTcpKeepAlive", "Failed to set keep alive. Exception " + ioEx.getMessage(), (String) null, (String) null, ioEx);
            }
        }
    }

    private final void setTcpReadTimeout(String timeoutMillis) throws IOException {
        this.readTimeout = Integer.parseInt(timeoutMillis);
        if (!this.useNio.booleanValue()) {
            this.socket.setSoTimeout(this.readTimeout);
        } else {
            this.socketChannel.setSoTimeout(this.readTimeout);
        }
    }

    private final void setTcpKeepAliveIdleTime(String idleTimeSeconds) throws IOException {
        setSocketOption(getSocketOptionByNameAndType("TCP_KEEPIDLE", Integer.class), Integer.valueOf(idleTimeSeconds));
    }

    private final void setTcpKeepAliveProbeInterval(String intervalSeconds) throws IOException {
        setSocketOption(getSocketOptionByNameAndType("TCP_KEEPINTERVAL", Integer.class), Integer.valueOf(intervalSeconds));
    }

    private final void setTcpKeepAliveProbeCount(String count) throws IOException {
        setSocketOption(getSocketOptionByNameAndType("TCP_KEEPCOUNT", Integer.class), Integer.valueOf(count));
    }

    private final <T> SocketOption<T> getSocketOptionByNameAndType(String name, Class<T> type) throws IOException {
        Set<SocketOption<?>> setSupportedOptions;
        if (this.useNio.booleanValue()) {
            setSupportedOptions = this.socketChannel.supportedOptions();
        } else {
            setSupportedOptions = Sockets.supportedOptions(this.socket.getClass());
        }
        Set<SocketOption<?>> supportedOptions = setSupportedOptions;
        SocketOption<T> socketOption = (SocketOption) supportedOptions.stream().filter(option -> {
            return name.equals(option.name());
        }).findFirst().orElseThrow(() -> {
            return new IOException("Socket option " + name + " is not supported by SocketChannels opened in this JVM");
        });
        if (!socketOption.type().equals(type)) {
            throw new IOException("Unexpected type for socket option " + name + ". SocketOption.type() to returns " + socketOption.type() + " Expected type is: " + type);
        }
        return socketOption;
    }

    private final <T> void setSocketOption(SocketOption<T> option, T value) throws IOException {
        if (this.useNio.booleanValue()) {
            this.socketChannel.setOption((SocketOption<SocketOption<T>>) option, (SocketOption<T>) value);
        } else {
            Sockets.setOption(this.socket, option, value);
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public Object getOption(int option) throws IOException {
        if (isClosed()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        switch (option) {
            case 3:
                if (!this.useNio.booleanValue()) {
                    return Integer.toString(this.socket.getSoTimeout());
                }
                return Integer.valueOf(this.socketChannel.getSoTimeout());
            case 101:
                return "" + this.readTimeout;
            default:
                return null;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void abort() throws IOException {
        try {
            if (this.socket != null) {
                this.socket.setSoLinger(true, 0);
            }
        } catch (Exception e) {
        }
        try {
            disconnect();
        } catch (IOException e2) {
        }
        abortTcpMultiplexerRegistration();
    }

    private final void abortTcpMultiplexerRegistration() {
        if (this.useNio.booleanValue() && this.socketChannel != null && this.isRegisteredEver) {
            forceCallback(new IOException("Connection aborted"));
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void sendUrgentByte(int urgentData) throws IOException {
        this.socket.sendUrgentData(urgentData);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isCharacteristicUrgentSupported() throws IOException {
        try {
            return !this.socket.getOOBInline();
        } catch (IOException e) {
            return false;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void setReadTimeoutIfRequired(@Blind(PropertiesBlinder.class) Properties prop) throws IOException {
        String tmp = (String) prop.get(SQLnetDef.TCP_READTIMEOUT_STR);
        if (tmp == null) {
            tmp = (String) prop.get(3);
        }
        if (tmp == null) {
            tmp = "0";
        }
        setOption(3, tmp);
    }

    public String getAddressInfo() {
        return this.addressInfo;
    }

    @Override // oracle.net.nt.AbstractAdapter
    public String toString() {
        return "host=" + this.host + ", port=" + this.port + ", socketOptions=" + this.socketOptions.toString() + "\n    socket=" + this.socket;
    }

    private final boolean isClosed() {
        if (this.socket == null) {
            return true;
        }
        return this.socket.isClosed();
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.socket.getKeepAlive();
    }

    @Override // oracle.net.nt.NTAdapter
    public InetAddress getInetAddress() {
        return this.socket.getInetAddress();
    }

    @Override // oracle.net.nt.NTAdapter
    public SocketChannel getSocketChannel() {
        return this.socketChannel;
    }

    @Override // oracle.net.nt.NTAdapter
    public NTAdapter.NetworkAdapterType getNetworkAdapterType() {
        return NTAdapter.NetworkAdapterType.TCP;
    }

    @Override // oracle.net.nt.NTAdapter
    public void registerForNonBlockingRead(Consumer<Throwable> onReady) throws IOException {
        this.isRegisteredEver = true;
        this.socketChannel.registerForNonBlockingRead(onReady);
    }

    @Override // oracle.net.nt.NTAdapter
    public final void registerForNonBlockingWrite(Consumer<Throwable> onReady) throws IOException {
        this.isRegisteredEver = true;
        this.socketChannel.registerForNonBlockingWrite(onReady);
    }

    @Override // oracle.net.nt.NTAdapter
    public final void forceCallback(Throwable onReadyError) {
        TcpMultiplexer.forceCallback(this.socketChannel.getUnderlyingChannel(), onReadyError);
    }

    @Override // oracle.net.nt.NTAdapter
    public final void restoreBlockingMode() throws IOException {
        if (!this.isRegisteredEver) {
            return;
        }
        SocketChannel underlyingChannel = this.socketChannel.getUnderlyingChannel();
        if (underlyingChannel.isBlocking()) {
            return;
        }
        TcpMultiplexer.restoreBlockingMode(underlyingChannel);
    }

    @Override // oracle.net.nt.NTAdapter
    @Blind
    public Properties getSqlNetOptions() {
        return (Properties) this.socketOptions.clone();
    }

    @Override // oracle.net.nt.NTAdapter
    public void enqueueBlockedWrites(boolean isEnabled) {
        this.socketChannel.enqueueBlockedWrites(isEnabled);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean completeBlockedWrites() throws IOException {
        return this.socketChannel.completeBlockedWrites();
    }

    @Override // oracle.net.nt.NTAdapter
    public void enqueueAllWrites(boolean isEnabled) {
        this.socketChannel.enqueueAllWrites(isEnabled);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean getEnqueueAllWrites() {
        return this.socketChannel.getEnqueueAllWrites();
    }

    @Override // oracle.net.nt.NTAdapter
    public void completeWrites() throws IOException {
        this.socketChannel.completeWrites();
    }

    @Override // oracle.net.nt.NTAdapter
    public final boolean awaitWriteReadiness(long timeoutMillis) throws IOException {
        SocketChannel channel = SocketChannelWrapper.unwrap(getSocketChannel());
        if (channel == null) {
            throw new ClosedChannelException();
        }
        boolean isBlockingBefore = channel.isBlocking();
        try {
            Selector selector = Selector.open();
            Throwable th = null;
            try {
                try {
                    channel.configureBlocking(false);
                    channel.register(selector, 4);
                    boolean z = 0 != selector.select(timeoutMillis);
                    if (selector != null) {
                        if (0 != 0) {
                            try {
                                selector.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            selector.close();
                        }
                    }
                    return z;
                } finally {
                }
            } finally {
            }
        } finally {
            channel.configureBlocking(isBlockingBefore);
        }
    }
}
