package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import oracle.jdbc.driver.DMSFactory;

/* compiled from: TcpNTAdapter.java */
/* loaded from: ojdbc8.jar:oracle/net/nt/MetricsEnabledInputStream.class */
class MetricsEnabledInputStream extends InputStream {
    DMSFactory.DMSPhase waitEvent;
    InputStream realStream;

    MetricsEnabledInputStream(InputStream is, DMSFactory.DMSPhase pe) {
        this.waitEvent = pe;
        this.realStream = is;
    }

    /* JADX WARN: Finally extract failed */
    @Override // java.io.InputStream
    public int read() throws IOException {
        long token = 0;
        boolean eventStarted = false;
        try {
            if (this.realStream.available() == 0) {
                token = this.waitEvent.start();
                eventStarted = true;
            }
            int ret = this.realStream.read();
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            return ret;
        } catch (Throwable th) {
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            throw th;
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // java.io.InputStream
    public int read(byte[] b) throws IOException {
        long token = 0;
        boolean eventStarted = false;
        try {
            if (this.realStream.available() < b.length) {
                token = this.waitEvent.start();
                eventStarted = true;
            }
            int ret = this.realStream.read(b);
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            return ret;
        } catch (Throwable th) {
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            throw th;
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // java.io.InputStream
    public int read(byte[] b, int off, int len) throws IOException {
        long token = 0;
        boolean eventStarted = false;
        try {
            if (this.realStream.available() < len) {
                token = this.waitEvent.start();
                eventStarted = true;
            }
            int ret = this.realStream.read(b, off, len);
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            return ret;
        } catch (Throwable th) {
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            throw th;
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // java.io.InputStream
    public long skip(long n) throws IOException {
        long token = 0;
        boolean eventStarted = false;
        try {
            if (this.realStream.available() < n) {
                token = this.waitEvent.start();
                eventStarted = true;
            }
            long ret = this.realStream.skip(n);
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            return ret;
        } catch (Throwable th) {
            if (eventStarted) {
                this.waitEvent.stop(token);
            }
            throw th;
        }
    }

    @Override // java.io.InputStream
    public int available() throws IOException {
        return this.realStream.available();
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        this.realStream.close();
    }

    @Override // java.io.InputStream
    public void mark(int readlimit) {
        this.realStream.mark(readlimit);
    }

    @Override // java.io.InputStream
    public void reset() throws IOException {
        this.realStream.reset();
    }

    @Override // java.io.InputStream
    public boolean markSupported() {
        return this.realStream.markSupported();
    }
}
