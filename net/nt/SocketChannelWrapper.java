package oracle.net.nt;

import java.io.IOException;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketException;
import java.net.SocketOption;
import java.nio.ByteBuffer;
import java.nio.channels.ClosedChannelException;
import java.nio.channels.NetworkChannel;
import java.nio.channels.SocketChannel;
import java.util.Set;
import java.util.function.Consumer;
import oracle.jdbc.diagnostics.Diagnosable;

/* loaded from: ojdbc8.jar:oracle/net/nt/SocketChannelWrapper.class */
public abstract class SocketChannelWrapper extends SocketChannel implements Diagnosable {
    protected final Diagnosable diagnosable;
    protected SocketChannel socketChannel;
    protected int bufferSize;
    protected boolean blockingReadMode;
    static final /* synthetic */ boolean $assertionsDisabled;

    abstract void disconnect() throws IOException;

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public /* bridge */ /* synthetic */ NetworkChannel setOption(SocketOption socketOption, Object obj) throws IOException {
        return setOption((SocketOption<SocketOption>) socketOption, (SocketOption) obj);
    }

    static {
        $assertionsDisabled = !SocketChannelWrapper.class.desiredAssertionStatus();
    }

    SocketChannelWrapper(SocketChannel socketChannel, Diagnosable diagnosable) {
        super(socketChannel != null ? socketChannel.provider() : null);
        this.socketChannel = null;
        this.bufferSize = 8192;
        this.blockingReadMode = true;
        this.socketChannel = socketChannel;
        this.diagnosable = diagnosable;
    }

    public final int readNow(ByteBuffer buffer) throws IOException {
        setBlockingReadMode(false);
        try {
            int readBytes = read(buffer);
            setBlockingReadMode(true);
            return readBytes;
        } catch (Throwable th) {
            setBlockingReadMode(true);
            throw th;
        }
    }

    protected final void setBlockingReadMode(boolean status) {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            ((SocketChannelWrapper) this.socketChannel).setBlockingReadMode(status);
        }
        this.blockingReadMode = status;
    }

    public SocketChannel getUnderlyingChannel() {
        return this.socketChannel;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    public void setBufferSize(int newBufferSize) {
        this.bufferSize = newBufferSize;
    }

    void setUnderlyingChannel(SocketChannel socketChannel) {
        this.socketChannel = socketChannel;
    }

    void setSoTimeout(int soTimeout) throws SocketException {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            ((SocketChannelWrapper) this.socketChannel).setSoTimeout(soTimeout);
            return;
        }
        throw new UnsupportedOperationException();
    }

    int getSoTimeout() {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            return ((SocketChannelWrapper) this.socketChannel).getSoTimeout();
        }
        throw new UnsupportedOperationException();
    }

    void registerForNonBlockingRead(Consumer<Throwable> onReadReady) throws IOException {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            ((SocketChannelWrapper) this.socketChannel).registerForNonBlockingRead(onReadReady);
            return;
        }
        throw new UnsupportedOperationException();
    }

    void registerForNonBlockingWrite(Consumer<Throwable> onWriteReady) throws IOException {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            ((SocketChannelWrapper) this.socketChannel).registerForNonBlockingWrite(onWriteReady);
            return;
        }
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.NetworkChannel
    public <T> T getOption(SocketOption<T> socketOption) throws IOException {
        return (T) requireOpenChannel().getOption(socketOption);
    }

    @Override // java.nio.channels.NetworkChannel
    public Set<SocketOption<?>> supportedOptions() {
        return requireNonNullChannel().supportedOptions();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public SocketChannel bind(SocketAddress local) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public <T> SocketChannel setOption(SocketOption<T> name, T value) throws IOException {
        return requireOpenChannel().setOption((SocketOption<SocketOption<T>>) name, (SocketOption<T>) value);
    }

    @Override // java.nio.channels.SocketChannel
    public SocketChannel shutdownInput() throws IOException {
        return requireOpenChannel().shutdownInput();
    }

    @Override // java.nio.channels.SocketChannel
    public SocketChannel shutdownOutput() throws IOException {
        return requireOpenChannel().shutdownOutput();
    }

    @Override // java.nio.channels.SocketChannel
    public Socket socket() {
        return requireNonNullChannel().socket();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean isConnected() {
        return this.socketChannel != null && this.socketChannel.isConnected();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean isConnectionPending() {
        return requireNonNullChannel().isConnectionPending();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean connect(SocketAddress remote) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel
    public boolean finishConnect() throws IOException {
        return requireOpenChannel().finishConnect();
    }

    @Override // java.nio.channels.SocketChannel
    public SocketAddress getRemoteAddress() throws IOException {
        return requireOpenChannel().getRemoteAddress();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ScatteringByteChannel
    public long read(ByteBuffer[] dsts, int offset, int length) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.GatheringByteChannel
    public long write(ByteBuffer[] srcs, int offset, int length) throws IOException {
        throw new UnsupportedOperationException();
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.NetworkChannel
    public SocketAddress getLocalAddress() throws IOException {
        return requireOpenChannel().getLocalAddress();
    }

    @Override // java.nio.channels.spi.AbstractSelectableChannel
    protected void implCloseSelectableChannel() throws IOException {
        requireOpenChannel().close();
    }

    @Override // java.nio.channels.spi.AbstractSelectableChannel
    protected void implConfigureBlocking(boolean block) throws IOException {
        requireOpenChannel().configureBlocking(block);
    }

    protected void enqueueAllWrites(boolean isEnabled) {
        if (!$assertionsDisabled && !(this.socketChannel instanceof SocketChannelWrapper)) {
            throw new AssertionError("Not a SocketChannelWrapper: " + this.socketChannel);
        }
        ((SocketChannelWrapper) this.socketChannel).enqueueAllWrites(isEnabled);
    }

    protected boolean getEnqueueAllWrites() {
        if (this.socketChannel instanceof SocketChannelWrapper) {
            return ((SocketChannelWrapper) this.socketChannel).getEnqueueAllWrites();
        }
        return false;
    }

    protected void completeWrites() throws IOException {
        if (!$assertionsDisabled && !(this.socketChannel instanceof SocketChannelWrapper)) {
            throw new AssertionError("Not a SocketChannelWrapper: " + this.socketChannel);
        }
        ((SocketChannelWrapper) this.socketChannel).completeWrites();
    }

    protected void enqueueBlockedWrites(boolean isEnabled) {
        if (!$assertionsDisabled && !(this.socketChannel instanceof SocketChannelWrapper)) {
            throw new AssertionError("Not a SocketChannelWrapper: " + this.socketChannel);
        }
        ((SocketChannelWrapper) this.socketChannel).enqueueBlockedWrites(isEnabled);
    }

    protected boolean completeBlockedWrites() throws IOException {
        if ($assertionsDisabled || (this.socketChannel instanceof SocketChannelWrapper)) {
            return ((SocketChannelWrapper) this.socketChannel).completeBlockedWrites();
        }
        throw new AssertionError("Not a SocketChannelWrapper: " + this.socketChannel);
    }

    protected SocketChannel requireOpenChannel() throws ClosedChannelException {
        SocketChannel socketChannel = this.socketChannel;
        if (socketChannel == null) {
            throw new ClosedChannelException();
        }
        return socketChannel;
    }

    private SocketChannel requireNonNullChannel() throws IllegalStateException {
        try {
            return requireOpenChannel();
        } catch (ClosedChannelException closedChannelException) {
            throw new IllegalStateException("Socket is closed.", closedChannelException);
        }
    }

    static ByteBuffer copy(ByteBuffer buffer, int length) {
        if (length < 1) {
            return null;
        }
        int origPosition = buffer.position();
        try {
            byte[] copyBytes = new byte[length];
            buffer.position(buffer.position() - length);
            buffer.get(copyBytes);
            ByteBuffer byteBufferWrap = ByteBuffer.wrap(copyBytes);
            buffer.position(origPosition);
            return byteBufferWrap;
        } catch (Throwable th) {
            buffer.position(origPosition);
            throw th;
        }
    }

    static SocketChannel unwrap(SocketChannel socketChannel) {
        SocketChannel underlyingChannel = socketChannel;
        while (true) {
            SocketChannel unwrapped = underlyingChannel;
            if (unwrapped instanceof SocketChannelWrapper) {
                underlyingChannel = ((SocketChannelWrapper) unwrapped).getUnderlyingChannel();
            } else {
                return unwrapped;
            }
        }
    }
}
