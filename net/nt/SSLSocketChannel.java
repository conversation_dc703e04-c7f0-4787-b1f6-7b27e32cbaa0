package oracle.net.nt;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.security.cert.X509Certificate;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.logging.Level;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLEngineResult;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import javax.net.ssl.SSLSession;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/SSLSocketChannel.class */
public class SSLSocketChannel extends SocketChannelWrapper {
    private static final String CLASS_NAME;
    private final SSLEngine sslEngine;
    private ByteBuffer localUnwrapBuffer;
    private ByteBuffer readBuffer;
    private ByteBuffer writeBuffer;
    private boolean isClosed;
    private boolean isHandshakeDone;
    private final ByteBuffer EMPTY_BUFFER;
    private final boolean isRenegotiating;
    private final DNVerifier dnVerifier;
    private SecurityInformation.DNMatchStatus dnMatchStatus;
    private final ExtendedSSLContext extendedSSLContext;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !SSLSocketChannel.class.desiredAssertionStatus();
        CLASS_NAME = SSLSocketChannel.class.getName();
    }

    public SSLSocketChannel(SocketChannel channel, SSLEngine engine, Diagnosable diagnosable, DNVerifier dnVerifier, boolean isRenegotiating, ExtendedSSLContext ctx) throws IOException {
        super(channel, diagnosable);
        this.isClosed = false;
        this.isHandshakeDone = false;
        this.EMPTY_BUFFER = ByteBuffer.allocate(0);
        this.dnMatchStatus = SecurityInformation.DNMatchStatus.NOT_VERIFIED;
        this.socketChannel = channel;
        this.sslEngine = engine;
        this.dnVerifier = dnVerifier;
        this.isRenegotiating = isRenegotiating;
        this.extendedSSLContext = ctx;
        initializeBuffers();
    }

    public SSLSocketChannel(SocketChannel channel, SSLEngine engine, Diagnosable diagnosable) throws IOException {
        this(channel, engine, diagnosable, null, false, null);
    }

    public SecurityInformation.DNMatchStatus getDnMatchStatus() {
        return this.dnMatchStatus;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ReadableByteChannel
    public int read(ByteBuffer dstBuffer) throws IOException {
        int readBytesCount;
        if (isClosed()) {
            return -1;
        }
        if (dstBuffer == null || !dstBuffer.hasRemaining()) {
            return 0;
        }
        if (!this.isHandshakeDone) {
            doSSLHandshake();
        }
        if (this.localUnwrapBuffer.hasRemaining()) {
            readBytesCount = readFromLocalUnwrapBuffer(dstBuffer);
        } else {
            int dstBufferInitialPosition = dstBuffer.position();
            fillAndUnwrap(dstBuffer);
            readBytesCount = dstBuffer.position() - dstBufferInitialPosition;
        }
        return readBytesCount;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.WritableByteChannel
    public int write(ByteBuffer srcBuffer) throws IOException {
        if (isClosed()) {
            throw new NetException(NetException.SOCKET_CLOSED_ERR);
        }
        if (!this.isHandshakeDone) {
            doSSLHandshake();
        }
        if (!writeToSocket()) {
            return 0;
        }
        int initialPosition = srcBuffer.position();
        wrapAndWriteToSocket(srcBuffer);
        if (this.writeBuffer.hasRemaining() && !srcBuffer.hasRemaining()) {
            boolean flushSuccessful = false;
            for (int attemptCount = 0; !flushSuccessful && attemptCount < 10; attemptCount++) {
                flushSuccessful = writeToSocket();
            }
            if (!flushSuccessful) {
                throw new IOException("Unable to write to the socket");
            }
        }
        return srcBuffer.position() - initialPosition;
    }

    private void wrapAndWriteToSocket(ByteBuffer srcBuffer) throws IOException {
        boolean wrapSuccessful = false;
        this.writeBuffer.clear();
        while (srcBuffer.hasRemaining()) {
            SSLEngineResult result = wrap(srcBuffer);
            if (result.getStatus() == SSLEngineResult.Status.OK) {
                wrapSuccessful = true;
                if (result.getHandshakeStatus() == SSLEngineResult.HandshakeStatus.NEED_TASK) {
                    runTasks();
                }
            } else if (result.getStatus() == SSLEngineResult.Status.BUFFER_OVERFLOW) {
                if (wrapSuccessful) {
                    if (!flushWriteBuffer()) {
                        break;
                    } else {
                        this.writeBuffer.clear();
                    }
                } else {
                    throw new IOException("Write error '" + result.getStatus() + '\'');
                }
            } else {
                shutdown();
                throw new IOException("Write error '" + result.getStatus() + '\'');
            }
        }
        flushWriteBuffer();
    }

    private boolean flushWriteBuffer() throws IOException {
        this.writeBuffer.flip();
        if (writeToSocket()) {
            return true;
        }
        return false;
    }

    public boolean hasRemaining() {
        return this.readBuffer.hasRemaining() || this.localUnwrapBuffer.hasRemaining();
    }

    private boolean fillAndUnwrap(ByteBuffer dstBuffer) throws IOException {
        SSLEngineResult wrapResult = null;
        boolean useLocalUnwrapBuffer = false;
        boolean readFromSocket = !this.readBuffer.hasRemaining();
        while (true) {
            if (wrapResult == null || wrapResult.getStatus() != SSLEngineResult.Status.OK) {
                if (readFromSocket) {
                    if (fillReadBuffer()) {
                        readFromSocket = false;
                    } else {
                        return false;
                    }
                }
                if (useLocalUnwrapBuffer) {
                    wrapResult = unwrapToLocalBuffer();
                } else {
                    wrapResult = unwrapData(dstBuffer);
                }
                if (wrapResult.getStatus() == SSLEngineResult.Status.BUFFER_OVERFLOW) {
                    if (useLocalUnwrapBuffer) {
                        throw new IOException("Read error '" + wrapResult.getStatus() + '\'');
                    }
                    useLocalUnwrapBuffer = true;
                    readFromSocket = false;
                } else if (wrapResult.getStatus() == SSLEngineResult.Status.BUFFER_UNDERFLOW) {
                    readFromSocket = true;
                }
            } else {
                boolean unwrapSuccessful = wrapResult != null && wrapResult.getStatus() == SSLEngineResult.Status.OK;
                if (unwrapSuccessful && useLocalUnwrapBuffer) {
                    readFromLocalUnwrapBuffer(dstBuffer);
                }
                return unwrapSuccessful;
            }
        }
    }

    private SSLEngineResult unwrapToLocalBuffer() throws IOException {
        if (!$assertionsDisabled && this.localUnwrapBuffer.hasRemaining()) {
            throw new AssertionError();
        }
        this.localUnwrapBuffer.clear();
        SSLEngineResult result = unwrapData(this.localUnwrapBuffer);
        this.localUnwrapBuffer.flip();
        return result;
    }

    private boolean fillReadBuffer() throws IOException {
        if (this.readBuffer.hasRemaining()) {
            this.readBuffer.compact();
        } else {
            this.readBuffer.clear();
        }
        int readBytes = readFromSocket();
        this.readBuffer.flip();
        return readBytes > 0;
    }

    private SSLEngineResult unwrapData(ByteBuffer dstBuffer) throws IOException {
        SSLEngineResult result = unwrap(this.readBuffer, dstBuffer);
        if (result.getStatus() == SSLEngineResult.Status.CLOSED) {
            shutdown();
            throw new IOException("Read error '" + result.getStatus() + '\'');
        }
        if (result.getHandshakeStatus() == SSLEngineResult.HandshakeStatus.NEED_TASK) {
            runTasks();
        }
        return result;
    }

    private void shutdown() throws IOException {
        if (this.isClosed) {
            return;
        }
        this.isClosed = true;
        try {
            if (!this.sslEngine.isOutboundDone()) {
                this.sslEngine.closeOutbound();
                this.writeBuffer.clear();
                wrap(this.EMPTY_BUFFER);
                this.writeBuffer.flip();
                writeToSocket();
            }
        } catch (IOException e) {
        }
        closeUnderlyingChannel();
    }

    private void closeUnderlyingChannel() {
        try {
            if (this.socketChannel instanceof SocketChannelWrapper) {
                ((SocketChannelWrapper) this.socketChannel).disconnect();
            } else {
                this.socketChannel.close();
            }
        } catch (Exception e) {
        }
    }

    private int readFromLocalUnwrapBuffer(ByteBuffer dstBuffer) {
        if (!this.localUnwrapBuffer.hasRemaining()) {
            return 0;
        }
        int bytesToCopy = Math.min(this.localUnwrapBuffer.remaining(), dstBuffer.remaining());
        for (int i = 0; i < bytesToCopy; i++) {
            dstBuffer.put(this.localUnwrapBuffer.get());
        }
        return bytesToCopy;
    }

    private void initializeBuffers() throws SSLException {
        SSLSession sslSession = this.sslEngine.getSession();
        this.localUnwrapBuffer = ByteBuffer.allocate(sslSession.getApplicationBufferSize());
        this.readBuffer = ByteBuffer.allocate(sslSession.getPacketBufferSize());
        this.writeBuffer = ByteBuffer.allocate(sslSession.getPacketBufferSize());
        this.localUnwrapBuffer.limit(0);
        this.readBuffer.limit(0);
        this.writeBuffer.limit(0);
    }

    private void doSSLHandshake() throws IOException {
        if (this.isHandshakeDone) {
            return;
        }
        boolean isEnqueueAllWrites = getEnqueueAllWrites();
        Metrics.ConnectionEvent event = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_BEGIN_HANDSHAKE_RENEGOTIATION : Metrics.ConnectionEvent.SSL_BEGIN_HANDSHAKE;
        if (isEnqueueAllWrites) {
            enqueueAllWrites(false);
        }
        begin(event);
        this.sslEngine.beginHandshake();
        SSLEngineResult.HandshakeStatus handShakeStatus = this.sslEngine.getHandshakeStatus();
        end(event);
        Metrics.ConnectionEvent event1 = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGOTIATION : Metrics.ConnectionEvent.SSL_HANDSHAKE;
        begin(event1);
        while (!this.isHandshakeDone && !this.isClosed) {
            switch (AnonymousClass1.$SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[handShakeStatus.ordinal()]) {
                case 1:
                    handShakeStatus = runTasks();
                    break;
                case 2:
                    handShakeStatus = unwrapHandshakeMessage();
                    break;
                case 3:
                    handShakeStatus = wrapHandshakeMessage();
                    break;
                case 4:
                    this.isHandshakeDone = true;
                    end(event1);
                    if (this.dnVerifier != null && !this.dnVerifier.isWeakDNMatchAllowed()) {
                        verifyDN();
                        break;
                    } else {
                        break;
                    }
                default:
                    throw new IllegalStateException("Unexpected handshake status '" + handShakeStatus + '\'');
            }
        }
        if (isEnqueueAllWrites) {
            enqueueAllWrites(true);
        }
    }

    /* renamed from: oracle.net.nt.SSLSocketChannel$1, reason: invalid class name */
    /* loaded from: ojdbc8.jar:oracle/net/nt/SSLSocketChannel$1.class */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus = new int[SSLEngineResult.HandshakeStatus.values().length];

        static {
            try {
                $SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[SSLEngineResult.HandshakeStatus.NEED_TASK.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[SSLEngineResult.HandshakeStatus.NEED_UNWRAP.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[SSLEngineResult.HandshakeStatus.NEED_WRAP.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[SSLEngineResult.HandshakeStatus.FINISHED.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    CompletionStage<Void> doSSLHandshakeAsync(Executor asyncExecutor) throws SSLException {
        if (this.isHandshakeDone) {
            return CompletionStageUtil.VOID_COMPLETED_FUTURE;
        }
        try {
            this.sslEngine.beginHandshake();
            return chainAsyncHandshakeIO(asyncExecutor);
        } catch (SSLException beginHandshakeFailure) {
            return CompletionStageUtil.failedStage(beginHandshakeFailure);
        }
    }

    private CompletionStage<Void> chainAsyncHandshakeIO(Executor asyncExecutor) {
        SSLEngineResult.HandshakeStatus handshakeStatus = this.sslEngine.getHandshakeStatus();
        while (true) {
            if (handshakeStatus != SSLEngineResult.HandshakeStatus.NEED_UNWRAP || this.readBuffer.hasRemaining()) {
                if (this.isClosed) {
                    return CompletionStageUtil.VOID_COMPLETED_FUTURE;
                }
                try {
                    switch (AnonymousClass1.$SwitchMap$javax$net$ssl$SSLEngineResult$HandshakeStatus[handshakeStatus.ordinal()]) {
                        case 1:
                            handshakeStatus = runTasks();
                            break;
                        case 2:
                            handshakeStatus = unwrapHandshakeMessage();
                            break;
                        case 3:
                            handshakeStatus = wrapHandshakeMessage();
                            break;
                        case 4:
                            this.isHandshakeDone = true;
                            if (this.dnVerifier != null && !this.dnVerifier.isWeakDNMatchAllowed()) {
                                verifyDN();
                            }
                            return CompletionStageUtil.VOID_COMPLETED_FUTURE;
                        default:
                            return CompletionStageUtil.failedStage(new IllegalStateException("Unexpected handshake status '" + handshakeStatus + '\''));
                    }
                } catch (IOException handshakeFailure) {
                    return CompletionStageUtil.failedStage(handshakeFailure);
                }
            } else {
                CompletableFuture<Void> ioFuture = new CompletableFuture<>();
                try {
                    registerForNonBlockingRead(error -> {
                        asyncExecutor.execute(() -> {
                            if (error == null) {
                                ioFuture.complete(null);
                            } else {
                                ioFuture.completeExceptionally(error);
                            }
                        });
                    });
                    return ioFuture.thenCompose((Function<? super Void, ? extends CompletionStage<U>>) CompletionStageUtil.normalCompletionHandler(nil -> {
                        SSLEngineResult.HandshakeStatus status = unwrapHandshakeMessage();
                        return status == SSLEngineResult.HandshakeStatus.FINISHED ? CompletionStageUtil.VOID_COMPLETED_FUTURE : chainAsyncHandshakeIO(asyncExecutor);
                    }));
                } catch (IOException registerFailure) {
                    return CompletionStageUtil.failedStage(registerFailure);
                }
            }
        }
    }

    private SSLEngineResult.HandshakeStatus wrapHandshakeMessage() throws IOException {
        this.writeBuffer.clear();
        Metrics.ConnectionEvent event = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGO_ROUND_TRIP_WRAP : Metrics.ConnectionEvent.SSL_HS_ROUND_TRIP_WRAP;
        begin(event);
        SSLEngineResult result = wrap(this.EMPTY_BUFFER);
        end(event);
        SSLEngineResult.HandshakeStatus handShakeStatus = result.getHandshakeStatus();
        if (result.getStatus() != SSLEngineResult.Status.OK) {
            throw new IOException("Handshake failed : " + result.getStatus());
        }
        this.writeBuffer.flip();
        Metrics.ConnectionEvent event1 = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGO_ROUND_TRIP_SEND : Metrics.ConnectionEvent.SSL_HS_ROUND_TRIP_SEND;
        begin(event1);
        writeToSocket();
        end(event1);
        return handShakeStatus;
    }

    private SSLEngineResult.HandshakeStatus unwrapHandshakeMessage() throws IOException {
        SSLEngineResult result;
        SSLEngineResult.HandshakeStatus handShakeStatus;
        Metrics.ConnectionEvent event = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGO_ROUND_TRIP_RECEIVE : Metrics.ConnectionEvent.SSL_HS_ROUND_TRIP_RECEIVE;
        begin(event);
        if (!this.readBuffer.hasRemaining()) {
            this.readBuffer.clear();
            while (readFromSocket() == 0) {
            }
            this.readBuffer.flip();
        }
        end(event);
        do {
            this.localUnwrapBuffer.clear();
            Metrics.ConnectionEvent event1 = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGO_ROUND_TRIP_UNWRAP : Metrics.ConnectionEvent.SSL_HS_ROUND_TRIP_UNWRAP;
            begin(event1);
            result = unwrap(this.readBuffer, this.localUnwrapBuffer);
            end(event1);
            handShakeStatus = result.getHandshakeStatus();
            if (result.getStatus() == SSLEngineResult.Status.OK) {
                this.readBuffer.compact();
                this.readBuffer.flip();
                this.localUnwrapBuffer.flip();
            } else if (result.getStatus() == SSLEngineResult.Status.BUFFER_UNDERFLOW) {
                this.readBuffer.compact();
                if (this.readBuffer.position() == this.readBuffer.capacity()) {
                    throw new IOException("Handshake failed : SSL packet is too big to hold in the read buffer");
                }
                while (readFromSocket() == 0) {
                }
                this.readBuffer.flip();
            } else {
                throw new IOException("Handshake failed : " + result.getStatus());
            }
        } while (result.getStatus() != SSLEngineResult.Status.OK);
        return handShakeStatus;
    }

    private SSLEngineResult unwrap(ByteBuffer srcBuffer, ByteBuffer dstBuffer) throws IOException {
        SSLEngineResult result;
        try {
            if (isEmptyTLSPacket(srcBuffer)) {
                result = new SSLEngineResult(SSLEngineResult.Status.OK, SSLEngineResult.HandshakeStatus.NOT_HANDSHAKING, 5, 0);
            } else {
                result = this.sslEngine.unwrap(srcBuffer, dstBuffer);
            }
            SSLEngineResult sSLEngineResult = result;
            tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unwrap", "SSLEngineResult=[{0}]", "SSLEngineResult=[{0}]\n{1}", null, () -> {
                if (isSensitiveEnabled()) {
                    return new Object[]{sSLEngineResult, Parameter.arg(Format.Style.PACKET_DUMP, copy(dstBuffer, sSLEngineResult.bytesProduced()), 0, sSLEngineResult.bytesProduced())};
                }
                return new Object[]{sSLEngineResult};
            });
            return result;
        } catch (SSLHandshakeException e) {
            throw handshakeFailure(e);
        } catch (Exception e2) {
            throw ((IOException) new IOException("IO Error " + e2.getMessage()).initCause(e2));
        }
    }

    private NetException handshakeFailure(SSLHandshakeException e) {
        if (this.extendedSSLContext != null && this.extendedSSLContext.getPemKeyStore() != null && this.extendedSSLContext.getPemKeyStore().getPrivateKeyCount() > 1) {
            return (NetException) new NetException(NetException.INVALID_PEM_PRIVATE_KEY_INDEX).initCause(e);
        }
        return (NetException) new NetException(NetException.SSL_HANDSHAKE_FAILURE, e.getMessage()).initCause(e);
    }

    private boolean isEmptyTLSPacket(ByteBuffer srcBuffer) {
        if (srcBuffer.remaining() >= 5) {
            int initialPosition = srcBuffer.position();
            byte recordType = srcBuffer.get();
            srcBuffer.getShort();
            short length = srcBuffer.getShort();
            if (recordType == 23 && length == 0) {
                return true;
            }
            srcBuffer.position(initialPosition);
            return false;
        }
        return false;
    }

    private SSLEngineResult wrap(ByteBuffer srcBuffer) throws IOException {
        try {
            SSLEngineResult result = this.sslEngine.wrap(srcBuffer, this.writeBuffer);
            tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "wrap", "SSLEngineResult=[{0}]", "SSLEngineResult=[{0}]\n{1}", null, () -> {
                if (isSensitiveEnabled()) {
                    return new Object[]{result, Parameter.arg(Format.Style.PACKET_DUMP, copy(srcBuffer, result.bytesConsumed()), 0, result.bytesConsumed())};
                }
                return new Object[]{result};
            });
            return result;
        } catch (Exception e) {
            throw ((IOException) new IOException("IO Error " + e.getMessage()).initCause(e));
        }
    }

    private int readFromSocket() throws IOException {
        if (!this.readBuffer.hasRemaining()) {
            throw new IOException("IO Error : No room left in the read buffer");
        }
        try {
            int bytesRead = this.socketChannel.read(this.readBuffer);
            if (bytesRead < 0) {
                throw new IOException("Connection closed");
            }
            return bytesRead;
        } catch (IOException x) {
            try {
                if (!this.sslEngine.isInboundDone()) {
                    this.sslEngine.closeInbound();
                }
            } catch (IOException e) {
            }
            shutdown();
            throw x;
        }
    }

    private boolean writeToSocket() throws IOException {
        try {
            this.socketChannel.write(this.writeBuffer);
            return !this.writeBuffer.hasRemaining();
        } catch (IOException x) {
            shutdown();
            throw x;
        }
    }

    private boolean isClosed() throws IOException {
        return this.isClosed || !this.socketChannel.isOpen() || this.socketChannel.socket().isInputShutdown() || this.socketChannel.socket().isOutputShutdown();
    }

    private SSLEngineResult.HandshakeStatus runTasks() throws IOException {
        try {
            Metrics.ConnectionEvent event = this.isRenegotiating ? Metrics.ConnectionEvent.SSL_RENEGO_ROUND_TRIP_RUNTASKS : Metrics.ConnectionEvent.SSL_HS_ROUND_TRIP_RUNTASKS;
            if (!this.isHandshakeDone) {
                begin(event);
            }
            while (true) {
                Runnable runnable = this.sslEngine.getDelegatedTask();
                if (runnable == null) {
                    break;
                }
                runnable.run();
            }
            if (!this.isHandshakeDone) {
                end(event);
            }
            return this.sslEngine.getHandshakeStatus();
        } catch (Exception e) {
            throw ((IOException) new IOException("IO Error " + e.getMessage()).initCause(e));
        }
    }

    void verifyDN() throws IOException {
        if (this.dnVerifier != null) {
            this.dnMatchStatus = this.dnVerifier.verify((X509Certificate) this.sslEngine.getSession().getPeerCertificates()[0]);
        } else {
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "verifyDN", "Server DN verification is disabled and connection is not secure.Enable DN verification through Connection Property 'oracle.net.ssl_server_dn_match' or through URL parameter 'SSL_SERVER_DN_MATCH'", null, null, new Object[0]);
        }
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    void disconnect() throws IOException {
        if (this.isClosed) {
            return;
        }
        shutdown();
    }

    public String toString() {
        return "SSLSocketChannel[" + socket().toString() + "]";
    }
}
