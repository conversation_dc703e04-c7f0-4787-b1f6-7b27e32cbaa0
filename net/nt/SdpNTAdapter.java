package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.nio.channels.SocketChannel;
import java.util.Hashtable;
import java.util.Properties;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.ns.SQLnetDef;
import oracle.net.nt.NTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/nt/SdpNTAdapter.class */
public class SdpNTAdapter extends AbstractAdapter {
    static final boolean DEBUG = false;
    static final String SDP_SOCKET_CLASS_NAME = "com.oracle.net.Sdp";
    private SocketChannel socketChannel;
    private InetSocketAddress inetSocketAddress;
    int count;
    int attempts;
    protected Socket socket;
    protected int sockTimeout;
    protected Properties socketOptions;
    static Method OPEN_SOCKET = null;
    static Method OPEN_SOCKET_CHANNEL = null;
    private static Hashtable<String, InetAddress[]> inetaddressesCache = new Hashtable<>();
    private static Hashtable<String, Integer> circularOffsets = new Hashtable<>();
    private static final Monitor CIRCULAR_OFFSETS_MONITOR = Monitor.newInstance();

    @Override // oracle.net.nt.AbstractAdapter
    public /* bridge */ /* synthetic */ String toString() {
        return super.toString();
    }

    private static Socket getSDPSocket() throws ClassNotFoundException, IOException {
        if (OPEN_SOCKET == null) {
            try {
                Class sdp = Class.forName(SDP_SOCKET_CLASS_NAME);
                OPEN_SOCKET = sdp.getMethod("openSocket", new Class[0]);
            } catch (ClassNotFoundException ex) {
                throw new IOException("SDP enabled, but SDP socket class not in classpath", ex);
            } catch (NoSuchMethodException ex2) {
                throw new IOException("SDP enabled but unable to get SDP socket class", ex2);
            }
        }
        try {
            return (Socket) OPEN_SOCKET.invoke(null, new Object[0]);
        } catch (IllegalAccessException ex3) {
            throw new IOException("SDP enabled, but SDP.openSocket could not be accessed", ex3);
        } catch (InvocationTargetException ex4) {
            throw new IOException("SDP enabled, but SDP.openSocket raised an exception", ex4);
        }
    }

    private static SocketChannel getSDPSocketChannel() throws ClassNotFoundException, IOException {
        if (OPEN_SOCKET_CHANNEL == null) {
            try {
                Class sdp = Class.forName(SDP_SOCKET_CLASS_NAME);
                OPEN_SOCKET_CHANNEL = sdp.getMethod("openSocketChannel", new Class[0]);
            } catch (ClassNotFoundException ex) {
                throw new IOException("SDP enabled, but SDP socket class not in classpath", ex);
            } catch (NoSuchMethodException ex2) {
                throw new IOException("SDP enabled but unable to get SDP socket class", ex2);
            }
        }
        try {
            return (SocketChannel) OPEN_SOCKET_CHANNEL.invoke(null, new Object[0]);
        } catch (IllegalAccessException ex3) {
            throw new IOException("SDP enabled, but SDP.openSocket could not be accessed", ex3);
        } catch (InvocationTargetException ex4) {
            throw new IOException("SDP enabled, but SDP.openSocket raised an exception", ex4);
        }
    }

    public SdpNTAdapter(String address, ConnOption connOption, @Blind(PropertiesBlinder.class) Properties socketOptions) throws NLException {
        this.socketOptions = socketOptions;
        this.inetSocketAddress = connOption.inetSocketAddress;
        this.host = connOption.host;
        this.port = connOption.port;
    }

    @Override // oracle.net.nt.NTAdapter
    public void connect(DMSFactory.DMSNoun dmsParent) throws IOException {
        String c_timeout = (String) this.socketOptions.get(2);
        Boolean useNio = Boolean.valueOf(Boolean.parseBoolean((String) this.socketOptions.get(20)));
        long socketConnectStartTime = System.currentTimeMillis();
        if (!useNio.booleanValue()) {
            this.socket = getSDPSocket();
        }
        try {
            if (useNio.booleanValue()) {
                this.socketChannel = getSDPSocketChannel();
                this.socket = this.socketChannel.socket();
            }
            this.socket.connect(this.inetSocketAddress, Integer.parseInt(c_timeout));
            setOption(3, c_timeout);
            setSocketOptions();
        } catch (IOException ea) {
            DownHostsCache.getInstance().markDownHost(this.inetSocketAddress.getAddress(), this.port);
            try {
                if (this.socket != null) {
                    this.socket.close();
                }
            } catch (Exception e) {
            }
            String newExMessage = String.format("%s, socket connect lapse %d ms. %s %d %s %d %s", ea.getMessage(), Long.valueOf(System.currentTimeMillis() - socketConnectStartTime), this.inetSocketAddress.getHostString(), Integer.valueOf(this.port), c_timeout, Integer.valueOf(this.count), useNio);
            IOException newEx = new IOException(newExMessage, ea);
            throw newEx;
        }
    }

    public void setSocketOptions() throws IOException {
        String temp = (String) this.socketOptions.get(0);
        if (temp != null) {
            setOption(0, temp);
        }
        String temp2 = (String) this.socketOptions.get(1);
        if (temp2 != null) {
            setOption(1, temp2);
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void disconnect() throws IOException {
        try {
            if (this.socket != null && !this.socket.isClosed()) {
                this.socket.close();
            }
        } finally {
            this.socket = null;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public InputStream getInputStream() throws IOException {
        return this.socket.getInputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public OutputStream getOutputStream() throws IOException {
        return this.socket.getOutputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public void setOption(int option, Object value) throws IOException {
        switch (option) {
            case 0:
                String tmp = (String) value;
                this.socket.setTcpNoDelay(tmp.equals("YES"));
                break;
            case 1:
                String tmp2 = (String) value;
                if (tmp2.equals("YES")) {
                    this.socket.setKeepAlive(true);
                    break;
                }
                break;
            case 3:
                this.readTimeout = Integer.parseInt((String) value);
                this.socket.setSoTimeout(this.readTimeout);
                break;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public Object getOption(int option) throws IOException {
        switch (option) {
            case 101:
                return "" + this.readTimeout;
            default:
                return null;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void abort() throws IOException {
        try {
            this.socket.setSoLinger(true, 0);
        } catch (Exception e) {
        }
        this.socket.close();
    }

    @Override // oracle.net.nt.NTAdapter
    public void sendUrgentByte(int urgentData) throws IOException {
        this.socket.sendUrgentData(urgentData);
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isCharacteristicUrgentSupported() throws IOException {
        try {
            return !this.socket.getOOBInline();
        } catch (IOException e) {
            return false;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void setReadTimeoutIfRequired(@Blind(PropertiesBlinder.class) Properties prop) throws IOException {
        String tmp = (String) prop.get(SQLnetDef.TCP_READTIMEOUT_STR);
        if (tmp == null) {
            tmp = "0";
        }
        setOption(3, tmp);
    }

    private static final InetAddress[] getAddressesInCircularOrder(String hostname, InetAddress[] inetAddressesFromJVM) {
        Monitor.CloseableLock lock = CIRCULAR_OFFSETS_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                InetAddress[] cachedAddresses = inetaddressesCache.get(hostname);
                Integer offset = circularOffsets.get(hostname);
                if (cachedAddresses == null || !areEquals(cachedAddresses, inetAddressesFromJVM)) {
                    offset = new Integer(0);
                    cachedAddresses = inetAddressesFromJVM;
                    inetaddressesCache.put(hostname, inetAddressesFromJVM);
                    circularOffsets.put(hostname, offset);
                }
                InetAddress[] addrb = getCopyAddresses(cachedAddresses, offset.intValue());
                circularOffsets.put(hostname, new Integer((offset.intValue() + 1) % cachedAddresses.length));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return addrb;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static final boolean areEquals(InetAddress[] add1, InetAddress[] add2) {
        if (add1.length != add2.length) {
            return false;
        }
        for (int i = 0; i < add1.length; i++) {
            if (!add1[i].equals(add2[i])) {
                return false;
            }
        }
        return true;
    }

    private static final InetAddress[] getCopyAddresses(InetAddress[] add, int nbOfRotation) {
        InetAddress[] addcp = new InetAddress[add.length];
        for (int i = 0; i < add.length; i++) {
            addcp[i] = add[(i + nbOfRotation) % add.length];
        }
        return addcp;
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.socket.getKeepAlive();
    }

    @Override // oracle.net.nt.NTAdapter
    public InetAddress getInetAddress() {
        return this.socket.getInetAddress();
    }

    @Override // oracle.net.nt.NTAdapter
    public SocketChannel getSocketChannel() {
        return this.socketChannel;
    }

    @Override // oracle.net.nt.NTAdapter
    public NTAdapter.NetworkAdapterType getNetworkAdapterType() {
        return NTAdapter.NetworkAdapterType.SDP;
    }
}
