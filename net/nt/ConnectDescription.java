package oracle.net.nt;

import java.util.ArrayList;
import java.util.Objects;

/* loaded from: ojdbc8.jar:oracle/net/nt/ConnectDescription.class */
public class ConnectDescription {
    private int delayInMillis;
    private String encryptionClient;
    private String encryptionClientTypes;
    private String checksumClient;
    private String checksumClientTypes;
    private String allowWeakCrypto;
    private String useSNI;
    private String tokenAuth;
    private String tokenLocation;
    private String passwordAuthentication;
    private String ociIamUrl;
    private String ociTenancy;
    private String ociCompartment;
    private String ociDatabase;
    private String ociConfigFile;
    private String ociProfile;
    private String azureDbAppIdUri;
    private String tenantId;
    private String clientId;
    private String clientCertificate;
    private String redirectUri;
    private String walletLocation;
    private String descriptionString;
    private String azureCredentials;
    private int retryCount = 0;
    private int connectTimeout = -1;
    private int transportConnectTimeout = -1;
    private int sdu = 0;
    private int tdu = 0;
    private int expireTime = -1;
    private String connectionIdPrefix = null;
    private String httpsProxy = null;
    private int httpsProxyPort = -1;
    private ArrayList<ConnOption> cOpts = new ArrayList<>(4);

    public String getAllowWeakCrypto() {
        return this.allowWeakCrypto;
    }

    public void setAllowWeakCrypto(String allowWeakCrypto) {
        this.allowWeakCrypto = allowWeakCrypto;
    }

    public String getEncryptionClient() {
        return this.encryptionClient;
    }

    public void setEncryptionClient(String encryptionClient) {
        this.encryptionClient = encryptionClient;
    }

    public String getEncryptionClientTypes() {
        return this.encryptionClientTypes;
    }

    public void setEncryptionClientTypes(String encryptionClientTypes) {
        this.encryptionClientTypes = encryptionClientTypes;
    }

    public String getChecksumClient() {
        return this.checksumClient;
    }

    public void setChecksumClient(String checksumClient) {
        this.checksumClient = checksumClient;
    }

    public String getChecksumClientTypes() {
        return this.checksumClientTypes;
    }

    public void setChecksumClientTypes(String checksumClientTypes) {
        this.checksumClientTypes = checksumClientTypes;
    }

    public void setConnectionIdPrefix(String prefix) {
        this.connectionIdPrefix = prefix;
    }

    public String getConnectionIdPrefix() {
        return this.connectionIdPrefix;
    }

    public void setRetryCount(int value) {
        this.retryCount = value;
    }

    public int getRetryCount() {
        return this.retryCount;
    }

    public void setDelayInMillis(int value) {
        this.delayInMillis = value;
    }

    public int getDelayInMillis() {
        return this.delayInMillis;
    }

    public int getConnectTimeout() {
        return this.connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getTransportConnectTimeout() {
        return this.transportConnectTimeout;
    }

    public void setTransportConnectTimeout(int transportConnectTimeout) {
        this.transportConnectTimeout = transportConnectTimeout;
    }

    public int getSdu() {
        return this.sdu;
    }

    public void setSdu(int sdu) {
        this.sdu = sdu;
    }

    public int getTdu() {
        return this.tdu;
    }

    public void setTdu(int tdu) {
        this.tdu = tdu;
    }

    public void setHttpsProxy(String httpsProxy) {
        this.httpsProxy = httpsProxy;
    }

    public String getHttpsProxy() {
        return this.httpsProxy;
    }

    public void setHttpsProxyPort(int httpsProxyPort) {
        this.httpsProxyPort = httpsProxyPort;
    }

    public int getHttpsProxyPort() {
        return this.httpsProxyPort;
    }

    public void useSNI(String value) {
        this.useSNI = value;
    }

    public String useSNI() {
        return this.useSNI;
    }

    public void addConnectOption(ConnOption co) {
        this.cOpts.add(co);
    }

    public ArrayList<ConnOption> getConnectOptions() {
        return this.cOpts;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public int getExpireTime() {
        return this.expireTime;
    }

    public void setTokenAuthentication(String tokenAuth) {
        this.tokenAuth = tokenAuth;
    }

    public String getTokenAuthentication() {
        return this.tokenAuth;
    }

    public void setTokenLocation(String tokenLocation) {
        this.tokenLocation = tokenLocation;
    }

    public String getTokenLocation() {
        return this.tokenLocation;
    }

    public void setPasswordAuthentication(String passwordAuthentication) {
        this.passwordAuthentication = passwordAuthentication;
    }

    public String getPasswordAuthentication() {
        return this.passwordAuthentication;
    }

    public void setOciIamUrl(String ociIamUrl) {
        this.ociIamUrl = ociIamUrl;
    }

    public String getOciIamUrl() {
        return this.ociIamUrl;
    }

    public void setOciTenancy(String ociTenancy) {
        this.ociTenancy = ociTenancy;
    }

    public String getOciTenancy() {
        return this.ociTenancy;
    }

    public void setOciCompartment(String ociCompartment) {
        this.ociCompartment = ociCompartment;
    }

    public String getOciCompartment() {
        return this.ociCompartment;
    }

    public void setOciDatabase(String ociDatabase) {
        this.ociDatabase = ociDatabase;
    }

    public String getOciDatabase() {
        return this.ociDatabase;
    }

    public void setOciConfigFile(String ociConfigFile) {
        this.ociConfigFile = ociConfigFile;
    }

    public String getOciConfigFile() {
        return this.ociConfigFile;
    }

    public void setOciProfile(String ociProfile) {
        this.ociProfile = ociProfile;
    }

    public String getOciProfile() {
        return this.ociProfile;
    }

    public void setAzureDbAppIdUri(String azureDbAppIdUri) {
        this.azureDbAppIdUri = azureDbAppIdUri;
    }

    public String getAzureDbAppIdUri() {
        return this.azureDbAppIdUri;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return this.tenantId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return this.clientId;
    }

    public void setClientCertificate(String clientCertificate) {
        this.clientCertificate = clientCertificate;
    }

    public String getClientCertificate() {
        return this.clientCertificate;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getRedirectUri() {
        return this.redirectUri;
    }

    public void setWalletLocation(String walletLocation) {
        this.walletLocation = walletLocation;
    }

    public String getWalletLocation() {
        return this.walletLocation;
    }

    public void setDescriptionString(String descriptionString) {
        this.descriptionString = descriptionString;
    }

    public String getDescriptionString() {
        return this.descriptionString;
    }

    public void setAzureCredentials(String azureCredentials) {
        this.azureCredentials = azureCredentials;
    }

    public String getAzureCredentials() {
        return this.azureCredentials;
    }

    public boolean isTokenAuthenticationEqual(ConnectDescription description) {
        return description != null && Objects.equals(this.tokenAuth, description.tokenAuth) && Objects.equals(this.tokenLocation, description.tokenLocation) && Objects.equals(this.passwordAuthentication, description.passwordAuthentication) && Objects.equals(this.ociIamUrl, description.ociIamUrl) && (this.ociIamUrl == null || Objects.equals(this.walletLocation, description.walletLocation)) && Objects.equals(this.ociTenancy, description.ociTenancy) && Objects.equals(this.ociCompartment, description.ociCompartment) && Objects.equals(this.ociDatabase, description.ociDatabase) && Objects.equals(this.ociConfigFile, description.ociConfigFile) && Objects.equals(this.ociProfile, description.ociProfile) && Objects.equals(this.azureDbAppIdUri, description.azureDbAppIdUri) && Objects.equals(this.tenantId, description.tenantId) && Objects.equals(this.clientId, description.clientId) && Objects.equals(this.clientCertificate, description.clientCertificate) && Objects.equals(this.redirectUri, description.redirectUri);
    }
}
