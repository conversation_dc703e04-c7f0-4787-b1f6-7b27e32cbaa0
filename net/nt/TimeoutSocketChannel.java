package oracle.net.nt;

import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Socket;
import java.net.SocketAddress;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.nio.channels.ClosedByInterruptException;
import java.nio.channels.SocketChannel;
import java.util.ArrayDeque;
import java.util.Queue;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.logging.Level;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.net.ns.NetException;
import oracle.net.nt.TimeoutInterruptHandler;

/* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutSocketChannel.class */
class TimeoutSocketChannel extends SocketChannelWrapper {
    private static final String CLASS_NAME;
    private int soTimeout;
    NetStatImpl netStat;
    private final Proxy proxy;
    private final InetSocketAddress serverAddress;
    private Socket socket;
    private InputStream ipStream;
    private OutputStream opStream;
    private boolean isWriteQueueEnabled;
    private boolean enqueueAllWrites;
    private final Queue<ByteBuffer> writeQueue;
    private byte[] tcpFastOpenBytes;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !TimeoutSocketChannel.class.desiredAssertionStatus();
        CLASS_NAME = TimeoutSocketChannel.class.getName();
        TcpFastOpen.init();
    }

    private TimeoutSocketChannel(InetSocketAddress serverAddress, NetStatImpl netStat, Proxy proxy, Diagnosable diagnosable) {
        super(null, diagnosable);
        this.soTimeout = 0;
        this.netStat = null;
        this.socket = null;
        this.ipStream = null;
        this.opStream = null;
        this.isWriteQueueEnabled = false;
        this.enqueueAllWrites = false;
        this.writeQueue = new ArrayDeque(0);
        this.serverAddress = serverAddress;
        this.netStat = netStat;
        this.proxy = proxy;
    }

    public TimeoutSocketChannel(InetSocketAddress serverAddress, int connectTimeout, NetStatImpl netStat, Proxy proxy, Diagnosable diagnosable, byte[] tcpFastOpenBytes) throws NumberFormatException, IOException {
        this(serverAddress, netStat, proxy, diagnosable);
        this.tcpFastOpenBytes = tcpFastOpenBytes;
        try {
            connect(serverAddress, connectTimeout);
        } catch (IOException ioException) {
            disconnect();
            throw ioException;
        }
    }

    private void connect(InetSocketAddress socketAddress, int connectTimeout) throws IOException, NumberFormatException {
        if (this.proxy == null) {
            initializeSocketChannel(this.serverAddress, connectTimeout);
        } else {
            initializeSocketChannel(this.proxy.address(), connectTimeout);
            ProxyHelper.connectViaProxy(this.proxy, this.serverAddress, this);
        }
    }

    private void initializeSocketChannel(SocketAddress remote, int connectTimeoutInMillis) throws IOException {
        System.currentTimeMillis();
        try {
            this.socketChannel = SocketChannel.open();
            this.socketChannel.configureBlocking(true);
            this.socket = this.socketChannel.socket();
            doConnect(remote, connectTimeoutInMillis);
            this.ipStream = this.socket.getInputStream();
            this.opStream = this.socket.getOutputStream();
        } catch (SocketTimeoutException ste) {
            throw newTimeoutException(ste);
        } catch (ClosedByInterruptException e) {
            throw new InterruptedIOException("Socket read interrupted");
        }
    }

    private void doConnect(SocketAddress remote, int connectTimeoutInMillis) throws IOException {
        if (this.tcpFastOpenBytes == null) {
            this.socket.connect(remote, connectTimeoutInMillis);
        } else {
            doTfoConnect(remote, connectTimeoutInMillis);
        }
    }

    private void doTfoConnect(SocketAddress remote, int connectTimeoutInMillis) throws IOException {
        SocketAddress virtualSocketAddress = TcpFastOpen.setTcpFastOpenBytes((InetSocketAddress) remote, this.tcpFastOpenBytes);
        try {
            try {
                this.socket.connect(virtualSocketAddress, connectTimeoutInMillis);
                int sentLength = TcpFastOpen.getBytesSentAndRemove((InetSocketAddress) virtualSocketAddress);
                if (this.socket.isConnected() && sentLength < this.tcpFastOpenBytes.length && sentLength != -1) {
                    this.socket.getOutputStream().write(this.tcpFastOpenBytes, sentLength, this.tcpFastOpenBytes.length - sentLength);
                }
            } catch (Throwable ex) {
                String errorMsg = TcpFastOpen.getErrorMessage((InetSocketAddress) virtualSocketAddress);
                if (errorMsg != null) {
                    throw new IOException(errorMsg, ex);
                }
                throw ex;
            }
        } catch (Throwable th) {
            int sentLength2 = TcpFastOpen.getBytesSentAndRemove((InetSocketAddress) virtualSocketAddress);
            if (this.socket.isConnected() && sentLength2 < this.tcpFastOpenBytes.length && sentLength2 != -1) {
                this.socket.getOutputStream().write(this.tcpFastOpenBytes, sentLength2, this.tcpFastOpenBytes.length - sentLength2);
            }
            throw th;
        }
    }

    static CompletionStage<TimeoutSocketChannel> openAsync(InetSocketAddress serverAddress, int connectTimeout, NetStatImpl netStat, Diagnosable diagnosable, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) {
        TimeoutSocketChannel newChannel = new TimeoutSocketChannel(serverAddress, netStat, null, diagnosable);
        return newChannel.connectAsync(connectTimeout, outboundTimeout, asyncExecutor).thenApply(nil -> {
            return newChannel;
        });
    }

    private final CompletionStage<Void> connectAsync(int connectTimeout, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) throws IOException {
        if (this.proxy != null) {
            return CompletionStageUtil.failedStage(new IOException("Asynchronous proxy connection is not supported"));
        }
        try {
            SocketChannel newChannel = SocketChannel.open();
            outboundTimeout.setChannel(newChannel);
            this.socketChannel = newChannel;
            this.socketChannel.configureBlocking(false);
            this.socket = this.socketChannel.socket();
            if (this.socketChannel.connect(this.serverAddress)) {
                this.socketChannel.configureBlocking(true);
                this.ipStream = this.socket.getInputStream();
                this.opStream = this.socket.getOutputStream();
                return CompletionStageUtil.completedStage(null);
            }
            AsyncConnectTask connectTask = new AsyncConnectTask(asyncExecutor);
            connectTask.start();
            CompletionStage<Void> connectStage = connectTask.getConnectStage();
            if (connectTimeout > 0) {
                connectTask.getClass();
                TimerTask keyCancellingTask = TimeoutInterruptHandler.scheduleTask(() -> {
                    connectTask.setTimeoutExpired();
                }, connectTimeout);
                return connectStage.whenComplete((nil, err) -> {
                    keyCancellingTask.cancel();
                });
            }
            return connectStage;
        } catch (IOException initializationFailure) {
            return CompletionStageUtil.failedStage(initializationFailure);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutSocketChannel$AsyncConnectTask.class */
    private class AsyncConnectTask implements Consumer<Throwable> {
        private final Executor asyncExecutor;
        private final Monitor cancellationLock;
        private final CompletableFuture<Void> connectFuture;
        private boolean isTimeoutExpired;

        private AsyncConnectTask(Executor asyncExecutor) {
            this.cancellationLock = Monitor.newInstance();
            this.connectFuture = new CompletableFuture<>();
            this.isTimeoutExpired = false;
            this.asyncExecutor = asyncExecutor;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void start() {
            try {
                TcpMultiplexer.registerForConnectEvent(TimeoutSocketChannel.this.socketChannel, this);
            } catch (IOException registrationFailure) {
                this.connectFuture.completeExceptionally(registrationFailure);
            }
        }

        @Override // java.util.function.Consumer
        public void accept(Throwable err) {
            this.asyncExecutor.execute(() -> {
                handleReadiness(err);
            });
        }

        private final void handleReadiness(Throwable err) {
            try {
                try {
                    Monitor.CloseableLock lock = this.cancellationLock.acquireCloseableLock();
                    Throwable th = null;
                    if (err != null) {
                        this.connectFuture.completeExceptionally(err);
                    } else if (!this.isTimeoutExpired) {
                        TimeoutSocketChannel.this.socketChannel.configureBlocking(false);
                        if (TimeoutSocketChannel.this.socketChannel.finishConnect()) {
                            TcpMultiplexer.restoreBlockingMode(TimeoutSocketChannel.this.socketChannel);
                            TimeoutSocketChannel.this.ipStream = TimeoutSocketChannel.this.socket.getInputStream();
                            TimeoutSocketChannel.this.opStream = TimeoutSocketChannel.this.socket.getOutputStream();
                            this.connectFuture.complete(null);
                        } else {
                            TcpMultiplexer.registerForConnectEvent(TimeoutSocketChannel.this.socketChannel, this);
                        }
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                } finally {
                }
            } catch (IOException connectFailure) {
                this.connectFuture.completeExceptionally(connectFailure);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public final void setTimeoutExpired() {
            this.isTimeoutExpired = true;
            Monitor.CloseableLock lock = this.cancellationLock.acquireCloseableLock();
            Throwable th = null;
            try {
                TimeoutInterruptHandler.IOReadTimeoutException timeoutException = new TimeoutInterruptHandler.IOReadTimeoutException("Socket connect timed out");
                this.asyncExecutor.execute(() -> {
                    this.connectFuture.completeExceptionally(timeoutException);
                });
                try {
                    TcpMultiplexer.forceCallback(TimeoutSocketChannel.this.socketChannel, timeoutException);
                    TimeoutSocketChannel.this.socketChannel.close();
                } catch (IOException e) {
                    this.connectFuture.completeExceptionally(timeoutException);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public final CompletionStage<Void> getConnectStage() {
            return this.connectFuture;
        }
    }

    void setNetStat(NetStatImpl netStat) {
        this.netStat = netStat;
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    public void disconnect() throws IOException {
        if (this.socketChannel != null && this.socketChannel.isOpen()) {
            try {
                this.socketChannel.close();
            } catch (Exception e) {
            }
        }
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    public void setSoTimeout(int soTimeout) throws SocketException {
        this.soTimeout = soTimeout;
        this.socket.setSoTimeout(soTimeout >= 0 ? soTimeout : 0);
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    public int getSoTimeout() {
        return this.soTimeout;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ReadableByteChannel
    public int read(ByteBuffer dst) throws ExecutionException, InterruptedException, TimeoutException, IOException {
        int bytesRead;
        ensureOpen();
        try {
            if (!this.blockingReadMode) {
                bytesRead = doNonBlockedRead(dst);
            } else if (isRegisteredWithMultiplexer()) {
                bytesRead = doRegisteredRead(dst);
            } else {
                bytesRead = doBlockedRead(dst);
            }
            logRead(dst, bytesRead);
            return bytesRead;
        } catch (IOException e) {
            throw handleIOFailure(e);
        }
    }

    private int doNonBlockedRead(ByteBuffer dst) throws IOException {
        boolean isBlockingBefore = this.socketChannel.isBlocking();
        if (isBlockingBefore) {
            this.socketChannel.configureBlocking(false);
        }
        try {
            int i = this.socketChannel.read(dst);
            if (isBlockingBefore) {
                this.socketChannel.configureBlocking(true);
            }
            return i;
        } catch (Throwable th) {
            if (isBlockingBefore) {
                this.socketChannel.configureBlocking(true);
            }
            throw th;
        }
    }

    private int doBlockedRead(ByteBuffer dst) throws IOException {
        try {
            byte[] readBuffer = dst.array();
            int offset = dst.position();
            int maxLen = dst.remaining();
            int bytesRead = this.ipStream.read(readBuffer, offset, maxLen);
            if (bytesRead >= 0) {
                dst.position(offset + bytesRead);
            }
            return bytesRead;
        } catch (SocketTimeoutException ste) {
            throw newTimeoutException(ste);
        } catch (ClosedByInterruptException e) {
            throw new InterruptedIOException("Socket read interrupted");
        }
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    protected void enqueueAllWrites(boolean isEnabled) {
        this.enqueueAllWrites = isEnabled;
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    protected boolean getEnqueueAllWrites() {
        return this.enqueueAllWrites;
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    protected void completeWrites() throws ExecutionException, InterruptedException, TimeoutException, IOException {
        if (!$assertionsDisabled && !getEnqueueAllWrites()) {
            throw new AssertionError("enqueueAllWrites is false");
        }
        this.enqueueAllWrites = false;
        if (this.writeQueue.isEmpty()) {
            return;
        }
        int writeSize = this.writeQueue.stream().mapToInt((v0) -> {
            return v0.remaining();
        }).sum();
        ByteBuffer writeBuffer = ByteBuffer.allocate(writeSize);
        while (!this.writeQueue.isEmpty()) {
            writeBuffer.put(this.writeQueue.remove());
        }
        writeBuffer.flip();
        while (writeBuffer.hasRemaining()) {
            write(writeBuffer);
        }
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.WritableByteChannel
    public int write(ByteBuffer src) throws ExecutionException, InterruptedException, TimeoutException, IOException {
        int bytesWritten;
        ensureOpen();
        try {
            if (this.enqueueAllWrites) {
                bytesWritten = src.remaining();
                enqueueWrite(src);
            } else if (this.isWriteQueueEnabled) {
                bytesWritten = src.remaining();
                tryNonBlockingWrite(src);
            } else if (isRegisteredWithMultiplexer()) {
                bytesWritten = doRegisteredWrite(src);
            } else {
                bytesWritten = doBlockedWrite(src);
            }
            return bytesWritten;
        } catch (IOException e) {
            throw handleIOFailure(e);
        }
    }

    private void tryNonBlockingWrite(ByteBuffer src) throws IOException {
        while (!this.writeQueue.isEmpty()) {
            ByteBuffer srcBuffer = this.writeQueue.peek();
            int length = this.socketChannel.write(srcBuffer);
            logWrite(srcBuffer, length);
            if (srcBuffer.hasRemaining()) {
                break;
            } else {
                this.writeQueue.remove();
            }
        }
        if (this.writeQueue.isEmpty()) {
            int length2 = this.socketChannel.write(src);
            logWrite(src, length2);
        }
        if (src.hasRemaining()) {
            enqueueWrite(src);
        }
    }

    private int doBlockedWrite(ByteBuffer src) throws IOException {
        try {
            if (src.hasRemaining()) {
                int offset = src.position();
                int length = src.remaining();
                byte[] buffer = src.array();
                this.opStream.write(buffer, offset, length);
                src.position(offset + length);
                logWrite(src, length);
                return length;
            }
            return 0;
        } catch (SocketTimeoutException ste) {
            throw newTimeoutException(ste);
        } catch (ClosedByInterruptException e) {
            throw new InterruptedIOException("Socket write interrupted");
        }
    }

    private void logRead(ByteBuffer dst, int length) throws IOException {
        if (length < 1) {
            return;
        }
        if (this.netStat != null) {
            this.netStat.incrementBytesReceived(length);
        }
        tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readFromSocket", "{0} bytes", "{0} bytes\n{1}", null, () -> {
            if (isSensitiveEnabled()) {
                Parameter dumpBuffer = Parameter.arg(Format.Style.PACKET_DUMP, copy(dst, length), 0, length);
                return new Object[]{Integer.valueOf(length), dumpBuffer};
            }
            return new Object[]{Integer.valueOf(length)};
        });
    }

    private void logWrite(ByteBuffer src, int length) throws IOException {
        if (length < 1) {
            return;
        }
        if (this.netStat != null) {
            this.netStat.incrementBytesSent(length);
        }
        tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "writeToSocket", "{0} bytes written to the Socket.", "{0} bytes written to the Socket. Packet Dump : \n{1}", null, () -> {
            if (isSensitiveEnabled()) {
                Parameter dumpBuffer = Parameter.arg(Format.Style.PACKET_DUMP, copy(src, length), 0, length);
                return new Object[]{Integer.valueOf(length), dumpBuffer};
            }
            return new Object[]{Integer.valueOf(length)};
        });
    }

    private IOException handleIOFailure(IOException e) {
        try {
            disconnect();
        } catch (Exception ex) {
            e.addSuppressed(ex);
        }
        return e;
    }

    private void ensureOpen() throws IOException {
        if (this.socketChannel == null || !this.socketChannel.isOpen()) {
            throw new NetException(NetException.SOCKET_CLOSED_ERR);
        }
    }

    @Override // oracle.net.nt.SocketChannelWrapper, java.nio.channels.SocketChannel, java.nio.channels.ScatteringByteChannel
    public long read(ByteBuffer[] dsts, int offset, int length) throws IOException {
        throw new IOException("Unsupported feature");
    }

    @Override // oracle.net.nt.SocketChannelWrapper, java.nio.channels.SocketChannel, java.nio.channels.GatheringByteChannel
    public long write(ByteBuffer[] srcs, int offset, int length) throws IOException {
        throw new IOException("Unsupported feature");
    }

    public String toString() {
        return "TimeoutSocketChannel[" + socket().toString() + "]";
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    final void registerForNonBlockingRead(Consumer<Throwable> onReadReady) throws IOException {
        SocketChannel socketChannel = requireOpenChannel();
        if (this.soTimeout > 0) {
            AtomicBoolean isRegistered = new AtomicBoolean(false);
            TimerTask cancelTask = scheduleRegistrationCancel(socketChannel, this.soTimeout, isRegistered);
            try {
                TcpMultiplexer.registerForReadEvent(socketChannel, err -> {
                    cancelTask.cancel();
                    onReadReady.accept(err);
                });
                isRegistered.set(true);
                return;
            } catch (IOException registrationException) {
                cancelTask.cancel();
                throw registrationException;
            }
        }
        TcpMultiplexer.registerForReadEvent(socketChannel, onReadReady);
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    final void registerForNonBlockingWrite(Consumer<Throwable> onWriteReady) throws IOException {
        SocketChannel socketChannel = requireOpenChannel();
        if (this.soTimeout > 0) {
            AtomicBoolean isRegistered = new AtomicBoolean(false);
            TimerTask cancelTask = scheduleRegistrationCancel(socketChannel, this.soTimeout, isRegistered);
            try {
                TcpMultiplexer.registerForWriteEvent(socketChannel, err -> {
                    cancelTask.cancel();
                    onWriteReady.accept(err);
                });
                isRegistered.set(true);
                return;
            } catch (IOException registrationException) {
                cancelTask.cancel();
                throw registrationException;
            }
        }
        TcpMultiplexer.registerForWriteEvent(socketChannel, onWriteReady);
    }

    private static TimerTask scheduleRegistrationCancel(SocketChannel socketChannel, int soTimeout, AtomicBoolean isRegistered) {
        return TimeoutInterruptHandler.scheduleTask(() -> {
            while (!isRegistered.get() && !Thread.currentThread().isInterrupted()) {
            }
            TcpMultiplexer.forceCallback(socketChannel, newTimeoutException(null));
        }, soTimeout);
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    protected void enqueueBlockedWrites(boolean isEnabled) {
        this.isWriteQueueEnabled = isEnabled;
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    protected boolean completeBlockedWrites() throws IOException {
        while (true) {
            ByteBuffer buffer = this.writeQueue.peek();
            if (buffer == null) {
                return true;
            }
            this.socketChannel.write(buffer);
            if (buffer.hasRemaining()) {
                return false;
            }
            this.writeQueue.remove();
        }
    }

    private boolean enqueueWrite(ByteBuffer buffer) {
        if (!this.isWriteQueueEnabled && !this.enqueueAllWrites) {
            return false;
        }
        ByteBuffer copy = ByteBuffer.allocate(buffer.remaining());
        copy.put(buffer).flip();
        this.writeQueue.add(copy);
        return true;
    }

    private static TimeoutInterruptHandler.IOReadTimeoutException newTimeoutException(Throwable cause) {
        TimeoutInterruptHandler.IOReadTimeoutException ioReadTimeoutException = new TimeoutInterruptHandler.IOReadTimeoutException("Socket read timed out");
        ioReadTimeoutException.initCause(cause);
        return ioReadTimeoutException;
    }

    private boolean isRegisteredWithMultiplexer() {
        return TcpMultiplexer.isRegistered(this.socketChannel);
    }

    private int doRegisteredRead(ByteBuffer dst) throws ExecutionException, InterruptedException, TimeoutException, IOException {
        int bytesRead = this.socketChannel.read(dst);
        if (bytesRead != 0) {
            return bytesRead;
        }
        CompletableFuture<Throwable> readReadyFuture = new CompletableFuture<>();
        TcpMultiplexer.registerForReadEvent(this.socketChannel, error -> {
            if (error == null) {
                readReadyFuture.complete(null);
            } else {
                readReadyFuture.completeExceptionally(error);
            }
        });
        awaitSocketTimeout(readReadyFuture, this.soTimeout);
        return this.socketChannel.read(dst);
    }

    private int doRegisteredWrite(ByteBuffer src) throws ExecutionException, InterruptedException, TimeoutException, IOException {
        int bytesWritten = this.socketChannel.write(src);
        if (!src.hasRemaining()) {
            return bytesWritten;
        }
        int timeoutRemaining = this.soTimeout;
        while (src.hasRemaining()) {
            CompletableFuture<Throwable> writeReadyFuture = new CompletableFuture<>();
            TcpMultiplexer.registerForWriteEvent(this.socketChannel, error -> {
                if (error == null) {
                    writeReadyFuture.complete(null);
                } else {
                    writeReadyFuture.completeExceptionally(error);
                }
            });
            long startTime = System.currentTimeMillis();
            awaitSocketTimeout(writeReadyFuture, timeoutRemaining);
            timeoutRemaining = (int) (timeoutRemaining - (System.currentTimeMillis() - startTime));
            bytesWritten += this.socketChannel.write(src);
        }
        return bytesWritten;
    }

    private <T> T awaitSocketTimeout(Future<T> future, int timeout) throws ExecutionException, InterruptedException, TimeoutException, IOException {
        T t;
        try {
            if (timeout > 0) {
                t = future.get(timeout, TimeUnit.MILLISECONDS);
            } else {
                t = future.get();
            }
            return t;
        } catch (InterruptedException interruptedException) {
            InterruptedIOException interruptedIOException = new InterruptedIOException("Socket read interrupted");
            interruptedIOException.initCause(interruptedException);
            throw interruptedIOException;
        } catch (ExecutionException executionException) {
            Throwable cause = executionException.getCause();
            if (cause instanceof IOException) {
                throw ((IOException) cause);
            }
            throw new IOException(cause);
        } catch (TimeoutException timeoutException) {
            throw newTimeoutException(timeoutException);
        }
    }
}
