package oracle.net.nt;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.SignatureException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.crypto.EncryptedPrivateKeyInfo;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/PEMKeyStore.class */
class PEMKeyStore implements Diagnosable {
    private static final String CLASS_NAME = PEMKeyStore.class.getName();
    private static final Pattern CERTIFICATE_PATTERN = Pattern.compile("-----BEGIN CERTIFICATE-----(.*?)-----END CERTIFICATE-----", 32);
    private static final Pattern PRIVATE_KEY_PATTERN = Pattern.compile("-----BEGIN (?:ENCRYPTED |)PRIVATE KEY-----(.*?)-----END (?:ENCRYPTED |)PRIVATE KEY-----", 32);
    private static final Pattern CLEAR_PRIVATE_KEY_PATTERN = Pattern.compile("-----BEGIN PRIVATE KEY-----(.*?)-----END PRIVATE KEY-----", 32);
    private static final Pattern ENCRYPTED_PRIVATE_KEY_PATTERN = Pattern.compile("-----BEGIN ENCRYPTED PRIVATE KEY-----(.*?)-----END ENCRYPTED PRIVATE KEY-----", 32);
    private static final String PEM_CERTIFICATE_TYPE = "X.509";
    private final SSLConfig config;
    private final KeyStore keyStore;
    private int privateKeyCount;
    private char[] pwd;
    private final List<Certificate> trustCertificates = new LinkedList();
    private final List<Certificate> chainedCertificates = new LinkedList();
    private PrivateKey privateKey;
    private final boolean isTrustStore;

    PEMKeyStore(SSLConfig cfg, KeyStore ks, boolean isTrustStore) throws Exception {
        this.isTrustStore = isTrustStore;
        this.config = cfg;
        this.keyStore = ks;
        String pemFile = isTrustStore ? cfg.getTrustStore() : cfg.getKeyStore();
        loadKeyStore(content(pemFile));
    }

    PEMKeyStore(SSLConfig cfg, KeyStore ks, String pemContent, boolean isTrustStore) throws Exception {
        this.isTrustStore = isTrustStore;
        this.config = cfg;
        this.keyStore = ks;
        loadKeyStore(pemContent);
    }

    KeyStore getKeyStore() {
        return this.keyStore;
    }

    int getPrivateKeyCount() {
        return this.privateKeyCount;
    }

    private void loadKeyStore(String pemContent) throws NetException {
        char[] chars;
        if (this.isTrustStore) {
            chars = this.config.getTrustStorePassword().getChars();
        } else {
            chars = this.config.getKeyStorePassword().getChars();
        }
        this.pwd = chars;
        try {
            try {
                try {
                    List<String> certs = new ArrayList<>();
                    Matcher certificateMatcher = CERTIFICATE_PATTERN.matcher(pemContent);
                    while (certificateMatcher.find()) {
                        certs.add(certificateMatcher.group(1).replace("\n", "").trim());
                    }
                    if (certs.isEmpty()) {
                        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "loadKeyStore", "No certificate found in the PEM keystore.", null, null);
                        throw new NetException(NetException.PEM_NO_CERTIFICATE_FOUND);
                    }
                    debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "loadKeyStore", "{0} certificate(s) found in the PEM keystore.", (String) null, (String) null, Integer.valueOf(certs.size()));
                    this.privateKey = getPrivateKey(pemContent);
                    initCertificates(certs);
                    debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "loadKeyStore", "Found {0} certificate(s) corresponding to the private key. Found {1} trust certificate(s).", null, null, Integer.valueOf(this.chainedCertificates.size()), Integer.valueOf(this.trustCertificates.size()));
                    this.keyStore.load(null, this.pwd);
                    if (this.privateKey != null) {
                        this.keyStore.setKeyEntry("key", this.privateKey, this.pwd, (Certificate[]) this.chainedCertificates.toArray(new Certificate[0]));
                    }
                    for (int i = 0; i < this.trustCertificates.size(); i++) {
                        this.keyStore.setCertificateEntry("oratc" + i, this.trustCertificates.get(i));
                    }
                } catch (NetException e) {
                    throw e;
                }
            } catch (Exception e2) {
                throw ((NetException) new NetException(NetException.PEM_PARSE_FAILURE).initCause(e2));
            }
        } finally {
            CustomSSLSocketFactory.clearPwd(this.pwd);
        }
    }

    private PrivateKey getPrivateKey(String pemContent) throws Exception {
        PKCS8EncodedKeySpec encodedKeySpec;
        int privateKeyIndex = this.config.getPemPrivateKeyIndex();
        Matcher privateKeyMatcher = PRIVATE_KEY_PATTERN.matcher(pemContent);
        String privateKeyContent = null;
        this.privateKeyCount = 0;
        while (privateKeyMatcher.find()) {
            this.privateKeyCount++;
            if (this.privateKeyCount == privateKeyIndex) {
                privateKeyContent = privateKeyMatcher.group().trim();
            }
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getPrivateKey", "Found {0} private keys from the PEM KeyStore. Index to be selected is {1}", null, null, Integer.valueOf(this.privateKeyCount), Integer.valueOf(privateKeyIndex));
        if (this.privateKeyCount == 0) {
            return null;
        }
        if (privateKeyContent == null) {
            throw new NetException(NetException.INVALID_PEM_PRIVATE_KEY_INDEX);
        }
        Matcher encPrivateKeyMatcher = ENCRYPTED_PRIVATE_KEY_PATTERN.matcher(privateKeyContent);
        if (encPrivateKeyMatcher.find()) {
            String privateKeyContent2 = encPrivateKeyMatcher.group(1).replace("\n", "").trim();
            EncryptedPrivateKeyInfo pkInfo = new EncryptedPrivateKeyInfo(decode(privateKeyContent2));
            PBEKeySpec pbeKeySpec = new PBEKeySpec(this.pwd);
            SecretKey secretKey = SecretKeyFactory.getInstance(pkInfo.getAlgName()).generateSecret(pbeKeySpec);
            encodedKeySpec = pkInfo.getKeySpec(secretKey);
        } else {
            Matcher clearPrivateKeyMatcher = CLEAR_PRIVATE_KEY_PATTERN.matcher(privateKeyContent);
            if (!clearPrivateKeyMatcher.find()) {
                return null;
            }
            String privateKeyContent3 = clearPrivateKeyMatcher.group(1).replace("\n", "").trim();
            encodedKeySpec = new PKCS8EncodedKeySpec(decode(privateKeyContent3));
        }
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(encodedKeySpec);
    }

    private void initCertificates(List<String> certs) throws Exception {
        CertificateFactory certFactory = CertificateFactory.getInstance(PEM_CERTIFICATE_TYPE);
        if (this.privateKey == null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCertificates", "No private key found. Might have only trust certificates.", null, null);
            for (int i = 0; i < certs.size(); i++) {
                this.trustCertificates.add(certFactory.generateCertificate(new ByteArrayInputStream(decode(certs.get(i)))));
            }
            return;
        }
        int index = 0;
        Certificate certInChain = null;
        BigInteger privateModulus = ((RSAPrivateKey) this.privateKey).getModulus();
        while (true) {
            if (index >= certs.size()) {
                break;
            }
            int i2 = index;
            index++;
            Certificate certificate = certFactory.generateCertificate(new ByteArrayInputStream(decode(certs.get(i2))));
            BigInteger publicModulus = ((RSAPublicKey) certificate.getPublicKey()).getModulus();
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCertificates", "Found Certificate {0}", (String) null, (String) null, certificate);
            if (publicModulus.equals(privateModulus)) {
                certInChain = certificate;
                this.chainedCertificates.add(certificate);
                break;
            }
            this.trustCertificates.add(certificate);
        }
        while (index < certs.size()) {
            int i3 = index;
            index++;
            Certificate certificate2 = certFactory.generateCertificate(new ByteArrayInputStream(decode(certs.get(i3))));
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initCertificates", "Found Certificate {0}", (String) null, (String) null, certificate2);
            if (isCertificateInChain(certInChain, certificate2) && !isSelfSigned(certificate2)) {
                certInChain = certificate2;
                this.chainedCertificates.add(certificate2);
            } else {
                this.trustCertificates.add(certificate2);
            }
        }
    }

    private boolean isCertificateInChain(Certificate chainCert, Certificate cert) throws NoSuchAlgorithmException, SignatureException, InvalidKeyException, CertificateException, NoSuchProviderException {
        try {
            chainCert.verify(cert.getPublicKey());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isSelfSigned(Certificate cert) throws NoSuchAlgorithmException, SignatureException, InvalidKeyException, CertificateException, NoSuchProviderException {
        try {
            cert.verify(cert.getPublicKey());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String content(String pemFile) throws NetException {
        try {
            return new String(Files.readAllBytes(Paths.get(pemFile, new String[0])), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw ((NetException) new NetException(NetException.UNABLE_TO_PARSE_WALLET_LOCATION).initCause(e));
        }
    }

    private byte[] decode(String content) {
        return Base64.getDecoder().decode(content);
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.config.diagnosable == null ? CommonDiagnosable.getInstance() : this.config.diagnosable;
    }
}
