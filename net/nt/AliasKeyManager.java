package oracle.net.nt;

import java.net.Socket;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.Principal;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.util.Enumeration;
import java.util.logging.Level;
import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.X509ExtendedKeyManager;
import javax.net.ssl.X509KeyManager;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ano.AnoServices;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/AliasKeyManager.class */
class AliasKeyManager extends X509ExtendedKeyManager {
    private static final String CLASS_NAME = AliasKeyManager.class.getName();
    private final String alias;
    private final X509KeyManager keyManager;

    static KeyManager[] wrapIfNeeded(SSLConfig sslConfig, KeyManager[] kms, KeyStore keyStore) throws NetException {
        if (kms == null || keyStore == null || kms.length == 0) {
            return kms;
        }
        if (!aliasOrThumbprintConfigured(sslConfig)) {
            return kms;
        }
        Diagnosable diagnosable = sslConfig.diagnosable == null ? CommonDiagnosable.getInstance() : sslConfig.diagnosable;
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "wrapIfNeeded", "Alias / Thumbprint specified. KeyManagers need to be wrapped with AliasKeyManager.", null, null);
        String choosenAlias = chooseAlias(keyStore, sslConfig, diagnosable);
        if (choosenAlias == null) {
            throw new NetException(NetException.SSL_CERT_ALIAS_NOTFOUND);
        }
        return wrapWithAliasKeyManager(kms, choosenAlias, diagnosable);
    }

    private static X509ExtendedKeyManager[] wrapWithAliasKeyManager(KeyManager[] keyManagers, String alias, Diagnosable diagnosable) {
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "wrapWithAliasKeyManager", "Wrapping with AliasManager. Alias = {0}", (String) null, (String) null, alias);
        X509ExtendedKeyManager[] newKeyManagers = new X509ExtendedKeyManager[keyManagers.length];
        for (int i = 0; i < keyManagers.length; i++) {
            newKeyManagers[i] = new AliasKeyManager((X509KeyManager) keyManagers[i], alias);
        }
        return newKeyManagers;
    }

    private static String chooseAlias(KeyStore keyStore, SSLConfig sslConfig, Diagnosable diagnosable) throws NetException {
        try {
            String alias = sslConfig.getCertificateAlias().trim();
            if (!alias.isEmpty() && keyStore.entryInstanceOf(alias, KeyStore.PrivateKeyEntry.class)) {
                diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "chooseAlias", "Certificate Alias is configured and found a matching certificate in the keystore. Alias = {0}", (String) null, (String) null, alias);
                return alias;
            }
            return chooseAliasUsingThumbprint(keyStore, sslConfig, diagnosable);
        } catch (Exception e) {
            throw ((NetException) new NetException(NetException.UNABLE_TO_INIT_SSL_CONTEXT).initCause(e));
        }
    }

    private static String chooseAliasUsingThumbprint(KeyStore keyStore, SSLConfig sslConfig, Diagnosable diagnosable) throws Exception {
        String[] thumbprintConfig = parseThumbprint(sslConfig, diagnosable);
        char[] pwd = sslConfig.getKeyStorePassword() == null ? null : sslConfig.getKeyStorePassword().getChars();
        String selectedAlias = null;
        KeyStore.ProtectionParameter pp = new KeyStore.PasswordProtection(pwd);
        Enumeration<String> aliases = keyStore.aliases();
        while (true) {
            if (!aliases.hasMoreElements()) {
                break;
            }
            String curAlias = aliases.nextElement();
            if (keyStore.entryInstanceOf(curAlias, KeyStore.PrivateKeyEntry.class)) {
                KeyStore.PrivateKeyEntry pke = (KeyStore.PrivateKeyEntry) keyStore.getEntry(curAlias, pp);
                if (isThumbprintMatching(pke.getCertificate(), thumbprintConfig[0], thumbprintConfig[1])) {
                    selectedAlias = curAlias;
                    break;
                }
            }
        }
        CustomSSLSocketFactory.clearPwd(pwd);
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "chooseAliasUsingThumbprint", "Certificate Thumbprint is configured. Matching certificate alias is {0}", (String) null, (String) null, selectedAlias);
        return selectedAlias;
    }

    private static String[] parseThumbprint(SSLConfig config, Diagnosable diagnosable) {
        String thumbprint = config.getCertificateThumbprint().trim();
        String hashAlgo = "SHA-1";
        if (thumbprint.toUpperCase().startsWith("SHA") && thumbprint.indexOf(58) != -1) {
            int indx = thumbprint.indexOf(58);
            hashAlgo = getHashAlgorithm(thumbprint.substring(0, indx).trim());
            thumbprint = thumbprint.substring(indx + 1).trim();
        }
        return new String[]{hashAlgo, thumbprint.replace(":", "")};
    }

    private static String getHashAlgorithm(String algoId) {
        if (algoId.equalsIgnoreCase(AnoServices.CHECKSUM_SHA1)) {
            return "SHA-1";
        }
        if (algoId.equalsIgnoreCase(AnoServices.CHECKSUM_SHA256)) {
            return "SHA-256";
        }
        return algoId;
    }

    private static boolean isThumbprintMatching(Certificate cert, String hashAlgo, String thumbprint) throws Exception {
        if (cert != null) {
            MessageDigest md = MessageDigest.getInstance(hashAlgo);
            md.update(cert.getEncoded());
            String certHash = toHex(md.digest());
            if (thumbprint.equalsIgnoreCase(certHash)) {
                return true;
            }
            return false;
        }
        return false;
    }

    private static String toHex(byte[] hash) {
        StringBuilder hexStrBuilder = new StringBuilder();
        for (byte b : hash) {
            hexStrBuilder.append(String.format("%02X", Byte.valueOf(b)));
        }
        return hexStrBuilder.toString();
    }

    private static boolean aliasOrThumbprintConfigured(SSLConfig config) {
        String tp = config.getCertificateThumbprint();
        String alias = config.getCertificateAlias();
        return ((tp == null || tp.isEmpty()) && (alias == null || alias.isEmpty())) ? false : true;
    }

    private AliasKeyManager(X509KeyManager keyManager, String alias) {
        this.keyManager = keyManager;
        this.alias = alias;
    }

    @Override // javax.net.ssl.X509KeyManager
    public X509Certificate[] getCertificateChain(String alias) {
        return this.keyManager.getCertificateChain(alias);
    }

    @Override // javax.net.ssl.X509KeyManager
    public String[] getClientAliases(String keyType, Principal[] issuers) {
        return this.keyManager.getClientAliases(keyType, issuers);
    }

    @Override // javax.net.ssl.X509KeyManager
    public PrivateKey getPrivateKey(String alias) {
        return this.keyManager.getPrivateKey(alias);
    }

    @Override // javax.net.ssl.X509KeyManager
    public String[] getServerAliases(String keyType, Principal[] issuers) {
        return this.keyManager.getServerAliases(keyType, issuers);
    }

    @Override // javax.net.ssl.X509KeyManager
    public String chooseClientAlias(String[] keyType, Principal[] issuers, Socket socket) {
        return chooseEngineClientAlias(keyType, issuers, null);
    }

    @Override // javax.net.ssl.X509KeyManager
    public String chooseServerAlias(String keyType, Principal[] issuers, Socket socket) {
        return chooseEngineServerAlias(keyType, issuers, null);
    }

    @Override // javax.net.ssl.X509ExtendedKeyManager
    public String chooseEngineClientAlias(String[] keyType, Principal[] issuers, SSLEngine engine) {
        return this.alias;
    }

    @Override // javax.net.ssl.X509ExtendedKeyManager
    public String chooseEngineServerAlias(String keyType, Principal[] issuers, SSLEngine engine) {
        return this.alias;
    }
}
