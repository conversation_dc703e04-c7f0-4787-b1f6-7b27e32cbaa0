package oracle.net.nt.adapter;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.SocketException;
import java.nio.channels.SocketChannel;
import java.util.Map;
import java.util.Properties;
import java.util.TimerTask;
import java.util.function.Consumer;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.util.BequeathModeMediator;
import oracle.jdbc.util.OracleEnvironment;
import oracle.jdbc.util.OracleServerProcessWrapper;
import oracle.net.ns.NetException;
import oracle.net.ns.SocketChannelFacade;
import oracle.net.nt.NTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;

/* loaded from: ojdbc8.jar:oracle/net/nt/adapter/BequeathAdapter.class */
public class BequeathAdapter implements NTAdapter {
    private static boolean getPidMethodAvailable;
    private static final int DEFAULT_NEGOCIATION_MAX_TIME = 3000000;
    private final OracleServerProcessWrapper wrapper;
    private final String tnsDescriptor;
    private int negociationMaxTime;
    private static final String CLASS_NAME;
    private static final String DEFAULT_TNS_DESCRIPTOR = "(DESCRIPTION=(LOCAL=YES)(ADDRESS=(PROTOCOL=beq)))";

    static {
        getPidMethodAvailable = false;
        try {
            Class.forName("java.lang.Process").getMethod("pid", new Class[0]);
            getPidMethodAvailable = true;
        } catch (ClassNotFoundException | NoSuchMethodException e) {
        }
        CLASS_NAME = BequeathAdapter.class.getName();
    }

    public BequeathAdapter(String SID, String oracleHome, String tnsDescriptor, Properties properties) throws IOException {
        this.negociationMaxTime = DEFAULT_NEGOCIATION_MAX_TIME;
        if (SID == null) {
            throw new IllegalArgumentException("SID cannot be null");
        }
        if (properties != null && properties.getProperty("oracle.net.CONNECT_TIMEOUT") != null) {
            this.negociationMaxTime = Integer.parseInt(properties.getProperty("oracle.net.CONNECT_TIMEOUT"));
        }
        try {
            this.wrapper = new OracleServerProcessWrapper(oracleHome);
            this.wrapper.addEnvironment(OracleEnvironment.SID.getEnvName(), SID);
            if (tnsDescriptor != null) {
                this.tnsDescriptor = tnsDescriptor;
            } else {
                this.tnsDescriptor = DEFAULT_TNS_DESCRIPTOR;
            }
        } catch (IOException e) {
            throw ((NetException) new NetException(NetException.ORACLE_ERROR).initCause(e));
        }
    }

    public void setAdapterEnvironment(Map<String, String> envs) {
        if (envs == null) {
            throw new IllegalArgumentException("envs cannot be null");
        }
        envs.forEach((k, v) -> {
            this.wrapper.addEnvironment(k, v);
        });
    }

    @Override // oracle.net.nt.NTAdapter
    public NTAdapter.NetworkAdapterType getNetworkAdapterType() {
        return NTAdapter.NetworkAdapterType.BEQ;
    }

    @Override // oracle.net.nt.NTAdapter
    public void connect(DMSFactory.DMSNoun dmsParent) throws IOException {
        this.wrapper.start(this.tnsDescriptor);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "server process wrapper started, initiate communication", (String) null, (Throwable) null);
        try {
            initiateCommunication();
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "server process communication initiated", (String) null, (Throwable) null);
        } catch (IOException e) {
            CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "Error initiating communication with server process", (String) null, e);
            this.wrapper.terminate();
            throw e;
        }
    }

    private void initiateCommunication() throws IOException {
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initiateCommunication", "initiate communication with server process", (String) null, null);
        BequeathModeMediator mediator = new BequeathModeMediator(this.wrapper.getInputStream(), this.wrapper.getOutputStream());
        Thread threadCurrentThread = Thread.currentThread();
        threadCurrentThread.getClass();
        TimerTask task = TimeoutInterruptHandler.scheduleTask(threadCurrentThread::interrupt, this.negociationMaxTime);
        try {
            try {
                mediator.startMediation();
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initiateCommunication", "mediator thread started", (String) null, null);
                if (task != null) {
                    task.cancel();
                }
                if (mediator.getStatus().getState() == BequeathModeMediator.MediationStatus.State.FAILED) {
                    CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initiateCommunication", "mediation has failed", (String) null, (String) null, (Exception) mediator.getStatus().getDetails());
                    throw new IOException("mediation has failed", (Exception) mediator.getStatus().getDetails());
                }
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initiateCommunication", "communication with server process done", (String) null, null);
            } catch (Exception e) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initiateCommunication", "mediator got exception", (String) null, null);
                if (e instanceof InterruptedException) {
                    throw ((NetException) new NetException(NetException.NT_BEQ_CONNECT_TIMEOUT).initCause(e));
                }
                throw ((NetException) new NetException(NetException.NT_CONNECTION_FAILED).initCause(e));
            }
        } catch (Throwable th) {
            if (task != null) {
                task.cancel();
            }
            throw th;
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public void disconnect() throws IOException {
        try {
            this.wrapper.stop();
        } catch (IOException e) {
            throw ((NetException) new NetException(NetException.NT_DISCONNECT_FAILED).initCause(e));
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public SocketChannel getSocketChannel() {
        try {
            return new SocketChannelFacade(this.wrapper.getInputStream(), this.wrapper.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("Cannot get SocketChannel for bequeath adapter");
        }
    }

    @Override // oracle.net.nt.NTAdapter
    public InputStream getInputStream() throws IOException {
        return this.wrapper.getInputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public OutputStream getOutputStream() throws IOException {
        return this.wrapper.getOutputStream();
    }

    @Override // oracle.net.nt.NTAdapter
    public void setOption(int option, Object value) throws IOException {
    }

    @Override // oracle.net.nt.NTAdapter
    public Object getOption(int option) throws IOException {
        return null;
    }

    @Override // oracle.net.nt.NTAdapter
    public void abort() throws IOException {
        this.wrapper.terminate();
    }

    @Override // oracle.net.nt.NTAdapter
    public void sendUrgentByte(int urgentData) throws IOException {
        this.wrapper.sendInterrupt();
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isCharacteristicUrgentSupported() throws IOException {
        return getPidMethodAvailable;
    }

    @Override // oracle.net.nt.NTAdapter
    public void setReadTimeoutIfRequired(Properties prop) throws IOException {
    }

    @Override // oracle.net.nt.NTAdapter
    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return false;
    }

    @Override // oracle.net.nt.NTAdapter
    public InetAddress getInetAddress() {
        return InetAddress.getLoopbackAddress();
    }

    @Override // oracle.net.nt.NTAdapter
    public void registerForNonBlockingWrite(Consumer<Throwable> onReady) throws IOException {
        onReady.accept(null);
    }

    @Override // oracle.net.nt.NTAdapter
    public void registerForNonBlockingRead(Consumer<Throwable> onReady) throws IOException {
        onReady.accept(null);
    }
}
