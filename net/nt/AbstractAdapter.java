package oracle.net.nt;

import java.net.Socket;
import java.util.Properties;

/* loaded from: ojdbc8.jar:oracle/net/nt/AbstractAdapter.class */
abstract class AbstractAdapter implements NTAdapter {
    protected String host;
    protected int port;
    protected Socket socket;
    protected int readTimeout;
    protected Properties socketOptions;

    AbstractAdapter() {
    }

    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        String ls = System.lineSeparator();
        stringBuilder.append("host=").append(this.host).append(", port=").append(this.port).append(ls);
        stringBuilder.append("socket_timeout=").append(this.readTimeout);
        if (this.socketOptions != null) {
            stringBuilder.append(ls).append("socketOptions=").append(this.socketOptions);
        }
        if (this.socket != null) {
            stringBuilder.append(ls).append("socket=").append(this.socket);
        }
        return stringBuilder.toString();
    }
}
