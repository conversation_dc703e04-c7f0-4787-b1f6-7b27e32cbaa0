package oracle.net.nt;

import java.io.File;
import java.security.KeyStore;
import java.security.Security;
import java.util.Objects;
import java.util.Properties;
import java.util.logging.Level;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/SSLConfig.class */
public class SSLConfig implements Diagnosable {
    public static final String DEFAULT_SSO_WALLET_FILE_NAME = "cwallet.sso";
    public static final String DEFAULT_PKCS12_WALLET_FILE_NAME = "ewallet.p12";
    public static final String DEFAULT_PEM_WALLET_FILE_NAME = "ewallet.pem";
    public static final String DEFAULT_CLEAR_PEM_WALLET_FILE_NAME = "cwallet.pem";
    public static final String SSO_WALLET_TYPE = "SSO";
    public static final String PKCS12_WALLET_TYPE = "PKCS12";
    public static final String PKCS11_WALLET_TYPE = "PKCS11";
    public static final String WINDOWS_MY_WALLET_TYPE = "Windows-MY";
    public static final String JKS_TYPE = "JKS";
    public static final String KSS_TYPE = "KSS";
    public static final String PEM_WALLET_TYPE = "PEM";
    public static final String DATA_URI_TYPE = "DATA_URI";
    public static final String SUPPORTED_METHOD_TYPE = "FILE";
    public static final String SSO_FILE_EXTENSION = ".sso";
    public static final String P12_FILE_EXTENSION = ".p12";
    public static final String PEM_FILE_EXTENSION = ".pem";
    public static final String PFX_FILE_EXTENSION = ".pfx";
    public static final String JKS_FILE_EXTENSION = ".jks";
    public static final String KSS_URI_SCHEME = "kss://";
    public static final String DATA_URI_SCHEME = "data:";
    public static final String BASE64_EXTENSION = ";base64,";
    public static final String ORACLE_PKI_PROVIDER_CLASS = "oracle.security.pki.OraclePKIProvider";
    public static final String KSS_PROVIDER_CLASS = "oracle.security.jps.internal.keystore.provider.FarmKeyStoreProvider";
    private String keyStore;
    private String keyStoreType;
    private OpaqueString keyStorePassword;
    private String certificateAlias;
    private String certificateThumbprint;
    private String keyManagerFacAlgo;
    private String trustStore;
    private String trustStoreType;
    private OpaqueString trustStorePassword;
    private String trustManagerFacAlgo;
    private String sslContextProtocol;
    private boolean isCaCertsTrusted;
    private boolean isWallet;
    private int pemPrivateKeyIndex = 1;
    private String sni;
    Diagnosable diagnosable;
    private static final String CLASS_NAME = SSLConfig.class.getName();
    public static final SSLConfig DEFAULT_SSL_CONFIG = new SSLConfig();

    private SSLConfig() {
    }

    public static SSLConfig newInstance(Properties props) throws NetException {
        SSLConfig newConfig = new SSLConfig();
        newConfig.readSSLConfig(props);
        return newConfig;
    }

    public String getKeyStore() {
        return this.keyStore;
    }

    public String getKeyStoreType() {
        return this.keyStoreType;
    }

    public OpaqueString getKeyStorePassword() {
        return this.keyStorePassword;
    }

    public String getCertificateAlias() {
        return this.certificateAlias;
    }

    public String getCertificateThumbprint() {
        return this.certificateThumbprint;
    }

    public String getKeyManagerFacAlgo() {
        return this.keyManagerFacAlgo;
    }

    public String getTrustStore() {
        return this.trustStore;
    }

    public String getTrustStoreType() {
        return this.trustStoreType;
    }

    public OpaqueString getTrustStorePassword() {
        return this.trustStorePassword;
    }

    public String getTrustManagerFacAlgo() {
        return this.trustManagerFacAlgo;
    }

    public String getSslContextProtocol() {
        return this.sslContextProtocol;
    }

    public boolean isCaCertsTrusted() {
        return this.isCaCertsTrusted;
    }

    public boolean isWallet() {
        return this.isWallet;
    }

    public int getPemPrivateKeyIndex() {
        return this.pemPrivateKeyIndex;
    }

    public boolean useSystemKeystore() {
        if ("NONE".equalsIgnoreCase(this.keyStore) || "NONE".equalsIgnoreCase(this.trustStore)) {
            return true;
        }
        return false;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SSLConfig sslConfig = (SSLConfig) o;
        return this.isCaCertsTrusted == sslConfig.isCaCertsTrusted && this.isWallet == sslConfig.isWallet && Objects.equals(this.keyStore, sslConfig.keyStore) && Objects.equals(this.keyStoreType, sslConfig.keyStoreType) && Objects.equals(this.keyStorePassword, sslConfig.keyStorePassword) && Objects.equals(this.certificateAlias, sslConfig.certificateAlias) && Objects.equals(this.certificateThumbprint, sslConfig.certificateThumbprint) && Objects.equals(this.keyManagerFacAlgo, sslConfig.keyManagerFacAlgo) && Objects.equals(this.trustStore, sslConfig.trustStore) && Objects.equals(this.trustStoreType, sslConfig.trustStoreType) && Objects.equals(this.trustStorePassword, sslConfig.trustStorePassword) && Objects.equals(this.trustManagerFacAlgo, sslConfig.trustManagerFacAlgo) && Objects.equals(this.sslContextProtocol, sslConfig.sslContextProtocol) && Objects.equals(Integer.valueOf(this.pemPrivateKeyIndex), Integer.valueOf(sslConfig.pemPrivateKeyIndex)) && Objects.equals(this.sni, sslConfig.sni);
    }

    public int hashCode() {
        return Objects.hash(this.keyStore, this.keyStoreType, this.keyStorePassword, this.certificateAlias, this.certificateThumbprint, this.keyManagerFacAlgo, this.trustStore, this.trustStoreType, this.trustStorePassword, this.trustManagerFacAlgo, this.sslContextProtocol, Boolean.valueOf(this.isCaCertsTrusted), Boolean.valueOf(this.isWallet), Integer.valueOf(this.pemPrivateKeyIndex), this.sni);
    }

    private void readSSLConfig(@Blind(PropertiesBlinder.class) Properties sslSocketProperties) throws NetException {
        String walletLocation = (String) sslSocketProperties.get(5);
        if (walletLocation == null) {
            readJavaxNetSSLConfig(sslSocketProperties);
        } else {
            readWalletSSLConfig(sslSocketProperties);
        }
        this.certificateAlias = (String) sslSocketProperties.getOrDefault(29, "");
        this.certificateThumbprint = (String) sslSocketProperties.getOrDefault(44, "");
        this.sslContextProtocol = (String) sslSocketProperties.getOrDefault(38, OracleConnection.CONNECTION_PROPERTY_SSL_CONTEXT_PROTOCOL_DEFAULT);
        this.isCaCertsTrusted = Boolean.valueOf((String) sslSocketProperties.get(41)).booleanValue();
        this.sni = (String) sslSocketProperties.get(49);
    }

    private void readJavaxNetSSLConfig(@Blind(PropertiesBlinder.class) Properties sslSocketProperties) {
        this.keyStore = (String) sslSocketProperties.get(8);
        if (this.keyStore != null) {
            this.keyStoreType = (String) sslSocketProperties.get(9);
            if (this.keyStoreType == null) {
                this.keyStoreType = resolveKeyStoreType(this.keyStore);
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readJavaxNetSSLConfig", "Resolved KeyStoreType = {0}", (String) null, (String) null, this.keyStoreType);
            }
            this.keyStorePassword = (OpaqueString) sslSocketProperties.getOrDefault(10, OpaqueString.NULL);
            this.keyManagerFacAlgo = (String) sslSocketProperties.get(14);
            if (this.keyManagerFacAlgo == null) {
                this.keyManagerFacAlgo = Security.getProperty("ssl.keyManagerFactory.algorithm");
            }
            if (this.keyManagerFacAlgo == null) {
                this.keyManagerFacAlgo = KeyManagerFactory.getDefaultAlgorithm();
            }
        }
        this.trustStore = (String) sslSocketProperties.get(11);
        if (this.trustStore != null) {
            this.trustStoreType = (String) sslSocketProperties.get(12);
            if (this.trustStoreType == null) {
                this.trustStoreType = resolveKeyStoreType(this.trustStore);
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readJavaxNetSSLConfig", "Resolved TrustStoreType = {0}", (String) null, (String) null, this.trustStoreType);
            }
            this.trustStorePassword = (OpaqueString) sslSocketProperties.getOrDefault(13, OpaqueString.NULL);
            this.trustManagerFacAlgo = (String) sslSocketProperties.get(15);
            if (this.trustManagerFacAlgo == null) {
                this.trustManagerFacAlgo = Security.getProperty("ssl.trustManagerFactory.algorithm");
            }
            if (this.trustManagerFacAlgo == null) {
                this.trustManagerFacAlgo = TrustManagerFactory.getDefaultAlgorithm();
            }
        }
    }

    private void readWalletSSLConfig(@Blind(PropertiesBlinder.class) Properties sslSocketProperties) throws NetException {
        this.isWallet = true;
        String walletLocation = (String) sslSocketProperties.get(5);
        if (walletLocation.equalsIgnoreCase("SYSTEM")) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readWalletSSLConfig", "Wallet location is configured as SYSTEM. Using default SSLContext.", null, null);
            this.keyStore = "NONE";
            this.trustStore = "NONE";
            return;
        }
        OpaqueString walletPassword = (OpaqueString) sslSocketProperties.getOrDefault(16, OpaqueString.NULL);
        this.pemPrivateKeyIndex = Integer.valueOf((String) sslSocketProperties.getOrDefault(46, "1")).intValue();
        boolean isPasswordProtected = !OpaqueString.isNull(walletPassword);
        if (walletLocation.startsWith("(")) {
            walletLocation = processWalletLocation(walletLocation, getDiagnosable());
        } else if (walletLocation.startsWith("file:")) {
            walletLocation = walletLocation.substring("file:".length());
        }
        if (!isDataUri(walletLocation)) {
            File walletPath = new File(walletLocation);
            if (!walletPath.exists()) {
                throw new NetException(NetException.UNABLE_TO_PARSE_WALLET_LOCATION, "Couldn't find file at " + walletLocation);
            }
            if (walletPath.isDirectory()) {
                walletLocation = resolveWalletLocation(walletLocation, isPasswordProtected);
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readWalletSSLConfig", "Wallet location does not contain filename. Resolved location is {0}", (String) null, (String) null, walletLocation);
            }
        }
        this.keyStore = walletLocation;
        this.keyStoreType = resolveKeyStoreType(walletLocation);
        this.keyStorePassword = walletPassword;
        this.keyManagerFacAlgo = KeyManagerFactory.getDefaultAlgorithm();
        this.trustStore = this.keyStore;
        this.trustStoreType = this.keyStoreType;
        this.trustStorePassword = this.keyStorePassword;
        this.trustManagerFacAlgo = TrustManagerFactory.getDefaultAlgorithm();
    }

    boolean isKeyStoreTrustStore() {
        return Objects.equals(this.keyStore, this.trustStore) && Objects.equals(this.keyStoreType, this.trustStoreType) && Objects.equals(this.keyStorePassword, this.trustStorePassword);
    }

    private static String resolveKeyStoreType(String keyStorePath) {
        if (keyStorePath == null || keyStorePath.length() == 0) {
            return KeyStore.getDefaultType();
        }
        String lowerCasePath = keyStorePath.toLowerCase();
        if (lowerCasePath.endsWith(SSO_FILE_EXTENSION)) {
            return SSO_WALLET_TYPE;
        }
        if (lowerCasePath.endsWith(P12_FILE_EXTENSION) || lowerCasePath.endsWith(PFX_FILE_EXTENSION)) {
            return PKCS12_WALLET_TYPE;
        }
        if (lowerCasePath.endsWith(PEM_FILE_EXTENSION)) {
            return PEM_WALLET_TYPE;
        }
        if (lowerCasePath.endsWith(JKS_FILE_EXTENSION)) {
            return JKS_TYPE;
        }
        if (lowerCasePath.startsWith(KSS_URI_SCHEME)) {
            return KSS_TYPE;
        }
        if (isDataUri(lowerCasePath)) {
            return DATA_URI_TYPE;
        }
        return KeyStore.getDefaultType();
    }

    static boolean isDataUri(String path) {
        return path != null && path.startsWith(DATA_URI_SCHEME);
    }

    static String extractBase64FromDataURI(String dataURI) throws NetException {
        int index = dataURI.indexOf(BASE64_EXTENSION);
        if (index == -1) {
            throw new NetException(NetException.NT_INVALID_DATA_URI_FORMAT, null, false, new Object[0]);
        }
        return dataURI.substring(index + BASE64_EXTENSION.length());
    }

    private String resolveWalletLocation(String walletDir, boolean isPasswordProtected) {
        if (isPasswordProtected) {
            return getWallet(walletDir, DEFAULT_PKCS12_WALLET_FILE_NAME, DEFAULT_PEM_WALLET_FILE_NAME);
        }
        return getWallet(walletDir, DEFAULT_SSO_WALLET_FILE_NAME, DEFAULT_CLEAR_PEM_WALLET_FILE_NAME);
    }

    private String getWallet(String walletDir, String preferredWallet, String alternateWallet) {
        File firstFile = new File(walletDir, preferredWallet);
        if (!firstFile.exists()) {
            File secondFile = new File(walletDir, alternateWallet);
            if (secondFile.exists()) {
                return walletDir + File.separator + alternateWallet;
            }
        }
        return walletDir + File.separator + preferredWallet;
    }

    private static String resolveKeyStoreLocation(String keyStorePath, String keyStoreType) throws NetException {
        if (keyStorePath == null || keyStoreType == null || keyStorePath.length() == 0 || keyStoreType.length() == 0) {
            return null;
        }
        File keyStoreFile = new File(keyStorePath);
        if (!isDataUri(keyStorePath) && !keyStoreFile.exists()) {
            return null;
        }
        if (keyStoreFile.isDirectory()) {
            switch (keyStoreType) {
                case "SSO":
                    return keyStorePath + File.separator + DEFAULT_SSO_WALLET_FILE_NAME;
                case "PKCS12":
                    return keyStorePath + File.separator + DEFAULT_PKCS12_WALLET_FILE_NAME;
                case "PEM":
                    return keyStorePath + File.separator + DEFAULT_PEM_WALLET_FILE_NAME;
                default:
                    return null;
            }
        }
        return keyStorePath;
    }

    public static String processWalletLocation(String walletLocation, Diagnosable diagnosable) throws NetException {
        try {
            NVNavigator nav = new NVNavigator();
            NVPair nvpWallet = new NVFactory().createNVPair(walletLocation);
            NVPair nvpMethod = nav.findNVPair(nvpWallet, "METHOD");
            NVPair nvpMethodData = nav.findNVPair(nvpWallet, "METHOD_DATA");
            NVPair nvpDirectory = nav.findNVPair(nvpMethodData, "DIRECTORY");
            String method = nvpMethod.getAtom();
            diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "processWalletLocation", "Wallet Parameter Configuration : Method {0}, Directory {1}", null, null, method, nvpDirectory.getAtom());
            if (method.equalsIgnoreCase(SUPPORTED_METHOD_TYPE)) {
                String directoryLoc = nvpDirectory.getAtom();
                return directoryLoc;
            }
            throw new NetException(NetException.UNSUPPORTED_METHOD_IN_WALLET_LOCATION, method);
        } catch (Exception ex) {
            diagnosable.debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "processWalletLocation", "Error in parsing wallet location {0}", (String) null, (String) null, ex);
            throw ((NetException) new NetException(NetException.UNABLE_TO_PARSE_WALLET_LOCATION).initCause(ex));
        }
    }

    public String toString() {
        return "SSLConfig {keyStore='" + this.keyStore + "', keyStoreType='" + this.keyStoreType + "', certificateAlias='" + this.certificateAlias + "', certificateThumbprint='" + this.certificateThumbprint + "', keyManagerFacAlgo='" + this.keyManagerFacAlgo + "', trustStore='" + this.trustStore + "', trustStoreType='" + this.trustStoreType + "', trustManagerFacAlgo='" + this.trustManagerFacAlgo + "', sslContextProtocol='" + this.sslContextProtocol + "', isCaCertsTrusted=" + this.isCaCertsTrusted + ", isWallet=" + this.isWallet + ", pemPrivateKeyIndex=" + this.pemPrivateKeyIndex + ", sni='" + this.sni + "'}";
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable == null ? CommonDiagnosable.getInstance() : this.diagnosable;
    }
}
