package oracle.net.nt;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.SocketException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import javax.net.ssl.SSLContext;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.driver.resource.ResourceType;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.util.OracleEnvironment;
import oracle.net.jdbc.nl.NLException;
import oracle.net.ns.NetException;
import oracle.net.nt.adapter.BequeathAdapter;
import oracle.net.resolver.NavAddress;

/* loaded from: ojdbc8.jar:oracle/net/nt/ConnOption.class */
public class ConnOption implements Cloneable {
    public NTAdapter nt;
    public String protocol;
    public String host;
    public int port;
    public String addr;
    public String sid;
    public String service_name;
    public String instance_name;
    public int delayBetweenRetry;
    public int tdu;
    public int sdu;
    public String httpsProxy;
    public int httpsProxyPort;
    public InetSocketAddress inetSocketAddress;
    public String sslServerCertDN;
    public String sslServerDNMatch;
    public String sslAllowWeakDNMatch;
    public String walletDirectory;
    public String sslVersion;
    public String sslCiphers;
    public String sslCertAlias;
    public String sslCertThumbprint;
    public String useTcpFastOpen;
    public String serverMode;
    public String colocationTag;
    public boolean sourceRoute;
    private ConnOption originalConnOption;
    public boolean done;
    public int connectTimeout;
    public int transportConnectTimeout;
    private static final String ADDRESS_PATTERN = "(ADDRESS=(PROTOCOL=%s)(PORT=%s)%s)";
    private DriverResources driverResources;
    public StringBuilder conn_data = new StringBuilder(200);
    public String webSocketUri = null;
    public Map<String, String> serverProcessEnvironmentVars = new HashMap();
    public String serverProcessArguments = null;
    private boolean redirectedConnection = false;

    private NTAdapter getNT(@Blind(PropertiesBlinder.class) Properties socketOptions, SSLContext sslContext, Diagnosable diagnosable) throws NetException {
        try {
            if (this.protocol.equalsIgnoreCase("tcp")) {
                return new TcpNTAdapter(this.addr, socketOptions, diagnosable, this);
            }
            if (this.protocol.equalsIgnoreCase("tcps") || this.protocol.equalsIgnoreCase("wss")) {
                return createTcpsNTAdapter(socketOptions, sslContext, diagnosable);
            }
            if (this.protocol.equalsIgnoreCase("sdp")) {
                return new SdpNTAdapter(this.addr, this, socketOptions);
            }
            if (this.protocol.equalsIgnoreCase("exadirect") || this.protocol.equalsIgnoreCase("msgqlt")) {
                return new MQLNTAdapter(this.addr, this, socketOptions);
            }
            if (this.protocol.equalsIgnoreCase("beq")) {
                String SIDToBeUsed = this.sid;
                if (SIDToBeUsed == null) {
                    SIDToBeUsed = this.serverProcessEnvironmentVars.get(OracleEnvironment.SID.getEnvName());
                    if (SIDToBeUsed == null) {
                        SIDToBeUsed = System.getenv(OracleEnvironment.SID.getEnvName());
                    }
                }
                if (SIDToBeUsed == null) {
                    throw new IllegalStateException(OracleEnvironment.SID.getEnvName() + " must be defined");
                }
                String oHome = this.serverProcessEnvironmentVars.get(OracleEnvironment.ORACLE_HOME.getEnvName());
                if (oHome == null) {
                    oHome = System.getenv(OracleEnvironment.ORACLE_HOME.getEnvName());
                }
                if (oHome == null) {
                    throw new IllegalStateException(OracleEnvironment.ORACLE_HOME.getEnvName() + " must be defined");
                }
                BequeathAdapter beqNt = new BequeathAdapter(SIDToBeUsed, oHome, this.serverProcessArguments, socketOptions);
                beqNt.setAdapterEnvironment(this.serverProcessEnvironmentVars);
                return beqNt;
            }
            throw new NetException(NetException.INVALID_NT_ADAPTER, "protocol: " + this.protocol);
        } catch (NLException e) {
            throw new NetException(NetException.NL_EXCEPTION);
        } catch (NetException netException) {
            throw netException;
        } catch (Exception ex) {
            throw ((NetException) new NetException(NetException.INVALID_NT_ADAPTER).initCause(ex));
        }
    }

    private TcpsNTAdapter createTcpsNTAdapter(@Blind(PropertiesBlinder.class) Properties socketOptions, SSLContext sslContext, Diagnosable diagnosable) throws IOException, NLException {
        TcpsNTAdapter tcpsNTAdapter = new TcpsNTAdapter(this.addr, socketOptions, diagnosable, this);
        if (sslContext != null) {
            tcpsNTAdapter.setSSLContext(sslContext);
            return tcpsNTAdapter;
        }
        if (this.driverResources == null || !this.driverResources.isProviderConfigured(ResourceType.TLS_CONFIGURATION) || socketOptions.containsKey(5) || socketOptions.containsKey(8) || socketOptions.containsKey(11)) {
            return tcpsNTAdapter;
        }
        try {
            SSLContext providedSSLContext = (SSLContext) this.driverResources.getResource(ResourceType.TLS_CONFIGURATION);
            tcpsNTAdapter.setSSLContext(providedSSLContext);
            return tcpsNTAdapter;
        } catch (SQLException sqlException) {
            throw new NetException(sqlException);
        }
    }

    public void connect(@Blind(PropertiesBlinder.class) Properties socketOptions, DMSFactory.DMSNoun dmsParent, Diagnosable diagnosable) throws IOException {
        connect(socketOptions, dmsParent, null, diagnosable);
    }

    public void connect(@Blind(PropertiesBlinder.class) Properties socketOptions, DMSFactory.DMSNoun dmsParent, SSLContext sslContext, Diagnosable diagnosable) throws IOException {
        if (this.nt == null) {
            this.nt = getNT(socketOptions, sslContext, diagnosable);
        }
        this.nt.connect(dmsParent);
    }

    final CompletionStage<Void> connectAsync(Properties socketOptions, DMSFactory.DMSNoun dmsParent, SSLContext sslContext, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor, Diagnosable diagnosable) {
        try {
            if (this.nt == null) {
                this.nt = getNT(socketOptions, sslContext, diagnosable);
            }
            return this.nt.connectAsync(dmsParent, outboundTimeout, asyncExecutor);
        } catch (IOException getNTFailure) {
            return CompletionStageUtil.failedStage(getNTFailure);
        }
    }

    public ConnOption getOriginalConnOption() {
        return this.originalConnOption == null ? this : this.originalConnOption;
    }

    public void setOriginalConnOption(ConnOption connOption) {
        this.originalConnOption = connOption;
    }

    public void redirectedConnection(boolean value) {
        this.redirectedConnection = value;
    }

    public boolean redirectedConnection() {
        return this.redirectedConnection;
    }

    public String toString() {
        StringBuilder stringBuilder = new StringBuilder("[");
        if (this.host != null) {
            stringBuilder.append("host=").append(this.host).append(" ");
        }
        if (this.port > 0) {
            stringBuilder.append("port=").append(this.port).append(" ");
        }
        if (this.sid != null) {
            stringBuilder.append("sid=").append(this.sid).append(" ");
        }
        if (this.protocol != null) {
            stringBuilder.append("protocol=").append(this.protocol).append(" ");
        }
        if (this.service_name != null) {
            stringBuilder.append("service_name=").append(this.service_name).append(" ");
        }
        if (this.addr != null) {
            stringBuilder.append("addr=").append(this.addr).append(" ");
        }
        if (this.conn_data != null) {
            stringBuilder.append("conn_data=").append((CharSequence) this.conn_data).append(" ");
        }
        if (this.sslServerCertDN != null) {
            stringBuilder.append("sslServerCertDN=").append(this.sslServerCertDN).append(" ");
        }
        stringBuilder.append("done=").append(this.done).append("]");
        return stringBuilder.toString();
    }

    boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.nt.isConnectionSocketKeepAlive();
    }

    public void setDriverResources(DriverResources driverResources) {
        this.driverResources = driverResources;
    }

    /* renamed from: clone, reason: merged with bridge method [inline-methods] */
    public ConnOption m719clone() {
        ConnOption connOption = new ConnOption();
        connOption.addr = this.addr;
        connOption.conn_data = new StringBuilder(this.conn_data.toString());
        connOption.connectTimeout = this.connectTimeout;
        connOption.delayBetweenRetry = this.delayBetweenRetry;
        connOption.done = this.done;
        connOption.driverResources = this.driverResources;
        connOption.host = this.host;
        connOption.httpsProxy = this.httpsProxy;
        connOption.httpsProxyPort = this.httpsProxyPort;
        connOption.inetSocketAddress = this.inetSocketAddress;
        connOption.instance_name = this.instance_name;
        connOption.nt = this.nt;
        connOption.originalConnOption = this.originalConnOption;
        connOption.port = this.port;
        connOption.protocol = this.protocol;
        connOption.sdu = this.sdu;
        connOption.serverProcessArguments = this.serverProcessArguments;
        connOption.serverProcessEnvironmentVars = this.serverProcessEnvironmentVars;
        connOption.service_name = this.service_name;
        connOption.sid = this.sid;
        connOption.sslAllowWeakDNMatch = this.sslAllowWeakDNMatch;
        connOption.sslServerCertDN = this.sslServerCertDN;
        connOption.sslServerDNMatch = this.sslServerDNMatch;
        connOption.sslVersion = this.sslVersion;
        connOption.sslCiphers = this.sslCiphers;
        connOption.sslCertAlias = this.sslCertAlias;
        connOption.sslCertThumbprint = this.sslCertThumbprint;
        connOption.tdu = this.tdu;
        connOption.transportConnectTimeout = this.transportConnectTimeout;
        connOption.walletDirectory = this.walletDirectory;
        connOption.webSocketUri = this.webSocketUri;
        connOption.useTcpFastOpen = this.useTcpFastOpen;
        connOption.colocationTag = this.colocationTag;
        connOption.sourceRoute = this.sourceRoute;
        connOption.serverMode = this.serverMode;
        connOption.redirectedConnection = this.redirectedConnection;
        return connOption;
    }

    public void formatConnectData() throws NetException {
        int startIndex;
        String hostPattern;
        if (this.protocol.equalsIgnoreCase("beq") || this.inetSocketAddress.getAddress() == null || (startIndex = this.conn_data.indexOf(NavAddress.CONNECT_DATA_ADDRESS)) == -1) {
            return;
        }
        if (this.inetSocketAddress.getAddress().getHostAddress().toString().equals(this.host)) {
            hostPattern = "(HOST=" + this.host + ")";
        } else {
            hostPattern = "(HOST=" + this.inetSocketAddress.getAddress().getHostAddress().toString() + ")(HOSTNAME=" + this.host + ")";
        }
        this.conn_data.replace(startIndex, startIndex + NavAddress.CONNECT_DATA_ADDRESS.length(), String.format(ADDRESS_PATTERN, this.protocol, Integer.valueOf(this.port), hostPattern));
    }

    String getConnectData() {
        return this.conn_data.toString();
    }

    void setConnectData(String connectData) {
        this.conn_data = new StringBuilder(connectData);
    }
}
