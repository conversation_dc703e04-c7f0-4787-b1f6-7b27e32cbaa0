package oracle.net.nt;

import java.io.IOException;
import java.io.InterruptedIOException;
import java.nio.channels.CancelledKeyException;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/nt/TcpMultiplexer.class */
public final class TcpMultiplexer {
    private static final String CLASS_NAME = TcpMultiplexer.class.getName();
    private static volatile boolean isStarted = false;
    private final Selector selector;
    private final ConcurrentLinkedQueue<Runnable> taskQueue;
    private final AtomicInteger pendingTaskCount;
    private final Thread pollingThread;

    /* loaded from: ojdbc8.jar:oracle/net/nt/TcpMultiplexer$LazyHolder.class */
    private static final class LazyHolder {
        private static final TcpMultiplexer INSTANCE;

        private LazyHolder() {
        }

        static {
            try {
                Selector selector = Selector.open();
                INSTANCE = new TcpMultiplexer(selector);
            } catch (IOException ioEx) {
                throw new RuntimeException(ioEx);
            }
        }
    }

    private static TcpMultiplexer soleInstance() {
        return LazyHolder.INSTANCE;
    }

    public static void registerForReadEvent(SocketChannel channel, Consumer<Throwable> onReady) throws IOException {
        soleInstance().register(channel, 1, onReady);
    }

    public static void registerForWriteEvent(SocketChannel channel, Consumer<Throwable> onReady) throws IOException {
        soleInstance().register(channel, 4, onReady);
    }

    public static void registerForConnectEvent(SocketChannel channel, Consumer<Throwable> onReady) throws IOException {
        soleInstance().register(channel, 8, onReady);
    }

    public static void forceCallback(SocketChannel channel, Throwable error) {
        SelectionKey key;
        if (!isStarted || (key = soleInstance().getKeyForChannel(channel)) == null) {
            return;
        }
        soleInstance().enqueueTask(() -> {
            onReady(key, error);
        });
    }

    static void restoreBlockingMode(SocketChannel socketChannel) throws IOException {
        Throwable pendingError;
        SelectionKey selectionKey = getRegisteredKey(socketChannel);
        if (selectionKey == null) {
            return;
        }
        selectionKey.cancel();
        socketChannel.configureBlocking(true);
        Registration registration = (Registration) selectionKey.attachment();
        if (registration == null || (pendingError = registration.pendingError) == null) {
            return;
        }
        if (pendingError instanceof IOException) {
            throw ((IOException) pendingError);
        }
        throw new IOException(pendingError);
    }

    static boolean isRegistered(SocketChannel socketChannel) {
        return getRegisteredKey(socketChannel) != null;
    }

    private static SelectionKey getRegisteredKey(SocketChannel socketChannel) {
        if (!isStarted) {
            return null;
        }
        TcpMultiplexer tcpMultiplexer = soleInstance();
        SelectionKey selectionKey = tcpMultiplexer.getKeyForChannel(socketChannel);
        if (selectionKey != null && selectionKey.isValid()) {
            return selectionKey;
        }
        return null;
    }

    public static void stop() {
        if (isStarted) {
            isStarted = false;
            soleInstance().pollingThread.interrupt();
        }
    }

    private TcpMultiplexer(Selector selector) {
        this.taskQueue = new ConcurrentLinkedQueue<>();
        this.pendingTaskCount = new AtomicInteger(0);
        this.selector = selector;
        this.pollingThread = new Thread(this::poll, getClass().getName());
        this.pollingThread.setDaemon(true);
        this.pollingThread.start();
        isStarted = true;
    }

    private void register(SocketChannel channel, int interest, Consumer<Throwable> onReady) throws IOException {
        enqueueTask(() -> {
            Registration registration;
            try {
                SelectionKey existingKey = channel.keyFor(this.selector);
                if (existingKey != null) {
                    registration = (Registration) existingKey.attachment();
                    if (!existingKey.isValid()) {
                        this.selector.selectNow();
                    }
                } else {
                    registration = new Registration();
                }
                registration.updateInterest(interest, onReady);
                channel.configureBlocking(false);
                channel.register(this.selector, registration.interest, registration);
            } catch (Throwable throwable) {
                onReady.accept(throwable);
            }
        });
    }

    private void enqueueTask(Runnable task) {
        this.taskQueue.add(task);
        if (this.pendingTaskCount.getAndIncrement() == 0) {
            this.selector.wakeup();
        }
    }

    private SelectionKey getKeyForChannel(SocketChannel channel) {
        return channel.keyFor(this.selector);
    }

    private void poll() throws IOException {
        int taskCount;
        while (true) {
            try {
                int selected = this.selector.select();
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedIOException(Thread.currentThread().getName() + " received a thread interrupt");
                }
                while (selected != 0) {
                    for (SelectionKey key : this.selector.selectedKeys()) {
                        onReady(key);
                    }
                    this.selector.selectedKeys().clear();
                    selected = this.selector.selectNow();
                }
                do {
                    taskCount = 0;
                    Runnable task = this.taskQueue.poll();
                    while (task != null) {
                        taskCount++;
                        task.run();
                        task = this.taskQueue.poll();
                    }
                } while (this.pendingTaskCount.addAndGet(-taskCount) > 0);
            } catch (Throwable selectFailure) {
                Registration[] callbacks = (Registration[]) this.selector.keys().stream().map(key2 -> {
                    return (Registration) key2.attachment();
                }).toArray(x$0 -> {
                    return new Registration[x$0];
                });
                try {
                    this.selector.close();
                } catch (IOException closeFailure) {
                    CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.INTERNAL, CLASS_NAME, "poll", closeFailure.getMessage(), "", closeFailure);
                }
                for (Registration callback : callbacks) {
                    callback.onError(selectFailure);
                }
                return;
            }
        }
    }

    private static void onReady(SelectionKey key) {
        onReady(key, null);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static void onReady(SelectionKey key, Throwable error) {
        try {
            Registration registration = (Registration) key.attachment();
            if (error == null) {
                registration.onReady(key.readyOps());
            } else {
                registration.onError(error);
            }
            key.interestOps(registration.interest);
        } catch (CancelledKeyException e) {
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TcpMultiplexer$Registration.class */
    private static final class Registration {
        private int interest;
        private Throwable pendingError;
        private Consumer<Throwable> connectCallback;
        private Consumer<Throwable> writeCallback;
        private Consumer<Throwable> readCallback;

        private Registration() {
            this.pendingError = null;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void updateInterest(int interest, Consumer<Throwable> callback) {
            if (this.pendingError != null) {
                Throwable error = this.pendingError;
                this.pendingError = null;
                callback.accept(error);
                return;
            }
            this.interest |= interest;
            if ((interest & 8) != 0) {
                this.connectCallback = callback;
            }
            if ((interest & 4) != 0) {
                this.writeCallback = callback;
            }
            if ((interest & 1) != 0) {
                this.readCallback = callback;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void onReady(int readyOps) {
            this.interest &= readyOps ^ (-1);
            try {
                if ((readyOps & 8) != 0) {
                    executeConnect(null);
                }
                if ((readyOps & 4) != 0) {
                    executeWrite(null);
                }
                if ((readyOps & 1) != 0) {
                    executeRead(null);
                }
            } catch (Throwable throwable) {
                handleCallbackError(throwable);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void onError(Throwable error) {
            this.interest = 0;
            try {
                boolean isErrorReceived = false | executeConnect(error);
                if (!(isErrorReceived | executeWrite(error) | executeRead(error))) {
                    this.pendingError = error;
                }
            } catch (Throwable throwable) {
                handleCallbackError(throwable);
            }
        }

        private boolean executeConnect(Throwable error) {
            Consumer<Throwable> connectCallback = this.connectCallback;
            this.connectCallback = null;
            if (connectCallback == null) {
                return false;
            }
            connectCallback.accept(error);
            return true;
        }

        private boolean executeWrite(Throwable error) {
            Consumer<Throwable> writeCallback = this.writeCallback;
            this.writeCallback = null;
            if (writeCallback == null) {
                return false;
            }
            writeCallback.accept(error);
            return true;
        }

        private boolean executeRead(Throwable error) {
            Consumer<Throwable> readCallback = this.readCallback;
            this.readCallback = null;
            if (readCallback == null) {
                return false;
            }
            readCallback.accept(error);
            return true;
        }

        private void handleCallbackError(Throwable throwable) {
            CommonDiagnosable.getInstance().debug(Level.SEVERE, SecurityLabel.INTERNAL, null, null, null, "I/O Readiness callback threw an exception", throwable);
        }
    }
}
