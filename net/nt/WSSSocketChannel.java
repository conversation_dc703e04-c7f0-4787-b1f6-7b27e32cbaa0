package oracle.net.nt;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Properties;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel.class */
public class WSSSocketChannel extends SocketChannelWrapper implements Monitor {
    private static final byte WS_OPCODE_CONTINUE = 0;
    private static final byte WS_OPCODE_TEXTDATA = 1;
    private static final byte WS_OPCODE_BINARYDATA = 2;
    private static final byte WS_OPCODE_CLOSE = 8;
    private static final byte WS_OPCODE_PING = 9;
    private static final byte WS_OPCODE_PONG = 10;
    private static final int HANDSHAKE_RESPONSE_BUFFER_SIZE = 1024;
    private static final byte MASK_BYTE_OPCODE = 15;
    private static final byte MASK_BYTE_FIN = -127;
    public static final byte[] WS_DUMMY_MASK_KEY = {0, 0, 0, 0};
    private ByteBuffer payloadBuffer;
    private boolean isClosed;
    private final Monitor.CloseableLock monitorLock;

    public WSSSocketChannel(SocketChannel socketChannel, String uri, String server, int port, String authUser, OpaqueString authPwd, Diagnosable diagnosable) throws NoSuchAlgorithmException, IOException, NumberFormatException {
        super(socketChannel, diagnosable);
        this.isClosed = false;
        this.monitorLock = Monitor.newDefaultLock();
        this.payloadBuffer = ByteBuffer.allocate(this.bufferSize);
        this.payloadBuffer.limit(0);
        doWSHandShake(uri, server, port, authUser, authPwd);
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.ReadableByteChannel
    public int read(ByteBuffer dstBuffer) throws IOException {
        int initialPosition = dstBuffer.position();
        if (!this.payloadBuffer.hasRemaining()) {
            readFromSocket();
        }
        while (this.payloadBuffer.hasRemaining() && dstBuffer.hasRemaining()) {
            dstBuffer.put(this.payloadBuffer.get());
        }
        return dstBuffer.position() - initialPosition;
    }

    @Override // java.nio.channels.SocketChannel, java.nio.channels.WritableByteChannel
    public int write(ByteBuffer srcBuffer) throws IOException {
        int payloadLength = srcBuffer.remaining();
        if (payloadLength > 0) {
            WSFrame.writeFrame(this.socketChannel, new WSBinaryFrame(srcBuffer));
        }
        return payloadLength;
    }

    public void sendPing(ByteBuffer payload) throws IOException {
        WSPingFrame pingFrame = new WSPingFrame(payload, this.socketChannel);
        WSFrame.writeFrame(this.socketChannel, pingFrame);
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    public void setBufferSize(int newBufferSize) {
        if (this.bufferSize == newBufferSize) {
            return;
        }
        this.bufferSize = newBufferSize;
        ByteBuffer newPayloadBuffer = ByteBuffer.allocate(newBufferSize);
        if (this.payloadBuffer.hasRemaining()) {
            newPayloadBuffer.put(this.payloadBuffer);
        }
        newPayloadBuffer.flip();
        this.payloadBuffer = newPayloadBuffer;
    }

    private void doWSHandShake(String uri, String host, int port, String authUser, OpaqueString authPwd) throws NoSuchAlgorithmException, IOException, NumberFormatException {
        WSHandshakeHelper handShakeHelper = new WSHandshakeHelper(uri, null, host, port, authUser, authPwd);
        handShakeHelper.sendHandshakeData(this.socketChannel);
        handShakeHelper.receiveHandshakeResponse(this.socketChannel);
    }

    private void readFromSocket() throws IOException {
        WSFrame frame = WSFrame.readFrame(this.socketChannel, this.payloadBuffer);
        if (frame.header.opCode == 9) {
            WSPongFrame pongFrame = new WSPongFrame(this.payloadBuffer);
            WSFrame.writeFrame(this.socketChannel, pongFrame);
            readFromSocket();
        } else if (frame.header.opCode == 8) {
            throw new IOException("WebSocket : Connection closed. (Error code : " + ((WSCloseFrame) frame).errorCode + ")");
        }
    }

    @Override // oracle.net.nt.SocketChannelWrapper
    public void disconnect() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (!this.isClosed && this.socketChannel != null && this.socketChannel.isOpen()) {
                    WSCloseFrame closeFrame = new WSCloseFrame();
                    WSFrame.writeFrame(this.socketChannel, closeFrame);
                    do {
                    } while (WSFrame.readFrame(this.socketChannel, this.payloadBuffer).header.opCode != 8);
                }
            } catch (Exception e) {
            }
            this.isClosed = true;
            try {
                if (this.socketChannel instanceof SocketChannelWrapper) {
                    ((SocketChannelWrapper) this.socketChannel).disconnect();
                }
            } catch (Exception e2) {
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSHandshakeHelper.class */
    private static class WSHandshakeHelper {
        private final String uri;
        private final String queryParam;
        private final String host;
        private final int port;
        private final OpaqueString httpBasicAuthKey;
        private final byte[] MAGIC = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11".getBytes(StandardCharsets.UTF_8);
        private final int SWITCHING_PROTOCOLS = 101;
        private final Pattern PAT_STATUS_LINE = Pattern.compile("^HTTP/1.[01]\\s+(\\d+)\\s+(.*)", 2);
        private final Pattern PAT_HEADER = Pattern.compile("([^:]+):\\s*(.*)");
        private final String key = genRandomKey();

        WSHandshakeHelper(String uri, String queryParam, String host, int port, String authUser, OpaqueString authPwd) {
            this.uri = uri;
            this.queryParam = queryParam;
            this.host = host;
            this.port = port;
            if (authUser != null && authPwd != null && authPwd != OpaqueString.NULL) {
                this.httpBasicAuthKey = getHTTPAuthHeader(authUser, authPwd);
            } else {
                this.httpBasicAuthKey = null;
            }
        }

        void sendHandshakeData(SocketChannel socketChannel) throws IOException {
            ByteBuffer handShakeRequestBuffer = ByteBuffer.wrap(generateUpgradeRequest().getBytes(StandardCharsets.ISO_8859_1));
            while (handShakeRequestBuffer.hasRemaining()) {
                socketChannel.write(handShakeRequestBuffer);
            }
        }

        void receiveHandshakeResponse(SocketChannel socketChannel) throws NoSuchAlgorithmException, IOException, NumberFormatException {
            ByteBuffer resBuffer = ByteBuffer.allocate(1024);
            socketChannel.read(resBuffer);
            resBuffer.flip();
            String response = new String(resBuffer.array(), resBuffer.arrayOffset(), resBuffer.limit(), StandardCharsets.ISO_8859_1);
            BufferedReader responseReader = new BufferedReader(new StringReader(response));
            validateStatus(responseReader.readLine());
            Properties responseHeaders = new Properties();
            String line = responseReader.readLine();
            while (true) {
                String currentLine = line;
                if (currentLine == null || currentLine.trim().length() <= 0) {
                    break;
                }
                parseHeader(currentLine, responseHeaders);
                line = responseReader.readLine();
            }
            validateResponseHeaders(responseHeaders);
        }

        private String generateUpgradeRequest() {
            StringBuilder request = new StringBuilder();
            request.append("GET ");
            if (this.uri != null && this.uri.length() > 0) {
                if (!this.uri.startsWith("/")) {
                    request.append("/");
                }
                request.append(this.uri);
            } else {
                request.append("/");
            }
            if (this.queryParam != null && this.queryParam.length() != 0) {
                request.append("?").append(this.queryParam);
            }
            request.append(" HTTP/1.1\r\n");
            request.append("Host: ").append(this.host);
            if (this.port > 0) {
                request.append(':').append(this.port);
            }
            request.append("\r\n");
            request.append("Upgrade: websocket\r\n");
            request.append("Connection: Upgrade\r\n");
            request.append("Sec-WebSocket-Key: ").append(this.key).append("\r\n");
            request.append("Sec-WebSocket-Version: 13\r\n");
            request.append("Sec-WebSocket-Protocol: sqlnet\r\n");
            if (this.httpBasicAuthKey != null) {
                request.append("Authorization: " + this.httpBasicAuthKey.get() + "\r\n");
            }
            request.append("Pragma: no-cache\r\n");
            request.append("Cache-Control: no-cache\r\n");
            request.append("\r\n");
            return request.toString();
        }

        private void validateStatus(String statusLine) throws IOException, NumberFormatException {
            Matcher matcher = this.PAT_STATUS_LINE.matcher(statusLine);
            if (!matcher.matches()) {
                throw new IOException("WebSocket: Unexpected HTTP response status line [" + statusLine + "]");
            }
            int statusCode = Integer.parseInt(matcher.group(1));
            String statusReason = matcher.group(2);
            if (statusCode != 101) {
                throw new IOException("WebSocket: Unable to upgrade to websocket protocol [" + statusCode + " : " + statusReason + "]");
            }
        }

        private void validateResponseHeaders(@Blind(PropertiesBlinder.class) Properties responseHeaders) throws NoSuchAlgorithmException, IOException {
            String connection = responseHeaders.getProperty("Connection");
            if (!"upgrade".equalsIgnoreCase(connection)) {
                throw new IOException("WebSocket: value of the header Connection is  " + connection + " (expected 'upgrade')");
            }
            String upgrade = responseHeaders.getProperty("Upgrade");
            if (!"websocket".equalsIgnoreCase(upgrade)) {
                throw new IOException("WebSocket: value of the header Upgrade is  " + connection + " (expected 'websocket')");
            }
            String respHashStr = responseHeaders.getProperty("Sec-WebSocket-Accept");
            byte[] responseHash = respHashStr == null ? null : respHashStr.getBytes(StandardCharsets.ISO_8859_1);
            if (responseHash == null || responseHash.length < 20) {
                throw new IOException("Invalid Sec-WebSocket-Accept hash");
            }
            byte[] expectedHash = expectedAcceptHash(this.key);
            for (int i = 0; i < 20; i++) {
                if (responseHash[i] != expectedHash[i]) {
                    throw new IOException("Sec-WebSocket-Accept hash does not match");
                }
            }
        }

        private OpaqueString getHTTPAuthHeader(String user, OpaqueString pwd) {
            String auth = user + ":" + pwd.get();
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(StandardCharsets.ISO_8859_1));
            String authHeader = "Basic " + new String(encodedAuth, StandardCharsets.ISO_8859_1);
            return OpaqueString.newOpaqueString(authHeader);
        }

        private void parseHeader(String headerLine, @Blind(PropertiesBlinder.class) Properties headerProperties) {
            Matcher matcher = this.PAT_HEADER.matcher(headerLine);
            if (matcher.matches()) {
                headerProperties.setProperty(matcher.group(1), matcher.group(2));
            }
        }

        private final String genRandomKey() {
            byte[] bytes = new byte[16];
            ThreadLocalRandom.current().nextBytes(bytes);
            return Base64.getEncoder().encodeToString(bytes);
        }

        private final byte[] expectedAcceptHash(String requestKey) throws NoSuchAlgorithmException {
            try {
                MessageDigest md = MessageDigest.getInstance(AnoServices.CHECKSUM_SHA1);
                md.update(this.key.getBytes(StandardCharsets.UTF_8));
                md.update(this.MAGIC);
                return Base64.getEncoder().encode(md.digest());
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSHeader.class */
    private static class WSHeader {
        private boolean isFinalChunk;
        private byte opCode;
        private int payloadLength;
        private boolean isPayloadMasked;
        private byte[] maskingKey;

        private WSHeader() {
        }

        void read(SocketChannel socketChannel) throws IOException {
            ByteBuffer headerBuffer = ByteBuffer.allocate(14);
            headerBuffer.limit(2);
            while (headerBuffer.hasRemaining()) {
                socketChannel.read(headerBuffer);
            }
            headerBuffer.flip();
            byte firstByte = headerBuffer.get();
            byte secondByte = headerBuffer.get();
            this.isFinalChunk = (firstByte & 128) != 0;
            this.opCode = (byte) (firstByte & 15);
            this.isPayloadMasked = (secondByte & 128) != 0;
            this.payloadLength = (byte) (Byte.MAX_VALUE & secondByte);
            readRemainingHeaderBytes(socketChannel, headerBuffer);
            if (this.payloadLength == 126) {
                this.payloadLength = headerBuffer.getShort() & 65535;
            } else if (this.payloadLength >= 127) {
                this.payloadLength = (int) headerBuffer.getLong();
            }
            if (this.isPayloadMasked) {
                this.maskingKey = new byte[4];
                headerBuffer.get(this.maskingKey);
            }
        }

        private void readRemainingHeaderBytes(SocketChannel socketChannel, ByteBuffer headerBuffer) throws IOException {
            int neededHeaderBytes = 2;
            if (this.payloadLength == 126) {
                neededHeaderBytes = 2 + 2;
            } else if (this.payloadLength >= 127) {
                neededHeaderBytes = 2 + 8;
            }
            if (this.isPayloadMasked) {
                neededHeaderBytes += 4;
            }
            headerBuffer.position(2);
            headerBuffer.limit(neededHeaderBytes);
            while (headerBuffer.hasRemaining()) {
                socketChannel.read(headerBuffer);
            }
            headerBuffer.flip();
            headerBuffer.position(2);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void write(SocketChannel socketChannel) throws IOException {
            ByteBuffer headerBuffer = ByteBuffer.allocate(14);
            byte firstByte = this.opCode;
            if (this.isFinalChunk) {
                firstByte = (byte) (firstByte | 128);
            }
            byte secondByte = 0;
            if (this.isPayloadMasked) {
                secondByte = Byte.MIN_VALUE;
            }
            headerBuffer.put(firstByte);
            if (this.payloadLength > 65535) {
                headerBuffer.put((byte) (secondByte | Byte.MAX_VALUE));
                headerBuffer.putLong(this.payloadLength);
            } else if (this.payloadLength >= 126) {
                headerBuffer.put((byte) (secondByte | 126));
                headerBuffer.putShort((short) this.payloadLength);
            } else {
                if (this.payloadLength != 0) {
                    secondByte = (byte) (secondByte | (this.payloadLength & 127));
                }
                headerBuffer.put(secondByte);
            }
            if (this.isPayloadMasked) {
                headerBuffer.put(this.maskingKey);
            }
            headerBuffer.flip();
            socketChannel.write(headerBuffer);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSFrame.class */
    private static abstract class WSFrame {
        protected WSHeader header;
        protected ByteBuffer payloadBuffer;
        protected SocketChannel socketChannel;

        abstract void readPayload() throws IOException;

        abstract void prepareForWrite() throws IOException;

        private WSFrame(WSHeader header, ByteBuffer payloadBuffer, SocketChannel sc) {
            this.header = header;
            this.payloadBuffer = payloadBuffer;
            this.socketChannel = sc;
        }

        private WSFrame() {
        }

        static WSFrame readFrame(SocketChannel socketChannel, ByteBuffer payloadBuffer) throws IOException {
            WSFrame frame;
            WSHeader wsHeader = new WSHeader();
            wsHeader.read(socketChannel);
            switch (wsHeader.opCode) {
                case 0:
                case 2:
                    frame = new WSBinaryFrame(wsHeader, payloadBuffer, socketChannel);
                    break;
                case 1:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                default:
                    throw new IOException("Websocket : Invalid frame type : " + ((int) wsHeader.opCode));
                case 8:
                    frame = new WSCloseFrame(wsHeader, payloadBuffer, socketChannel);
                    break;
                case 9:
                    frame = new WSPingFrame(wsHeader, payloadBuffer, socketChannel);
                    break;
                case 10:
                    frame = new WSPongFrame(wsHeader, payloadBuffer, socketChannel);
                    break;
            }
            frame.readPayload();
            frame.maskOrUnmaskPayload();
            return frame;
        }

        static void writeFrame(SocketChannel socketChannel, WSFrame wsFrame) throws IOException {
            wsFrame.prepareForWrite();
            wsFrame.header.write(socketChannel);
            if (wsFrame.payloadBuffer != null) {
                wsFrame.maskOrUnmaskPayload();
                while (wsFrame.payloadBuffer.hasRemaining()) {
                    socketChannel.write(wsFrame.payloadBuffer);
                }
            }
        }

        private void maskOrUnmaskPayload() {
            if (this.header.isPayloadMasked && this.header.payloadLength > 0) {
                int intialPosition = this.payloadBuffer.position();
                for (int i = intialPosition; i < this.payloadBuffer.limit(); i++) {
                    byte maskedByte = this.payloadBuffer.get(i);
                    this.payloadBuffer.put(i, (byte) (maskedByte ^ this.header.maskingKey[i % 4]));
                }
                this.payloadBuffer.rewind();
                this.payloadBuffer.position(intialPosition);
            }
        }

        protected void readPayloadFromSocket() throws IOException {
            this.payloadBuffer.clear();
            this.payloadBuffer.limit(this.header.payloadLength);
            if (this.header.payloadLength <= 0) {
                return;
            }
            while (this.payloadBuffer.hasRemaining()) {
                this.socketChannel.read(this.payloadBuffer);
            }
            this.payloadBuffer.flip();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSCloseFrame.class */
    private static class WSCloseFrame extends WSFrame {
        int errorCode;

        private WSCloseFrame(WSHeader header, ByteBuffer payloadBuffer, SocketChannel sc) {
            super(header, payloadBuffer, sc);
        }

        private WSCloseFrame() {
            super();
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void prepareForWrite() throws IOException {
            this.header = new WSHeader();
            this.header.isFinalChunk = true;
            this.header.isPayloadMasked = true;
            this.header.maskingKey = WSSSocketChannel.WS_DUMMY_MASK_KEY;
            this.header.opCode = (byte) 8;
            this.header.payloadLength = 0;
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void readPayload() throws IOException {
            readPayloadFromSocket();
            this.errorCode = this.payloadBuffer.getShort();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSBinaryFrame.class */
    private static class WSBinaryFrame extends WSFrame {
        private WSBinaryFrame(WSHeader header, ByteBuffer payloadBuffer, SocketChannel sc) {
            super(header, payloadBuffer, sc);
        }

        private WSBinaryFrame(ByteBuffer payloadBuffer) {
            super();
            this.payloadBuffer = payloadBuffer;
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void prepareForWrite() throws IOException {
            this.header = new WSHeader();
            this.header.isFinalChunk = true;
            this.header.isPayloadMasked = true;
            this.header.maskingKey = WSSSocketChannel.WS_DUMMY_MASK_KEY;
            this.header.opCode = (byte) 2;
            this.header.payloadLength = this.payloadBuffer.remaining();
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void readPayload() throws IOException {
            readPayloadFromSocket();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSPingFrame.class */
    private static class WSPingFrame extends WSFrame {
        private WSPingFrame(WSHeader header, ByteBuffer payloadBuffer, SocketChannel sc) {
            super(header, payloadBuffer, sc);
        }

        private WSPingFrame(ByteBuffer payloadBuffer, SocketChannel sc) {
            super(null, payloadBuffer, sc);
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void prepareForWrite() throws IOException {
            this.header = new WSHeader();
            this.header.isFinalChunk = true;
            this.header.isPayloadMasked = true;
            this.header.maskingKey = WSSSocketChannel.WS_DUMMY_MASK_KEY;
            this.header.opCode = (byte) 9;
            this.header.payloadLength = this.payloadBuffer != null ? this.payloadBuffer.remaining() : 0;
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void readPayload() throws IOException {
            readPayloadFromSocket();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/WSSSocketChannel$WSPongFrame.class */
    private static class WSPongFrame extends WSFrame {
        private WSPongFrame(WSHeader header, ByteBuffer payloadBuffer, SocketChannel sc) {
            super(header, payloadBuffer, sc);
        }

        private WSPongFrame(ByteBuffer payloadBuffer) {
            super();
            this.payloadBuffer = payloadBuffer;
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void prepareForWrite() throws IOException {
            this.header = new WSHeader();
            this.header.isFinalChunk = true;
            this.header.isPayloadMasked = true;
            this.header.maskingKey = WSSSocketChannel.WS_DUMMY_MASK_KEY;
            this.header.opCode = (byte) 10;
            this.header.payloadLength = this.payloadBuffer != null ? this.payloadBuffer.remaining() : 0;
        }

        @Override // oracle.net.nt.WSSSocketChannel.WSFrame
        void readPayload() throws IOException {
            readPayloadFromSocket();
            if (this.header.payloadLength > 0) {
                byte[] pingData = new byte[this.payloadBuffer.remaining()];
                this.payloadBuffer.get(pingData);
            }
        }
    }
}
