package oracle.net.nt;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.logging.Level;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.net.ssl.SNIHostName;
import javax.net.ssl.SNIServerName;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLParameters;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/net/nt/SNIHelper.class */
class SNIHelper implements Diagnosable {
    private static final String CLASS_NAME = SNIHelper.class.getName();
    private static final Set<String> SNI_PARAMS = new HashSet(Arrays.asList("SERVICE_NAME", "SERVER", "INSTANCE_NAME", "COLOCATION_TAG"));
    private static final Set<String> SNI_DEFAULT_IGNORE_LIST = new HashSet(Arrays.asList("CID", "CONNECTION_ID_PREFIX", "CONNECTION_ID", "POOL_PURITY", "POOL_CONNECTION_CLASS", SQLnetDef.TCP_FAST_OPEN_PARAM_NAME));
    private static final Set<String> SNI_DEFAULT_REDIRECT_IGNORE_LIST = new HashSet();
    private static final Pattern SNI_PATTERN = Pattern.compile("[\\w._-]{11,256}");
    private final Properties socketOptions;
    private final ConnOption connOption;
    private final Diagnosable diagnosable;
    private final String sni;
    private String serviceName;
    private String serverMode;
    private String instance;
    private String colocationTag;

    static {
        SNI_DEFAULT_REDIRECT_IGNORE_LIST.addAll(SNI_DEFAULT_IGNORE_LIST);
        SNI_DEFAULT_REDIRECT_IGNORE_LIST.add("USE_DBROUTER");
    }

    SNIHelper(ConnOption cOption, Diagnosable diagnosable, @Blind(PropertiesBlinder.class) Properties socketOptions) {
        this.connOption = cOption;
        this.diagnosable = diagnosable;
        this.socketOptions = socketOptions;
        this.sni = getSNI(cOption);
    }

    String getSNI() {
        return this.sni;
    }

    void configure(SSLEngine sslEngine) {
        if (this.sni == null || this.sni.isEmpty()) {
            return;
        }
        try {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "newSSLEngine", "Adding SNI={0}", (String) null, (String) null, this.sni);
            SSLParameters sslParams = sslEngine.getSSLParameters();
            List<SNIServerName> sniServerNames = new ArrayList<>(1);
            sniServerNames.add(new SNIHostName(this.sni.getBytes("ASCII")));
            sslParams.setServerNames(sniServerNames);
            sslEngine.setSSLParameters(sslParams);
        } catch (Exception e) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "configureSNI", "SNI not enabled because of failure {0}", (String) null, (String) null, e.getMessage());
        }
    }

    private String getSNI(ConnOption connOption) {
        if (!isSNIEnabled()) {
            return null;
        }
        if (this.serviceName == null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSNI", "Disabling SNI as service name not available.", null, null);
            return null;
        }
        StringBuilder sniBuilder = new StringBuilder();
        sniBuilder.append(format("S", this.serviceName));
        sniBuilder.append(format("T", this.serverMode));
        sniBuilder.append(format("I", this.instance));
        sniBuilder.append(format("C", this.colocationTag));
        if (sniBuilder.length() > 0) {
            sniBuilder.append("V" + SQLnetDef.MAX_NS_VERSION.length() + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + SQLnetDef.MAX_NS_VERSION);
        }
        String sniStr = sniBuilder.toString();
        if (isValidSNI(sniStr)) {
            return sniStr;
        }
        return null;
    }

    private String format(String prefix, String value) {
        if (value == null) {
            return "";
        }
        String value2 = value.trim();
        if (value2.isEmpty()) {
            return "";
        }
        return prefix + value2.length() + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + value2 + OracleConnection.CLIENT_INFO_KEY_SEPARATOR;
    }

    private boolean isValidSNI(String sniStr) {
        boolean isSNIValid = SNI_PATTERN.matcher(sniStr).matches();
        if (!isSNIValid) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "isValidSNI", "Disabling SNI as the generated value {0} is invalid.", (String) null, (String) null, sniStr);
        }
        return isSNIValid;
    }

    private boolean isSNIEnabled() {
        Set<String> sniIgnoreListConfig;
        String sniConfigStr = (String) this.socketOptions.get(47);
        if (sniConfigStr == null) {
            return false;
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "isSNIEnabled", "SNI Config = {0}", (String) null, (String) null, sniConfigStr);
        if (!sniConfigStr.trim().toLowerCase().matches("true|on|yes")) {
            return false;
        }
        if (this.connOption.conn_data == null || this.connOption.conn_data.length() == 0) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "isSNIEnabled", "Unable to get CONNECT_DATA, disabling SNI", null, null);
            return false;
        }
        String sniIgnoreList = (String) this.socketOptions.getOrDefault(48, "");
        Set<String> defaultIgnoreList = this.connOption.redirectedConnection() ? SNI_DEFAULT_REDIRECT_IGNORE_LIST : SNI_DEFAULT_IGNORE_LIST;
        if (sniIgnoreList.isEmpty()) {
            sniIgnoreListConfig = Collections.emptySet();
        } else {
            sniIgnoreListConfig = (Set) Arrays.stream(sniIgnoreList.toUpperCase().split(",")).collect(Collectors.toSet());
        }
        return parseAndValidateConnectData(this.connOption.conn_data.toString(), defaultIgnoreList, sniIgnoreListConfig);
    }

    private boolean parseAndValidateConnectData(String connectData, Set<String> defaultIgnore, Set<String> configIgnore) {
        NVPair cdataNV = getConnectDataNVPair(connectData);
        if (cdataNV == null) {
            return false;
        }
        int paramSize = cdataNV.getListSize();
        for (int i = 0; i < paramSize; i++) {
            NVPair paramNV = cdataNV.getListElement(i);
            String paramName = paramNV.getName().toUpperCase();
            if (SNI_PARAMS.contains(paramName)) {
                initSNIParam(paramName, paramNV.getAtom());
            } else if (!defaultIgnore.contains(paramName) && !configIgnore.contains(paramName)) {
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "isSNIEnabled", "Disabling SNI as param {0} present under CONNECT_DATA. Skip processing for other params", (String) null, (String) null, paramName);
                return false;
            }
        }
        return paramSize > 0;
    }

    private void initSNIParam(String name, String value) {
        switch (name) {
            case "SERVICE_NAME":
                this.serviceName = value;
                break;
            case "SERVER":
                this.serverMode = value.substring(0, 1).toUpperCase();
                break;
            case "INSTANCE_NAME":
                this.instance = value;
                break;
            case "COLOCATION_TAG":
                this.colocationTag = value;
                break;
        }
    }

    private NVPair getConnectDataNVPair(String cdata) {
        try {
            NVPair rootNV = new NVFactory().createNVPair(cdata);
            if (rootNV == null) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectDataNVPair", "Invalid Connect Data = {0}", (String) null, (String) null, cdata);
                return null;
            }
            if (rootNV.getName().equalsIgnoreCase("CONNECT_DATA")) {
                return rootNV;
            }
            NVNavigator navigator = new NVNavigator();
            return navigator.findNVPair(rootNV, "CONNECT_DATA");
        } catch (NLException e) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectDataNVPair", "SNI not enabled because of failure {0}", (String) null, (String) null, e.getMessage());
            return null;
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }
}
