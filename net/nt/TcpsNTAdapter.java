package oracle.net.nt;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.channels.SocketChannel;
import java.security.cert.X509Certificate;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.logging.Level;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import oracle.jdbc.OracleConnectionStringBuilder;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jdbc.nl.NLException;
import oracle.net.nt.NTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/nt/TcpsNTAdapter.class */
public class TcpsNTAdapter extends TcpNTAdapter {
    private static final String CLASS_NAME;
    private static final int SSL_CERT_SAN_TYPE_DNS_NAME = 2;
    private static final int SSL_CERT_SAN_TYPE_IP_ADDR = 7;
    private String dnToMatch;
    private SecurityInformation.DNMatchStatus dnMatchStatus;
    private String hostNameForDNMatch;
    private String serviceNameForDNMatch;
    private SocketChannel underlyingSocketChannel;
    private SSLEngine sslEngine;
    private String webSocketURI;
    private ExtendedSSLContext extendedSSLContext;
    private SSLConfig sslConfig;
    private final DNVerifier dnVerifier;
    private final SNIHelper sniHelper;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !TcpsNTAdapter.class.desiredAssertionStatus();
        CLASS_NAME = TcpsNTAdapter.class.getName();
    }

    public TcpsNTAdapter(String address, @Blind(PropertiesBlinder.class) Properties socketOptions, Diagnosable diagnosable, ConnOption connOption) throws IOException, NLException {
        super(address, socketOptions, diagnosable, connOption);
        this.dnMatchStatus = SecurityInformation.DNMatchStatus.NOT_VERIFIED;
        this.hostNameForDNMatch = null;
        this.serviceNameForDNMatch = null;
        this.underlyingSocketChannel = null;
        this.webSocketURI = "/sqlnet";
        this.hostNameForDNMatch = this.host;
        this.dnVerifier = new DNVerifier(connOption, Boolean.valueOf((String) getOption(106)).booleanValue(), diagnosable);
        this.sniHelper = new SNIHelper(connOption, diagnosable, socketOptions);
        if (this.sniHelper.getSNI() != null) {
            socketOptions.put(49, this.sniHelper.getSNI());
        }
        if (OracleConnectionStringBuilder.PROTOCOL_WSS.equalsIgnoreCase(this.protocol) && connOption.webSocketUri != null) {
            this.webSocketURI = connOption.webSocketUri;
        }
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public void connect(DMSFactory.DMSNoun dmsParent) throws IOException {
        this.sslEngine = newSSLEngine();
        super.connect(dmsParent);
    }

    @Override // oracle.net.nt.TcpNTAdapter
    protected void establishSocket(InetSocketAddress inetAddr, DMSFactory.DMSNoun dmsParent) throws IOException {
        long socketConnectStartTime = System.currentTimeMillis();
        super.establishSocket(inetAddr, dmsParent);
        try {
            this.underlyingSocketChannel = this.socketChannel;
            this.socketChannel = new SSLSocketChannel(this.socketChannel, this.sslEngine, getDiagnosable(), this.dnVerifier, false, this.extendedSSLContext);
            this.socket = this.socketChannel.socket();
            setSSLSocketOptions();
            if (OracleConnectionStringBuilder.PROTOCOL_WSS.equalsIgnoreCase(this.protocol)) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "establishSocket", "websocketURI={0}. ", (String) null, (String) null, this.webSocketURI);
                String httpAuthUser = (String) this.socketOptions.get(26);
                OpaqueString httpAuthPwd = (OpaqueString) this.socketOptions.get(27);
                this.socketChannel = new WSSSocketChannel(this.socketChannel, this.webSocketURI, this.host, this.port, httpAuthUser, httpAuthPwd, getDiagnosable());
            }
        } catch (IOException ioe) {
            throw socketConnectFailure(ioe, System.currentTimeMillis() - socketConnectStartTime, inetAddr.getHostString());
        }
    }

    private IOException socketConnectFailure(IOException initCause, long elapsed, String hostIdentifier) {
        Object[] objArr = new Object[8];
        objArr[0] = initCause.getMessage();
        objArr[1] = Long.valueOf(elapsed);
        objArr[2] = hostIdentifier;
        objArr[3] = Integer.valueOf(this.port);
        objArr[4] = this.proxy == null ? "" : "Proxy = " + this.proxy.toString();
        objArr[5] = Integer.valueOf(this.connectTimeout);
        objArr[6] = getInetAddress();
        objArr[7] = this.useNio;
        String newExMessage = String.format("%s, socket connect lapse %d ms. %s %d %s %s %s %s", objArr);
        IOException newEx = new IOException(newExMessage, initCause);
        return newEx;
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public final CompletionStage<Void> connectAsync(DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) {
        if (!this.useNio.booleanValue()) {
            return CompletionStageUtil.failedStage(new IOException("Asynchronous connection is not supported when oracle.jdbc.javaNetNio=false"));
        }
        if (OracleConnectionStringBuilder.PROTOCOL_WSS.equalsIgnoreCase(this.protocol)) {
            return CompletionStageUtil.failedStage(new IOException("Asynchronous connection is not supported with the WebSocket Secureprotocol"));
        }
        try {
            this.sslEngine = newSSLEngine();
            return super.connectAsync(dmsParent, outboundTimeout, asyncExecutor);
        } catch (IOException newEngineFailure) {
            return CompletionStageUtil.failedStage(newEngineFailure);
        }
    }

    @Override // oracle.net.nt.TcpNTAdapter
    protected CompletionStage<Void> establishSocketAsync(InetSocketAddress inetAddr, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeout, Executor asyncExecutor) {
        long socketConnectStartTime = System.currentTimeMillis();
        return super.establishSocketAsync(inetAddr, dmsParent, outboundTimeout, asyncExecutor).thenCompose(nil -> {
            try {
                this.underlyingSocketChannel = this.socketChannel;
                SSLSocketChannel sslSocketChannel = new SSLSocketChannel(this.socketChannel, this.sslEngine, getDiagnosable(), this.dnVerifier, false, this.extendedSSLContext);
                this.socketChannel = sslSocketChannel;
                setSSLSocketOptions();
                return sslSocketChannel.doSSLHandshakeAsync(asyncExecutor).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, ioException -> {
                    throw new AsyncHandshakeException(ioException);
                }));
            } catch (IOException ioe) {
                return CompletionStageUtil.failedStage(socketConnectFailure(ioe, System.currentTimeMillis() - socketConnectStartTime, inetAddr.getHostString()));
            }
        });
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TcpsNTAdapter$AsyncHandshakeException.class */
    static class AsyncHandshakeException extends IOException {
        AsyncHandshakeException(IOException cause) {
            super(cause);
        }
    }

    public void renegotiateSession() throws IOException {
        this.sslEngine = newSSLEngine();
        SSLSocketChannel newSSLSocketChannel = new SSLSocketChannel(this.underlyingSocketChannel, this.sslEngine, getDiagnosable(), this.dnVerifier, true, this.extendedSSLContext);
        if (this.protocol.equalsIgnoreCase("wss")) {
            this.socketChannel.setUnderlyingChannel(newSSLSocketChannel);
        } else {
            this.socketChannel = newSSLSocketChannel;
        }
        this.socket = this.socketChannel.socket();
        String temp = (String) this.socketOptions.get(3);
        if (temp != null) {
            setOption(3, temp);
        }
        setSSLSocketOptions();
    }

    public void setSSLSocketOptions() throws IOException {
        super.setSocketOptions();
    }

    public String getNegotiatedTLSVersion() throws IOException {
        SSLSession session = getSSLSession();
        if (session != null) {
            return session.getProtocol();
        }
        return null;
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public void setOption(int option, Object value) throws IOException {
        switch (option) {
            case 108:
                setServerDNMatchValue((String[]) value);
                break;
            default:
                super.setOption(option, value);
                break;
        }
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public Object getOption(int option) throws IOException {
        switch (option) {
            case 102:
                String cipherSuiteInUse = getSSLSession().getCipherSuite();
                if (cipherSuiteInUse != null && cipherSuiteInUse.indexOf("NULL") == -1) {
                    return "TRUE";
                }
                return "FALSE";
            case 103:
                X509Certificate l_certificate = (X509Certificate) getSSLSession().getPeerCertificates()[0];
                return l_certificate.getSubjectDN().getName();
            case 104:
                return getSSLSession().getPeerCertificateChain();
            case 105:
                return getSSLSession().getCipherSuite();
            case 106:
                String sslServerDNMatchRequiredString = (String) this.socketOptions.getOrDefault(4, this.socketOptions.get(40));
                return Boolean.toString(sslServerDNMatchRequiredString.equalsIgnoreCase("YES") || sslServerDNMatchRequiredString.equalsIgnoreCase("ON") || sslServerDNMatchRequiredString.equalsIgnoreCase("TRUE"));
            default:
                return super.getOption(option);
        }
    }

    private SSLSession getSSLSession() {
        return this.sslEngine.getSession();
    }

    public SecurityInformation.DNMatchStatus getDNMatchStatus() {
        return ((SSLSocketChannel) this.socketChannel).getDnMatchStatus();
    }

    public String getSNI() {
        return this.sniHelper.getSNI();
    }

    public void verifyDN() throws IOException {
        if (this.dnVerifier.isWeakDNMatchAllowed()) {
            ((SSLSocketChannel) this.socketChannel).verifyDN();
        }
    }

    private void setServerDNMatchValue(String[] value) {
        String origSSLServerCertDN = value[0];
        String origServiceName = value[1];
        if (value[2] != null) {
            this.hostNameForDNMatch = value[2];
        }
        if (origSSLServerCertDN != null) {
            this.dnToMatch = origSSLServerCertDN;
        }
        if (origServiceName != null) {
            this.serviceNameForDNMatch = origServiceName.trim();
        }
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public NTAdapter.NetworkAdapterType getNetworkAdapterType() {
        return NTAdapter.NetworkAdapterType.TCPS;
    }

    private SSLEngine newSSLEngine() throws IOException {
        if (this.extendedSSLContext == null) {
            if (this.sslConfig == null) {
                this.sslConfig = SSLConfig.newInstance(this.socketOptions);
            }
            this.extendedSSLContext = SSLContextCache.instance().get(this.sslConfig, getDiagnosable());
        }
        SSLEngine newEngine = this.extendedSSLContext.context().createSSLEngine(this.inetSocketAddress.getHostString(), this.port);
        TcpsConfigure.configure(newEngine, this.socketOptions, getDiagnosable());
        newEngine.setUseClientMode(true);
        this.sniHelper.configure(newEngine);
        return newEngine;
    }

    @Override // oracle.net.nt.TcpNTAdapter, oracle.net.nt.NTAdapter
    public final void registerForNonBlockingRead(Consumer<Throwable> onReady) throws IOException {
        if (!$assertionsDisabled && !(this.socketChannel instanceof SSLSocketChannel)) {
            throw new AssertionError("Not an SSLSocketChannel: " + this.socketChannel);
        }
        if (((SSLSocketChannel) this.socketChannel).hasRemaining()) {
            onReady.accept(null);
        } else {
            super.registerForNonBlockingRead(onReady);
        }
    }

    void setSSLContext(SSLContext sslContext) {
        this.extendedSSLContext = ExtendedSSLContext.wrap(sslContext);
    }
}
