package oracle.net.nt;

import java.util.concurrent.atomic.LongAdder;

/* loaded from: ojdbc8.jar:oracle/net/nt/SSLContextCacheInfo.class */
public final class SSLContextCacheInfo {
    private static final LongAdder cacheHitCount = new LongAdder();
    private static final LongAdder cacheMissCount = new LongAdder();
    private static final LongAdder cacheRemoveCount = new LongAdder();
    private static boolean CAPTURE_CACHE_INFO = false;

    public static boolean isCacheEnabled() {
        return !SSLContextCache.DISABLE_CACHE;
    }

    public static long cacheHitCount() {
        return cacheHitCount.longValue();
    }

    public static long cacheMissCount() {
        return cacheMissCount.longValue();
    }

    public static long cacheRemoveCount() {
        return cacheRemoveCount.longValue();
    }

    public static void reset() {
        SSLContextCache.instance().reset();
        cacheHitCount.reset();
        cacheMissCount.reset();
        cacheRemoveCount.reset();
    }

    public static void captureCacheInfo() {
        CAPTURE_CACHE_INFO = true;
    }

    public static int cacheSize() {
        return SSLContextCache.instance().cacheSize();
    }

    static void incrementCacheHit() {
        if (CAPTURE_CACHE_INFO) {
            cacheHitCount.increment();
        }
    }

    static void incrementCacheMiss() {
        if (CAPTURE_CACHE_INFO) {
            cacheMissCount.increment();
        }
    }

    static void incrementCacheRemove() {
        if (CAPTURE_CACHE_INFO) {
            cacheRemoveCount.increment();
        }
    }
}
