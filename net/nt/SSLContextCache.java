package oracle.net.nt;

import java.io.File;
import java.io.IOException;
import java.security.Security;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/nt/SSLContextCache.class */
class SSLContextCache {
    private static final String CLASS_NAME = SSLContextCache.class.getName();
    private static final SSLContextCache SINGLETON_INSTANCE = new SSLContextCache();
    private static final ConcurrentHashMap<SSLConfig, CacheEntry> CACHE_MAP = new ConcurrentHashMap<>();
    private static final Lock CACHE_LOCK = new ReentrantLock();
    private static final Set<String> CACHEABLE_KEYSTORE_TYPES = (Set) Stream.of((Object[]) new String[]{SSLConfig.SSO_WALLET_TYPE, SSLConfig.PEM_WALLET_TYPE, SSLConfig.PKCS12_WALLET_TYPE, SSLConfig.JKS_TYPE, SSLConfig.DATA_URI_TYPE}).collect(Collectors.toSet());
    private static int MAX_CACHE_SIZE;
    static final boolean DISABLE_CACHE;

    static {
        MAX_CACHE_SIZE = 15;
        try {
            MAX_CACHE_SIZE = Integer.parseInt(System.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CONTEXT_CACHE_SIZE, OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CONTEXT_CACHE_SIZE_DEFAULT));
        } catch (NumberFormatException e) {
        }
        DISABLE_CACHE = MAX_CACHE_SIZE <= 0;
    }

    private SSLContextCache() {
    }

    static SSLContextCache instance() {
        return SINGLETON_INSTANCE;
    }

    int cacheSize() {
        return CACHE_MAP.size();
    }

    void reset() {
        CACHE_LOCK.lock();
        CACHE_MAP.clear();
        CACHE_LOCK.unlock();
    }

    ExtendedSSLContext get(SSLConfig sslConfig, Diagnosable diagnosable) throws IOException {
        Diagnosable commonDiagnosable;
        if (diagnosable == null) {
            try {
                commonDiagnosable = CommonDiagnosable.getInstance();
            } catch (Throwable th) {
                sslConfig.diagnosable = null;
                throw th;
            }
        } else {
            commonDiagnosable = diagnosable;
        }
        sslConfig.diagnosable = commonDiagnosable;
        if (DISABLE_CACHE || !cacheable(sslConfig)) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "get", "Not Using Cache. Creating SSLContext for config = {0}", (String) null, (String) null, sslConfig);
            ExtendedSSLContext extendedSSLContextCreateSSLContext = createSSLContext(sslConfig);
            sslConfig.diagnosable = null;
            return extendedSSLContextCreateSSLContext;
        }
        CacheEntry cacheEntry = CACHE_MAP.get(sslConfig);
        if (cacheEntry == null || !cacheEntry.isValid()) {
            ExtendedSSLContext extendedSSLContextContext = createCacheEntry(sslConfig).context();
            sslConfig.diagnosable = null;
            return extendedSSLContextContext;
        }
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "get", "Got a valid entry in cache and returning it.", null, null);
        SSLContextCacheInfo.incrementCacheHit();
        ExtendedSSLContext extendedSSLContextContext2 = cacheEntry.context();
        sslConfig.diagnosable = null;
        return extendedSSLContextContext2;
    }

    private CacheEntry createCacheEntry(SSLConfig config) throws IOException {
        CACHE_LOCK.lock();
        try {
            CacheEntry entry = CACHE_MAP.get(config);
            if (entry != null) {
                if (entry.isValid()) {
                    config.diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createCacheEntry", "Found a valid entry in cache and returning it.", null, null);
                    SSLContextCacheInfo.incrementCacheHit();
                    CACHE_LOCK.unlock();
                    return entry;
                }
                config.diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createCacheEntry", "Removing invalid entry for config = {0}", (String) null, (String) null, config);
                removeEntry(config);
            }
            config.diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createCacheEntry", "No entry in cache, creating new one.", null, null);
            ExtendedSSLContext context = createSSLContext(config);
            SSLContextCacheInfo.incrementCacheMiss();
            if (CACHE_MAP.size() >= MAX_CACHE_SIZE) {
                removeLRUEntry(config.diagnosable);
            }
            CacheEntry newEntry = new CacheEntry(context, config);
            CACHE_MAP.put(config, newEntry);
            CACHE_LOCK.unlock();
            return newEntry;
        } catch (Throwable th) {
            CACHE_LOCK.unlock();
            throw th;
        }
    }

    private void removeLRUEntry(Diagnosable diagnosable) {
        Enumeration<SSLConfig> keys = CACHE_MAP.keys();
        long usedTime = Long.MAX_VALUE;
        SSLConfig configToBeRemoved = null;
        while (keys.hasMoreElements()) {
            SSLConfig config = keys.nextElement();
            CacheEntry cacheEntry = CACHE_MAP.get(config);
            if (cacheEntry.lastUsed() < usedTime) {
                configToBeRemoved = config;
                usedTime = cacheEntry.lastUsed();
            }
        }
        if (configToBeRemoved != null) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "removeLRUEntry", "Reached cache max size. Removing the least recently used entry = {0}", (String) null, (String) null, configToBeRemoved);
            removeEntry(configToBeRemoved);
        }
    }

    private ExtendedSSLContext createSSLContext(SSLConfig config) throws IOException {
        return CustomSSLSocketFactory.getSSLContext(config, config.diagnosable);
    }

    private boolean cacheable(SSLConfig config) {
        if (isCachebleKeyStoreType(config.getKeyStoreType()) || isCachebleKeyStoreType(config.getTrustStoreType())) {
            return true;
        }
        return false;
    }

    private boolean isCachebleKeyStoreType(String type) {
        if (type == null || type.isEmpty()) {
            return false;
        }
        return CACHEABLE_KEYSTORE_TYPES.contains(type);
    }

    private void removeEntry(SSLConfig config) {
        if (CACHE_MAP.remove(config) != null) {
            SSLContextCacheInfo.incrementCacheRemove();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/SSLContextCache$CacheEntry.class */
    private static class CacheEntry {
        private final ExtendedSSLContext extendedSSLContext;
        private final SSLConfig config;
        private long lastUsed;
        private final String[] cachedFiles = cachedFiles();
        private final Long[] cachedFileModifiedTimes = new Long[this.cachedFiles.length];

        CacheEntry(ExtendedSSLContext ectx, SSLConfig config) {
            this.extendedSSLContext = ectx;
            this.config = config;
            for (int i = 0; i < this.cachedFiles.length; i++) {
                this.cachedFileModifiedTimes[i] = Long.valueOf(new File(this.cachedFiles[i]).lastModified());
            }
            this.lastUsed = System.currentTimeMillis();
        }

        boolean isValid() {
            for (int i = 0; i < this.cachedFiles.length; i++) {
                if (isKeyStoreModified(this.cachedFiles[i], this.cachedFileModifiedTimes[i].longValue())) {
                    CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, SSLContextCache.CLASS_NAME, "isValid", "Invalid entry, Keystore file {0} updated or removed.", (String) null, (String) null, this.cachedFiles[i]);
                    return false;
                }
            }
            if (isProviderRemoved(this.extendedSSLContext.getKeyStoreProvider()) || isProviderRemoved(this.extendedSSLContext.getTrustStoreProvider())) {
                return false;
            }
            return true;
        }

        ExtendedSSLContext context() {
            this.lastUsed = System.currentTimeMillis();
            return this.extendedSSLContext;
        }

        long lastUsed() {
            return this.lastUsed;
        }

        private String[] cachedFiles() {
            if (this.config.isWallet()) {
                return (this.config.getKeyStore() == null || SSLConfig.isDataUri(this.config.getKeyStore())) ? new String[0] : new String[]{this.config.getKeyStore()};
            }
            List<String> files = new LinkedList<>();
            if (this.config.getKeyStore() != null) {
                files.add(this.config.getKeyStore());
            }
            if (this.config.getTrustStore() != null) {
                files.add(this.config.getTrustStore());
            }
            return (String[]) files.toArray(new String[files.size()]);
        }

        private boolean isKeyStoreModified(String filePath, long creationTime) {
            File ksFile = new File(filePath);
            if (!ksFile.exists() || ksFile.lastModified() > creationTime) {
                return true;
            }
            return false;
        }

        private boolean isProviderRemoved(String provider) {
            if (provider != null && Security.getProvider(provider) == null) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, SSLContextCache.CLASS_NAME, "isProviderRemoved", "Provider {0} removed at runtime.", (String) null, (String) null, provider);
                return true;
            }
            return false;
        }
    }
}
