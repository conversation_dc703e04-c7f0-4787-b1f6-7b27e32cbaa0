package oracle.net.nt;

import java.io.IOException;
import java.security.cert.CertificateParsingException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.logging.Level;
import javax.naming.InvalidNameException;
import javax.naming.ldap.LdapName;
import javax.naming.ldap.Rdn;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.SecurityInformation;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/DNVerifier.class */
class DNVerifier implements Diagnosable {
    private static final String CLASS_NAME = DNVerifier.class.getName();
    private static final int SSL_CERT_SAN_TYPE_DNS_NAME = 2;
    private static final int SSL_CERT_SAN_TYPE_IP_ADDR = 7;
    private final ConnOption connOption;
    private final boolean isDNmatchEnabled;
    private final Diagnosable diagnosable;
    private SecurityInformation.DNMatchStatus dnMatchStatus = SecurityInformation.DNMatchStatus.NOT_VERIFIED;

    DNVerifier(ConnOption connOption, boolean isDNmatchEnabled, Diagnosable diagnosable) {
        this.connOption = connOption;
        this.isDNmatchEnabled = isDNmatchEnabled;
        this.diagnosable = diagnosable;
    }

    SecurityInformation.DNMatchStatus verify(X509Certificate serverCert) throws IOException {
        if (!this.isDNmatchEnabled) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "verify", "Server DN verification is disabled and connection is not secure.Enable DN verification through Connection Property 'oracle.net.ssl_server_dn_match' or through URL parameter 'SSL_SERVER_DN_MATCH'", null, null);
            return SecurityInformation.DNMatchStatus.NOT_VERIFIED;
        }
        return verifyServerCertificate(serverCert);
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    boolean isWeakDNMatchAllowed() {
        ConnOption originalConnOption = this.connOption.getOriginalConnOption();
        return originalConnOption.sslAllowWeakDNMatch != null && (originalConnOption.sslAllowWeakDNMatch.equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || originalConnOption.sslAllowWeakDNMatch.equalsIgnoreCase("true") || originalConnOption.sslAllowWeakDNMatch.equalsIgnoreCase("yes"));
    }

    public SecurityInformation.DNMatchStatus verifyServerCertificate(X509Certificate serverCertificate) throws IOException {
        ConnOption originalConnOption = this.connOption.getOriginalConnOption();
        if (originalConnOption.sslServerCertDN != null) {
            return verifyConfiguredDN(serverCertificate, originalConnOption.sslServerCertDN);
        }
        return verifyHostOrServiceName(serverCertificate);
    }

    private SecurityInformation.DNMatchStatus verifyConfiguredDN(X509Certificate serverCertificate, String dnToMatch) throws IOException {
        String serverCertDN = serverCertificate.getSubjectDN().getName();
        if (doFullDNMatch(serverCertDN, dnToMatch)) {
            return SecurityInformation.DNMatchStatus.VERIFIED_MATCHING_CONFIG;
        }
        throw new NetException(NetException.MISMATCH_SERVER_CERT_DN, null, false, dnToMatch, serverCertDN);
    }

    private SecurityInformation.DNMatchStatus verifyHostOrServiceName(X509Certificate serverCertificate) throws IOException {
        String hostNameForError;
        ConnOption originalConnOption = this.connOption.getOriginalConnOption();
        String hostNameForDNMatch = this.connOption.host;
        String originalHostNameForDNMatch = originalConnOption.host;
        if (matchCNAndSANs(serverCertificate, hostNameForDNMatch) || matchCNAndSANs(serverCertificate, originalHostNameForDNMatch)) {
            return SecurityInformation.DNMatchStatus.VERIFIED_MATCHING_HOSTNAME;
        }
        boolean isAllowWeakDNMatchEnabled = isWeakDNMatchAllowed();
        String originalServiceNameForDNMatch = originalConnOption.service_name;
        if (Objects.equals(originalHostNameForDNMatch, hostNameForDNMatch)) {
            hostNameForError = hostNameForDNMatch;
        } else {
            hostNameForError = originalHostNameForDNMatch + ", " + hostNameForDNMatch;
        }
        if (!isAllowWeakDNMatchEnabled || originalServiceNameForDNMatch == null) {
            throw new NetException(NetException.MISMATCH_SERVER_CERT_DN_HOSTNAME, null, false, hostNameForError, getCNValue(serverCertificate), Optional.ofNullable(getDNSSubjectAlts(serverCertificate)).map((v0) -> {
                return Arrays.toString(v0);
            }).orElse("null"));
        }
        if (matchSANs(serverCertificate, originalServiceNameForDNMatch) || matchCN(serverCertificate, originalServiceNameForDNMatch)) {
            return SecurityInformation.DNMatchStatus.VERIFIED_MATCHING_SERVICENAME;
        }
        throw new NetException(NetException.MISMATCH_SERVER_CERT_DN_SERVICE_NAME, null, false, hostNameForError, originalServiceNameForDNMatch, getCNValue(serverCertificate), Optional.ofNullable(getDNSSubjectAlts(serverCertificate)).map((v0) -> {
            return Arrays.toString(v0);
        }).orElse("null"));
    }

    private boolean doFullDNMatch(String serverDN, String matchDN) throws IOException {
        if (serverDN == null || matchDN == null) {
            return false;
        }
        try {
            LdapName serverLdapName = new LdapName(serverDN);
            LdapName userLdapName = new LdapName(matchDN);
            return serverLdapName.equals(userLdapName);
        } catch (InvalidNameException e) {
            throw new IOException((Throwable) e);
        }
    }

    private boolean matchCNAndSANs(X509Certificate serverCertificate, String hostName) throws IOException {
        if (hostName == null) {
            return false;
        }
        if (matchSANs(serverCertificate, hostName) || matchCN(serverCertificate, hostName)) {
            return true;
        }
        return false;
    }

    private boolean matchCN(X509Certificate serverCert, String matchStr) throws IOException {
        String serverCN = getCNValue(serverCert);
        return compare(serverCN, matchStr);
    }

    private boolean matchSANs(X509Certificate serverCert, String matchStr) throws CertificateParsingException, IOException {
        String[] altNames = getDNSSubjectAlts(serverCert);
        if (altNames != null) {
            for (String altName : altNames) {
                if (compare(altName, matchStr)) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    private String[] getDNSSubjectAlts(X509Certificate serverCert) throws CertificateParsingException, IOException {
        try {
            LinkedList<String> subjectAltList = new LinkedList<>();
            Collection<List<?>> altNames = serverCert.getSubjectAlternativeNames();
            if (altNames == null) {
                return null;
            }
            for (List<?> altName : altNames) {
                Integer type = (Integer) altName.get(0);
                if (type.intValue() == 2 || type.intValue() == 7) {
                    subjectAltList.add((String) altName.get(1));
                }
            }
            return (String[]) subjectAltList.toArray(new String[subjectAltList.size()]);
        } catch (CertificateParsingException cpe) {
            throw new IOException(cpe);
        }
    }

    private String getCNValue(X509Certificate serverCert) throws IOException {
        try {
            return getCNValue(new LdapName(serverCert.getSubjectDN().getName()));
        } catch (InvalidNameException invalidNameException) {
            throw new IOException((Throwable) invalidNameException);
        }
    }

    private String getCNValue(LdapName ldapName) {
        for (Rdn rdn : ldapName.getRdns()) {
            if (rdn.getType().equalsIgnoreCase("CN")) {
                return (String) rdn.getValue();
            }
        }
        return null;
    }

    private boolean compare(String certValue, String matchValue) {
        String certValue2 = certValue.toLowerCase();
        String matchValue2 = matchValue.toLowerCase();
        if (certValue2.equals(matchValue2)) {
            return true;
        }
        int certLeftLblEndIndex = certValue2.indexOf(46);
        int matchLeftLblEndIndex = matchValue2.indexOf(46);
        if (certLeftLblEndIndex > 0 && matchLeftLblEndIndex > 0) {
            String certValRemaining = certValue2.substring(certLeftLblEndIndex);
            String matchValRemaining = matchValue2.substring(matchLeftLblEndIndex);
            if (certValRemaining.equals(matchValRemaining)) {
                String certValLeftLbl = certValue2.substring(0, certLeftLblEndIndex);
                String matchValLeftLbl = matchValue2.substring(0, matchLeftLblEndIndex);
                return wildcardCompare(certValLeftLbl, matchValLeftLbl);
            }
            return false;
        }
        return false;
    }

    private boolean wildcardCompare(String certLftLbl, String matchLftLbl) {
        if (certLftLbl.equals("*")) {
            return !matchLftLbl.isEmpty();
        }
        int wildcardCharIndex = certLftLbl.indexOf(42);
        if (wildcardCharIndex == -1) {
            return false;
        }
        if (wildcardCharIndex == certLftLbl.length() - 1) {
            return matchLftLbl.startsWith(certLftLbl.substring(0, certLftLbl.length() - 1));
        }
        if (wildcardCharIndex == 0) {
            return matchLftLbl.endsWith(certLftLbl.substring(1));
        }
        String startPattern = certLftLbl.substring(0, wildcardCharIndex);
        String endPattern = certLftLbl.substring(wildcardCharIndex + 1);
        return matchLftLbl.startsWith(startPattern) && matchLftLbl.endsWith(endPattern);
    }
}
