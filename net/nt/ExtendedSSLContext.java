package oracle.net.nt;

import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.Security;
import java.util.Enumeration;
import java.util.logging.Level;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.OpaqueString;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/ExtendedSSLContext.class */
class ExtendedSSLContext implements Diagnosable {
    private static final String CLASS_NAME = ExtendedSSLContext.class.getName();
    private SSLContext context;
    private SSLConfig config;
    private KeyStore keyStore;
    private KeyStore trustStore;
    private KeyManager[] keyManagers;
    private TrustManager[] trustManagers;
    private PEMKeyStore pemKeyStore;
    private String keyStoreProvider;
    private String trustStoreProvider;

    static ExtendedSSLContext newInstance(SSLConfig config) throws NetException {
        return new ExtendedSSLContext(config);
    }

    static ExtendedSSLContext wrap(SSLContext sslContext) {
        return new ExtendedSSLContext(sslContext);
    }

    private ExtendedSSLContext(SSLConfig config) throws KeyManagementException, NetException {
        this.config = config;
        createSSLContext();
    }

    private ExtendedSSLContext(SSLContext ctx) {
        this.config = null;
        this.context = ctx;
    }

    SSLContext context() {
        return this.context;
    }

    SSLConfig config() {
        return this.config;
    }

    String getKeyStoreProvider() {
        return this.keyStoreProvider;
    }

    String getTrustStoreProvider() {
        return this.trustStoreProvider;
    }

    PEMKeyStore getPemKeyStore() {
        return this.pemKeyStore;
    }

    private void createSSLContext() throws KeyManagementException, NetException {
        try {
            initKeyStore();
            initKeyManagers();
        } catch (Exception e) {
            handleException(e, NetException.UNABLE_TO_INIT_KEY_STORE);
        }
        try {
            initTrustStore();
            initTrustManagers();
        } catch (Exception e2) {
            handleException(e2, NetException.UNABLE_TO_INIT_TRUST_STORE);
        }
        try {
            this.context = SSLContext.getInstance(this.config.getSslContextProtocol());
            this.context.init(this.keyManagers, this.trustManagers, null);
        } catch (Exception e3) {
            handleException(e3, NetException.UNABLE_TO_INIT_SSL_CONTEXT);
        }
    }

    private void handleException(Exception e, int netErrNumber) throws NetException {
        if (e instanceof NetException) {
            throw ((NetException) e);
        }
        throw ((NetException) new NetException(netErrNumber).initCause(e));
    }

    private void initKeyStore() throws Exception {
        if (this.config.getKeyStore() == null) {
            return;
        }
        this.keyStore = createKeyStore(this.config.getKeyStoreType(), this.config.getKeyStore(), this.config.getKeyStorePassword(), false);
        if (isProviderRegistered(this.keyStore)) {
            this.keyStoreProvider = this.keyStore.getProvider().getName();
        }
    }

    private void initKeyManagers() throws Exception {
        if (this.keyStore == null) {
            return;
        }
        char[] pwd = this.config.getKeyStorePassword().getChars();
        try {
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(this.config.getKeyManagerFacAlgo());
            kmf.init(this.keyStore, pwd);
            this.keyManagers = AliasKeyManager.wrapIfNeeded(this.config, kmf.getKeyManagers(), this.keyStore);
        } finally {
            CustomSSLSocketFactory.clearPwd(pwd);
        }
    }

    private void initTrustStore() throws Exception {
        if (this.config.getTrustStore() == null) {
            return;
        }
        if (this.config.isCaCertsTrusted()) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initTrustStore", "Loading Truststore and including CA certificates as well", null, null);
            this.trustStore = CustomSSLSocketFactory.mergeCaCerts(createKeyStore(this.config.getTrustStoreType(), this.config.getTrustStore(), this.config.getTrustStorePassword(), true));
        } else if (this.config.isKeyStoreTrustStore() && this.keyStore != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initTrustStore", "For SSO, PKCS12 and PEM wallets, KeyStore and TrustStore are same. Avoid loading it twice.", null, null);
            this.trustStore = this.keyStore;
        } else {
            this.trustStore = createKeyStore(this.config.getTrustStoreType(), this.config.getTrustStore(), this.config.getTrustStorePassword(), true);
        }
        if (isProviderRegistered(this.trustStore)) {
            this.trustStoreProvider = this.trustStore.getProvider().getName();
        }
    }

    private void initTrustManagers() throws Exception {
        if (this.config.isWallet() && this.trustStore != null && !containsTrustCertificate()) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "initTrustManagers", "Not initializing TrustManagers as the TrustStore does not contain trust certificates.", null, null);
            return;
        }
        if (this.config.getTrustManagerFacAlgo() == null) {
            return;
        }
        char[] pw = this.config.getTrustStorePassword() == null ? null : this.config.getTrustStorePassword().getChars();
        try {
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(this.config.getTrustManagerFacAlgo());
            tmf.init(this.trustStore);
            this.trustManagers = tmf.getTrustManagers();
        } finally {
            CustomSSLSocketFactory.clearPwd(pw);
        }
    }

    private KeyStore createKeyStore(String type, String path, OpaqueString pwd, boolean isTrustStore) throws Exception {
        KeyStore ks;
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createKeyStore", "Creating Keystore Type = {0}, Path = {1}, isTrustStore = {2}", null, null, type, path, Boolean.valueOf(isTrustStore));
        PEMKeyStore pks = null;
        if (SSLConfig.DATA_URI_TYPE.equals(type)) {
            DataURIKeyStore dataURIKeyStore = new DataURIKeyStore(this.config, isTrustStore);
            ks = dataURIKeyStore.getKeyStore();
            pks = dataURIKeyStore.getPemKeyStore();
        } else {
            ks = CustomSSLSocketFactory.getKeyStoreInstance(type, getDiagnosable());
            if (SSLConfig.PEM_WALLET_TYPE.equals(type)) {
                pks = new PEMKeyStore(this.config, ks, isTrustStore);
            } else if (SSLConfig.KSS_TYPE.equals(type)) {
                CustomSSLSocketFactory.loadKSSKeyStore(ks, path, pwd);
            } else {
                CustomSSLSocketFactory.loadFileBasedKeyStore(ks, path, pwd);
            }
        }
        if (pks != null && !isTrustStore) {
            this.pemKeyStore = pks;
        }
        return ks;
    }

    private boolean containsTrustCertificate() throws KeyStoreException {
        Enumeration<String> aliases = this.trustStore.aliases();
        while (aliases.hasMoreElements()) {
            if (this.trustStore.isCertificateEntry(aliases.nextElement())) {
                return true;
            }
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "containsTrustCertificate", "TrustManager does not contain trust certificate", null, null);
        return false;
    }

    private boolean isProviderRegistered(KeyStore ks) {
        return (ks == null || ks.getProvider() == null || Security.getProvider(ks.getProvider().getName()) == null) ? false : true;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return (this.config == null || this.config.diagnosable == null) ? CommonDiagnosable.getInstance() : this.config.diagnosable;
    }
}
