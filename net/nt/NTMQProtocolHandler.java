package oracle.net.nt;

import com.oracle.common.internal.net.ipclw.mql.LocalQueue;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/NTMQProtocolHandler.class */
public class NTMQProtocolHandler {
    private static final String CLASS_NAME = NTMQProtocolHandler.class.getName();
    private ByteBuffer remoteQueueNameBuffer;
    private byte ntmqVersion;
    private byte msgqltProtocolType;
    private byte[] sessionIdentifier;
    private int tduMTUSize;
    private short connectFlag;
    private short endianess;
    private short messageQueueLength;
    private boolean sendSessionIdentifier;
    private boolean sendSessionIdentifierIMD;
    private boolean rcvSessionIdentifier;
    private boolean rcvSessionIdentifierIMD;
    private boolean fcEnabled;
    private short fcInitialPostCount;
    private long remoteFcbAddr;
    private ByteBuffer remoteFcbKey;
    protected static final byte NTMQHTCN = 1;
    protected static final byte NTMQHTDC = 2;
    protected static final byte NTMQHTRF = 3;
    protected static final byte NTMQHTDA = 4;
    protected static final byte NTMQHTEO = 5;
    protected static final byte NTMQHTIR = 6;
    protected static final byte NTMQHTDI = 7;
    protected static final byte NTMQHFARQ = 1;
    protected static final byte NTMQHFARS = 2;
    protected static final byte NTMQHFSID = 4;
    protected static final byte NTMQHFPAD = 8;
    private static final short NTMQCFSI = 1;
    private static final short NTMQCFSIIMD = 2;
    private static final short NTMQ_BIG_ENDIAN = 1;
    private static final short NTMQ_LITTLE_ENDIAN = 256;
    protected static final byte NO_SID_HEADER_SIZE = 2;
    protected static final int SESSION_IDENTIFIER_SIZE = 16;
    protected static final byte WITH_SID_HEADER_SIZE = 18;
    protected static final int MIN_CONNECT_PACKET_SIZE = 28;
    protected static final int MIN_FLOW_CONTROL_PACKET_SIZE = 12;
    private byte packetType = -1;
    private byte packetFlag = -1;
    private int headerPacketSize = 2;

    NTMQProtocolHandler(byte msgqltProtocolType, boolean queueMsgRequireSID, boolean rdmaMsgRequireSID) {
        this.msgqltProtocolType = msgqltProtocolType;
        this.rcvSessionIdentifier = queueMsgRequireSID;
        this.rcvSessionIdentifierIMD = rdmaMsgRequireSID;
    }

    protected byte getPacketType() {
        return this.packetType;
    }

    public void processNTMQPacket(ByteBuffer message) throws NetException {
        ByteOrder originalByteOrder = message.order();
        message.order(ByteOrder.LITTLE_ENDIAN);
        parseHeaderPacket(message);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "processNTMQPacket", "NTMQ Packet Type is {0}", (String) null, (String) null, Byte.valueOf(this.packetType));
        if (this.packetType == 1) {
            parseConnectPacket(message);
        } else if (this.packetType < 1 || this.packetType > 7) {
            throw new NetException(NetException.NT_NTMQ_INVALID_PACKET);
        }
        message.order(originalByteOrder);
    }

    protected void parseHeaderPacket(ByteBuffer headerPacket) {
        ByteOrder originalByteOrder = headerPacket.order();
        headerPacket.order(ByteOrder.LITTLE_ENDIAN);
        this.packetType = headerPacket.get();
        this.packetFlag = headerPacket.get();
        headerPacket.order(originalByteOrder);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "parseHeaderPacket", "NTMQ Header Packet received on remoteQueue. Size {0}, Type {1}, Flag {2}", null, null, Integer.valueOf(headerPacket.position()), Byte.valueOf(this.packetType), Byte.valueOf(this.packetFlag));
    }

    protected void parseConnectPacket(ByteBuffer connectPacket) {
        this.ntmqVersion = connectPacket.get();
        this.msgqltProtocolType = connectPacket.get();
        this.connectFlag = connectPacket.getShort();
        if ((this.connectFlag & 1) != 0) {
            this.sendSessionIdentifier = true;
            this.headerPacketSize = 18;
        } else {
            this.sendSessionIdentifier = false;
            this.headerPacketSize = 2;
        }
        if ((this.connectFlag & 2) != 0) {
            this.sendSessionIdentifierIMD = true;
        }
        this.sessionIdentifier = new byte[16];
        connectPacket.get(this.sessionIdentifier, 0, 16);
        this.tduMTUSize = connectPacket.getInt();
        this.endianess = connectPacket.getShort();
        this.messageQueueLength = connectPacket.getShort();
        this.remoteQueueNameBuffer = ByteBuffer.allocate(this.messageQueueLength);
        byte[] remoteQueueNameByteArray = new byte[this.messageQueueLength];
        this.remoteQueueNameBuffer.order(ByteOrder.LITTLE_ENDIAN);
        connectPacket.get(remoteQueueNameByteArray, 0, this.messageQueueLength);
        this.remoteQueueNameBuffer.put(remoteQueueNameByteArray);
        this.remoteQueueNameBuffer.flip();
        parseFlowControlPacket(connectPacket);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "parseConnectPacket", "NTMQ Connect Packet received on RemoteQueue. size {0}, ntmqVersion {1}, msgltProtocolType {2}, connectFlag {3}, sendSessionIdentifier {4}, tduMTUsize {5}, endianess {6}, messageQueueLength {7}", null, null, Integer.valueOf(connectPacket.limit()), Byte.valueOf(this.ntmqVersion), Byte.valueOf(this.msgqltProtocolType), Short.valueOf(this.connectFlag), Boolean.valueOf(this.sendSessionIdentifier), Integer.valueOf(this.tduMTUSize), Short.valueOf(this.endianess), Short.valueOf(this.messageQueueLength));
    }

    protected void parseFlowControlPacket(ByteBuffer flowControlPacket) {
        ByteOrder originalByteOrder = flowControlPacket.order();
        flowControlPacket.order(ByteOrder.LITTLE_ENDIAN);
        byte enabled = flowControlPacket.get();
        this.fcEnabled = enabled != 0;
        if (this.fcEnabled) {
            int keySize = flowControlPacket.get() & 255;
            this.fcInitialPostCount = flowControlPacket.getShort();
            this.remoteFcbAddr = flowControlPacket.getLong();
            this.remoteFcbKey = ByteBuffer.allocateDirect(keySize);
            this.remoteFcbKey.put(flowControlPacket).flip();
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "parseFlowControlPacket", "NTMQ Flow Control Packet Received. fcenabled {0}, initialPostCount {1}, rva {2}, keySize {3}", null, null, Boolean.valueOf(this.fcEnabled), Short.valueOf(this.fcInitialPostCount), Long.toHexString(this.remoteFcbAddr), Integer.valueOf(keySize));
        }
        flowControlPacket.order(originalByteOrder);
    }

    protected void prepareConnectPacket(ByteBuffer connectPacket, byte[] sessionIdentifier, int tduMDUSize, ByteOrder endianess, LocalQueue localQueue) {
        ByteBuffer localQueueName = localQueue.getName().getBuffer();
        localQueueName.rewind();
        localQueueName.position(0);
        connectPacket.order(ByteOrder.LITTLE_ENDIAN);
        connectPacket.put((byte) localQueue.getContext().getMajorVersion());
        connectPacket.put(this.msgqltProtocolType);
        short connectFlag = 0;
        if (this.rcvSessionIdentifier) {
            connectFlag = (short) (0 | 1);
        }
        if (this.rcvSessionIdentifierIMD) {
            connectFlag = (short) (connectFlag | 2);
        }
        connectPacket.putShort(connectFlag);
        if (sessionIdentifier == null || sessionIdentifier.length != 16) {
            if (this.rcvSessionIdentifier | this.rcvSessionIdentifierIMD) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "prepareConnectPacket", "Session identifier is required, but no session ID was found", null, null);
            }
            sessionIdentifier = new byte[16];
            for (int i = 0; i < 16; i++) {
                sessionIdentifier[i] = 0;
            }
        }
        connectPacket.put(sessionIdentifier);
        connectPacket.putInt(tduMDUSize);
        connectPacket.putShort((short) 1);
        connectPacket.putShort((short) localQueueName.limit());
        connectPacket.put(localQueueName);
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "prepareConnectPacket", "Connect Packet to be sent to Remote Queue : size = {0}, ntmqVersion {1}, msgqltProtocolType{2}, rcvSessionIdentifier {3}, tduMDUSize {4} , endianess {5}, localQueueNameLength {6}", null, null, Integer.valueOf(connectPacket.limit()), Integer.valueOf(localQueue.getContext().getMajorVersion()), Byte.valueOf(this.msgqltProtocolType), Boolean.valueOf(this.rcvSessionIdentifier), Integer.valueOf(tduMDUSize), connectPacket.order(), Integer.valueOf(localQueueName.limit()));
    }

    protected void appendFlowControlPacket(ByteBuffer connectPacket, boolean enabled, short postCount, long fcbAddr, ByteBuffer fcbKey) {
        if (enabled) {
            connectPacket.put((byte) 1);
            connectPacket.put((byte) fcbKey.remaining());
            connectPacket.putShort(postCount);
            connectPacket.putLong(fcbAddr);
            fcbKey.order(ByteOrder.LITTLE_ENDIAN);
            connectPacket.put(fcbKey);
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "appendFlowControlPacket", "Flow Control Packet to be sent to RemoteQueue: enabled {0}, keySize{1}, initialPostCount {2}, rva {3}", null, null, Boolean.valueOf(enabled), Integer.valueOf(fcbKey.flip().remaining()), Short.valueOf(postCount), Long.valueOf(fcbAddr));
        } else {
            int flowControlPacketLength = connectPacket.remaining();
            byte[] flowControlData = new byte[flowControlPacketLength];
            for (int i = 0; i < flowControlPacketLength; i++) {
                flowControlData[i] = 0;
            }
            connectPacket.put(flowControlData);
            CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "appendFlowControlPacket", "JDBC disables Flow Control", null, null);
        }
        connectPacket.flip();
    }

    protected int prepareHeaderPacket(ByteBuffer headerPacket, byte packetType, byte flag, byte[] sessionIdentifier, boolean immediateMsg) {
        headerPacket.rewind();
        headerPacket.order(ByteOrder.LITTLE_ENDIAN);
        headerPacket.put(packetType);
        boolean includeSID = this.sendSessionIdentifier || (immediateMsg && this.sendSessionIdentifierIMD);
        if (includeSID) {
            flag = (byte) (flag | 4);
        }
        headerPacket.put(flag);
        if (includeSID) {
            if (sessionIdentifier == null || sessionIdentifier.length != 16) {
                CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.STATIC, CLASS_NAME, "prepareHeaderPacket", "Session identifier is required, but no session ID was found", null, null);
            } else {
                headerPacket.put(sessionIdentifier);
            }
        }
        headerPacket.flip();
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.INTERNAL, CLASS_NAME, "prepareHeaderPacket", "Header Packet to be sent to RemoteQueue. size {0}, packetType {1}, sendSessionIdentifier {2}, flag {3}", null, null, Integer.valueOf(headerPacket.limit()), Byte.valueOf(packetType), Boolean.valueOf(this.sendSessionIdentifier), Byte.valueOf(flag));
        return headerPacket.limit();
    }

    protected int getHeaderPacketSize() {
        return this.headerPacketSize;
    }

    protected boolean isConnectPacket() {
        return this.packetType == 1;
    }

    protected boolean isDataPacket() {
        return this.packetType == 4 || this.packetType == 7;
    }

    protected boolean isDisconnectPacket() {
        return this.packetType == 2;
    }

    public boolean isDataIRPacket() {
        return this.packetType == 7;
    }

    protected byte getPacketFlag() {
        return this.packetFlag;
    }

    protected ByteBuffer getRemoteQueueNameBuffer() {
        return this.remoteQueueNameBuffer;
    }

    protected int getTduMtu() {
        return this.tduMTUSize;
    }

    protected byte[] getSID() {
        return this.sessionIdentifier;
    }

    protected boolean isSIDRequiredForRqMsg() {
        return this.sendSessionIdentifier;
    }

    protected boolean isSIDRequiredForRdmaMsg() {
        return this.sendSessionIdentifierIMD;
    }

    protected boolean isFlowControlEnabled() {
        return this.fcEnabled;
    }

    protected short getFcPostCount() {
        return this.fcInitialPostCount;
    }

    protected long getFcAddr() {
        return this.remoteFcbAddr;
    }

    protected ByteBuffer getFcKey() {
        return this.remoteFcbKey;
    }
}
