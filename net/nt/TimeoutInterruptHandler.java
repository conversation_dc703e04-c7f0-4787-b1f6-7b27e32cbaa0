package oracle.net.nt;

import java.io.InterruptedIOException;
import java.nio.channels.SocketChannel;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutInterruptHandler.class */
public final class TimeoutInterruptHandler {
    private static final Timer INTERRUPT_TIMER = new Timer("InterruptTimer", true);
    static ConcurrentHashMap<Thread, InterruptTask> outboundTimerTasksHash = new ConcurrentHashMap<>();
    static ConcurrentHashMap<Thread, InterruptTask> soTimerTasksHash = new ConcurrentHashMap<>();

    /* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutInterruptHandler$InterruptTaskType.class */
    public enum InterruptTaskType {
        SO_TIMEOUT,
        OUTBOUND_TIMEOUT
    }

    private TimeoutInterruptHandler() {
    }

    public static InterruptTask scheduleInterrupt(InterruptTaskType taskType, int time, Thread threadTobeInterrupted, SocketChannel socketChannel, boolean sendAttn) {
        if (time <= 0) {
            return null;
        }
        Map<Thread, InterruptTask> taskMap = getMap(taskType);
        if (taskMap.get(threadTobeInterrupted) != null) {
            cancelInterrupt(taskType, threadTobeInterrupted);
        }
        InterruptTask interruptTask = new InterruptTask(threadTobeInterrupted, time, socketChannel, sendAttn);
        INTERRUPT_TIMER.schedule(interruptTask, time);
        taskMap.put(Thread.currentThread(), interruptTask);
        return interruptTask;
    }

    public static InterruptTask scheduleInterrupt(InterruptTaskType taskType, int time, Thread threadTobeInterrupted, SocketChannel socketChannel) {
        return scheduleInterrupt(taskType, time, threadTobeInterrupted, socketChannel, false);
    }

    public static InterruptTask scheduleInterrupt(InterruptTaskType taskType, int time, Thread threadTobeInterrupted) {
        return scheduleInterrupt(taskType, time, threadTobeInterrupted, null);
    }

    public static InterruptTask cancelInterrupt(InterruptTaskType taskType, Thread threadTobeInterrupted) {
        Map<Thread, InterruptTask> taskMap = getMap(taskType);
        InterruptTask interruptTask = taskMap.remove(threadTobeInterrupted);
        if (interruptTask != null) {
            interruptTask.cancel();
            INTERRUPT_TIMER.purge();
        }
        return interruptTask;
    }

    public static InterruptTask cancelInterrupt(InterruptTaskType taskType, SocketChannel socketChannel) {
        ConcurrentHashMap<Thread, InterruptTask> taskMap = getMap(taskType);
        InterruptTask interruptTask = (InterruptTask) taskMap.searchValues(1L, task -> {
            if (task.isSocketChannel(socketChannel)) {
                return task;
            }
            return null;
        });
        if (interruptTask == null) {
            return null;
        }
        taskMap.remove(interruptTask.getThread());
        interruptTask.cancel();
        INTERRUPT_TIMER.purge();
        return interruptTask;
    }

    public static boolean isInterruptScheduled(InterruptTaskType taskType, Thread threadTobeInterrupted) {
        Map<Thread, InterruptTask> taskMap = getMap(taskType);
        return taskMap.get(threadTobeInterrupted) != null;
    }

    public static void resetTimer(InterruptTaskType taskType, Thread threadTobeInterrupted) {
        getMap(taskType);
        InterruptTask interruptTask = cancelInterrupt(taskType, threadTobeInterrupted);
        if (interruptTask == null) {
            return;
        }
        if (interruptTask.isInterrupted) {
            Thread.interrupted();
        }
        scheduleInterrupt(taskType, interruptTask.time, threadTobeInterrupted, interruptTask.socketChannel, interruptTask.sendAttn);
    }

    private static ConcurrentHashMap<Thread, InterruptTask> getMap(InterruptTaskType taskType) {
        if (taskType.compareTo(InterruptTaskType.OUTBOUND_TIMEOUT) == 0) {
            return outboundTimerTasksHash;
        }
        return soTimerTasksHash;
    }

    public static void stopTimer() {
        INTERRUPT_TIMER.cancel();
    }

    public static TimerTask scheduleTask(Runnable task, long msDelay) {
        TimerTask purgingTask = new TimerPurgingTask(task);
        INTERRUPT_TIMER.schedule(purgingTask, msDelay);
        return purgingTask;
    }

    public static TimerTask scheduleFixedDelayRepeatingTask(Runnable task, long periodOfMilliseconds) {
        TimerTask purgingTask = new TimerPurgingTask(task);
        INTERRUPT_TIMER.schedule(purgingTask, 0L, periodOfMilliseconds);
        return purgingTask;
    }

    public static void scheduleAtFixedRate(TimerTask task, long delay, long period) {
        INTERRUPT_TIMER.scheduleAtFixedRate(task, delay, period);
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutInterruptHandler$InterruptTask.class */
    public static class InterruptTask extends TimerTask {
        private final Thread threadTobeInterrupted;
        private volatile boolean isInterrupted;
        private final int time;
        private final SocketChannel socketChannel;
        private boolean sendAttn;

        public InterruptTask(Thread thread, int time, SocketChannel socketChannel) {
            this.isInterrupted = false;
            this.sendAttn = false;
            this.threadTobeInterrupted = thread;
            this.time = time;
            this.socketChannel = socketChannel;
        }

        public InterruptTask(Thread thread, int time, SocketChannel socketChannel, boolean sendAttn) {
            this(thread, time, socketChannel);
            this.sendAttn = sendAttn;
        }

        public boolean isInterrupted() {
            return this.isInterrupted;
        }

        public boolean isSocketChannel(SocketChannel socketChannel) {
            if (this.socketChannel == socketChannel) {
                return true;
            }
            return false;
        }

        public Thread getThread() {
            return this.threadTobeInterrupted;
        }

        private void sendAttentionMarker() {
            if (this.sendAttn) {
                try {
                    this.socketChannel.socket().sendUrgentData(33);
                } catch (Throwable th) {
                }
            }
        }

        @Override // java.util.TimerTask, java.lang.Runnable
        public void run() {
            this.isInterrupted = true;
            sendAttentionMarker();
            this.threadTobeInterrupted.interrupt();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutInterruptHandler$IOReadTimeoutException.class */
    public static class IOReadTimeoutException extends InterruptedIOException {
        private static final long serialVersionUID = 1;

        IOReadTimeoutException(String msg) {
            super(msg);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/net/nt/TimeoutInterruptHandler$TimerPurgingTask.class */
    private static final class TimerPurgingTask extends TimerTask {
        private static final String CLASS_NAME = TimerPurgingTask.class.getName();
        private final Runnable runnableTask;
        private boolean isPurged;

        private TimerPurgingTask(Runnable runnableTask) {
            this.isPurged = false;
            this.runnableTask = runnableTask;
        }

        @Override // java.util.TimerTask, java.lang.Runnable
        public void run() {
            try {
                this.runnableTask.run();
            } catch (Throwable throwable) {
                CommonDiagnosable.getInstance().debug(Level.SEVERE, SecurityLabel.UNKNOWN, CLASS_NAME, "run", "Unexpected exception thrown by timer task", (String) null, throwable);
            }
        }

        @Override // java.util.TimerTask
        public boolean cancel() {
            boolean isCancelled = super.cancel();
            if (!this.isPurged) {
                TimeoutInterruptHandler.INTERRUPT_TIMER.purge();
                this.isPurged = true;
            }
            return isCancelled;
        }
    }
}
