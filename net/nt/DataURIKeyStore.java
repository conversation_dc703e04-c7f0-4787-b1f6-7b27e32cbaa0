package oracle.net.nt;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.Base64;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.internal.OpaqueString;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/DataURIKeyStore.class */
class DataURIKeyStore implements Diagnosable {
    private final SSLConfig config;
    private final String uri;
    private final OpaqueString password;
    private final byte[] uriDataBytes;
    private final String uriDataStr;
    private final String resolvedKeyStoreType;
    private final KeyStore keyStore;
    private final boolean isTrustStore;
    private PEMKeyStore pemKeyStore;

    DataURIKeyStore(SSLConfig cfg, boolean isTrustStore) throws Exception {
        this.config = cfg;
        this.isTrustStore = isTrustStore;
        this.uri = isTrustStore ? cfg.getTrustStore() : cfg.getKeyStore();
        this.password = isTrustStore ? cfg.getTrustStorePassword() : cfg.getKeyStorePassword();
        this.uriDataBytes = decodeURIData(this.uri);
        this.uriDataStr = new String(this.uriDataBytes, StandardCharsets.UTF_8);
        this.resolvedKeyStoreType = resolveKeyStoreType();
        this.keyStore = CustomSSLSocketFactory.getKeyStoreInstance(this.resolvedKeyStoreType, getDiagnosable());
        if (SSLConfig.PEM_WALLET_TYPE.equals(this.resolvedKeyStoreType)) {
            loadDataURIPEMKeyStore();
        } else {
            loadDataURISSOKeyStore();
        }
    }

    KeyStore getKeyStore() {
        return this.keyStore;
    }

    PEMKeyStore getPemKeyStore() {
        return this.pemKeyStore;
    }

    private void loadDataURISSOKeyStore() throws Exception {
        CustomSSLSocketFactory.loadKeyStore(this.keyStore, new ByteArrayInputStream(this.uriDataBytes), this.password);
    }

    private void loadDataURIPEMKeyStore() throws Exception {
        this.pemKeyStore = new PEMKeyStore(this.config, this.keyStore, this.uriDataStr, this.isTrustStore);
    }

    private String base64Data(String dataURI) throws NetException {
        int index = dataURI.indexOf(SSLConfig.BASE64_EXTENSION);
        if (index == -1) {
            throw new NetException(NetException.NT_INVALID_DATA_URI_FORMAT, null, false, new Object[0]);
        }
        return dataURI.substring(index + SSLConfig.BASE64_EXTENSION.length());
    }

    private byte[] decodeURIData(String uri) throws NetException {
        try {
            return Base64.getDecoder().decode(base64Data(uri));
        } catch (IllegalArgumentException e) {
            throw ((NetException) new NetException(NetException.INVALID_BASE64_WALLET_LOCATION).initCause(e));
        }
    }

    private String resolveKeyStoreType() {
        if (this.uriDataStr.startsWith("-----")) {
            return SSLConfig.PEM_WALLET_TYPE;
        }
        return SSLConfig.SSO_WALLET_TYPE;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.config.diagnosable == null ? CommonDiagnosable.getInstance() : this.config.diagnosable;
    }
}
