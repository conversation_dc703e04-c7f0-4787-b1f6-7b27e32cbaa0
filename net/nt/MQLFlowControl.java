package oracle.net.nt;

import com.oracle.common.base.Disposable;
import com.oracle.common.internal.net.ipclw.mql.AtomicBuffers;
import com.oracle.common.internal.net.ipclw.mql.Context;
import com.oracle.common.internal.net.ipclw.mql.KeyedBufferSequence;
import com.oracle.common.internal.net.ipclw.mql.KeyedSingleBufferSequence;
import com.oracle.common.internal.net.ipclw.mql.RegistrationKey;
import com.oracle.common.internal.net.ipclw.mql.RemoteQueue;
import com.oracle.common.io.BufferManager;
import com.oracle.common.io.BufferSequence;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/net/nt/MQLFlowControl.class */
public class MQLFlowControl {
    private static final String CLASS_NAME = MQLFlowControl.class.getName();
    private RemoteQueue remoteQueue;
    private ByteBuffer localFCB;
    private RegistrationKey localFCBKey;
    private ByteBuffer writeBuffer;
    private KeyedBufferSequence writeBufferSeq;
    private RegistrationKey remoteFCBKey;
    private short localBufferPostCount;
    private short availableRcvBuffers;
    private short remoteBufferPostCount;
    private byte localIRCount;
    private byte remoteIRCount;
    private BufferSequence interrupt;
    static final int USR_WRITE_WAIT = 5000;
    static final int SYS_WRITE_WAIT = 0;
    static final int USR_PENDING_WAIT = 5000;
    static final int SYS_PENDING_WAIT = 0;
    static final int FCB_SIZE = 8;
    static final int OFFSET_SHORT_BUFFER_COUNT = 0;
    static final int BUFFER_COUNT_SIZE = 2;
    static final int OFFSET_BYTE_INTERRUPT_REQUEST = 2;
    static final int INTERRUPT_REQUEST_SIZE = 1;
    private boolean receivedIR = false;
    private boolean rdmaPending = false;
    private Disposable cookie = new Disposable() { // from class: oracle.net.nt.MQLFlowControl.1
        public void dispose() {
            MQLFlowControl.this.rdmaPending = false;
        }
    };
    private short lastCountSent = 0;

    public MQLFlowControl(Context localQueueContext, Context keyRegistryContext) throws IOException {
        initLocalFCB(localQueueContext);
        initWriteBuffer(keyRegistryContext);
    }

    private void initLocalFCB(Context localQueueContext) throws IOException {
        this.localFCB = ByteBuffer.allocateDirect(8);
        this.localFCB.order(ByteOrder.nativeOrder());
        this.localFCB.clear();
        this.localFCBKey = localQueueContext.register(this.localFCB);
        this.localFCB.clear();
    }

    private void initWriteBuffer(Context keyRegContext) throws IOException {
        this.writeBuffer = ByteBuffer.allocateDirect(8).order(ByteOrder.LITTLE_ENDIAN);
        RegistrationKey key = keyRegContext.register(this.writeBuffer);
        this.writeBufferSeq = new KeyedSingleBufferSequence((BufferManager) null, this.writeBuffer, keyRegContext, key);
    }

    protected RegistrationKey getLocalFCBKey() {
        return this.localFCBKey;
    }

    protected void resetLocalFCB() {
        this.localFCB.clear();
        while (this.localFCB.hasRemaining()) {
            this.localFCB.put((byte) 0);
        }
        this.localFCB.clear();
    }

    protected void onFlowControlEnabled(short initialBufferPostCount, ByteBuffer keyBuffer, long addr, RemoteQueue remoteQueue, BufferSequence interrupt) throws IOException {
        this.remoteQueue = remoteQueue;
        this.interrupt = interrupt;
        this.localFCB.putShort(0, initialBufferPostCount);
        this.availableRcvBuffers = initialBufferPostCount;
        this.remoteBufferPostCount = initialBufferPostCount;
        this.remoteFCBKey = new RegistrationKey(keyBuffer, addr, 8L);
        writeRemoteFCB(true);
    }

    private short readRemoteBufferPostCount() {
        long fcbBytes;
        short rdmaCounter;
        do {
            fcbBytes = this.localFCB.getLong(0);
            rdmaCounter = this.localFCB.getShort(0);
        } while (fcbBytes != AtomicBuffers.getAndAdd(this.localFCB, 0, 0L));
        return rdmaCounter;
    }

    private boolean isInterruptRequested() {
        long fcbBytes;
        byte rdmaCounter;
        do {
            fcbBytes = this.localFCB.getLong(0);
            rdmaCounter = this.localFCB.get(2);
        } while (fcbBytes != AtomicBuffers.getAndAdd(this.localFCB, 0, 0L));
        if (((byte) (rdmaCounter - this.remoteIRCount)) > 0) {
            this.remoteIRCount = rdmaCounter;
            return true;
        }
        return false;
    }

    public void sendCounterUpdate() throws IOException {
        while (needToUpdate()) {
            writeRemoteFCB(true);
        }
    }

    protected void onBufferPosted(int nPosted) {
        if (nPosted <= 0) {
            return;
        }
        this.localBufferPostCount = (short) (this.localBufferPostCount + nPosted);
        try {
            writeRemoteFCB(false, false);
        } catch (IOException e) {
            CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "onDisconnect", "Error attempting schedule RDMA write", null, null);
        }
    }

    private void writeRemoteFCB(boolean awaitWork) throws IOException {
        writeRemoteFCB(awaitWork, true);
    }

    private boolean writeRemoteFCB(boolean awaitWork, boolean canAwait) throws IOException {
        if (this.rdmaPending) {
            if (canAwait) {
                while (this.remoteQueue.isWorkPending()) {
                    this.remoteQueue.getContext().await(5000, 0);
                }
            } else {
                return false;
            }
        }
        this.writeBuffer.putShort(0, this.localBufferPostCount);
        this.writeBuffer.put(2, this.localIRCount);
        short lastLastCountSent = this.lastCountSent;
        this.lastCountSent = this.localBufferPostCount;
        BufferSequence iMsg = null;
        if (this.receivedIR) {
            iMsg = this.interrupt;
            this.receivedIR = false;
        }
        this.rdmaPending = true;
        if (canAwait) {
            while (!this.remoteQueue.write(this.remoteFCBKey, 0L, this.writeBufferSeq, iMsg, this.cookie, 1)) {
                this.remoteQueue.getContext().await(5000, 0);
            }
            if (awaitWork) {
                while (this.remoteQueue.isWorkPending()) {
                    this.remoteQueue.getContext().await(5000, 0);
                }
                return true;
            }
            return true;
        }
        if (!this.remoteQueue.write(this.remoteFCBKey, 0L, this.writeBufferSeq, iMsg, this.cookie, 1)) {
            this.receivedIR = iMsg != null;
            this.lastCountSent = lastLastCountSent;
            this.rdmaPending = false;
            return false;
        }
        return true;
    }

    public boolean needToUpdate() {
        return this.receivedIR || this.localBufferPostCount > this.lastCountSent || this.rdmaPending;
    }

    protected boolean sendInterruptRequest(boolean canAwait) throws IOException {
        this.localIRCount = (byte) (this.localIRCount + 1);
        if (writeRemoteFCB(true, canAwait)) {
            return true;
        }
        this.localIRCount = (byte) (this.localIRCount - 1);
        return false;
    }

    public byte getInterruptRequestCount() {
        return this.localIRCount;
    }

    protected int getAvailableBufferCount() {
        short prevPostCount = this.remoteBufferPostCount;
        this.remoteBufferPostCount = readRemoteBufferPostCount();
        this.availableRcvBuffers = (short) (this.availableRcvBuffers + ((short) (this.remoteBufferPostCount - prevPostCount)));
        return this.availableRcvBuffers;
    }

    protected void onMessageSent() {
        this.availableRcvBuffers = (short) (this.availableRcvBuffers - 1);
    }

    protected void onMessageReceived() {
        if (isInterruptRequested()) {
            this.receivedIR = true;
        }
    }

    protected void onIRMessage(byte msgCounter) {
        if (((byte) (msgCounter - this.remoteIRCount)) > 0) {
            this.remoteIRCount = msgCounter;
            this.receivedIR = true;
        }
    }

    protected void setLocalPostCount(short count) {
        this.localBufferPostCount = count;
    }

    protected int getLocalPostCount() {
        return this.localBufferPostCount;
    }

    public void onDisconnect(Context localQueueContext) {
        if (this.localFCB != null) {
            try {
                localQueueContext.unregister(this.localFCBKey);
            } catch (IOException e) {
                CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "onDisconnect", "Error attempting schedule RDMA write", null, null);
            }
            this.localFCB = null;
        }
        if (this.writeBufferSeq != null) {
            this.writeBufferSeq.dispose();
            this.writeBufferSeq = null;
        }
        if (this.interrupt != null) {
            this.interrupt.dispose();
        }
    }

    public String toString() {
        return "Local Counter = " + ((int) this.localBufferPostCount) + "Local Counter Last Written = " + ((int) this.lastCountSent) + " Remote Counter Last Read = " + ((int) this.remoteBufferPostCount) + " Current Remote Counter = " + ((int) readRemoteBufferPostCount()) + " Remote Available Receive Buffers = " + ((int) this.availableRcvBuffers) + " Interrupt Requested = " + isInterruptRequested();
    }
}
