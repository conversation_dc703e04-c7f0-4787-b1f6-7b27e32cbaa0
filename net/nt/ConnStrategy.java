package oracle.net.nt;

import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.logging.Level;
import javax.net.ssl.SSLContext;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.ExecutionEventNotifier;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.nt.TcpsNTAdapter;
import oracle.net.nt.TimeoutInterruptHandler;
import oracle.net.resolver.EnvVariableResolver;
import oracle.net.resolver.TimeUnitSuffixUtility;

/* loaded from: ojdbc8.jar:oracle/net/nt/ConnStrategy.class */
public class ConnStrategy implements Diagnosable {
    private static final String CLASS_NAME = ConnStrategy.class.getName();
    static final int MAX_ITERATION_COUNT = 100;
    private boolean optFound;
    public boolean reuseOpt;
    private ConnOption currentOption;
    public int sdu;
    public int tdu;
    public int nextOptToTry;
    public Properties socketOptions;
    private String osuser;
    private String programName;
    public int retryCount;
    private int lastRetryCounter;
    private int lastRetryConnectDescription;
    private final Properties userProp;
    private ConnectDescription currentDescription;
    private String defaultHostName;
    private final ArrayList<ConnectDescription> descriptionList;
    private final SSLContext sslContext;
    private final OracleHostnameResolver hostnameResolver;
    private int outboundConnectTimeout;
    private String connectionIdPrefix;
    private final Diagnosable diagnosable;
    private final TraceEventListener traceEventListener;
    private Object userContext;
    private DriverResources driverResources;

    public ConnStrategy(@Blind(PropertiesBlinder.class) Properties up) {
        this(up, null, null, null, null);
    }

    public ConnStrategy(@Blind(PropertiesBlinder.class) Properties up, SSLContext sslContext, OracleHostnameResolver hostnameResolver, Diagnosable diagnosable, TraceEventListener traceEventListener) {
        this.optFound = false;
        this.reuseOpt = false;
        this.socketOptions = new Properties();
        this.retryCount = 0;
        this.lastRetryCounter = 0;
        this.lastRetryConnectDescription = 0;
        this.descriptionList = new ArrayList<>(4);
        this.outboundConnectTimeout = 0;
        this.diagnosable = diagnosable;
        this.nextOptToTry = 0;
        this.osuser = up.getProperty("oracle.jdbc.v$session.osuser");
        this.programName = up.getProperty("oracle.jdbc.v$session.program");
        this.defaultHostName = up.getProperty(OracleConnection.CONNECTION_PROPERTY_LOCAL_HOST_NAME);
        DownHostsCache.DOWN_HOSTS_TIMEOUT = Integer.parseInt(up.getProperty(OracleConnection.CONNECTION_PROPERTY_DOWN_HOSTS_TIMEOUT, OracleConnection.CONNECTION_PROPERTY_DOWN_HOSTS_TIMEOUT_DEFAULT));
        this.userProp = up;
        createSocketOptions(up);
        this.sslContext = sslContext;
        this.hostnameResolver = hostnameResolver;
        this.traceEventListener = traceEventListener;
    }

    public String getOSUsername() {
        return this.osuser;
    }

    public String getProgramName() {
        return this.programName;
    }

    public String getHostname() {
        return this.defaultHostName;
    }

    public void createSocketOptions(@Blind(PropertiesBlinder.class) Properties up) {
        createSocketOptions(up, this.socketOptions, !this.reuseOpt);
    }

    static void createSocketOptions(@Blind(PropertiesBlinder.class) Properties up, @Blind(PropertiesBlinder.class) Properties socketOptions) {
        createSocketOptions(up, socketOptions, true);
    }

    static void createSocketOptions(@Blind(PropertiesBlinder.class) Properties up, @Blind(PropertiesBlinder.class) Properties socketOptions, boolean setDefaultNoDelay) {
        boolean tcpNoDelayParam = false;
        Enumeration<Object> e = up.keys();
        while (e.hasMoreElements()) {
            String propertyKey = (String) e.nextElement();
            if (propertyKey.equalsIgnoreCase(SQLnetDef.TCP_NODELAY_STR)) {
                tcpNoDelayParam = true;
                String propertyValue = up.getProperty(propertyKey).toUpperCase();
                if (propertyValue.equals("NO")) {
                    socketOptions.put(0, "NO");
                } else {
                    socketOptions.put(0, "YES");
                }
            } else if (propertyKey.equalsIgnoreCase(SQLnetDef.TCP_READTIMEOUT_STR)) {
                String propertyValue2 = up.getProperty(propertyKey);
                socketOptions.put(3, propertyValue2);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.CONNECT_TIMEOUT")) {
                String propertyValue3 = up.getProperty(propertyKey);
                socketOptions.put(2, String.valueOf(TimeUnitSuffixUtility.getTimeInMilliseconds(propertyValue3, false, 0)));
            } else if (propertyKey.equalsIgnoreCase("oracle.net.ssl_server_dn_match")) {
                String propertyValue4 = up.getProperty(propertyKey);
                socketOptions.put(4, propertyValue4);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_THIN_SSL_ALLOW_WEAK_DN_MATCH)) {
                String propertyValue5 = up.getProperty(propertyKey);
                socketOptions.put(43, propertyValue5);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.ssl_server_cert_dn")) {
                String propertyValue6 = up.getProperty(propertyKey);
                socketOptions.put(28, propertyValue6);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.wallet_location")) {
                String propertyValue7 = up.getProperty(propertyKey);
                socketOptions.put(5, EnvVariableResolver.resolveEnvPlaceHolders(propertyValue7, up));
            } else if (propertyKey.equalsIgnoreCase("oracle.net.wallet_password")) {
                Object password = up.get("oracle.net.wallet_password");
                socketOptions.put(16, password instanceof String ? OpaqueString.newOpaqueString((String) password) : (OpaqueString) password);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.ssl_version")) {
                String propertyValue8 = up.getProperty(propertyKey);
                socketOptions.put(6, propertyValue8);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.ssl_cipher_suites")) {
                String propertyValue9 = up.getProperty(propertyKey);
                socketOptions.put(7, propertyValue9);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.keyStore")) {
                String propertyValue10 = up.getProperty(propertyKey);
                socketOptions.put(8, propertyValue10);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.keyStoreType")) {
                String propertyValue11 = up.getProperty(propertyKey);
                socketOptions.put(9, propertyValue11);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.keyStorePassword")) {
                Object password2 = up.get(propertyKey);
                socketOptions.put(10, password2 instanceof String ? OpaqueString.newOpaqueString((String) password2) : (OpaqueString) password2);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.trustStore")) {
                String propertyValue12 = up.getProperty(propertyKey);
                socketOptions.put(11, propertyValue12);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.trustStoreType")) {
                String propertyValue13 = up.getProperty(propertyKey);
                socketOptions.put(12, propertyValue13);
            } else if (propertyKey.equalsIgnoreCase("javax.net.ssl.trustStorePassword")) {
                Object password3 = up.get(propertyKey);
                socketOptions.put(13, password3 instanceof String ? OpaqueString.newOpaqueString((String) password3) : (OpaqueString) password3);
            } else if (propertyKey.equalsIgnoreCase("ssl.keyManagerFactory.algorithm")) {
                String propertyValue14 = up.getProperty(propertyKey);
                socketOptions.put(14, propertyValue14);
            } else if (propertyKey.equalsIgnoreCase(SQLnetDef.FORCE_DNS_LOAD_BALANCING_STR)) {
                String propertyValue15 = up.getProperty(propertyKey);
                socketOptions.put(18, propertyValue15);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_NET_KEEPALIVE)) {
                String propertyValue16 = up.getProperty(propertyKey);
                if (Boolean.parseBoolean(propertyValue16)) {
                    socketOptions.put(1, "YES");
                } else {
                    socketOptions.put(1, "NO");
                }
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_DEFAULT_USE_NIO)) {
                String propertyValue17 = up.getProperty(propertyKey);
                socketOptions.put(20, propertyValue17);
            } else if (propertyKey.equalsIgnoreCase(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_LOCAL_IP_MSGQ)) {
                String propertyValue18 = up.getProperty(propertyKey);
                socketOptions.put(21, propertyValue18);
            } else if (propertyKey.equalsIgnoreCase(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_TRANSPORT)) {
                String propertyValue19 = up.getProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_TRANSPORT);
                socketOptions.put(22, propertyValue19);
            } else if (propertyKey.equalsIgnoreCase(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_BUSYWAIT)) {
                String propertyValue20 = up.getProperty(propertyKey);
                socketOptions.put(23, propertyValue20);
            } else if (propertyKey.equalsIgnoreCase(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_KERNELWAIT)) {
                String propertyValue21 = up.getProperty(propertyKey);
                socketOptions.put(24, propertyValue21);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_THIN_OUTBOUND_CONNECT_TIMEOUT)) {
                String propertyValue22 = up.getProperty(propertyKey);
                socketOptions.put(25, String.valueOf(TimeUnitSuffixUtility.getTimeInMilliseconds(propertyValue22, true, 0)));
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_USER)) {
                String propertyValue23 = up.getProperty(propertyKey);
                socketOptions.put(26, propertyValue23);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_PASSWORD)) {
                Object password4 = up.get(OracleConnection.CONNECTION_PROPERTY_WEBSOCKET_PASSWORD);
                socketOptions.put(27, password4 instanceof String ? OpaqueString.newOpaqueString((String) password4) : (OpaqueString) password4);
            } else if (propertyKey.equalsIgnoreCase("oracle.net.ssl_certificate_alias")) {
                String propertyValue24 = up.getProperty(propertyKey);
                socketOptions.put(29, propertyValue24);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CERTIFICATE_THUMBPRINT)) {
                String propertyValue25 = up.getProperty(propertyKey);
                socketOptions.put(44, propertyValue25);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_THIN_HTTPS_PROXY_HOST)) {
                String propertyValue26 = up.getProperty(propertyKey);
                socketOptions.put(30, propertyValue26);
                socketOptions.put(31, up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_HTTPS_PROXY_PORT));
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_SOCKS_PROXY_HOST)) {
                String propertyValue27 = up.getProperty(propertyKey);
                socketOptions.put(36, propertyValue27);
                socketOptions.put(37, up.getProperty(OracleConnection.CONNECTION_PROPERTY_SOCKS_PROXY_PORT));
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_PROXY_REMOTE_DNS)) {
                String propertyValue28 = up.getProperty(propertyKey);
                socketOptions.put(45, propertyValue28);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_TCP_KEEPIDLE)) {
                String propertyValue29 = up.getProperty(propertyKey);
                socketOptions.put(33, propertyValue29);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_TCP_KEEPINTERVAL)) {
                String propertyValue30 = up.getProperty(propertyKey);
                socketOptions.put(34, propertyValue30);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_TCP_KEEPCOUNT)) {
                String propertyValue31 = up.getProperty(propertyKey);
                socketOptions.put(35, propertyValue31);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_SSL_CONTEXT_PROTOCOL)) {
                String propertyValue32 = up.getProperty(propertyKey);
                socketOptions.put(38, propertyValue32);
            } else if (propertyKey.equalsIgnoreCase(SQLnetDef.SSL_SERVER_DN_MATCH_DEFAULT)) {
                String propertyValue33 = up.getProperty(propertyKey);
                socketOptions.put(40, propertyValue33);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_PROVIDER_TLS_CONFIGURATION)) {
                Object providerSSLContext = up.get(propertyKey);
                socketOptions.put(42, providerSSLContext instanceof String ? OpaqueString.newOpaqueString((String) providerSSLContext) : (OpaqueString) providerSSLContext);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_NET_USE_TCP_FAST_OPEN)) {
                String propertyValue34 = up.getProperty(propertyKey);
                socketOptions.put(109, propertyValue34);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_NET_USE_SNI)) {
                String propertyValue35 = up.getProperty(propertyKey);
                socketOptions.put(47, propertyValue35);
            } else if (propertyKey.equalsIgnoreCase(OracleConnection.CONNECTION_PROPERTY_NET_SNI_IGNORE_LIST)) {
                String propertyValue36 = up.getProperty(propertyKey);
                socketOptions.put(48, propertyValue36);
            }
        }
        if (!tcpNoDelayParam && setDefaultNoDelay) {
            socketOptions.put(0, "YES");
        }
    }

    public void addSocketOptions(boolean keepAlive) {
        if (keepAlive) {
            this.socketOptions.put(1, "YES");
        } else if (!this.reuseOpt && !this.socketOptions.containsKey(1)) {
            this.socketOptions.put(1, "NO");
        }
    }

    public void addSocketOptions_FORCE_DNS_LOAD_BALANCING_OFF() {
        this.socketOptions.put(18, "true");
    }

    public ConnOption execute(boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent) throws InterruptedException, NetException, InterruptedIOException {
        if (this.diagnosable.isDebugEnabled()) {
            StringBuilder stringBuilder = new StringBuilder();
            Iterator<ConnectDescription> it = this.descriptionList.iterator();
            while (it.hasNext()) {
                ArrayList<ConnOption> cOpts = it.next().getConnectOptions();
                int max = cOpts.size();
                for (int i = 0; i < max; i++) {
                    stringBuilder.append(cOpts.get(i));
                    if (i < max) {
                        stringBuilder.append(System.lineSeparator());
                    }
                }
            }
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "execute", "number of descriptions={0}, connect options={1} ", null, null, Integer.valueOf(this.descriptionList.size()), stringBuilder.toString());
        }
        Exception causeException = null;
        if (this.lastRetryConnectDescription == 0 && this.nextOptToTry == 0 && this.lastRetryCounter == 0) {
            DownHostsCache.getInstance().reorderDescriptions(this.descriptionList);
        }
        for (int d = this.lastRetryConnectDescription; d < this.descriptionList.size(); d++) {
            ConnectDescription desc = this.descriptionList.get(d);
            ArrayList<ConnOption> cOpts2 = desc.getConnectOptions();
            prepareForExecuteDescription(desc);
            this.outboundConnectTimeout = getOutboundConnectTimeout(desc);
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "execute", "RETRY_COUNT={0}, LAST RETRY COUNTER={1}, OUTBOUND_CONNECT_TIMEOUT={2}, TRANSPORT_CONNECT_TIMEOUT={3},SDU={4}, TDU={5}. ", null, null, Integer.valueOf(this.retryCount), Integer.valueOf(this.lastRetryCounter), Integer.valueOf(this.outboundConnectTimeout), Integer.valueOf(desc.getTransportConnectTimeout()), Integer.valueOf(this.sdu), Integer.valueOf(this.tdu));
            int delay = desc.getDelayInMillis();
            for (int i2 = this.lastRetryCounter; i2 <= this.retryCount; i2++) {
                int iterationCount = 0;
                if (this.nextOptToTry == 0) {
                    DownHostsCache.getInstance().reorderAddresses(cOpts2);
                }
                while (this.nextOptToTry < cOpts2.size()) {
                    int i3 = iterationCount;
                    iterationCount++;
                    if (i3 >= 100) {
                        break;
                    }
                    try {
                        try {
                            this.currentOption = cOpts2.get(this.nextOptToTry);
                            prepareForExecuteConnOption(this.currentOption);
                            executeConnOption(this.currentOption, dmsParent, desc.getTransportConnectTimeout(), startNewOCTOInterruptTask, this.outboundConnectTimeout);
                            handleExecuteConnOptionCompletion(this.currentOption);
                            this.lastRetryCounter = i2;
                            this.lastRetryConnectDescription = d;
                            ConnOption connOption = this.currentOption;
                            this.nextOptToTry++;
                            return connOption;
                        } catch (IOException err) {
                            try {
                                handleExecuteConnOptionFailure(this.currentOption, err);
                                causeException = err;
                                this.nextOptToTry++;
                            } catch (Throwable th) {
                                this.nextOptToTry++;
                                throw th;
                            }
                        }
                    } catch (InterruptedIOException interruptErr) {
                        handleExecuteConnOptionFailure(this.currentOption, interruptErr);
                        if (causeException != null) {
                            interruptErr.addSuppressed(causeException);
                        }
                        throw interruptErr;
                    }
                }
                this.nextOptToTry = 0;
                if (!(causeException instanceof NetException) || ((NetException) causeException).isTransient()) {
                    if (delay > 0 && i2 < this.retryCount) {
                        try {
                            Thread.sleep(delay);
                        } catch (InterruptedException interruptException) {
                            throw new InterruptedIOException(interruptException.getMessage());
                        }
                    }
                }
            }
            this.lastRetryCounter = 0;
            DownHostsCache.getInstance().markDownDescription(desc);
        }
        if (causeException == null) {
            throw new NetException(NetException.NT_CONNECTION_FAILED);
        }
        throw createExecuteException(causeException);
    }

    private static NetException createExecuteException(Exception cause) {
        NetException netException;
        if (cause instanceof NetException) {
            return (NetException) cause;
        }
        if (cause instanceof UnknownHostException) {
            netException = new NetException(NetException.UNKNOWN_HOST);
        } else {
            netException = new NetException(NetException.NT_CONNECTION_FAILED);
        }
        netException.initCause(cause);
        return netException;
    }

    private void logDescriptionList(String methodName) {
        if (!this.diagnosable.isDebugEnabled()) {
            return;
        }
        this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, methodName, "CS contains following Connection Options. number of descriptions={0}. ", (String) null, (String) null, Integer.valueOf(this.descriptionList.size()));
        Iterator<ConnectDescription> it = this.descriptionList.iterator();
        while (it.hasNext()) {
            ConnectDescription desc = it.next();
            ArrayList<ConnOption> cOpts = desc.getConnectOptions();
            Iterator<ConnOption> it2 = cOpts.iterator();
            while (it2.hasNext()) {
                ConnOption copt = it2.next();
                this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, methodName, "ConnOption={0}.", (String) null, (String) null, copt);
            }
        }
    }

    public String getConnectionIdPrefix() {
        return this.connectionIdPrefix;
    }

    private void prepareForExecuteDescription(ConnectDescription desc) {
        setSecurityOptions(desc);
        if (desc.getTransportConnectTimeout() >= 0) {
            this.socketOptions.put(2, Integer.toString(desc.getTransportConnectTimeout()));
        }
        this.sdu = desc.getSdu();
        this.tdu = desc.getTdu();
        this.retryCount = desc.getRetryCount();
        if (this.retryCount < 0) {
            this.retryCount = 0;
        }
        this.socketOptions.put(17, Integer.toString(this.retryCount));
        setExpireTimeSocketOption(desc.getExpireTime(), this.socketOptions);
        this.connectionIdPrefix = this.userProp.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_CONNECTIONID_PREFIX, desc.getConnectionIdPrefix());
        if (desc.getTokenAuthentication() != null || "OCI_TOKEN".equalsIgnoreCase(desc.getPasswordAuthentication()) || "AZURE_TOKEN".equalsIgnoreCase(desc.getPasswordAuthentication())) {
            this.socketOptions.put(40, "true");
        }
        if (desc.useSNI() != null) {
            this.socketOptions.put(47, desc.useSNI());
        }
    }

    private void setSecurityOptions(ConnectDescription desc) {
        if (desc.getEncryptionClient() != null) {
            this.userProp.setProperty("oracle.net.encryption_client", desc.getEncryptionClient());
        }
        if (desc.getEncryptionClientTypes() != null) {
            this.userProp.setProperty("oracle.net.encryption_types_client", desc.getEncryptionClientTypes());
        }
        if (desc.getChecksumClient() != null) {
            this.userProp.setProperty("oracle.net.crypto_checksum_client", desc.getChecksumClient());
        }
        if (desc.getChecksumClientTypes() != null) {
            this.userProp.setProperty("oracle.net.crypto_checksum_types_client", desc.getChecksumClientTypes());
        }
        if (desc.getAllowWeakCrypto() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_ALLOW_WEAK_CRYPTO, desc.getAllowWeakCrypto());
        }
        if (desc.getTokenAuthentication() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION, desc.getTokenAuthentication());
        }
        if (desc.getTokenLocation() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_TOKEN_LOCATION, desc.getTokenLocation());
        }
        if (desc.getPasswordAuthentication() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION, desc.getPasswordAuthentication());
        }
        if (desc.getOciIamUrl() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_IAM_URL, desc.getOciIamUrl());
        }
        if (desc.getOciTenancy() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_TENANCY, desc.getOciTenancy());
        }
        if (desc.getOciCompartment() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_COMPARTMENT, desc.getOciCompartment());
        }
        if (desc.getOciDatabase() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_DATABASE, desc.getOciDatabase());
        }
        if (desc.getOciConfigFile() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_CONFIG_FILE, desc.getOciConfigFile());
        }
        if (desc.getOciProfile() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_OCI_PROFILE, desc.getOciProfile());
        }
        if (desc.getAzureDbAppIdUri() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_AZURE_DB_APP_ID_URI, desc.getAzureDbAppIdUri());
        }
        if (desc.getTenantId() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_TENANT_ID, desc.getTenantId());
        }
        if (desc.getClientId() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_CLIENT_ID, desc.getClientId());
        }
        if (desc.getClientCertificate() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_CLIENT_CERTIFICATE, desc.getClientCertificate());
        }
        if (desc.getRedirectUri() != null) {
            this.userProp.setProperty(OracleConnection.CONNECTION_PROPERTY_REDIRECT_URI, desc.getRedirectUri());
        }
    }

    private void prepareForExecuteConnOption(ConnOption option) {
        if (option.walletDirectory != null) {
            this.socketOptions.put(5, EnvVariableResolver.resolveFilePath(option.walletDirectory, this.userProp));
        }
        if (option.sslServerDNMatch != null) {
            this.socketOptions.put(4, option.sslServerDNMatch);
        }
        if (option.sslVersion != null) {
            this.socketOptions.put(6, option.sslVersion);
        }
        if (option.sslCiphers != null) {
            this.socketOptions.put(7, option.sslCiphers);
        }
        if (option.sslCertAlias != null) {
            this.socketOptions.put(29, option.sslCertAlias);
        }
        if (option.sslCertThumbprint != null) {
            this.socketOptions.put(44, option.sslCertThumbprint);
        }
        if (option.sslServerCertDN == null) {
            option.sslServerCertDN = (String) this.socketOptions.get(28);
        }
        if (option.sslAllowWeakDNMatch == null) {
            option.sslAllowWeakDNMatch = (String) this.socketOptions.get(43);
        }
        if (option.sslServerCertDN != null && this.socketOptions.get(4) == null) {
            this.socketOptions.put(4, "TRUE");
        }
        if (option.useTcpFastOpen != null) {
            this.socketOptions.put(109, option.useTcpFastOpen);
        }
        option.setDriverResources(this.driverResources);
    }

    private void handleExecuteConnOptionCompletion(ConnOption option) {
        option.sdu = this.sdu;
        option.tdu = this.tdu;
        this.optFound = true;
    }

    private void handleExecuteConnOptionFailure(ConnOption option, IOException err) {
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "handleExecuteConnOptionFailure", "option={0}, exception occurred during connect. {0}", null, null, option, err.getMessage());
        DownHostsCache.getInstance().markDownHost(option);
        this.userContext = new ExecutionEventNotifier().setTraceEventListener(this.traceEventListener).setJdbcExecutionEvent(TraceEventListener.JdbcExecutionEvent.VIP_RETRY).setUserContext(this.userContext).addParam(err.getMessage()).addParam(option.protocol).addParam(option.host).addParam(Integer.valueOf(option.port)).addParam(option.service_name).addParam(option.sid).addParam(option.conn_data).addParam(option.addr).sendNotification();
    }

    private void executeConnOption(ConnOption option, DMSFactory.DMSNoun dmsParent, int transportConnectTimeout, boolean startNewOCTOInterruptTask, int outboundConnectTimeout) throws IOException {
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "executeConnOption", "Trying connection. option={0}, nextOptToTry={1}, currentOption.addr={2}. ", null, null, option, Integer.valueOf(this.nextOptToTry), this.currentOption.addr);
        if (outboundConnectTimeout > 0 && startNewOCTOInterruptTask) {
            TimeoutInterruptHandler.scheduleInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, outboundConnectTimeout, Thread.currentThread());
        }
        option.connectTimeout = outboundConnectTimeout;
        option.transportConnectTimeout = transportConnectTimeout;
        try {
            option.connect(this.socketOptions, dmsParent, this.sslContext, this.diagnosable);
        } catch (InterruptedIOException interruptException) {
            if (outboundConnectTimeout > 0 && !cancelOutboundTimeout()) {
                throw createOutboundTimeoutException(option);
            }
            throw interruptException;
        } catch (IOException connectException) {
            if (outboundConnectTimeout > 0) {
                cancelOutboundTimeout();
            }
            throw connectException;
        }
    }

    private NetException createOutboundTimeoutException(ConnOption option) {
        return new NetException(NetException.TIMEOUT, null, false, "Outbound connect", option.getOriginalConnOption().connectTimeout + "ms", "host " + option.host + " port " + option.port);
    }

    private boolean cancelOutboundTimeout() {
        TimeoutInterruptHandler.InterruptTask outboundTimeoutTask = TimeoutInterruptHandler.cancelInterrupt(TimeoutInterruptHandler.InterruptTaskType.OUTBOUND_TIMEOUT, Thread.currentThread());
        if (outboundTimeoutTask != null && outboundTimeoutTask.isInterrupted()) {
            Thread.interrupted();
        }
        return (outboundTimeoutTask == null || outboundTimeoutTask.isInterrupted()) ? false : true;
    }

    private final int getOutboundConnectTimeout(ConnectDescription desc) {
        if (desc.getConnectTimeout() >= 0) {
            return desc.getConnectTimeout();
        }
        String timeoutSocketOption = (String) this.socketOptions.get(25);
        if (timeoutSocketOption != null) {
            try {
                return Integer.parseInt(timeoutSocketOption);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    public CompletionStage<ConnOption> executeAsync(boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        logDescriptionList("executeAsync");
        if (this.lastRetryConnectDescription >= this.descriptionList.size()) {
            return CompletionStageUtil.completedStage(null);
        }
        return executeDescriptionAsync(this.lastRetryConnectDescription, dmsParent, startNewOCTOInterruptTask, outboundTimeoutHandler, asyncExecutor).handle(CompletionStageUtil.completionHandler(option -> {
            if (option != null) {
                return option;
            }
            throw new NetException(NetException.NT_CONNECTION_FAILED);
        }, IOException.class, causeException -> {
            if (causeException instanceof TcpsNTAdapter.AsyncHandshakeException) {
                throw causeException;
            }
            throw createExecuteException(causeException);
        }));
    }

    private final CompletionStage<ConnOption> executeDescriptionAsync(int descriptionIndex, DMSFactory.DMSNoun dmsParent, boolean startNewOCTOInterruptTask, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        CompletionStage<ConnOption> completionStageCompletedStage;
        ConnectDescription desc = this.descriptionList.get(descriptionIndex);
        ArrayList<ConnOption> cOpts = desc.getConnectOptions();
        prepareForExecuteDescription(desc);
        this.outboundConnectTimeout = getOutboundConnectTimeout(desc);
        int delay = desc.getDelayInMillis();
        if (this.lastRetryCounter <= this.retryCount) {
            completionStageCompletedStage = executeConnOptionListAsync(this.lastRetryCounter, cOpts, delay, desc.getTransportConnectTimeout(), dmsParent, startNewOCTOInterruptTask, this.outboundConnectTimeout, outboundTimeoutHandler, asyncExecutor);
        } else {
            completionStageCompletedStage = CompletionStageUtil.completedStage(null);
        }
        CompletionStage<ConnOption> executeStage = completionStageCompletedStage;
        int nextDescriptionIndex = descriptionIndex + 1;
        return executeStage.exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, causeException -> {
            if (nextDescriptionIndex < this.descriptionList.size()) {
                return null;
            }
            throw causeException;
        })).thenCompose(CompletionStageUtil.normalCompletionHandler(option -> {
            if (option != null) {
                this.lastRetryConnectDescription = descriptionIndex;
                return CompletionStageUtil.completedStage(option);
            }
            this.lastRetryCounter = 0;
            if (nextDescriptionIndex < this.descriptionList.size()) {
                return executeDescriptionAsync(nextDescriptionIndex, dmsParent, startNewOCTOInterruptTask, outboundTimeoutHandler, asyncExecutor);
            }
            return CompletionStageUtil.completedStage(null);
        }));
    }

    private CompletionStage<ConnOption> executeConnOptionListAsync(int currentRetryCount, List<ConnOption> cOpts, int delay, int transportConnectTimeout, DMSFactory.DMSNoun dmsParent, boolean startNewOCTOInterruptTask, int outboundConnectTimeout, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        CompletionStage<ConnOption> completionStageCompletedStage;
        if (this.nextOptToTry < cOpts.size()) {
            completionStageCompletedStage = executeNextConnOptionAsync(cOpts, dmsParent, transportConnectTimeout, startNewOCTOInterruptTask, outboundConnectTimeout, outboundTimeoutHandler, asyncExecutor);
        } else {
            completionStageCompletedStage = CompletionStageUtil.completedStage(null);
        }
        CompletionStage<ConnOption> executeStage = completionStageCompletedStage;
        return executeStage.exceptionally(CompletionStageUtil.exceptionalCompletionHandler(IOException.class, causeException -> {
            if (currentRetryCount < this.retryCount) {
                return null;
            }
            throw causeException;
        })).thenCompose(CompletionStageUtil.normalCompletionHandler(option -> {
            if (option != null) {
                this.lastRetryCounter = currentRetryCount;
                return CompletionStageUtil.completedStage(option);
            }
            this.nextOptToTry = 0;
            DownHostsCache.getInstance().reorderAddresses(cOpts);
            if (currentRetryCount < this.retryCount) {
                CompletableFuture<Void> delayedFuture = new CompletableFuture<>();
                if (delay <= 0) {
                    delayedFuture.complete(null);
                } else {
                    TimeoutInterruptHandler.scheduleTask(() -> {
                        delayedFuture.complete(null);
                    }, delay);
                }
                return delayedFuture.thenCompose((Function<? super Void, ? extends CompletionStage<U>>) nil -> {
                    return executeConnOptionListAsync(currentRetryCount + 1, cOpts, delay, transportConnectTimeout, dmsParent, startNewOCTOInterruptTask, outboundConnectTimeout, outboundTimeoutHandler, asyncExecutor);
                });
            }
            return CompletionStageUtil.completedStage(null);
        }));
    }

    private final CompletionStage<ConnOption> executeNextConnOptionAsync(List<ConnOption> cOpts, DMSFactory.DMSNoun dmsParent, int transportConnectTimeout, boolean startNewOCTOInterruptTask, int outboundConnectTimeout, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        this.currentOption = cOpts.get(this.nextOptToTry);
        prepareForExecuteConnOption(this.currentOption);
        return executeConnOptionAsync(this.currentOption, dmsParent, startNewOCTOInterruptTask, outboundConnectTimeout, outboundTimeoutHandler, transportConnectTimeout, asyncExecutor).handle((nil, throwable) -> {
            CompletionStage<ConnOption> nextStage;
            CompletionStage<ConnOption> completionStageFailedStage;
            this.nextOptToTry++;
            Throwable throwable = CompletionStageUtil.unwrapCompletionException(throwable);
            if (throwable == null) {
                handleExecuteConnOptionCompletion(this.currentOption);
                nextStage = CompletionStageUtil.completedStage(this.currentOption);
            } else if (throwable instanceof IOException) {
                handleExecuteConnOptionFailure(this.currentOption, (IOException) throwable);
                if (this.nextOptToTry < cOpts.size()) {
                    completionStageFailedStage = executeNextConnOptionAsync(cOpts, dmsParent, transportConnectTimeout, startNewOCTOInterruptTask, outboundConnectTimeout, outboundTimeoutHandler, asyncExecutor);
                } else {
                    completionStageFailedStage = CompletionStageUtil.failedStage(throwable);
                }
                nextStage = completionStageFailedStage;
            } else {
                nextStage = CompletionStageUtil.failedStage(throwable);
            }
            return nextStage;
        }).thenCompose(Function.identity());
    }

    private final CompletionStage<Void> executeConnOptionAsync(ConnOption option, DMSFactory.DMSNoun dmsParent, boolean startNewOCTOInterruptTask, int outboundConnectTimeout, AsyncOutboundTimeoutHandler outboundTimeoutHandler, int transportConnectTimeout, Executor asyncExecutor) {
        option.connectTimeout = outboundConnectTimeout;
        option.transportConnectTimeout = transportConnectTimeout;
        if (outboundConnectTimeout > 0 && startNewOCTOInterruptTask) {
            try {
                outboundTimeoutHandler.scheduleTimeout(Duration.ofMillis(outboundConnectTimeout), createOutboundTimeoutException(option));
            } catch (IOException timeoutExpired) {
                return CompletionStageUtil.failedStage(timeoutExpired);
            }
        }
        CompletionStage<Void> connectStage = option.connectAsync(this.socketOptions, dmsParent, this.sslContext, outboundTimeoutHandler, asyncExecutor, this.diagnosable);
        if (outboundConnectTimeout > 0) {
            return connectStage.whenComplete((result, connectException) -> {
                if (connectException != null) {
                    outboundTimeoutHandler.cancelTimeout();
                }
            });
        }
        return connectStage;
    }

    public boolean optAvailable() {
        return this.optFound;
    }

    public ConnOption getOption() {
        return this.currentOption;
    }

    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.currentOption.isConnectionSocketKeepAlive();
    }

    public ConnectDescription newConnectDescription() {
        this.currentDescription = new ConnectDescription();
        return this.currentDescription;
    }

    public ConnectDescription currentDescription() {
        return this.currentDescription;
    }

    public void closeDescription() {
        this.descriptionList.add(this.currentDescription);
        this.currentDescription = null;
    }

    public List<ConnectDescription> getAllDescriptions() {
        return this.descriptionList;
    }

    public ConnectDescription getConnectedDescription() {
        if (!this.optFound) {
            return null;
        }
        return this.descriptionList.get(this.lastRetryConnectDescription);
    }

    public int getOutboundConnectTimeout() {
        return this.outboundConnectTimeout;
    }

    public boolean isUsingCustomHostnameResolver() {
        return this.hostnameResolver != null;
    }

    private void setExpireTimeSocketOption(int expireTimeMinutes, @Blind(PropertiesBlinder.class) Properties socketOptions) {
        if (expireTimeMinutes < 0) {
            return;
        }
        int expireTimeSeconds = expireTimeMinutes * 60;
        socketOptions.putIfAbsent(33, Integer.toString(expireTimeSeconds));
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    public OracleHostnameResolver getHostnameResolver() {
        return this.hostnameResolver;
    }

    public final void setDriverResources(DriverResources driverResources) {
        this.driverResources = driverResources;
    }
}
