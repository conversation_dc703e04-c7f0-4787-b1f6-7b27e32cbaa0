package oracle.net.nt;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Level;
import javax.net.ssl.SSLEngine;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/nt/TcpsConfigure.class */
public class TcpsConfigure {
    private static final String CLASS_NAME = TcpsConfigure.class.getName();
    private static final HashSet<String> UNSUPPORTED_PROTOCOLS_SET = new HashSet<>();
    private static final Map<String, String[]> CONFIG_VALUE_MAP;

    static {
        UNSUPPORTED_PROTOCOLS_SET.add("SSLv3");
        UNSUPPORTED_PROTOCOLS_SET.add("SSLv2Hello");
        CONFIG_VALUE_MAP = new HashMap();
        CONFIG_VALUE_MAP.put("1.2", new String[]{"TLSv1.2"});
        CONFIG_VALUE_MAP.put("1.3", new String[]{"TLSv1.3"});
        CONFIG_VALUE_MAP.put("1.2 or 1.3", new String[]{"TLSv1.2", "TLSv1.3"});
        CONFIG_VALUE_MAP.put("1.3 or 1.2", new String[]{"TLSv1.3", "TLSv1.2"});
        CONFIG_VALUE_MAP.put("0", new String[0]);
        CONFIG_VALUE_MAP.put("undetermined", new String[0]);
    }

    private TcpsConfigure() {
    }

    public static void configure(SSLEngine sslEngine, Properties socketOptions, Diagnosable diagnosable) throws NetException {
        String[] tlsVersions = getTLSVersions(socketOptions, sslEngine, diagnosable);
        String[] cipherSuites = getCipherSuites(socketOptions, diagnosable);
        if (tlsVersions != null) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "configure", "Configuring SSL Versions = {0}", (String) null, (String) null, Arrays.toString(tlsVersions));
            try {
                sslEngine.setEnabledProtocols(tlsVersions);
            } catch (IllegalArgumentException e) {
                throw ((NetException) new NetException(NetException.UNSUPPORTED_SSL_PROTOCOL).initCause(e));
            }
        }
        if (cipherSuites != null) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "configure", "Configuring SSL CipherSuites = {0}", (String) null, (String) null, Arrays.toString(cipherSuites));
            try {
                sslEngine.setEnabledCipherSuites(cipherSuites);
            } catch (IllegalArgumentException e2) {
                throw ((NetException) new NetException(NetException.UNSUPPORTED_SSL_CIPHER_SUITE).initCause(e2));
            }
        }
    }

    private static String[] getCipherSuites(Properties socketOptions, Diagnosable diagnosable) throws NetException {
        String cipherSuites = (String) socketOptions.getOrDefault(7, OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CIPHER_SUITES_DEFAULT);
        if (cipherSuites == null) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getCipherSuites", "No SSL CipherSuites configured", null, null);
            return null;
        }
        String cipherSuites2 = cipherSuites.trim();
        if (cipherSuites2.startsWith("(") && cipherSuites2.endsWith(")")) {
            cipherSuites2 = cipherSuites2.substring(1, cipherSuites2.length() - 1);
        }
        return cipherSuites2.split("\\s*,\\s*");
    }

    private static String[] getTLSVersions(Properties socketOptions, SSLEngine engine, Diagnosable diagnosable) throws NetException {
        String versionStr = (String) socketOptions.getOrDefault(6, OracleConnection.CONNECTION_PROPERTY_THIN_SSL_VERSION_DEFAULT);
        if (versionStr == null) {
            diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "getTLSVersions", "No TLS versions configured. Using the default supported versions.", null, null);
            List<String> supportedVersionList = new ArrayList<>();
            String[] allProtocols = engine.getSupportedProtocols();
            if (allProtocols == null) {
                return null;
            }
            for (String proto : allProtocols) {
                if (!UNSUPPORTED_PROTOCOLS_SET.contains(proto)) {
                    supportedVersionList.add(proto);
                }
            }
            return (String[]) supportedVersionList.toArray(new String[0]);
        }
        String versionStr2 = versionStr.trim();
        diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getTLSVersions", "TLS Version = {0}", (String) null, (String) null, versionStr2);
        if (versionStr2.startsWith("{") && versionStr2.endsWith("}")) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getTLSVersions", "TLS Version config value in new format.", null, null);
            return versionStr2.substring(1, versionStr2.length() - 1).split("\\s*,\\s*");
        }
        if (versionStr2.startsWith("(") && versionStr2.endsWith(")")) {
            diagnosable.debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getTLSVersions", "TLS Version config value in primitive format.", null, null);
            versionStr2 = versionStr2.substring(1, versionStr2.length() - 1);
        }
        if (!CONFIG_VALUE_MAP.containsKey(versionStr2)) {
            throw new NetException(NetException.INVALID_SSL_VERSION);
        }
        String[] supportedVersions = CONFIG_VALUE_MAP.get(versionStr2);
        if (supportedVersions.length == 0) {
            return null;
        }
        return supportedVersions;
    }
}
