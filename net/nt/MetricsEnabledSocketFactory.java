package oracle.net.nt;

import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import javax.net.SocketFactory;
import oracle.jdbc.driver.DMSFactory;

/* compiled from: TcpNTAdapter.java */
/* loaded from: ojdbc8.jar:oracle/net/nt/MetricsEnabledSocketFactory.class */
class MetricsEnabledSocketFactory extends SocketFactory {
    DMSFactory.DMSNoun base;
    DMSFactory.DMSNoun waitnoun;
    DMSFactory.DMSPhase waitEvent;

    MetricsEnabledSocketFactory(DMSFactory.DMSNoun dmsParent) {
        this.waitEvent = DMSFactory.getInstance().createPhaseEvent(dmsParent, "DBWaitTime", "Time spent waiting for DB");
        this.waitEvent.deriveMetric(DMSFactory.SensorIntf_all);
    }

    @Override // javax.net.SocketFactory
    public Socket createSocket() {
        return new MetricsEnabledSocket(this.waitEvent);
    }

    @Override // javax.net.SocketFactory
    public Socket createSocket(String host, int port) throws IOException {
        return new MetricsEnabledSocket(this.waitEvent, host, port);
    }

    @Override // javax.net.SocketFactory
    public Socket createSocket(String host, int port, InetAddress localAddr, int localPort) throws IOException {
        return new MetricsEnabledSocket(this.waitEvent, host, port, localAddr, localPort);
    }

    @Override // javax.net.SocketFactory
    public Socket createSocket(InetAddress host, int port) throws IOException {
        return new MetricsEnabledSocket(this.waitEvent, host, port);
    }

    @Override // javax.net.SocketFactory
    public Socket createSocket(InetAddress address, int port, InetAddress localAddress, int localPort) throws IOException {
        return new MetricsEnabledSocket(this.waitEvent, address, port, localAddress, localPort);
    }
}
