package oracle.net.nt;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.channels.UnresolvedAddressException;
import java.security.AccessController;
import java.security.PrivilegedActionException;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/net/nt/TcpFastOpen.class */
class TcpFastOpen {
    private static final String TFO_JDBC_LIB_NAME = "tfojdbc1";
    private static final String LIB_TFO_JDBC = "libtfojdbc1.so";
    private static RuntimeException libTfoJdbcPreloadException = null;
    private static boolean IS_PREFER_IPV4_STACK_PROPERTY_SET = false;
    private static final TcpFastOpen INSTANCE = new TcpFastOpen();

    private static native InetSocketAddress setTcpFastOpenBytes0(byte[] bArr, int i, int i2, byte[] bArr2, boolean z);

    private static native String getErrorMessage0(byte[] bArr, int i, int i2);

    private static native int getBytesSentAndRemove(byte[] bArr, int i, int i2);

    private TcpFastOpen() {
    }

    public static TcpFastOpen getInstance() {
        return INSTANCE;
    }

    private static boolean isLibTfoJdbcPreloadSet() throws PrivilegedActionException {
        String ldPreloadValue = getPreloadEnvValue();
        if (ldPreloadValue != null && ldPreloadValue.contains(LIB_TFO_JDBC)) {
            return true;
        }
        libTfoJdbcPreloadException = new RuntimeException(DatabaseError.createSqlException(DatabaseError.EOJ_LIB_TFO_JDBC_PRELOAD_NOT_SET).getMessage());
        return false;
    }

    private static void loadLibTfoJdbcLibrary() throws PrivilegedActionException {
        try {
            AccessController.doPrivileged(() -> {
                System.loadLibrary(TFO_JDBC_LIB_NAME);
                return null;
            });
        } catch (PrivilegedActionException ex) {
            throw new RuntimeException(ex);
        }
    }

    public static void init() throws PrivilegedActionException {
        if (isLibTfoJdbcPreloadSet()) {
            loadLibTfoJdbcLibrary();
        }
        try {
            AccessController.doPrivileged(() -> {
                IS_PREFER_IPV4_STACK_PROPERTY_SET = "true".equals(System.getProperty("java.net.preferIPv4Stack"));
                return null;
            });
        } catch (PrivilegedActionException ex) {
            throw new RuntimeException(ex);
        }
    }

    private static String getPreloadEnvValue() throws PrivilegedActionException {
        String[] value = {null};
        try {
            AccessController.doPrivileged(() -> {
                value[0] = System.getenv("LD_PRELOAD");
                return null;
            });
            return value[0];
        } catch (PrivilegedActionException ex) {
            throw new RuntimeException(ex);
        }
    }

    public static InetSocketAddress setTcpFastOpenBytes(InetSocketAddress socketAddress, byte[] tcpFastOpenBytes) {
        if (libTfoJdbcPreloadException != null) {
            throw libTfoJdbcPreloadException;
        }
        if (socketAddress.isUnresolved()) {
            throw new UnresolvedAddressException();
        }
        InetAddress inetAddr = socketAddress.getAddress();
        byte[] addr = inetAddr.getAddress();
        return setTcpFastOpenBytes0(addr, addr.length, socketAddress.getPort(), tcpFastOpenBytes, IS_PREFER_IPV4_STACK_PROPERTY_SET);
    }

    public static String getErrorMessage(InetSocketAddress socketAddress) {
        try {
            InetAddress inetAddr = socketAddress.getAddress();
            byte[] addr = inetAddr.getAddress();
            return getErrorMessage0(addr, addr.length, socketAddress.getPort());
        } catch (UnsatisfiedLinkError e) {
            return null;
        }
    }

    public static int getBytesSentAndRemove(InetSocketAddress socketAddress) {
        InetAddress inetAddr = socketAddress.getAddress();
        byte[] addr = inetAddr.getAddress();
        return getBytesSentAndRemove(addr, addr.length, socketAddress.getPort());
    }
}
