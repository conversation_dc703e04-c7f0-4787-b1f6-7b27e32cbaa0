package oracle.net.nt;

import java.io.IOException;
import java.nio.channels.SocketChannel;
import java.time.Duration;
import java.util.TimerTask;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/net/nt/AsyncOutboundTimeoutHandler.class */
public final class AsyncOutboundTimeoutHandler {
    private final AsyncOutboundTimeoutHandler loginTimeoutHandler;
    private final Monitor monitor = Monitor.newInstance();
    private SocketChannel channel = null;
    private TimerTask timeoutTask = null;
    private Thread interruptThread = null;
    private IOException timeoutException;

    private AsyncOutboundTimeoutHandler(AsyncOutboundTimeoutHandler loginTimeoutHandler) {
        this.loginTimeoutHandler = loginTimeoutHandler;
    }

    final void setChannel(SocketChannel channel) throws IOException {
        if (this.loginTimeoutHandler != null) {
            this.loginTimeoutHandler.setChannel(channel);
        }
        Monitor.CloseableLock lock = this.monitor.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureUnexpired();
                this.channel = channel;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public void setInterruptThread(Thread thread) throws IOException {
        if (this.loginTimeoutHandler != null) {
            this.loginTimeoutHandler.setInterruptThread(thread);
        }
        Monitor.CloseableLock lock = this.monitor.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureUnexpired();
                this.interruptThread = thread;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public final void scheduleTimeout(Duration duration, IOException timeoutException) throws IOException {
        Monitor.CloseableLock lock = this.monitor.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureUnexpired();
                cancelTimeout();
                scheduleTimeoutTask(duration, timeoutException);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void scheduleTimeoutTask(Duration duration, IOException timeoutException) {
        this.timeoutTask = TimeoutInterruptHandler.scheduleTask(() -> {
            executeTimeout(timeoutException);
        }, duration.toMillis());
    }

    private void executeTimeout(IOException timeoutException) {
        Monitor.CloseableLock lock = this.monitor.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.timeoutException = timeoutException;
                if (this.channel != null) {
                    TcpMultiplexer.forceCallback(this.channel, timeoutException);
                }
                if (this.interruptThread != null) {
                    this.interruptThread.interrupt();
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public final boolean cancelTimeout() {
        Monitor.CloseableLock lock = this.monitor.acquireCloseableLock();
        Throwable th = null;
        try {
            return this.timeoutTask == null ? false : this.timeoutTask.cancel();
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private final void ensureUnexpired() throws IOException {
        if (this.timeoutException != null) {
            throw this.timeoutException;
        }
    }

    public static AsyncOutboundTimeoutHandler newInstance(AsyncOutboundTimeoutHandler loginTimeoutHandler) {
        return new AsyncOutboundTimeoutHandler(loginTimeoutHandler);
    }

    public static AsyncOutboundTimeoutHandler newScheduledInstance(AsyncOutboundTimeoutHandler loginTimeoutHandler, Duration duration, IOException timeoutException) {
        AsyncOutboundTimeoutHandler newHandler = new AsyncOutboundTimeoutHandler(loginTimeoutHandler);
        newHandler.scheduleTimeoutTask(duration, timeoutException);
        return newHandler;
    }
}
