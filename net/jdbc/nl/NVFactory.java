package oracle.net.jdbc.nl;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NVFactory.class */
public class NVFactory {
    public NVPair createNVPair(String nvString) throws NLException {
        NVTokens nvt = new NVTokens();
        nvt.parseTokens(nvString);
        return _readTopLevelNVPair(nvt);
    }

    private NVPair _readTopLevelNVPair(NVTokens nvt) throws NLException {
        int tk = nvt.getToken();
        nvt.eatToken();
        if (tk != 1) {
            Object[] local = {"(", getContext(nvt)};
            throw new InvalidSyntaxException("SyntaxError-04602", local);
        }
        String name = _readNVLiteral(nvt);
        NVPair nvp = new NVPair(name);
        int token = nvt.getToken();
        int tk2 = token;
        if (token != 3) {
            return _readRightHandSide(nvp, nvt);
        }
        while (true) {
            if (tk2 == 8 || tk2 == 3) {
                name = name + nvt.popLiteral();
                tk2 = nvt.getToken();
            } else {
                nvp.setName(name);
                return _readRightHandSide(nvp, nvt);
            }
        }
    }

    private NVPair _readNVPair(NVTokens nvt) throws NLException {
        int tk = nvt.getToken();
        nvt.eatToken();
        if (tk != 1 && tk != 3) {
            Object[] local = {"( or ,", getContext(nvt)};
            throw new InvalidSyntaxException("SyntaxError-04602", local);
        }
        String name = _readNVLiteral(nvt);
        NVPair nvp = new NVPair(name);
        return _readRightHandSide(nvp, nvt);
    }

    private NVPair _readRightHandSide(NVPair nvp, NVTokens nvt) throws NLException {
        switch (nvt.getToken()) {
            case 2:
            case 3:
                nvp.setAtom(nvp.getName());
                break;
            case 4:
                nvt.eatToken();
                if (nvt.getToken() == 8) {
                    String value = _readNVLiteral(nvt);
                    nvp.setAtom(value);
                    break;
                } else {
                    _readNVList(nvt, nvp);
                    break;
                }
            default:
                Object[] local = {"=", getContext(nvt)};
                throw new InvalidSyntaxException("SyntaxError-04602", local);
        }
        int tk = nvt.getToken();
        if (tk == 2) {
            nvt.eatToken();
        } else if (tk != 3) {
            Object[] local2 = {nvt.getLiteral(), getContext(nvt)};
            throw new InvalidSyntaxException("UnexpectedChar-04605", local2);
        }
        return nvp;
    }

    private String _readNVLiteral(NVTokens nvt) throws NLException {
        int tk = nvt.getToken();
        if (tk != 8) {
            Object[] local = {"LITERAL", getContext(nvt)};
            throw new InvalidSyntaxException("SyntaxError-04602", local);
        }
        return nvt.popLiteral();
    }

    private void _readNVList(NVTokens nvt, NVPair parent) throws NLException {
        int tk = nvt.getToken();
        if (tk != 1 && tk != 3) {
            return;
        }
        NVPair child = _readNVPair(nvt);
        parent.addListElement(child);
        if ((tk == 3 || child.getName() == child.getAtom()) && parent.getListType() != NVPair.LIST_COMMASEP) {
            parent.setListType(NVPair.LIST_COMMASEP);
        }
        _readNVList(nvt, parent);
    }

    private String getContext(NVTokens nvt) throws NLException {
        return " " + nvt.popLiteral() + " " + nvt.popLiteral() + " " + nvt.popLiteral();
    }
}
