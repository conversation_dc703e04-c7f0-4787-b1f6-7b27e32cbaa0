package oracle.net.jdbc.nl;

import java.io.PrintStream;
import java.security.AccessController;
import java.util.Vector;
import oracle.net.resolver.NavSchemaObject;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NVPair.class */
public final class NVPair {
    private String _name;
    private int _rhsType;
    private String _atom;
    private Vector _list;
    private int _listType;
    private NVPair _parent;
    public static int RHS_NONE = 0;
    public static int RHS_ATOM = 1;
    public static int RHS_LIST = 2;
    public static int LIST_REGULAR = 3;
    public static int LIST_COMMASEP = 4;
    static final String LINE_SEPARATOR = getLineSeparatorProperty();

    public NVPair(String name) {
        this._name = name;
        this._atom = null;
        this._list = null;
        this._listType = LIST_REGULAR;
        this._parent = null;
        this._rhsType = RHS_NONE;
    }

    public NVPair(String name, String atom) throws InvalidSyntaxException {
        this(name);
        setAtom(atom);
    }

    public NVPair(String name, NVPair child) {
        this(name);
        addListElement(child);
    }

    public String getName() {
        return this._name;
    }

    public void setName(String name) {
        this._name = name;
    }

    public NVPair getParent() {
        return this._parent;
    }

    private void _setParent(NVPair parent) {
        this._parent = parent;
    }

    public int getRHSType() {
        return this._rhsType;
    }

    public int getListType() {
        return this._listType;
    }

    public void setListType(int type) {
        this._listType = type;
    }

    public String getAtom() {
        return this._atom;
    }

    public void setAtom(String atom) throws InvalidSyntaxException {
        if (this._name.indexOf("COMMENT") == -1 && containsComment(atom)) {
            Object[] local = {"#", getName()};
            throw new InvalidSyntaxException("UnexpectedChar-04603", local);
        }
        this._rhsType = RHS_ATOM;
        this._atom = atom;
        this._list = null;
    }

    private boolean containsComment(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) == '#' && (i == 0 || str.charAt(i - 1) != '\\')) {
                return true;
            }
        }
        return false;
    }

    public int getListSize() {
        if (this._list == null) {
            return 0;
        }
        return this._list.size();
    }

    public NVPair getListElement(int pos) {
        if (this._list == null) {
            return null;
        }
        return (NVPair) this._list.elementAt(pos);
    }

    public void addListElement(NVPair pair) {
        if (this._list == null) {
            this._rhsType = RHS_LIST;
            this._list = new Vector(3, 5);
            this._atom = null;
        }
        this._list.addElement(pair);
        pair._setParent(this);
    }

    public void removeListElement(int pos) {
        if (this._list != null) {
            this._list.removeElementAt(pos);
            if (getListSize() == 0) {
                this._list = null;
                this._rhsType = RHS_NONE;
            }
        }
    }

    private String space(int count) {
        String blank_str = new String("");
        for (int i = 0; i < count; i++) {
            blank_str = blank_str + " ";
        }
        return blank_str;
    }

    public String trimValueToString() {
        String tmpStr = valueToString().trim();
        return tmpStr.substring(1, tmpStr.length() - 1);
    }

    public String valueToString() {
        String out = "";
        if (this._rhsType == RHS_ATOM) {
            out = out + this._atom;
        } else if (this._rhsType == RHS_LIST) {
            if (this._listType == LIST_REGULAR) {
                for (int i = 0; i < getListSize(); i++) {
                    out = out + getListElement(i).toString();
                }
            } else if (this._listType == LIST_COMMASEP) {
                for (int i2 = 0; i2 < getListSize(); i2++) {
                    NVPair listElem = getListElement(i2);
                    out = out + listElem.getName();
                    if (i2 != getListSize() - 1) {
                        out = out + ", ";
                    }
                }
            }
        }
        return out;
    }

    public String toString() {
        String out = "(" + this._name + "=";
        if (this._rhsType == RHS_ATOM) {
            out = out + this._atom;
        } else if (this._rhsType == RHS_LIST) {
            if (this._listType == LIST_REGULAR) {
                for (int i = 0; i < getListSize(); i++) {
                    out = out + getListElement(i).toString();
                }
            } else if (this._listType == LIST_COMMASEP) {
                String out2 = out + " (";
                for (int i2 = 0; i2 < getListSize(); i2++) {
                    NVPair listElem = getListElement(i2);
                    out2 = out2 + listElem.getName();
                    if (i2 != getListSize() - 1) {
                        out2 = out2 + ", ";
                    }
                }
                out = out2 + ")";
            }
        }
        return out + ")";
    }

    public String toString(int level, boolean enable) {
        String out;
        String str;
        String out2 = "";
        String temp = new String(this._name);
        if (this._rhsType == RHS_LIST) {
            if (this._listType == LIST_REGULAR) {
                String rhs = "";
                for (int i = 0; i < getListSize(); i++) {
                    if (temp.equalsIgnoreCase("ADDRESS") || temp.equalsIgnoreCase("RULE")) {
                        str = rhs + getListElement(i).toString(level + 1, false);
                    } else {
                        str = rhs + getListElement(i).toString(level + 1, true);
                    }
                    rhs = str;
                }
                if (!rhs.equals("")) {
                    if (temp.equalsIgnoreCase("ADDRESS") || temp.equalsIgnoreCase("RULE")) {
                        out = out2 + space(level * 2) + "(" + this._name + " = ";
                    } else {
                        out = out2 + space(level * 2) + "(" + this._name + " =" + LINE_SEPARATOR;
                    }
                    String out3 = out + rhs;
                    out2 = (temp.equalsIgnoreCase("ADDRESS") || temp.equalsIgnoreCase("RULE")) ? out3 + ")" + LINE_SEPARATOR : level == 0 ? out3 + ")" : level == 1 ? out3 + space(level * 2) + ")" : out3 + space(level * 2) + ")" + LINE_SEPARATOR;
                }
            } else if (this._listType == LIST_COMMASEP) {
                String out4 = out2 + "(" + this._name + "= (";
                for (int i2 = 0; i2 < getListSize(); i2++) {
                    NVPair listElem = getListElement(i2);
                    out4 = out4 + listElem.getName();
                    if (i2 != getListSize() - 1) {
                        out4 = out4 + ", ";
                    }
                }
                out2 = out4 + NavSchemaObject.CID4v2;
            }
        } else if (this._rhsType == RHS_ATOM) {
            if (level == 0) {
                if (temp.indexOf("COMMENT") != -1) {
                    this._atom = modifyCommentString(this._atom);
                    out2 = out2 + "(" + this._atom + ")";
                } else {
                    out2 = out2 + "(" + this._name + " = " + this._atom + ")";
                }
            } else if (temp.indexOf("COMMENT") != -1) {
                this._atom = modifyCommentString(this._atom);
                out2 = out2 + this._atom + LINE_SEPARATOR;
            } else {
                out2 = !enable ? out2 + "(" + this._name + " = " + this._atom + ")" : (out2 + space(level * 2) + "(" + this._name + " = " + this._atom + ")") + LINE_SEPARATOR;
            }
        }
        return out2;
    }

    public String modifyCommentString(String str) {
        String str1 = "";
        int offset = 0;
        while (offset < str.length()) {
            char current_char = str.charAt(offset);
            switch (current_char) {
                case '\\':
                    if (str.charAt(offset + 1) != '(' && str.charAt(offset + 1) != '=' && str.charAt(offset + 1) != ')' && str.charAt(offset + 1) != ',' && str.charAt(offset + 1) != '\\') {
                        break;
                    } else {
                        offset++;
                        break;
                    }
                    break;
            }
            int i = offset;
            offset++;
            str1 = str1 + str.charAt(i);
        }
        return str1;
    }

    public void println() {
        System.out.println(toString());
    }

    public void println(PrintStream pout) {
        if (this._rhsType == RHS_ATOM) {
            pout.println("          (" + this._name + " = " + this._atom + ")");
        } else if (this._rhsType == RHS_LIST) {
            for (int i = 0; i < getListSize(); i++) {
                getListElement(i).println(pout);
            }
        }
    }

    private static String getLineSeparatorProperty() {
        return (String) AccessController.doPrivileged(() -> {
            return System.getProperty("line.separator");
        });
    }
}
