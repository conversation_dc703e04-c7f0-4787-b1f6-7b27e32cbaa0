package oracle.net.jdbc.nl;

import java.util.Locale;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NLException.class */
public class NLException extends Exception {
    public Object[] msg_params;
    private String error_index;
    private String error_msg;

    public NLException(String err_mesg) {
        this.error_msg = err_mesg;
    }

    public NLException(String err_index, Object param) {
        this.error_index = err_index;
        Object[] l_params = {param};
        this.msg_params = l_params;
        initErrorMessage();
    }

    public NLException(String err_index, Object[] params) {
        this.error_index = err_index;
        this.msg_params = params;
        initErrorMessage();
    }

    private void initErrorMessage() {
        NetStrings nlStr = new NetStrings("oracle.net.jdbc.nl.mesg.NLSR", Locale.getDefault());
        this.error_msg = nlStr.getString(this.error_index, this.msg_params);
    }

    @Override // java.lang.Throwable
    public String toString() {
        return getErrorMessage();
    }

    public String getErrorIndex() {
        return this.error_index;
    }

    public String getErrorMessage() {
        return this.error_msg;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return getErrorMessage();
    }
}
