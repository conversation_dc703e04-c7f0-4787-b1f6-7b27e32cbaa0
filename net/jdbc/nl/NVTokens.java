package oracle.net.jdbc.nl;

import java.util.Vector;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NVTokens.class */
public final class NVTokens {
    public static final int TKN_NONE = 0;
    public static final int TKN_LPAREN = 1;
    public static final int TKN_RPAREN = 2;
    public static final int TKN_COMMA = 3;
    public static final int TKN_EQUAL = 4;
    public static final int TKN_LITERAL = 8;
    public static final int TKN_EOS = 9;
    private static final char TKN_LPAREN_VALUE = '(';
    private static final char TKN_RPAREN_VALUE = ')';
    private static final char TKN_COMMA_VALUE = ',';
    private static final char TKN_EQUAL_VALUE = '=';
    private static final char TKN_BKSLASH_VALUE = '\\';
    private static final char TKN_DQUOTE_VALUE = '\"';
    private static final char TKN_SQUOTE_VALUE = '\'';
    private static final char TKN_EOS_VALUE = '%';
    private static final char TKN_SPC_VALUE = ' ';
    private static final char TKN_TAB_VALUE = '\t';
    private static final char TKN_LF_VALUE = '\n';
    private static final char TKN_CR_VALUE = '\r';
    private Vector _tkType = null;
    private Vector _tkValue = null;
    private int _numTokens = 0;
    private int _tkPos = 0;

    private static boolean _isWhiteSpace(char it) {
        if (it == ' ' || it == '\t' || it == '\n' || it == '\r') {
            return true;
        }
        return false;
    }

    private static String _trimWhiteSpace(String it) {
        int length = it.length();
        int start = 0;
        int end = length;
        while (start < length && _isWhiteSpace(it.charAt(start))) {
            start++;
        }
        while (start < end && _isWhiteSpace(it.charAt(end - 1))) {
            end--;
        }
        return it.substring(start, end);
    }

    public boolean parseTokens(String nvString) {
        this._numTokens = 0;
        this._tkPos = 0;
        this._tkType = new Vector(25, 25);
        this._tkValue = new Vector(25, 25);
        int len = nvString.length();
        boolean eql_seen = false;
        char[] input = nvString.toCharArray();
        int pos = 0;
        while (pos < len) {
            while (pos < len && _isWhiteSpace(input[pos])) {
                pos++;
            }
            if (pos < len) {
                switch (input[pos]) {
                    case '(':
                        eql_seen = false;
                        _addToken(1, '(');
                        pos++;
                        break;
                    case ')':
                        eql_seen = false;
                        _addToken(2, ')');
                        pos++;
                        break;
                    case ',':
                        eql_seen = false;
                        _addToken(3, ',');
                        pos++;
                        break;
                    case '=':
                        eql_seen = true;
                        _addToken(4, '=');
                        pos++;
                        break;
                    default:
                        int startPos = pos;
                        int endPos = -1;
                        boolean quoted_str = false;
                        char quote_char = '\"';
                        if (input[pos] == '\'' || input[pos] == '\"') {
                            quoted_str = true;
                            quote_char = input[pos];
                            pos++;
                        }
                        while (true) {
                            if (pos < len) {
                                if (input[pos] == '\\') {
                                    pos += 2;
                                } else if (quoted_str) {
                                    if (input[pos] == quote_char) {
                                        pos++;
                                        endPos = pos;
                                    }
                                    pos++;
                                } else {
                                    if (input[pos] == '(' || input[pos] == ')' || ((input[pos] == ',' && !eql_seen) || (input[pos] == '=' && !eql_seen))) {
                                    }
                                    pos++;
                                }
                            }
                        }
                        endPos = pos;
                        if (endPos == -1) {
                            endPos = pos;
                        }
                        _addToken(8, _trimWhiteSpace(nvString.substring(startPos, endPos)));
                        break;
                }
            }
        }
        _addToken(9, '%');
        return true;
    }

    public int getToken() throws NLException {
        if (this._tkType == null) {
            throw new UninitializedObjectException("ParseError-04604", "");
        }
        if (this._tkPos < this._numTokens) {
            return ((Integer) this._tkType.elementAt(this._tkPos)).intValue();
        }
        throw new NLException("NoLiterals-04610", "");
    }

    public int popToken() throws NLException {
        if (this._tkType == null) {
            throw new UninitializedObjectException("ParseError-04604", "");
        }
        if (this._tkPos < this._numTokens) {
            Vector vector = this._tkType;
            int i = this._tkPos;
            this._tkPos = i + 1;
            int token = ((Integer) vector.elementAt(i)).intValue();
            return token;
        }
        throw new NLException("NoLiterals-04610", "");
    }

    public String getLiteral() throws NLException {
        if (this._tkValue == null) {
            throw new UninitializedObjectException("ParseError-04604", "");
        }
        if (this._tkPos < this._numTokens) {
            String theLiteral = (String) this._tkValue.elementAt(this._tkPos);
            return theLiteral;
        }
        throw new NLException("NoLiterals-04610", "");
    }

    public String popLiteral() throws NLException {
        if (this._tkValue == null) {
            throw new UninitializedObjectException("ParseError-04604", "");
        }
        if (this._tkPos < this._numTokens) {
            Vector vector = this._tkValue;
            int i = this._tkPos;
            this._tkPos = i + 1;
            String theLiteral = (String) vector.elementAt(i);
            return theLiteral;
        }
        throw new NLException("NoLiterals-04610", "");
    }

    public void eatToken() {
        if (this._tkPos < this._numTokens) {
            this._tkPos++;
        }
    }

    public String toString() {
        if (this._tkType == null) {
            return "*NO TOKENS*";
        }
        String out = "Tokens";
        for (int i = 0; i < this._numTokens; i++) {
            out = out + " : " + this._tkValue.elementAt(i);
        }
        return out;
    }

    public void println() {
        System.out.println(toString());
    }

    private void _addToken(int tk, char tk_char) {
        _addToken(tk, String.valueOf(tk_char));
    }

    private void _addToken(int tk, String tk_val) {
        this._tkType.addElement(new Integer(tk));
        this._tkValue.addElement(tk_val);
        this._numTokens++;
    }
}
