package oracle.net.jdbc.nl;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Reader;
import java.io.Writer;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.StringTokenizer;
import java.util.Vector;
import oracle.jdbc.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NLParamParser.class */
public class NLParamParser {
    private String filename;
    private Hashtable ht;
    private Vector linebuffer;
    private int filePermissions;
    private int Commentcnt;
    private int nvStringcnt;
    private int Groupcnt;
    private boolean hasComments;
    private boolean hasGroups;
    private String[] errstr;
    private int errstrcnt;
    private static final int MAX_ERRORS = 50;
    public static final byte IGNORE_NONE = 0;
    public static final byte IGNORE_NL_EXCEPTION = 1;
    public static final byte IGNORE_FILE_EXCEPTION = 2;
    public static final byte NLPASUCC = 1;
    public static final byte NLPAOVWR = 2;
    public static final byte NLPAFAIL = -1;
    private static boolean DEBUG = false;

    public static NLParamParser createEmptyParamParser() {
        return new NLParamParser();
    }

    private NLParamParser() {
        this.filePermissions = 0;
        this.Commentcnt = 0;
        this.nvStringcnt = 0;
        this.Groupcnt = 0;
        this.hasComments = false;
        this.hasGroups = false;
        this.filename = null;
        this.ht = new Hashtable(128);
    }

    public NLParamParser(String filename) throws IOException, NLException {
        this(filename, (byte) 2);
    }

    public NLParamParser(String filename, byte ignore_exception) throws IOException, NLException {
        this.filePermissions = 0;
        this.Commentcnt = 0;
        this.nvStringcnt = 0;
        this.Groupcnt = 0;
        this.hasComments = false;
        this.hasGroups = false;
        this.filename = filename;
        this.ht = new Hashtable(128);
        FileReader fr = null;
        BufferedReader br = null;
        try {
            try {
                fr = new FileReader(filename);
                br = new BufferedReader(fr);
                initializeNlpa(br, ignore_exception);
                if (fr != null) {
                    fr.close();
                }
                if (br != null) {
                    br.close();
                }
            } catch (FileNotFoundException e) {
                if ((ignore_exception & 2) == 0) {
                    throw new FileNotFoundException(filename);
                }
                if (fr != null) {
                    fr.close();
                }
                if (br != null) {
                    br.close();
                }
            }
        } catch (Throwable th) {
            if (fr != null) {
                fr.close();
            }
            if (br != null) {
                br.close();
            }
            throw th;
        }
    }

    public NLParamParser(Reader dataStream) throws IOException, NLException {
        this(dataStream, (byte) 0);
    }

    public NLParamParser(Reader dataStream, byte ignore_exception) throws IOException, NLException {
        this.filePermissions = 0;
        this.Commentcnt = 0;
        this.nvStringcnt = 0;
        this.Groupcnt = 0;
        this.hasComments = false;
        this.hasGroups = false;
        BufferedReader br = new BufferedReader(dataStream);
        this.filename = null;
        this.ht = new Hashtable(128);
        initializeNlpa(br, ignore_exception);
    }

    private void initializeNlpa(BufferedReader br, byte ignore_exception) throws IOException, NLException {
        this.linebuffer = new Vector(100, 50);
        this.errstr = new String[50];
        while (true) {
            try {
                String newline = br.readLine();
                if (newline == null) {
                    break;
                } else {
                    this.linebuffer.addElement(newline);
                }
            } catch (IOException e) {
                if ((ignore_exception & 2) == 0) {
                    throw new IOException("Unable to read a line from : " + this.filename);
                }
            }
        }
        String nvElem = "";
        String linesep = NVPair.LINE_SEPARATOR;
        String commentKey = "";
        String commentValue = "";
        for (int i = 0; i < this.linebuffer.size(); i++) {
            String line = (String) this.linebuffer.elementAt(i);
            if (line.length() != 0) {
                if (line.charAt(0) == '#') {
                    if (line.indexOf(".ORA Configuration ") != -1 || line.indexOf(" Network Configuration File: ") != -1 || line.indexOf("Generated by") != -1) {
                        if (DEBUG) {
                            System.out.println(line + ": this comment ignored");
                        }
                    } else if (commentKey.length() != 0) {
                        commentValue = commentValue + line + linesep;
                    } else {
                        commentKey = "COMMENT#" + this.Commentcnt;
                        commentValue = line + linesep;
                        if (!this.hasComments) {
                            this.hasComments = true;
                        }
                    }
                } else if (line.charAt(0) == ' ' || line.charAt(0) == '\t' || line.charAt(0) == ')' || line.charAt(0) == '(') {
                    if (commentValue.length() == 0) {
                        if (nvElem.length() == 0) {
                            line = eatNLPWS(line);
                        }
                        String line2 = checkNLPforComments(line);
                        if (line2.length() != 0) {
                            nvElem = nvElem + line2 + linesep;
                        }
                    } else if (nvElem.length() == 0 && commentValue.length() != 0) {
                        if (checkNLPforComments(eatNLPWS(line)).length() != 0 && (ignore_exception & 1) == 0) {
                            throw new NLException("InvalidChar-04611", "");
                        }
                    } else if (nvElem.length() != 0 && commentValue.length() != 0) {
                        commentKey = "";
                        commentValue = "";
                        nvElem = nvElem + checkNLPforComments(line) + linesep;
                    }
                } else if (nvElem.length() == 0 && commentValue.length() == 0) {
                    nvElem = nvElem + checkNLPforComments(line) + linesep;
                } else if (nvElem.length() == 0 && commentValue.length() != 0) {
                    try {
                        addNLPListElement(commentKey + "=" + modifyCommentString(commentValue));
                    } catch (NLException e2) {
                        storeError(nvElem, ignore_exception);
                        if ((ignore_exception & 1) == 0) {
                            throw e2;
                        }
                    }
                    commentKey = "";
                    commentValue = "";
                    this.Commentcnt++;
                    nvElem = nvElem + checkNLPforComments(line) + linesep;
                } else if (nvElem.length() != 0 && commentValue.length() == 0) {
                    try {
                        addNLPListElement(nvElem);
                    } catch (NLException e3) {
                        storeError(nvElem, ignore_exception);
                        if ((ignore_exception & 1) == 0) {
                            throw e3;
                        }
                    }
                    nvElem = "" + checkNLPforComments(line) + linesep;
                } else if (nvElem.length() != 0 && commentValue.length() != 0) {
                    try {
                        addNLPListElement(nvElem);
                    } catch (NLException e4) {
                        storeError(nvElem, ignore_exception);
                        if ((ignore_exception & 1) == 0) {
                            throw e4;
                        }
                    }
                    nvElem = "" + checkNLPforComments(line) + linesep;
                    try {
                        addNLPListElement(commentKey + "=" + modifyCommentString(commentValue));
                    } catch (NLException e5) {
                        storeError(nvElem, ignore_exception);
                        if ((ignore_exception & 1) == 0) {
                            throw e5;
                        }
                    }
                    commentKey = "";
                    commentValue = "";
                    this.Commentcnt++;
                }
            }
        }
        if (nvElem.length() != 0) {
            try {
                addNLPListElement(nvElem);
            } catch (NLException e6) {
                storeError(nvElem, ignore_exception);
                if ((ignore_exception & 1) == 0) {
                    throw e6;
                }
            }
            nvElem = "";
        }
        if (commentValue.length() != 0) {
            try {
                addNLPListElement(commentKey + "=" + modifyCommentString(commentValue));
            } catch (NLException e7) {
                storeError(nvElem, ignore_exception);
                if ((ignore_exception & 1) == 0) {
                    throw e7;
                }
            }
            this.Commentcnt++;
        }
    }

    private String modifyCommentString(String str) {
        String str2;
        String str1 = "";
        for (int i = 0; i < str.length(); i++) {
            char current_char = str.charAt(i);
            switch (current_char) {
                case '(':
                    str2 = str1 + "\\(";
                    break;
                case ')':
                    str2 = str1 + "\\)";
                    break;
                case ',':
                    str2 = str1 + "\\,";
                    break;
                case '=':
                    str2 = str1 + "\\=";
                    break;
                case '\\':
                    str2 = str1 + "\\\\";
                    break;
                default:
                    str2 = str1 + str.charAt(i);
                    break;
            }
            str1 = str2;
        }
        return str1;
    }

    private String checkNLPforComments(String str) {
        StringBuffer str1 = new StringBuffer(str.length());
        for (int i = 0; i < str.length(); i++) {
            char current_char = str.charAt(i);
            if (current_char == '#') {
                if (i != 0) {
                    if (str.charAt(i - 1) != '\\') {
                        break;
                    }
                    str1.append(current_char);
                } else {
                    return "";
                }
            } else {
                str1.append(current_char);
            }
        }
        return str1.toString();
    }

    private String eatNLPWS(String str) {
        StringBuffer str1 = new StringBuffer(str.length());
        int offset = 0;
        boolean NWScharfound = false;
        while (!NWScharfound) {
            int i = offset;
            offset++;
            char current_char = str.charAt(i);
            if (current_char != ' ' || current_char != '\t') {
                NWScharfound = true;
                for (int i2 = offset - 1; str.charAt(i2) == '\n'; i2++) {
                    str1.append(str.charAt(i2));
                }
            } else if (current_char == '\n') {
                return "";
            }
        }
        return str1.toString();
    }

    public void saveNLParams() throws IOException {
        if (this.filename == null) {
            return;
        }
        FileWriter fw = null;
        try {
            fw = new FileWriter(this.filename);
            String orafileType = OracleConnection.CONNECTION_PROPERTY_THIN_VSESSION_TERMINAL_DEFAULT;
            StringTokenizer st = new StringTokenizer(this.filename, File.separator);
            while (st.hasMoreTokens()) {
                orafileType = st.nextToken();
            }
            writeToStream(fw, orafileType, this.filename);
            if (fw != null) {
                fw.close();
            }
        } catch (Throwable th) {
            if (fw != null) {
                fw.close();
            }
            throw th;
        }
    }

    public void writeToStream(Writer out, String headerFileType, String headerFileName) {
        PrintWriter pw = new PrintWriter(new BufferedWriter(out));
        pw.println("# " + headerFileType + " Network Configuration File: " + headerFileName + "");
        pw.println("# Generated by Oracle configuration tools.");
        pw.println("");
        if (this.hasGroups) {
            saveNLPGroups(pw);
        }
        Enumeration e = this.ht.elements();
        while (e.hasMoreElements()) {
            NVPair nvp = (NVPair) e.nextElement();
            String Value = nvp.toString(0, true);
            if (DEBUG) {
                System.out.println("The initial stringified NVPair is:\n" + Value);
            }
            if (!Value.equals("")) {
                char[] chkstr = new char[Value.length() - 2];
                Value.getChars(1, Value.length() - 1, chkstr, 0);
                String newValue = new String(chkstr);
                if (DEBUG) {
                    System.out.println("The modified NV String is:\n" + newValue);
                }
                pw.println(newValue);
                pw.println("");
            }
        }
        pw.close();
    }

    public void saveNLParams(String filename) throws IOException {
        String oldFilenameOrNullIfStream = this.filename;
        this.filename = filename;
        saveNLParams();
        this.filename = oldFilenameOrNullIfStream;
    }

    public String getFilename() {
        return this.filename;
    }

    public boolean configuredInFile() {
        return this.filename != null;
    }

    public int getNLPListSize() {
        this.nvStringcnt = 0;
        Enumeration e = this.ht.keys();
        while (e.hasMoreElements()) {
            String paramName = (String) e.nextElement();
            if (paramName.indexOf("COMMENT") == -1) {
                this.nvStringcnt++;
            }
        }
        return this.nvStringcnt;
    }

    public boolean inErrorList(String name) {
        boolean exists = false;
        if (DEBUG) {
            System.out.println("Entering inErrorList():");
        }
        int i = 0;
        while (true) {
            if ((exists && i >= this.errstrcnt) || this.errstrcnt == 0) {
                break;
            }
            if (this.errstr[i].indexOf(name) != -1) {
                exists = true;
            }
            i++;
        }
        return exists;
    }

    public NVPair getNLPListElement(String Name) {
        String UName = Name.toUpperCase();
        return (NVPair) this.ht.get(UName);
    }

    public String[] getNLPAllNames() {
        int size = getNLPListSize();
        String[] Names = new String[size];
        int count = 0;
        Enumeration e = this.ht.keys();
        while (e.hasMoreElements()) {
            String paramName = (String) e.nextElement();
            if (paramName.indexOf("COMMENT") == -1) {
                int i = count;
                count++;
                Names[i] = paramName;
            }
        }
        return Names;
    }

    public String[] getNLPAllElements() {
        int size = getNLPListSize();
        String[] nvstrings = new String[size];
        int count = 0;
        Enumeration e = this.ht.elements();
        while (e.hasMoreElements()) {
            NVPair nvp = (NVPair) e.nextElement();
            if (nvp.getName().indexOf("COMMENT") == -1) {
                String nvelem = nvp.toString();
                int i = count;
                count++;
                nvstrings[i] = nvelem;
            }
        }
        return nvstrings;
    }

    public byte addNLPListElement(String Name, Object Value) {
        try {
            Object retobj = this.ht.put(Name, Value);
            return retobj != null ? (byte) 2 : (byte) 1;
        } catch (NullPointerException e) {
            if (DEBUG) {
                System.out.println(e.getMessage());
                return (byte) -1;
            }
            return (byte) -1;
        }
    }

    public void addNLPGroupProfile(String[] Names) {
        StringBuilder sbAppend = new StringBuilder().append("GROUP#");
        int i = this.Groupcnt;
        this.Groupcnt = i + 1;
        String GroupName = new String(sbAppend.append(i).toString());
        if (!this.hasGroups) {
            this.hasGroups = true;
        }
        addNLPListElement(GroupName, Names);
    }

    private String[] getNLPGroupProfile(String Name) {
        String UName = Name.toUpperCase();
        return (String[]) this.ht.get(UName);
    }

    private void saveNLPGroups(PrintWriter pw) {
        for (int i = 0; i < this.Groupcnt; i++) {
            String GroupName = new String("GROUP#" + i);
            String[] Value = getNLPGroupProfile(GroupName);
            for (int j = 0; j < Value.length; j++) {
                if (DEBUG) {
                    System.out.println("Current Value in Group Profile: " + Value[j]);
                }
                if (Value[j] != null) {
                    NVPair nvp = getNLPListElement(Value[j]);
                    if (nvp != null) {
                        String paramValue = nvp.toString(0, true);
                        if (DEBUG) {
                            System.out.println("Parameter Value = " + paramValue);
                        }
                        char[] chkstr = new char[paramValue.length() - 2];
                        paramValue.getChars(1, paramValue.length() - 1, chkstr, 0);
                        String newValue = new String(chkstr);
                        pw.println(newValue);
                        pw.println("");
                        NVPair nvp_gone = removeNLPListElement(Value[j]);
                        if (nvp_gone == null && DEBUG) {
                            System.out.println("saveNLPGroups(): Could notremove param from Hashtable");
                        }
                    } else if (DEBUG) {
                        System.out.println("No such Parameter in the Table");
                    }
                }
            }
            removeNLPGroupProfile(GroupName);
        }
    }

    public void addNLPListElement(String nvString) throws NLException {
        String newstr;
        char[] chkstr = new char[nvString.length() + 2];
        if (DEBUG) {
            System.out.println("Entering Method addNLPListElement\n");
            System.out.println("String to add is: " + nvString + "");
        }
        nvString.getChars(0, nvString.length(), chkstr, 1);
        if (chkstr[1] == '(') {
            newstr = nvString;
        } else {
            chkstr[0] = '(';
            String os = System.getProperty("os.name");
            if (os != null && (os.equals("Windows NT") || os.equals("Windows 95"))) {
                if (chkstr[chkstr.length - 2] == '/' || chkstr[chkstr.length - 2] == '\\') {
                    chkstr[chkstr.length - 2] = ')';
                } else {
                    chkstr[chkstr.length - 1] = ')';
                }
            } else if (chkstr[chkstr.length - 2] == '\\') {
                chkstr[chkstr.length - 2] = ')';
            } else {
                chkstr[chkstr.length - 1] = ')';
            }
            newstr = new String(chkstr);
            if (DEBUG) {
                System.out.println("The modified NV String is: " + newstr + "");
            }
        }
        NVFactory nvf = new NVFactory();
        NVPair nvp = nvf.createNVPair(newstr);
        if (nvp.getRHSType() == NVPair.RHS_NONE) {
            throw new NLException("NullRHS-04612", nvp.getName());
        }
        String name = nvp.getName();
        String uname = name.toUpperCase();
        nvp.setName(uname);
        String[] unames = uname.split(",");
        for (String aliasUname : unames) {
            if (DEBUG) {
                System.out.println("The final NV String is: " + nvp.toString() + "");
            }
            int retcode = addNLPListElement(aliasUname, nvp);
            switch (retcode) {
                case -1:
                    if (DEBUG) {
                        System.out.println("The value for the Name: " + aliasUname + " could not be inserted\n");
                        break;
                    } else {
                        break;
                    }
                case 2:
                    if (DEBUG) {
                        System.out.println("The value for the Name: " + aliasUname + " was overwritten\n");
                        break;
                    } else {
                        break;
                    }
            }
        }
    }

    public NVPair removeNLPListElement(String Name) {
        String UName = Name.toUpperCase();
        if (DEBUG) {
            System.out.println("Trying to remove: " + UName + " from Table");
        }
        Object retobj = this.ht.remove(UName);
        if (retobj != null) {
            return (NVPair) retobj;
        }
        return null;
    }

    public void removeNLPGroupProfile(String Name) {
        String UName = Name.toUpperCase();
        if (DEBUG) {
            System.out.println("Trying to remove: " + UName + " GroupName from Table");
        }
        this.ht.remove(UName);
    }

    public void removeNLPAllElements() {
        this.ht.clear();
    }

    public String toString() {
        String out = "";
        Enumeration e = this.ht.elements();
        while (e.hasMoreElements()) {
            NVPair nvp = (NVPair) e.nextElement();
            String Value = nvp.toString();
            out = out + Value + "\n";
        }
        return out;
    }

    public boolean fileHasComments() {
        return this.hasComments;
    }

    public void println() {
        System.out.println(toString());
    }

    public void setFilePermissions(int filePermissions) {
        this.filePermissions = filePermissions;
    }

    private void storeError(String nvElem, byte ignore_exception) throws NLException {
        if (this.errstrcnt < 50) {
            String[] strArr = this.errstr;
            int i = this.errstrcnt;
            this.errstrcnt = i + 1;
            strArr[i] = nvElem;
            return;
        }
        if ((ignore_exception & 1) == 0) {
            throw new NLException("Too many errors", "");
        }
    }
}
