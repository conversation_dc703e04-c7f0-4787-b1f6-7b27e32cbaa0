package oracle.net.jdbc.nl;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NVNavigator.class */
public class NVNavigator {
    public NVPair findNVPairRecurse(NVPair nvp, String name) {
        if (nvp == null || name.equalsIgnoreCase(nvp.getName())) {
            return nvp;
        }
        if (nvp.getRHSType() == NVPair.RHS_ATOM) {
            return null;
        }
        for (int i = 0; i < nvp.getListSize(); i++) {
            NVPair child = findNVPairRecurse(nvp.getListElement(i), name);
            if (child != null) {
                return child;
            }
        }
        return null;
    }

    public NVPair findNVPair(NVPair nvp, String name) {
        if (nvp == null || nvp.getRHSType() != NVPair.RHS_LIST) {
            return null;
        }
        for (int i = 0; i < nvp.getListSize(); i++) {
            NVPair child = nvp.getListElement(i);
            if (name.equalsIgnoreCase(child.getName())) {
                return child;
            }
        }
        return null;
    }

    public NVPair findNVPair(NVPair nvp, String[] names) {
        NVPair current = nvp;
        for (String str : names) {
            current = findNVPair(current, str);
            if (current == null) {
                return null;
            }
        }
        return current;
    }
}
