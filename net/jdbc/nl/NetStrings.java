package oracle.net.jdbc.nl;

import java.text.FieldPosition;
import java.text.MessageFormat;
import java.util.Locale;
import java.util.ResourceBundle;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/nl/NetStrings.class */
public class NetStrings {
    ResourceBundle strBundle;
    Locale loc;

    public NetStrings(String strFile, Locale loc) {
        this.strBundle = ResourceBundle.getBundle(strFile, loc);
    }

    public NetStrings(Locale loc) {
        this.strBundle = ResourceBundle.getBundle("oracle.net.jdbc.nl.mesg.NLSR", loc);
    }

    public NetStrings() {
        this.strBundle = ResourceBundle.getBundle("oracle.net.jdbc.nl.mesg.NLSR", Locale.getDefault());
    }

    public String getString(String key) {
        return this.strBundle.getString(key);
    }

    public String getString(String key, Object[] params) {
        String pattern = this.strBundle.getString(key);
        StringBuffer buff = new StringBuffer();
        MessageFormat msgFmt = new MessageFormat(pattern);
        msgFmt.format(params, buff, (FieldPosition) null);
        return buff.toString();
    }
}
