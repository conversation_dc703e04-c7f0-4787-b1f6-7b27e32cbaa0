package oracle.net.jdbc.TNSAddress;

import java.util.Vector;
import oracle.jdbc.OracleConnection;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.resolver.NavSchemaObject;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/DescriptionList.class */
public class DescriptionList implements SchemaObject {
    private SchemaObject child;
    private NVPair childnv;
    protected SchemaObjectFactoryInterface f;
    public Vector children = new Vector();
    public boolean sourceRoute = false;
    public boolean loadBalance = true;
    public boolean failover = true;

    public DescriptionList(SchemaObjectFactoryInterface f) {
        this.f = null;
        this.f = f;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public int isA() {
        return 3;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String isA_String() {
        return "DESCRIPTION_LIST";
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromString(String s) throws SOException, NetException, NLException {
        NVPair nvp = new NVFactory().createNVPair(s);
        initFromNVPair(nvp);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromNVPair(NVPair nvp) throws SOException, NetException {
        init();
        int listsize = nvp.getListSize();
        if (listsize == 0) {
            throw new SOException();
        }
        for (int i = 0; i < listsize; i++) {
            this.childnv = nvp.getListElement(i);
            if (this.childnv.getName().equalsIgnoreCase("SOURCE_ROUTE")) {
                this.sourceRoute = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("LOAD_BALANCE")) {
                this.loadBalance = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("FAILOVER")) {
                this.failover = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("DESCRIPTION")) {
                SchemaObjectFactoryInterface schemaObjectFactoryInterface = this.f;
                SchemaObjectFactoryInterface schemaObjectFactoryInterface2 = this.f;
                this.child = schemaObjectFactoryInterface.create(2);
                this.child.initFromNVPair(this.childnv);
                this.children.addElement(this.child);
            } else {
                throw new SOException();
            }
        }
        if (this.children.size() == 0) {
            throw new SOException();
        }
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        String s = new String("");
        if (this.children.size() < 1) {
            return s;
        }
        new String("");
        for (int i = 0; i < this.children.size(); i++) {
            String child = ((SchemaObject) this.children.elementAt(i)).toString();
            if (!child.equals("")) {
                s = s + child;
            }
        }
        if (s.equals("") && this.sourceRoute) {
            s = s + NavSchemaObject.SR;
        }
        if (s.equals("") && !this.loadBalance) {
            s = s + "(LOAD_BALANCE=no)";
        }
        if (s.equals("") && !this.failover) {
            s = s + NavSchemaObject.NFO;
        }
        if (!s.equals("")) {
            s = "(DESCRIPTION_LIST=" + s + ")";
        }
        return s;
    }

    protected void init() {
        this.children.removeAllElements();
        this.child = null;
        this.childnv = null;
        this.sourceRoute = false;
        this.loadBalance = true;
        this.failover = true;
    }
}
