package oracle.net.jdbc.TNSAddress;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/SchemaObjectFactory.class */
public class SchemaObjectFactory implements SchemaObjectFactoryInterface {
    @Override // oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface
    public SchemaObject create(int type) {
        switch (type) {
            case 0:
                return new Address(this);
            case 1:
                return new AddressList(this);
            case 2:
                return new Description(this);
            case 3:
                return new DescriptionList(this);
            case 4:
                return new ServiceAlias(this);
            default:
                return null;
        }
    }
}
