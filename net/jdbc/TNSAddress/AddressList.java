package oracle.net.jdbc.TNSAddress;

import java.util.Vector;
import oracle.jdbc.OracleConnection;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.resolver.NavSchemaObject;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/AddressList.class */
public class AddressList implements SchemaObject {
    private SchemaObject child;
    private NVPair childnv;
    protected SchemaObjectFactoryInterface f;
    public Vector children = new Vector();
    public boolean sourceRoute = false;
    public boolean loadBalance = false;
    public boolean failover = true;

    public AddressList(SchemaObjectFactoryInterface f) {
        this.f = null;
        this.f = f;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public int isA() {
        return 1;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String isA_String() {
        return "ADDRESS_LIST";
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromString(String s) throws SOException, NetException, NLException {
        NVPair nvp = new NVFactory().createNVPair(s);
        initFromNVPair(nvp);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromNVPair(NVPair nvp) throws SOException, NetException {
        init();
        int listsize = nvp.getListSize();
        if (listsize == 0) {
            throw new SOException();
        }
        for (int i = 0; i < listsize; i++) {
            this.childnv = nvp.getListElement(i);
            if (this.childnv.getName().equalsIgnoreCase("SOURCE_ROUTE")) {
                this.sourceRoute = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("LOAD_BALANCE")) {
                this.loadBalance = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("FAILOVER")) {
                this.failover = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("ADDRESS")) {
                SchemaObjectFactoryInterface schemaObjectFactoryInterface = this.f;
                SchemaObjectFactoryInterface schemaObjectFactoryInterface2 = this.f;
                this.child = schemaObjectFactoryInterface.create(0);
                this.child.initFromNVPair(this.childnv);
                this.children.addElement(this.child);
            } else if (this.childnv.getName().equalsIgnoreCase("ADDRESS_LIST")) {
                SchemaObjectFactoryInterface schemaObjectFactoryInterface3 = this.f;
                SchemaObjectFactoryInterface schemaObjectFactoryInterface4 = this.f;
                this.child = schemaObjectFactoryInterface3.create(1);
                this.child.initFromNVPair(this.childnv);
                this.children.addElement(this.child);
            } else {
                throw new SOException();
            }
        }
        if (this.children.size() == 0) {
            throw new SOException();
        }
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        String s = new String("");
        if (this.children.size() < 1) {
            return s;
        }
        String s2 = s + "(ADDRESS_LIST=";
        for (int i = 0; i < this.children.size(); i++) {
            s2 = s2 + ((SchemaObject) this.children.elementAt(i)).toString();
        }
        if (this.sourceRoute) {
            s2 = s2 + NavSchemaObject.SR;
        }
        if (this.loadBalance) {
            s2 = s2 + NavSchemaObject.LB;
        }
        if (!this.failover) {
            s2 = s2 + NavSchemaObject.NFO;
        }
        return s2 + ")";
    }

    protected void init() {
        this.children.removeAllElements();
        this.child = null;
        this.childnv = null;
        this.sourceRoute = false;
        this.loadBalance = false;
        this.failover = true;
    }
}
