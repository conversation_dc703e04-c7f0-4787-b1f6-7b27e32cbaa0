package oracle.net.jdbc.TNSAddress;

import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/ServiceAlias.class */
public class ServiceAlias implements SchemaObject {
    protected SchemaObject child;
    public String name;
    private SchemaObjectFactoryInterface f;

    public ServiceAlias(SchemaObjectFactoryInterface f) {
        this.f = null;
        this.f = f;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public int isA() {
        return 4;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String isA_String() {
        return null;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromString(String s) throws SOException, NetException, NLException {
        if (s.charAt(0) != '(') {
            s = "(" + s + ")";
        }
        NVPair nvp = new NVFactory().createNVPair(s);
        initFromNVPair(nvp);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromNVPair(NVPair nvp) throws SOException, NetException {
        if (nvp.getListSize() != 1) {
            throw new SOException();
        }
        NVPair childnv = nvp.getListElement(0);
        if (childnv.getName().equalsIgnoreCase("DESCRIPTION_LIST")) {
            SchemaObjectFactoryInterface schemaObjectFactoryInterface = this.f;
            SchemaObjectFactoryInterface schemaObjectFactoryInterface2 = this.f;
            this.child = schemaObjectFactoryInterface.create(3);
        } else if (childnv.getName().equalsIgnoreCase("DESCRIPTION")) {
            SchemaObjectFactoryInterface schemaObjectFactoryInterface3 = this.f;
            SchemaObjectFactoryInterface schemaObjectFactoryInterface4 = this.f;
            this.child = schemaObjectFactoryInterface3.create(2);
        } else if (childnv.getName().equalsIgnoreCase("ADDRESS_LIST")) {
            SchemaObjectFactoryInterface schemaObjectFactoryInterface5 = this.f;
            SchemaObjectFactoryInterface schemaObjectFactoryInterface6 = this.f;
            this.child = schemaObjectFactoryInterface5.create(1);
        } else if (childnv.getName().equalsIgnoreCase("ADDRESS")) {
            SchemaObjectFactoryInterface schemaObjectFactoryInterface7 = this.f;
            SchemaObjectFactoryInterface schemaObjectFactoryInterface8 = this.f;
            this.child = schemaObjectFactoryInterface7.create(0);
        } else {
            throw new SOException();
        }
        this.child.initFromNVPair(childnv);
        this.name = nvp.getName();
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        return this.name + "=" + this.child.toString();
    }
}
