package oracle.net.jdbc.TNSAddress;

import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/Address.class */
public class Address implements SchemaObject {
    public String addr;
    public String prot;
    public String host;
    public int port;
    public String httpsProxy;
    public int httpsProxyPort;
    public String webSocketURI;
    protected SchemaObjectFactoryInterface f;

    public Address(SchemaObjectFactoryInterface f) {
        this.f = null;
        this.f = f;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public int isA() {
        return 0;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String isA_String() {
        return "ADDRESS";
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromString(String s) throws SOException, NLException {
        NVPair nvp = new NVFactory().createNVPair(s);
        initFromNVPair(nvp);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromNVPair(NVPair nvp) throws SOException {
        init();
        if (nvp == null || !nvp.getName().equalsIgnoreCase("address")) {
            throw new SOException("Null or unexpected name");
        }
        NVNavigator nav = new NVNavigator();
        NVPair protnvp = nav.findNVPair(nvp, "PROTOCOL");
        if (protnvp == null || protnvp.getAtom() == null) {
            throw new SOException("PROTOCOL is not specified");
        }
        this.prot = protnvp.getAtom();
        NVPair nvpHost = nav.findNVPair(nvp, "HOST");
        if (nvpHost != null) {
            this.host = nvpHost.getAtom();
        } else {
            this.host = null;
        }
        NVPair nvpPort = nav.findNVPair(nvp, "PORT");
        if (nvpPort != null && nvpPort.getAtom() != null) {
            try {
                this.port = Integer.parseInt(nvpPort.getAtom());
            } catch (Exception e) {
                throw new SOException(new NetException(NetException.PORT_NUMBER_ERROR, nvpPort.getAtom()).getMessage());
            }
        } else if (this.prot.equalsIgnoreCase("beq")) {
            this.port = 0;
        } else {
            this.port = 1521;
        }
        if (this.port < 0 || this.port > 65535) {
            throw new SOException(new NetException(NetException.PORT_NUMBER_ERROR, String.valueOf(this.port)).getMessage());
        }
        NVPair nvpHttpProxy = nav.findNVPair(nvp, "HTTPS_PROXY");
        NVPair nvpHttpProxyPort = nav.findNVPair(nvp, "HTTPS_PROXY_PORT");
        if (nvpHttpProxy != null) {
            this.httpsProxy = nvpHttpProxy.getAtom();
        }
        if (this.httpsProxy != null && nvpHttpProxyPort != null) {
            this.httpsProxyPort = Integer.parseInt(nvpHttpProxyPort.getAtom());
        }
        NVPair nvpWebSockUri = nav.findNVPair(nvp, "WEBSOCK_URI");
        if (nvpWebSockUri != null) {
            this.webSocketURI = nvpWebSockUri.getAtom();
        }
        if (this.addr == null) {
            this.addr = nvp.toString();
        }
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        String s = new String("");
        if (this.host != null) {
            s = s + "(HOST=" + this.host + ")";
        }
        if (this.port != 0) {
            s = s + "(PORT=" + this.port + ")";
        }
        if (this.prot != null) {
            s = s + "(PROTOCOL=" + this.prot + ")";
        }
        if (s != "") {
            s = "(ADDRESS=" + s + ")";
        }
        return s;
    }

    public String getProtocol() {
        return this.prot;
    }

    protected void init() {
        this.addr = null;
        this.prot = null;
        this.httpsProxy = null;
        this.httpsProxyPort = -1;
        this.port = 0;
        this.host = null;
        this.webSocketURI = null;
    }
}
