package oracle.net.jdbc.TNSAddress;

import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/SchemaObject.class */
public interface SchemaObject {
    public static final int ADDR = 0;
    public static final int ADDR_LIST = 1;
    public static final int DESC = 2;
    public static final int DESC_LIST = 3;
    public static final int ALIAS = 4;
    public static final int SERVICE = 5;
    public static final int DB_SERVICE = 6;

    int isA();

    String isA_String();

    void initFromString(String str) throws SOEx<PERSON>, NetException, NLException;

    void initFromNVPair(NVPair nVPair) throws SOException, NetException;

    String toString();
}
