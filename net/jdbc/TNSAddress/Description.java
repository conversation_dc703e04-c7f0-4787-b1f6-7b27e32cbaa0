package oracle.net.jdbc.TNSAddress;

import java.util.Vector;
import oracle.jdbc.OracleConnection;
import oracle.net.jdbc.nl.InvalidSyntaxException;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.resolver.NavSchemaObject;
import oracle.net.resolver.TimeUnitSuffixUtility;

/* loaded from: ojdbc8.jar:oracle/net/jdbc/TNSAddress/Description.class */
public class Description implements SchemaObject {
    private SchemaObject child;
    private NVPair childnv;
    private SchemaObjectFactoryInterface f;
    protected String SDU;
    protected String TDU;
    protected String httpsProxy;
    protected String httpsProxyPort;
    protected String useSNI;
    protected String sendBufSize;
    protected String receiveBufSize;
    protected String connectData;
    protected String SID;
    protected String server;
    protected String failoverMode;
    protected String instanceRole;
    protected String serviceName;
    protected String instanceName;
    protected String handlerName;
    protected String oracleHome;
    protected String connectTimeout;
    protected String transportTimeout;
    protected String retryCount;
    protected String expireTime;
    protected String netConnectionIdPrefix;
    protected String useTcpFastOpen;
    protected String colocationTag;
    protected String authTypes;
    protected String sslServerCertDN;
    protected String sslServerDNMatch;
    protected String sslAllowWeakDNMatch;
    protected String myWalletDirectory;
    protected String sslVersion;
    protected String sslCiphers;
    protected String sslCertAlias;
    protected String sslCertThumbprint;
    protected String encryptionClient;
    protected String encryptionClientTypes;
    protected String checksumClient;
    protected String checksumClientTypes;
    protected String allowWeakCrypto;
    protected String descriptionString;
    protected String tokenAuthentication;
    protected String tokenLocation;
    protected String passwordAuthentication;
    protected String ociIamUrl;
    protected String ociTenancy;
    protected String ociCompartment;
    protected String ociDatabase;
    protected String ociConfigFile;
    protected String ociProfile;
    protected String azureDbAppIdUri;
    protected String tenantId;
    protected String clientId;
    protected String clientCertificate;
    protected String redirectUri;
    protected String azureCredentials;
    protected Vector children = new Vector();
    protected boolean sourceRoute = false;
    protected boolean loadBalance = false;
    protected boolean failover = true;
    protected boolean keepAlive = false;
    protected Vector protocolStacks = new Vector();
    private Vector authParams = new Vector();
    private Vector extraConnInfo = new Vector();
    private Vector extraInfo = new Vector();
    protected int delayInMillis = 1000;
    protected String BEQServerProcessEnvironmentVars = null;
    protected String BEQServerProcessArguments = null;

    public Description(SchemaObjectFactoryInterface f) {
        this.f = null;
        this.f = f;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public int isA() {
        return 2;
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String isA_String() {
        return "DESCRIPTION";
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromString(String s) throws SOException, NetException, NLException {
        NVPair nvp = new NVFactory().createNVPair(s);
        initFromNVPair(nvp);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public void initFromNVPair(NVPair nvp) throws SOException, NetException {
        init();
        int listsize = nvp.getListSize();
        if (listsize == 0) {
            throw new SOException();
        }
        this.descriptionString = nvp.toString();
        for (int i = 0; i < listsize; i++) {
            this.childnv = nvp.getListElement(i);
            if (this.childnv.getName().equalsIgnoreCase("SOURCE_ROUTE")) {
                this.sourceRoute = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("LOAD_BALANCE")) {
                this.loadBalance = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("FAILOVER")) {
                this.failover = this.childnv.getAtom().equalsIgnoreCase("yes") || this.childnv.getAtom().equalsIgnoreCase(OracleConnection.NETWORK_COMPRESSION_ON) || this.childnv.getAtom().equalsIgnoreCase("true");
            } else if (this.childnv.getName().equalsIgnoreCase("ENABLE")) {
                this.keepAlive = this.childnv.getAtom().equalsIgnoreCase("broken");
            } else if (this.childnv.getName().equalsIgnoreCase("PROTOCOL_STACK")) {
                this.protocolStacks.addElement(this.childnv.toString());
            } else if (this.childnv.getName().equalsIgnoreCase("ADDRESS")) {
                SchemaObjectFactoryInterface schemaObjectFactoryInterface = this.f;
                SchemaObjectFactoryInterface schemaObjectFactoryInterface2 = this.f;
                this.child = schemaObjectFactoryInterface.create(0);
                this.child.initFromNVPair(this.childnv);
                this.children.addElement(this.child);
            } else if (this.childnv.getName().equalsIgnoreCase("ADDRESS_LIST")) {
                SchemaObjectFactoryInterface schemaObjectFactoryInterface3 = this.f;
                SchemaObjectFactoryInterface schemaObjectFactoryInterface4 = this.f;
                this.child = schemaObjectFactoryInterface3.create(1);
                this.child.initFromNVPair(this.childnv);
                this.children.addElement(this.child);
            } else if (this.childnv.getName().equalsIgnoreCase("SDU")) {
                this.SDU = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("TDU")) {
                this.TDU = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("SEND_BUF_SIZE")) {
                this.sendBufSize = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("RECV_BUF_SIZE")) {
                this.receiveBufSize = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("HTTPS_PROXY")) {
                this.httpsProxy = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("HTTPS_PROXY_PORT")) {
                this.httpsProxyPort = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("CONNECT_DATA")) {
                this.connectData = this.childnv.valueToString();
                int cdlistsize = this.childnv.getListSize();
                if (cdlistsize == 0) {
                    throw new SOException();
                }
                for (int j = 0; j < cdlistsize; j++) {
                    NVPair cdlistnv = this.childnv.getListElement(j);
                    if (cdlistnv.getName().equalsIgnoreCase("SID")) {
                        this.SID = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("SERVER")) {
                        this.server = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("SERVICE_NAME")) {
                        this.serviceName = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("INSTANCE_NAME")) {
                        this.instanceName = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("HANDLER_NAME")) {
                        this.handlerName = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("ORACLE_HOME")) {
                        this.oracleHome = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("FAILOVER_MODE")) {
                        this.failoverMode = this.childnv.getListElement(j).toString();
                    } else if (cdlistnv.getName().equalsIgnoreCase("INSTANCE_ROLE")) {
                        this.instanceRole = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("CONNECTION_ID_PREFIX")) {
                        this.netConnectionIdPrefix = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase("COLOCATION_TAG")) {
                        this.colocationTag = cdlistnv.getAtom();
                    } else if (cdlistnv.getName().equalsIgnoreCase(SQLnetDef.TCP_FAST_OPEN_PARAM_NAME)) {
                        setUseTcpFastOpen(SQLnetDef.TCP_FAST_OPEN_PARAM_NAME, cdlistnv.getAtom());
                    } else if (cdlistnv.getName().equalsIgnoreCase(SQLnetDef.TCP_FAST_OPEN_PARAM_NAME_2)) {
                        setUseTcpFastOpen(SQLnetDef.TCP_FAST_OPEN_PARAM_NAME_2, cdlistnv.getAtom());
                    } else {
                        String param = cdlistnv.toString().trim();
                        this.extraConnInfo.addElement(param.substring(1, param.length() - 1));
                    }
                }
            } else if (this.childnv.getName().equalsIgnoreCase("RETRY_DELAY")) {
                this.delayInMillis = TimeUnitSuffixUtility.getTimeInMilliseconds(this.childnv.getAtom(), true);
            } else if (this.childnv.getName().equalsIgnoreCase("SECURITY")) {
                int slistsize = this.childnv.getListSize();
                if (slistsize == 0) {
                    throw new SOException();
                }
                for (int j2 = 0; j2 < slistsize; j2++) {
                    NVPair slistnv = this.childnv.getListElement(j2);
                    if (slistnv.getName().equalsIgnoreCase("AUTHENTICATION")) {
                        this.authTypes = slistnv.toString();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_server_cert_dn")) {
                        this.sslServerCertDN = slistnv.getAtom();
                        if (this.sslServerCertDN != null && this.sslServerCertDN.startsWith("\"") && this.sslServerCertDN.endsWith("\"")) {
                            this.sslServerCertDN = this.sslServerCertDN.substring(1, this.sslServerCertDN.length() - 1);
                        }
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_server_dn_match")) {
                        this.sslServerDNMatch = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_allow_weak_dn_match")) {
                        this.sslAllowWeakDNMatch = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("wallet_location")) {
                        this.myWalletDirectory = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("my_wallet_directory") && this.myWalletDirectory == null) {
                        this.myWalletDirectory = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_version")) {
                        this.sslVersion = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_cipher_suites")) {
                        this.sslCiphers = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_certificate_alias")) {
                        this.sslCertAlias = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("ssl_certificate_thumbprint")) {
                        this.sslCertThumbprint = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("encryption_client")) {
                        this.encryptionClient = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("encryption_types_client")) {
                        this.encryptionClientTypes = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("crypto_checksum_client")) {
                        this.checksumClient = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("crypto_checksum_types_client")) {
                        this.checksumClientTypes = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("allow_weak_crypto")) {
                        this.allowWeakCrypto = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("token_auth")) {
                        this.tokenAuthentication = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("token_location")) {
                        this.tokenLocation = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("password_auth")) {
                        this.passwordAuthentication = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_iam_url")) {
                        this.ociIamUrl = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_tenancy")) {
                        this.ociTenancy = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_compartment")) {
                        this.ociCompartment = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_database")) {
                        this.ociDatabase = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_config_file")) {
                        this.ociConfigFile = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("oci_profile")) {
                        this.ociProfile = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("azure_db_app_id_uri")) {
                        this.azureDbAppIdUri = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("tenant_id")) {
                        this.tenantId = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("client_id")) {
                        this.clientId = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("client_certificate")) {
                        this.clientCertificate = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("redirect_uri")) {
                        this.redirectUri = slistnv.getAtom();
                    } else if (slistnv.getName().equalsIgnoreCase("azure_credentials")) {
                        this.azureCredentials = slistnv.getAtom();
                    } else {
                        this.authParams.addElement(slistnv.toString());
                    }
                }
            } else if (this.childnv.getName().equalsIgnoreCase("CONNECT_TIMEOUT")) {
                this.connectTimeout = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("TRANSPORT_CONNECT_TIMEOUT")) {
                this.transportTimeout = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("RETRY_COUNT")) {
                this.retryCount = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("EXPIRE_TIME")) {
                this.expireTime = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("ENVS")) {
                this.BEQServerProcessEnvironmentVars = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("ARGS")) {
                this.BEQServerProcessArguments = this.childnv.getAtom();
            } else if (this.childnv.getName().equalsIgnoreCase("USE_SNI")) {
                this.useSNI = this.childnv.getAtom();
            } else {
                if (this.childnv.getName().equalsIgnoreCase("HS") && this.childnv.getAtom() == null) {
                    try {
                        this.childnv.setAtom("OK");
                    } catch (InvalidSyntaxException e) {
                    }
                }
                String param2 = this.childnv.toString().trim();
                this.extraInfo.addElement(param2.substring(1, param2.length() - 1));
            }
        }
    }

    private void setUseTcpFastOpen(String key, String value) throws NetException {
        if (SQLnetDef.isValidTcpFastOpenValue(value)) {
            this.useTcpFastOpen = value;
            return;
        }
        throw new NetException(NetException.INVALID_CONNECT_DATA_CONFIG, null, false, key, value);
    }

    @Override // oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        String s = new String("");
        new String("");
        for (int i = 0; i < this.children.size(); i++) {
            String child = ((SchemaObject) this.children.elementAt(i)).toString();
            if (!child.equals("")) {
                s = s + child;
            }
        }
        if (!s.equals("") && this.sourceRoute) {
            s = s + NavSchemaObject.SR;
        }
        if (!s.equals("") && this.loadBalance) {
            s = s + NavSchemaObject.LB;
        }
        if (!s.equals("") && !this.failover) {
            s = s + NavSchemaObject.NFO;
        }
        if (this.keepAlive) {
            s = s + "(ENABLE=broken)";
        }
        if (this.SDU != null) {
            s = s + "(SDU=" + this.SDU + ")";
        }
        if (this.TDU != null) {
            s = s + "(TDU=" + this.TDU + ")";
        }
        if (this.useSNI != null) {
            s = s + "(USE_SNI=" + this.useSNI + ")";
        }
        if (this.sendBufSize != null) {
            s = s + "(SEND_BUF_SIZE=" + this.sendBufSize + ")";
        }
        if (this.receiveBufSize != null) {
            s = s + "(RECV_BUF_SIZE=" + this.receiveBufSize + ")";
        }
        if (this.protocolStacks.size() != 0) {
            for (int i2 = 0; i2 < this.protocolStacks.size(); i2++) {
                s = s + ((String) this.protocolStacks.elementAt(i2));
            }
        }
        if (this.SID != null || this.server != null || this.serviceName != null || this.instanceName != null || this.handlerName != null || this.extraConnInfo.size() != 0 || this.oracleHome != null) {
            String s2 = s + NavSchemaObject.CD;
            if (this.SID != null) {
                s2 = s2 + "(SID=" + this.SID + ")";
            }
            if (this.server != null) {
                s2 = s2 + "(SERVER=" + this.server + ")";
            }
            if (this.serviceName != null) {
                s2 = s2 + "(SERVICE_NAME=" + this.serviceName + ")";
            }
            if (this.instanceName != null) {
                s2 = s2 + "(INSTANCE_NAME=" + this.instanceName + ")";
            }
            if (this.colocationTag != null) {
                s2 = s2 + "(COLOCATION_TAG=" + this.colocationTag + ")";
            }
            if (this.handlerName != null) {
                s2 = s2 + "(HANDLER_NAME=" + this.handlerName + ")";
            }
            if (this.oracleHome != null) {
                s2 = s2 + "(ORACLE_HOME=" + this.oracleHome + ")";
            }
            if (this.instanceRole != null) {
                s2 = s2 + "(INSTANCE_ROLE=" + this.instanceRole + ")";
            }
            if (this.failoverMode != null) {
                s2 = s2 + this.failoverMode;
            }
            for (int i3 = 0; i3 < this.extraConnInfo.size(); i3++) {
                s2 = s2 + "(" + ((String) this.extraConnInfo.elementAt(i3)) + ")";
            }
            s = s2 + ")";
        }
        if (this.authTypes != null || this.authParams.size() != 0) {
            String s3 = s + "(SECURITY=";
            if (this.authTypes != null) {
                s3 = s3 + "(AUTHENTICATION=" + this.authTypes + ")";
            }
            for (int i4 = 0; i4 < this.authParams.size(); i4++) {
                s3 = s3 + ((String) this.authParams.elementAt(i4));
            }
            s = s3 + ")";
        }
        for (int i5 = 0; i5 < this.extraInfo.size(); i5++) {
            s = s + "(" + ((String) this.extraInfo.elementAt(i5)) + ")";
        }
        if (!s.equals("")) {
            s = "(DESCRIPTION=" + s + ")";
        }
        return s;
    }

    protected void init() {
        this.children.removeAllElements();
        this.child = null;
        this.childnv = null;
        this.sourceRoute = false;
        this.loadBalance = false;
        this.failover = true;
        this.keepAlive = false;
        this.protocolStacks.removeAllElements();
        this.SDU = null;
        this.TDU = null;
        this.SID = null;
        this.httpsProxy = null;
        this.httpsProxyPort = null;
        this.server = null;
        this.serviceName = null;
        this.instanceName = null;
        this.handlerName = null;
        this.oracleHome = null;
        this.authTypes = null;
        this.sendBufSize = null;
        this.receiveBufSize = null;
        this.failoverMode = null;
        this.instanceRole = null;
        this.authParams.removeAllElements();
        this.extraConnInfo.removeAllElements();
        this.extraInfo.removeAllElements();
    }
}
