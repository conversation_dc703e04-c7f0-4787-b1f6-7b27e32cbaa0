package oracle.net.resolver;

import java.util.HashMap;
import oracle.jdbc.internal.Monitor;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NameResolverFactory.class */
public class NameResolverFactory {
    private static final String TNS_ADMIN_PROPERTY = "oracle.net.tns_admin";
    private static final boolean DEBUG = false;
    private static HashMap resolverMap = new HashMap();
    private static Monitor RESOLVER_MAP_MONITOR = Monitor.newInstance();

    public static NameResolver getNameResolver(String tnsAdmin, String osuser, String programName) throws NetException {
        if (tnsAdmin != null) {
            tnsAdmin = tnsAdmin.trim();
        }
        if (tnsAdmin != null && tnsAdmin.length() == 0) {
            throw new NetException(NetException.TNS_ADMIN_EMPTY);
        }
        Monitor.CloseableLock lock = RESOLVER_MAP_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NameResolver nr = (NameResolver) resolverMap.get(tnsAdmin);
                if (nr == null) {
                    nr = new NameResolver(tnsAdmin, osuser, programName);
                    resolverMap.put(tnsAdmin, nr);
                }
                NameResolver nameResolver = nr;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return nameResolver;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }
}
