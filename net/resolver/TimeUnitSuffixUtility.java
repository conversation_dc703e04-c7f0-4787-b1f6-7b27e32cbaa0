package oracle.net.resolver;

import java.util.Optional;
import java.util.stream.Stream;

/* loaded from: ojdbc8.jar:oracle/net/resolver/TimeUnitSuffixUtility.class */
public class TimeUnitSuffixUtility {
    private static int getTimeInMilliseconds(String value) {
        Optional<TimeUnit> suffix = Stream.of((Object[]) TimeUnit.values()).filter(p -> {
            return value.toUpperCase().endsWith(p.name());
        }).findAny();
        if (suffix.isPresent()) {
            return Integer.parseInt(value.toUpperCase().replaceFirst(suffix.get().name(), "").trim()) * suffix.get().getMultiplier();
        }
        return Integer.parseInt(value);
    }

    public static int getTimeInMilliseconds(String value, boolean valueInSeconds) {
        if (value.isEmpty() || !value.matches("\\d*")) {
            return getTimeInMilliseconds(value);
        }
        return valueInSeconds ? Integer.parseInt(value) * 1000 : Integer.parseInt(value);
    }

    public static int getTimeInMilliseconds(String value, boolean valueInSeconds, int defaultValue) {
        try {
            return getTimeInMilliseconds(value, valueInSeconds);
        } catch (NumberFormatException e) {
            return valueInSeconds ? defaultValue * 1000 : defaultValue;
        }
    }
}
