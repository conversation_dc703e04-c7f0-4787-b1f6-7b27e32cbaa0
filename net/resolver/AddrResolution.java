package oracle.net.resolver;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.Stack;
import java.util.Vector;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.net.ssl.SSLContext;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.replay.OracleDataSource;
import oracle.net.jdbc.TNSAddress.SOException;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.jndi.JndiAttrs;
import oracle.net.ns.NetException;
import oracle.net.nt.AsyncOutboundTimeoutHandler;
import oracle.net.nt.ConnOption;
import oracle.net.nt.ConnStrategy;
import oracle.net.nt.ConnectDescription;

/* loaded from: ojdbc8.jar:oracle/net/resolver/AddrResolution.class */
public class AddrResolution implements Diagnosable {
    static final String ADDRESS_FORMAT = "(ADDRESS=(PROTOCOL=%s)(HOST=%s)(PORT=%s))";
    static final String CID_FORMAT = "(CID=(PROGRAM=%s)(HOST=%s)(USER=%s))";
    static final String CONNECT_DATA_FORMAT = "(CONNECT_DATA=%s%s%s)";
    static final String DESCRIPTION_FORMAT = "(DESCRIPTION=%s%s)";
    static final String SERVERMODE_FORMAT = "(SERVER=%s)";
    static final String EMPTY_STRING = "";
    static final String SID_KEY = "(SID=%s)";
    static final String SERVICE_NAME_KEY = "(SERVICE_NAME=%s)";
    private ConnStrategy connStrategy;
    private Stack<ConnStrategy> connStrategyStack;
    private static final int MAX_CONNECTION_STRATEGY_COUNT = 20;
    private Properties up;
    private static final String default_proxy_rules = "__jdbc__";
    private static final String service_alias_name = "ora-net-service-alias";
    private static final String service_attr_name = "orclnetdescstring";
    private static final int length_of_alias_prefix = 6;
    public static final int DEFAULT_DATABASE_PORT = 1521;
    public static final String DEFAULT_CONNECT_PROTOCOL = "TCP";
    private boolean newSyntax;
    public boolean connection_revised;
    public boolean connection_redirected;
    private String TNSAddress;
    private final SSLContext sslContext;
    private final OracleHostnameResolver hostnameResolver;
    private final Diagnosable diagnosable;
    private final TraceEventListener traceEventListener;
    public String redirectConnectData;
    private final List<ConnectDescription> resolvedDescriptions;
    private ConnectDescription connectedDescription;
    private DriverResources driverResources;
    private static final String CLASS_NAME = AddrResolution.class.getName();
    static final List<String> SERVER_MODES = Arrays.asList("SHARED", "DEDICATED", "POOLED");
    private static final Pattern HOST_INFO_PATTERN = Pattern.compile("(?<hostnames>([A-z0-9][A-z0-9._-]+,?)+)(:(?<port>\\d+))?");
    private static final Pattern EZ_URL_PATTERN = Pattern.compile("(.*)@(//)?(?<hostinfo>(" + HOST_INFO_PATTERN.pattern() + ")+)(/(?<servicename>[A-z][A-z0-9,-.]+))?(:(?<servermode>dedicated|shared|pooled))?(/(?<instance>[A-z][A-z0-9]+))?", 2);
    private static final Pattern TNS_NAME_URL_PATTERN = Pattern.compile("(.*)@(?<tnsalias>[A-z][A-z0-9,-.]+)");
    private static final Pattern pattern = Pattern.compile("(?=ldaps?://)");

    public AddrResolution(String TNSdesc, @Blind(PropertiesBlinder.class) Properties _up) throws NetException {
        this(TNSdesc, _up, null, null, null, null);
    }

    public AddrResolution(String TNSdesc, @Blind(PropertiesBlinder.class) Properties _up, SSLContext sslContext, OracleHostnameResolver hostnameResolver, Diagnosable diagnosable, TraceEventListener traceEventListener) throws NetException {
        this.connStrategy = null;
        this.connStrategyStack = new Stack<>();
        this.newSyntax = true;
        this.connection_revised = false;
        this.connection_redirected = false;
        this.up = _up;
        this.TNSAddress = TNSdesc;
        this.diagnosable = diagnosable;
        if (TNSdesc.startsWith("ldap:") || TNSdesc.startsWith("ldaps:")) {
            boolean isLdapUrlList = false;
            Vector<String> ldapUrlList = computeLdapList(TNSdesc);
            if (ldapUrlList.size() > 1) {
                isLdapUrlList = true;
            } else {
                int pos = TNSdesc.lastIndexOf(47);
                if (pos == -1) {
                    throw new NetException(NetException.INVALID_LDAP_URL);
                }
                this.up.put("java.naming.provider.url", TNSdesc.substring(0, pos));
                this.TNSAddress = TNSdesc.substring(pos + 1, TNSdesc.length());
            }
            if (!isLdapUrlList) {
                JndiAttrs ja = new JndiAttrs(this.up);
                String[] attrname = {service_attr_name};
                try {
                    Vector _service = ja.getAttrs(this.TNSAddress, attrname);
                    this.TNSAddress = (String) _service.firstElement();
                    this.connection_revised = true;
                    try {
                        ja.close();
                    } catch (NetException e) {
                    }
                } catch (NetException e2) {
                    try {
                        ja.close();
                    } catch (NetException e3) {
                    }
                } catch (Throwable th) {
                    try {
                        ja.close();
                    } catch (NetException e4) {
                    }
                    throw th;
                }
            } else {
                processLdapFailoverLoadblance(ldapUrlList);
            }
        }
        String prop = this.up.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_OLDSYNTAX);
        if (prop != null && (prop.equalsIgnoreCase("ON") || prop.equalsIgnoreCase("TRUE") || prop.equalsIgnoreCase("YES"))) {
            this.newSyntax = false;
        }
        this.sslContext = sslContext;
        this.hostnameResolver = hostnameResolver;
        this.traceEventListener = traceEventListener;
        initConnStrategy(this.connection_revised ? getTNSAddressInUpperCase() : this.TNSAddress);
        this.resolvedDescriptions = Collections.unmodifiableList(new ArrayList(this.connStrategy.getAllDescriptions()));
    }

    private Vector<String> computeLdapList(String tnsDesc) {
        String[] ldapUrlS = pattern.split(tnsDesc);
        int nonEmptyLdapUrlsCount = 0;
        for (int i = 0; i < ldapUrlS.length; i++) {
            ldapUrlS[i] = ldapUrlS[i].trim();
            if (ldapUrlS[i].length() != 0) {
                nonEmptyLdapUrlsCount++;
            }
        }
        Vector<String> ret = new Vector<>(nonEmptyLdapUrlsCount);
        for (int i2 = 0; i2 < ldapUrlS.length; i2++) {
            if (ldapUrlS[i2].length() != 0) {
                ret.add(ldapUrlS[i2]);
            }
        }
        return ret;
    }

    private void processLdapFailoverLoadblance(Vector<String> urlList) throws NetException {
        if (urlList.size() <= 1) {
            throw new NetException(NetException.INVALID_LDAP_URL);
        }
        boolean ldapFailover = true;
        boolean ldapLoadbalance = true;
        String prop = this.up.getProperty("oracle.net.ldap_failover");
        if (prop != null && (prop.equalsIgnoreCase("OFF") || prop.equalsIgnoreCase("FALSE") || prop.equalsIgnoreCase("NO"))) {
            ldapFailover = false;
        }
        String prop2 = this.up.getProperty("oracle.net.ldap_loadbalance");
        if (prop2 != null && (prop2.equalsIgnoreCase("OFF") || prop2.equalsIgnoreCase("FALSE") || prop2.equalsIgnoreCase("NO"))) {
            ldapLoadbalance = false;
        }
        if (urlList.size() > 1) {
            urlList = NavDescriptionList.setActiveChildren(urlList, ldapFailover, ldapLoadbalance);
        }
        StringBuilder sbJndiUrlList = new StringBuilder();
        int listSize = urlList.size();
        Hashtable tabMapUrlToTns = new Hashtable(listSize);
        for (int i = 0; i < listSize; i++) {
            String url = urlList.elementAt(i);
            int pos = url.lastIndexOf(47);
            if (pos == -1) {
                throw new NetException(NetException.INVALID_LDAP_URL);
            }
            String jndiUrl = url.substring(0, pos);
            String tnsAddr = url.substring(pos + 1, url.length());
            sbJndiUrlList.append(jndiUrl);
            if (i < listSize - 1) {
                sbJndiUrlList.append(' ');
            }
            tabMapUrlToTns.put(jndiUrl.substring(jndiUrl.indexOf(47)), tnsAddr);
        }
        String jndiUrlList = new String(sbJndiUrlList);
        this.up.put("java.naming.provider.url", jndiUrlList);
        JndiAttrs ja = new JndiAttrs(this.up);
        String ldapUlrConnected = ja.getLdapUrlUsed();
        this.TNSAddress = (String) tabMapUrlToTns.get(ldapUlrConnected.substring(ldapUlrConnected.indexOf(47)));
        String[] attrNames = {service_attr_name};
        try {
            Vector serviceList = ja.getAttrs(this.TNSAddress, attrNames);
            ja.close();
            this.TNSAddress = (String) serviceList.firstElement();
            this.connection_revised = true;
        } catch (Throwable th) {
            ja.close();
            throw th;
        }
    }

    public String getTNSAddressInUpperCase() {
        return this.TNSAddress.toUpperCase();
    }

    public String getTNSAddress() {
        return this.TNSAddress;
    }

    private void initConnStrategy(String TNSdesc) throws NetException {
        ConnStrategy orig_cs = this.connStrategy;
        this.connStrategy = new ConnStrategy(this.up, this.sslContext, this.hostnameResolver, this.diagnosable, this.traceEventListener);
        this.connStrategy.setDriverResources(this.driverResources);
        if (this.connection_redirected) {
            configureConnStrategyAfterRedirect(orig_cs);
        }
        if (orig_cs != null) {
            this.connStrategyStack.push(orig_cs);
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "initConnStrategy", "Connection strategy {0} stored in stack, conn strategy updated to new conn strategy: {1}", null, null, orig_cs.toString(), this.connStrategy.toString());
        }
        resolveTNSAddress(TNSdesc);
    }

    public ConnOption resolveAndExecute(String TNSdesc, DMSFactory.DMSNoun dmsParent) throws IOException {
        return resolveAndExecute(TNSdesc, false, dmsParent);
    }

    public ConnOption resolveAndExecute(String descriptor, boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent) throws InterruptedException, IOException {
        if (descriptor != null && this.connStrategyStack.size() < 20) {
            initConnStrategy(descriptor);
        } else if (this.connStrategy == null) {
            return null;
        }
        ConnOption cOption = null;
        do {
            try {
                cOption = this.connStrategy.execute(startNewOCTOInterruptTask, dmsParent);
                updateConnectedDescription();
            } catch (NetException e) {
                if (this.connection_redirected && !this.connStrategyStack.empty()) {
                    debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "resolveAndExecute", "Connection establishment with conn strategy: {0} failed, trying previous conn strategy from stack", (String) null, (String) e, (Object) this.connStrategy.toString());
                    this.connStrategy = this.connStrategyStack.pop();
                } else {
                    throw e;
                }
            }
        } while (cOption == null);
        return cOption;
    }

    public final CompletionStage<ConnOption> resolveAndExecuteAsync(String TNSdesc, boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        if (TNSdesc != null && this.connStrategyStack.size() < 20) {
            try {
                initConnStrategy(TNSdesc);
            } catch (NetException resolutionFailure) {
                return CompletionStageUtil.failedStage(resolutionFailure);
            }
        } else if (this.connStrategy == null) {
            return CompletionStageUtil.completedStage(null);
        }
        return executeStackAsync(startNewOCTOInterruptTask, dmsParent, outboundTimeoutHandler, asyncExecutor);
    }

    private final CompletionStage<ConnOption> executeStackAsync(boolean startNewOCTOInterruptTask, DMSFactory.DMSNoun dmsParent, AsyncOutboundTimeoutHandler outboundTimeoutHandler, Executor asyncExecutor) {
        return this.connStrategy.executeAsync(startNewOCTOInterruptTask, dmsParent, outboundTimeoutHandler, asyncExecutor).handle((cOption, throwable) -> {
            Throwable throwable = CompletionStageUtil.unwrapCompletionException(throwable);
            if ((throwable instanceof NetException) && this.connection_redirected && !this.connStrategyStack.isEmpty()) {
                debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "executeStackAsync", "Connection establishment with conn strategy: {0} failed, trying previous conn strategy from stack", (String) null, (String) throwable, (Object) this.connStrategy.toString());
                this.connStrategy = this.connStrategyStack.pop();
                return executeStackAsync(startNewOCTOInterruptTask, dmsParent, outboundTimeoutHandler, asyncExecutor);
            }
            if (throwable != null) {
                return CompletionStageUtil.failedStage(throwable);
            }
            updateConnectedDescription();
            return CompletionStageUtil.completedStage(cOption);
        }).thenCompose(Function.identity());
    }

    private void updateConnectedDescription() {
        if (!this.connection_redirected) {
            this.connectedDescription = this.connStrategy.getConnectedDescription();
        }
    }

    private final void configureConnStrategyAfterRedirect(ConnStrategy redirectedConnStrategy) {
        this.connStrategy.sdu = redirectedConnStrategy.sdu;
        this.connStrategy.tdu = redirectedConnStrategy.tdu;
        this.connStrategy.retryCount = redirectedConnStrategy.retryCount;
        this.connStrategy.socketOptions = redirectedConnStrategy.socketOptions;
        this.connStrategy.reuseOpt = true;
    }

    private final void resolveTNSAddress(String TNSdesc) throws NetException {
        String TNSdesc2 = TNSdesc.trim();
        if (TNSdesc2.indexOf(41) == -1 || TNSdesc2.indexOf(40) != 0) {
            this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, getClass().getName(), "resolveTNSAddress", "TNSdesc = {0}", (String) null, (String) null, TNSdesc2);
            if (TNSdesc2.startsWith("//") || ((TNSdesc2.matches("[\\w[.+*?!/;%@a~#'$&:|^()<>\\-\\\\\\\"]]*") && !TNSdesc2.matches("[[\\w-]\\.]*:[\\d]*:[+]?+[[\\w\\$\\#-]\\.]*(?i)(:[\\w]*)?(?-i)")) || TNSdesc2.matches("[\\[[\\w:]*\\]]") || TNSdesc2.matches("[[\\w-]\\.]*:[\\d]*/[[\\w\\$\\#]\\.]*(?i)(:[\\w]*)?(?-i)"))) {
                this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, getClass().getName(), "resolveTNSAddress", "resolveName() ...", null, null);
                String tnsAdmin = this.up.getProperty(OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN);
                NameResolver nr = NameResolverFactory.getNameResolver(tnsAdmin, this.connStrategy.getOSUsername(), this.connStrategy.getProgramName());
                this.TNSAddress = nr.resolveName(TNSdesc2.replaceAll("#", "\\\\#").replaceAll("\\(", "\\\\(").replaceAll("\\)", "\\\\)"));
                if (this.TNSAddress.indexOf(41) == -1 || this.TNSAddress.indexOf(40) != 0) {
                    EZConnectResolver resolver = EZConnectResolver.newInstance(this.TNSAddress);
                    this.TNSAddress = resolver.getResolvedUrl();
                }
            } else {
                this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, getClass().getName(), "resolveTNSAddress", "calling resolveSimple() ", null, null);
                this.TNSAddress = resolveSimple(TNSdesc2);
            }
            this.TNSAddress = addConnectionProperties(this.TNSAddress);
            this.diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, getClass().getName(), "resolveTNSAddress", "TNSAddress = {0}", (String) null, (String) null, this.TNSAddress);
            resolveAddrTree(this.TNSAddress);
            return;
        }
        resolveAddrTree(addConnectionProperties(TNSdesc2));
    }

    private String addConnectionProperties(String TNSdesc) {
        String newTNSdesc = TNSdesc;
        if (this.up.containsKey("oracle.jdbc.targetInstanceName")) {
            newTNSdesc = appendInstanceName(TNSdesc, this.up.getProperty("oracle.jdbc.targetInstanceName"));
        }
        if (this.up.containsKey("oracle.jdbc.targetServiceName")) {
            newTNSdesc = replaceServiceName(newTNSdesc, this.up.getProperty("oracle.jdbc.targetServiceName"));
        }
        if (this.up.containsKey("oracle.jdbc.targetShardingKey")) {
            String superShardingKey = null;
            if (this.up.containsKey("oracle.jdbc.targetSuperShardingKey")) {
                superShardingKey = this.up.getProperty("oracle.jdbc.targetSuperShardingKey");
            }
            newTNSdesc = appendShardKeys(newTNSdesc, this.up.getProperty("oracle.jdbc.targetShardingKey"), superShardingKey);
        }
        if (this.up.containsKey("oracle.jdbc.readOnlyInstanceAllowed")) {
            String isAllowReadOnly = this.up.getProperty("oracle.jdbc.readOnlyInstanceAllowed");
            if ("true".equalsIgnoreCase(isAllowReadOnly)) {
                newTNSdesc = appendReadOnly(newTNSdesc);
            }
        }
        return newTNSdesc;
    }

    private String resolveSimple(String TNSdesc) throws NetException {
        int sColon;
        ConnOption co = new ConnOption();
        int initialIndex = 0;
        boolean isIPV6Literal = false;
        boolean isServerModePresent = false;
        String serverMode = "";
        if (TNSdesc.startsWith("[")) {
            initialIndex = TNSdesc.indexOf(93);
            if (initialIndex == -1) {
                throw new NetException(NetException.HOST_PORT_SID_EXPECTED);
            }
            isIPV6Literal = true;
        }
        int fColon = TNSdesc.indexOf(58, initialIndex);
        if (fColon == -1 || (sColon = TNSdesc.indexOf(58, fColon + 1)) == -1) {
            throw new NetException(NetException.HOST_PORT_SID_EXPECTED);
        }
        int eColon = TNSdesc.indexOf(58, sColon + 1);
        if (eColon != -1 && TNSdesc.length() > eColon + 6) {
            serverMode = TNSdesc.substring(eColon + 1);
            isServerModePresent = SERVER_MODES.contains(serverMode.toUpperCase());
            if (isServerModePresent) {
                co.sid = TNSdesc.substring(sColon + 1, eColon);
                eColon = TNSdesc.indexOf(58, eColon + 1);
            } else {
                co.sid = TNSdesc.substring(sColon + 1);
            }
        } else {
            co.sid = TNSdesc.substring(sColon + 1);
        }
        if (eColon != -1) {
            throw new NetException(NetException.HOST_PORT_SID_EXPECTED);
        }
        try {
            if (isIPV6Literal) {
                co.host = TNSdesc.substring(1, fColon - 1);
            } else {
                co.host = TNSdesc.substring(0, fColon);
            }
            co.port = Integer.parseInt(TNSdesc.substring(fColon + 1, sColon));
            co.addr = String.format(ADDRESS_FORMAT, "TCP", co.host, Integer.valueOf(co.port));
            StringBuilder sb = co.conn_data;
            Object[] objArr = new Object[2];
            Object[] objArr2 = new Object[3];
            objArr2[0] = String.format(SID_KEY, co.sid);
            objArr2[1] = isServerModePresent ? String.format(SERVERMODE_FORMAT, serverMode) : "";
            objArr2[2] = String.format(CID_FORMAT, this.connStrategy.getProgramName(), this.connStrategy.getHostname(), this.connStrategy.getOSUsername());
            objArr[0] = String.format(CONNECT_DATA_FORMAT, objArr2);
            objArr[1] = co.addr;
            sb.append(String.format(DESCRIPTION_FORMAT, objArr));
            co.protocol = "TCP";
            Object[] objArr3 = new Object[2];
            Object[] objArr4 = new Object[3];
            objArr4[0] = String.format(SID_KEY, co.sid);
            objArr4[1] = isServerModePresent ? String.format(SERVERMODE_FORMAT, serverMode) : "";
            objArr4[2] = "";
            objArr3[0] = String.format(CONNECT_DATA_FORMAT, objArr4);
            objArr3[1] = co.addr;
            String descriptionString = String.format(DESCRIPTION_FORMAT, objArr3);
            return descriptionString;
        } catch (NumberFormatException e) {
            throw new NetException(NetException.PORT_NUMBER_ERROR);
        }
    }

    private void resolveAddrTree(String TNSdesc) throws NetException {
        NavSchemaObjectFactory fac = new NavSchemaObjectFactory();
        NavServiceAlias sa = (NavServiceAlias) fac.create(4);
        try {
            String alias = TNSdesc.startsWith("alias=") ? TNSdesc : "alias=" + TNSdesc;
            sa.initFromString(alias);
            sa.navigate(this.connStrategy, null);
            boolean throwStashedException = true;
            NetException stashedException = null;
            for (int i = 0; i < this.connStrategy.getAllDescriptions().size(); i++) {
                ConnectDescription desc = this.connStrategy.getAllDescriptions().get(i);
                try {
                    resolveConnOptions(desc.getConnectOptions());
                    throwStashedException = false;
                } catch (NetException e) {
                    debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveAddrTree", "Failed to create conn options for DESCRIPTION = {0}", (String) null, (String) null, desc.toString());
                    stashedException = e;
                }
            }
            if (throwStashedException && stashedException != null) {
                throw ((NetException) stashedException.fillInStackTrace());
            }
        } catch (SOException soe) {
            throw new NetException(NetException.SO_EXCEPTION, soe.getMessage());
        } catch (NLException nle) {
            throw new NetException(NetException.NL_EXCEPTION, nle.getMessage());
        }
    }

    private void resolveConnOptions(ArrayList<ConnOption> cOpts) throws NetException {
        NetException stashedException = null;
        int initialSize = cOpts.size();
        for (int i = 0; i < initialSize; i++) {
            ConnOption cOption = cOpts.get(i);
            cOption.redirectedConnection(this.connection_redirected);
            if (this.connection_redirected && this.redirectConnectData != null) {
                cOption.conn_data = new StringBuilder(this.redirectConnectData);
            }
            try {
                Iterator<InetSocketAddress> resolvedSocketAddresses = InetAddressResolver.resolveInetSocketAddresses(cOption.host, cOption.port, this.hostnameResolver, this.connStrategy.socketOptions, cOption.protocol, cOption.addr, cOption.httpsProxy);
                while (resolvedSocketAddresses.hasNext()) {
                    ConnOption connOption = cOpts.get(i).m719clone();
                    connOption.host = connOption.host == null ? InetAddress.getLoopbackAddress().getHostName() : connOption.host;
                    connOption.inetSocketAddress = resolvedSocketAddresses.next();
                    connOption.formatConnectData();
                    debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "Connect data after formatting: {0}", (String) null, (String) null, connOption.conn_data);
                    if (connOption.protocol.equalsIgnoreCase("beq")) {
                        debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "New conn option created with bequeath protocol", null, null);
                    } else if (connOption.inetSocketAddress.getAddress() != null) {
                        debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "New conn option created using IP={0} from host={1} after hostname resolution", null, null, connOption.inetSocketAddress.getAddress().getHostAddress(), connOption.host);
                    } else {
                        debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "New conn option created with host={0}", (String) null, (String) null, connOption.host);
                    }
                    cOpts.add(connOption);
                }
            } catch (UnknownHostException e) {
                stashedException = new NetException(NetException.UNKNOWN_HOST, e.getMessage(), e);
                debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "Hostname resolution for ADDRESS with host={0} failed with error={1}", null, null, cOpts.get(i).host, e);
            } catch (NetException e2) {
                stashedException = e2;
                debug(Level.FINEST, SecurityLabel.INTERNAL, getClass().getName(), "resolveConnOptions", "Hostname resolution for ADDRESS with host={0} failed with error={1}", null, null, cOpts.get(i).host, e2);
            }
        }
        if (cOpts.size() == initialSize && stashedException != null) {
            throw stashedException;
        }
        for (int i2 = 0; i2 < initialSize; i2++) {
            cOpts.remove(0);
        }
    }

    private StringBuilder insertCID(String TNSaddr) throws NetException {
        NVFactory nvf = new NVFactory();
        NVNavigator nvn = new NVNavigator();
        StringBuilder tns = new StringBuilder(2048);
        try {
            nvn.findNVPairRecurse(nvf.createNVPair(TNSaddr), OracleDataSource.DESCRIPTION);
            NVPair addrl = nvn.findNVPairRecurse(nvf.createNVPair(TNSaddr), "address_list");
            NVPair addr = nvn.findNVPairRecurse(nvf.createNVPair(TNSaddr), "address");
            NVPair cdata = nvn.findNVPairRecurse(nvf.createNVPair(TNSaddr), "connect_data");
            NVPair sroute = nvn.findNVPairRecurse(nvf.createNVPair(TNSaddr), "source_route");
            if (cdata != null) {
                NVPair sid = nvn.findNVPair(cdata, "SID");
                nvn.findNVPair(cdata, "CID");
                NVPair sn = nvn.findNVPair(cdata, "SERVICE_NAME");
                NVPair server = nvn.findNVPair(cdata, "SERVER");
                if (sid == null && sn == null) {
                    throw new NetException(NetException.SID_INFORMATION_MISSING);
                }
                tns.append("(DESCRIPTION=");
                if (addrl != null && addrl.getListSize() > 0) {
                    for (int i = 0; i < addrl.getListSize(); i++) {
                        NVPair nvp = addrl.getListElement(i);
                        tns.append(nvp.toString());
                    }
                } else if (addr != null) {
                    tns.append(addr.toString());
                } else {
                    throw new NetException(NetException.ADDRESS_NOT_DEFINED);
                }
                Object[] objArr = new Object[3];
                objArr[0] = (sn != null ? sn : sid).toString();
                objArr[1] = server != null ? server.toString() : "";
                objArr[2] = String.format(CID_FORMAT, this.connStrategy.getProgramName(), this.connStrategy.getHostname(), this.connStrategy.getOSUsername());
                tns.append(String.format(CONNECT_DATA_FORMAT, objArr));
                if (sroute != null) {
                    tns.append(sroute.toString());
                }
                tns.append(")");
                return tns;
            }
            throw new NetException(NetException.CONNECT_DATA_MISSING);
        } catch (NLException exp) {
            NetException ne = new NetException(NetException.CSTRING_PARSING);
            ne.initCause(exp);
            throw ne;
        }
    }

    @Blind(PropertiesBlinder.class)
    public Properties getUp() {
        return this.up;
    }

    @Blind(PropertiesBlinder.class)
    public Properties getSocketOptions() {
        return this.connStrategy.socketOptions;
    }

    public boolean isConnectionSocketKeepAlive() throws SocketException {
        return this.connStrategy.isConnectionSocketKeepAlive();
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0021 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private java.lang.String appendInstanceName(java.lang.String r6, java.lang.String r7) {
        /*
            r5 = this;
            java.lang.String r0 = "CONNECT_DATA[\\s]*=([^)]+)\\)"
            r1 = 2
            java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r1)
            r8 = r0
            r0 = r8
            r1 = r6
            java.util.regex.Matcher r0 = r0.matcher(r1)
            r9 = r0
            java.lang.StringBuffer r0 = new java.lang.StringBuffer
            r1 = r0
            r1.<init>()
            r10 = r0
            r0 = r9
            boolean r0 = r0.find()
            r11 = r0
        L1f:
            r0 = r11
            if (r0 == 0) goto L5b
            r0 = r9
            r1 = r10
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r3 = r2
            r3.<init>()
            r3 = r9
            java.lang.String r3 = r3.group()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r3 = "(INSTANCE_NAME="
            java.lang.StringBuilder r2 = r2.append(r3)
            r3 = r7
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r3 = ")"
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            java.lang.String r2 = java.util.regex.Matcher.quoteReplacement(r2)
            java.util.regex.Matcher r0 = r0.appendReplacement(r1, r2)
            r0 = r9
            boolean r0 = r0.find()
            r11 = r0
            goto L1f
        L5b:
            r0 = r9
            r1 = r10
            java.lang.StringBuffer r0 = r0.appendTail(r1)
            r0 = r10
            java.lang.String r0 = r0.toString()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.resolver.AddrResolution.appendInstanceName(java.lang.String, java.lang.String):java.lang.String");
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x001f */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static java.lang.String replaceServiceName(java.lang.String r5, java.lang.String r6) {
        /*
            java.lang.String r0 = "\\([\\s]*SERVICE_NAME[\\s]*=([^)]+)\\)"
            r1 = 2
            java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r1)
            r7 = r0
            r0 = r7
            r1 = r5
            java.util.regex.Matcher r0 = r0.matcher(r1)
            r8 = r0
            java.lang.StringBuffer r0 = new java.lang.StringBuffer
            r1 = r0
            r1.<init>()
            r9 = r0
            r0 = r8
            boolean r0 = r0.find()
            r10 = r0
        L1d:
            r0 = r10
            if (r0 == 0) goto L4f
            r0 = r8
            r1 = r9
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r3 = r2
            r3.<init>()
            java.lang.String r3 = "(SERVICE_NAME="
            java.lang.StringBuilder r2 = r2.append(r3)
            r3 = r6
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r3 = ")"
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            java.lang.String r2 = java.util.regex.Matcher.quoteReplacement(r2)
            java.util.regex.Matcher r0 = r0.appendReplacement(r1, r2)
            r0 = r8
            boolean r0 = r0.find()
            r10 = r0
            goto L1d
        L4f:
            r0 = r8
            r1 = r9
            java.lang.StringBuffer r0 = r0.appendTail(r1)
            r0 = r9
            java.lang.String r0 = r0.toString()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.resolver.AddrResolution.replaceServiceName(java.lang.String, java.lang.String):java.lang.String");
    }

    public static String replaceServiceNameInUrl(String url, String gsmServiceName, String catalogServiceName) {
        if (url.toUpperCase().contains("DESCRIPTION")) {
            return replaceServiceName(url, catalogServiceName);
        }
        String urlWithoutWhiteSpaces = url.replaceAll("\\s+", "");
        Matcher matcher = EZ_URL_PATTERN.matcher(urlWithoutWhiteSpaces);
        if (matcher.find()) {
            StringBuffer sb = new StringBuffer();
            int startIndex = matcher.start("servicename");
            int endIndex = matcher.end("servicename");
            sb.append(url.substring(0, startIndex));
            sb.append(catalogServiceName);
            if (endIndex != -1) {
                sb.append(url.substring(endIndex));
            }
            return sb.toString();
        }
        return url;
    }

    public static String getServiceName(String url) {
        if (url.toUpperCase().contains("DESCRIPTION")) {
            Pattern pattern2 = Pattern.compile("(.*)\\([\\s]*SERVICE_NAME[\\s]*=([^)]+)\\)", 2);
            Matcher matcher = pattern2.matcher(url);
            boolean result = matcher.find();
            if (result) {
                return matcher.group(2);
            }
            return null;
        }
        String urlWithoutWhiteSpaces = url.replaceAll("\\s+", "");
        Matcher matcher2 = EZ_URL_PATTERN.matcher(urlWithoutWhiteSpaces);
        if (matcher2.matches()) {
            return matcher2.group("servicename");
        }
        return null;
    }

    public static String resolveTNSAlias(String url, @Blind(PropertiesBlinder.class) Properties up, String tnsAdmin) {
        String aliasName;
        String urlWithoutWhiteSpaces = url.replaceAll("\\s+", "");
        String newUrl = null;
        Matcher matcher = TNS_NAME_URL_PATTERN.matcher(urlWithoutWhiteSpaces);
        if (matcher.matches() && (aliasName = matcher.group("tnsalias")) != null && tnsAdmin != null) {
            try {
                String osuser = up.getProperty("oracle.jdbc.v$session.osuser");
                String programName = up.getProperty("oracle.jdbc.v$session.program");
                NameResolver nr = NameResolverFactory.getNameResolver(tnsAdmin, osuser, programName);
                String aliasVal = nr.resolveName(aliasName.trim());
                int startIndex = matcher.start("tnsalias");
                if (startIndex != -1) {
                    StringBuffer sb = new StringBuffer();
                    sb.append(url.substring(0, startIndex));
                    sb.append(aliasVal);
                    newUrl = sb.toString();
                }
            } catch (NetException e) {
            }
        }
        return newUrl;
    }

    /* JADX WARN: Incorrect condition in loop: B:12:0x007b */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private java.lang.String appendShardKeys(java.lang.String r6, java.lang.String r7, java.lang.String r8) {
        /*
            r5 = this;
            java.lang.String r0 = "CONNECT_DATA[\\s]*=([^)]+)\\)"
            r1 = 2
            java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r1)
            r9 = r0
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r1 = r0
            r1.<init>()
            r1 = r7
            if (r1 == 0) goto L31
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r2 = r1
            r2.<init>()
            java.lang.String r2 = "(SHARDING_KEY_B64="
            java.lang.StringBuilder r1 = r1.append(r2)
            r2 = r7
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r2 = ")"
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r1 = r1.toString()
            goto L33
        L31:
            java.lang.String r1 = ""
        L33:
            java.lang.StringBuilder r0 = r0.append(r1)
            r1 = r8
            if (r1 == 0) goto L57
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r2 = r1
            r2.<init>()
            java.lang.String r2 = "(SUPER_SHARDING_KEY_B64="
            java.lang.StringBuilder r1 = r1.append(r2)
            r2 = r8
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r2 = ")"
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r1 = r1.toString()
            goto L59
        L57:
            java.lang.String r1 = ""
        L59:
            java.lang.StringBuilder r0 = r0.append(r1)
            java.lang.String r0 = r0.toString()
            r10 = r0
            r0 = r9
            r1 = r6
            java.util.regex.Matcher r0 = r0.matcher(r1)
            r11 = r0
            java.lang.StringBuffer r0 = new java.lang.StringBuffer
            r1 = r0
            r1.<init>()
            r12 = r0
            r0 = r11
            boolean r0 = r0.find()
            r13 = r0
        L79:
            r0 = r13
            if (r0 == 0) goto Laa
            r0 = r11
            r1 = r12
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r3 = r2
            r3.<init>()
            r3 = r11
            java.lang.String r3 = r3.group()
            java.lang.StringBuilder r2 = r2.append(r3)
            r3 = r10
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            java.lang.String r2 = java.util.regex.Matcher.quoteReplacement(r2)
            java.util.regex.Matcher r0 = r0.appendReplacement(r1, r2)
            r0 = r11
            boolean r0 = r0.find()
            r13 = r0
            goto L79
        Laa:
            r0 = r11
            r1 = r12
            java.lang.StringBuffer r0 = r0.appendTail(r1)
            r0 = r12
            java.lang.String r0 = r0.toString()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.resolver.AddrResolution.appendShardKeys(java.lang.String, java.lang.String, java.lang.String):java.lang.String");
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0025 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private java.lang.String appendReadOnly(java.lang.String r6) {
        /*
            r5 = this;
            java.lang.String r0 = "CONNECT_DATA[\\s]*=([^)]+)\\)"
            r1 = 2
            java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r1)
            r7 = r0
            java.lang.String r0 = "(READONLY_OK=true)"
            r8 = r0
            r0 = r7
            r1 = r6
            java.util.regex.Matcher r0 = r0.matcher(r1)
            r9 = r0
            java.lang.StringBuffer r0 = new java.lang.StringBuffer
            r1 = r0
            r1.<init>()
            r10 = r0
            r0 = r9
            boolean r0 = r0.find()
            r11 = r0
        L23:
            r0 = r11
            if (r0 == 0) goto L55
            r0 = r9
            r1 = r10
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r3 = r2
            r3.<init>()
            r3 = r9
            java.lang.String r3 = r3.group()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r3 = "(READONLY_OK=true)"
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            java.lang.String r2 = java.util.regex.Matcher.quoteReplacement(r2)
            java.util.regex.Matcher r0 = r0.appendReplacement(r1, r2)
            r0 = r9
            boolean r0 = r0.find()
            r11 = r0
            goto L23
        L55:
            r0 = r9
            r1 = r10
            java.lang.StringBuffer r0 = r0.appendTail(r1)
            r0 = r10
            java.lang.String r0 = r0.toString()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.net.resolver.AddrResolution.appendReadOnly(java.lang.String):java.lang.String");
    }

    public ConnStrategy getConnStrategy() {
        return this.connStrategy;
    }

    public boolean isUsingCustomHostnameResolver() {
        return this.connStrategy.isUsingCustomHostnameResolver();
    }

    public void clearConnStrategyStack() {
        this.connStrategyStack.clear();
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.diagnosable;
    }

    public List<ConnectDescription> getResolvedDescriptions() {
        return this.resolvedDescriptions;
    }

    public ConnectDescription getConnectedDescription() {
        return this.connectedDescription;
    }

    public void setDriverResources(DriverResources driverResources) {
        this.driverResources = driverResources;
        if (this.connStrategy != null) {
            this.connStrategy.setDriverResources(driverResources);
        }
    }

    public String getRedirectConnectData() {
        return this.redirectConnectData;
    }

    public void setRedirectConnectData(String redirectConnectData) {
        this.redirectConnectData = redirectConnectData;
    }
}
