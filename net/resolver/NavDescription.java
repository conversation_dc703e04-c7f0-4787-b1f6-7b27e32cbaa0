package oracle.net.resolver;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Vector;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.jdbc.TNSAddress.Description;
import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnOption;
import oracle.net.nt.ConnStrategy;
import oracle.net.nt.ConnectDescription;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavDescription.class */
public class NavDescription extends Description implements NavSchemaObject {
    private Vector activeChildren;
    private int descProcessed;
    private NetException stashedException;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !NavDescription.class.desiredAssertionStatus();
    }

    public NavDescription(SchemaObjectFactoryInterface fac) {
        super(fac);
        this.stashedException = null;
        this.activeChildren = new Vector(1, 10);
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void navigate(ConnStrategy cs, StringBuffer sBuf) throws NetException {
        sBuf.setLength(0);
        sBuf.append("(DESCRIPTION=");
        ConnectDescription desc = cs.newConnectDescription();
        desc.setDescriptionString(this.descriptionString);
        if (this.SDU != null) {
            desc.setSdu(getIntValue(this.SDU, desc.getSdu()));
        }
        if (this.TDU != null) {
            desc.setTdu(getIntValue(this.TDU, desc.getTdu()));
        }
        if (this.connectTimeout != null) {
            desc.setConnectTimeout(TimeUnitSuffixUtility.getTimeInMilliseconds(this.connectTimeout, true, desc.getConnectTimeout()));
        }
        if (this.transportTimeout != null) {
            desc.setTransportConnectTimeout(TimeUnitSuffixUtility.getTimeInMilliseconds(this.transportTimeout, true, desc.getTransportConnectTimeout()));
        }
        if (this.retryCount != null) {
            cs.retryCount = getIntValue(this.retryCount, cs.retryCount);
            desc.setRetryCount(cs.retryCount);
        }
        if (this.delayInMillis != -1) {
            desc.setDelayInMillis(this.delayInMillis);
        }
        if (!this.failover) {
            sBuf.append(NavSchemaObject.NFO);
        }
        if (this.netConnectionIdPrefix != null) {
            desc.setConnectionIdPrefix(this.netConnectionIdPrefix);
        }
        if (this.checksumClient != null) {
            desc.setChecksumClient(this.checksumClient);
        }
        if (this.checksumClientTypes != null) {
            desc.setChecksumClientTypes(this.checksumClientTypes);
        }
        if (this.encryptionClient != null) {
            desc.setEncryptionClient(this.encryptionClient);
        }
        if (this.encryptionClientTypes != null) {
            desc.setEncryptionClientTypes(this.encryptionClientTypes);
        }
        if (this.allowWeakCrypto != null) {
            desc.setAllowWeakCrypto(this.allowWeakCrypto);
        }
        if (this.useSNI != null) {
            desc.useSNI(this.useSNI);
        }
        if (this.httpsProxy != null) {
            desc.setHttpsProxy(this.httpsProxy);
        }
        if (this.httpsProxyPort != null) {
            try {
                desc.setHttpsProxyPort(Integer.parseInt(this.httpsProxyPort));
            } catch (NumberFormatException e) {
                throw new NetException(NetException.PORT_NUMBER_ERROR, "Invalid https proxy port number format");
            }
        }
        cs.addSocketOptions(this.keepAlive);
        if (this.sourceRoute) {
            if (!backwardCompatibilityCase(this.children, cs)) {
                this.activeChildren = this.children;
                try {
                    ((NavSchemaObject) this.activeChildren.elementAt(0)).navigate(cs, sBuf);
                    for (int i = 1; i < this.activeChildren.size(); i++) {
                        ((NavSchemaObject) this.activeChildren.elementAt(i)).addToString(cs);
                    }
                } catch (NetException e2) {
                    this.stashedException = e2;
                    throw e2;
                }
            } else {
                setConnectionOption(cs, sBuf);
            }
            closeNVPair(cs);
        } else {
            this.activeChildren = NavDescriptionList.setActiveChildren(this.children, this.failover, this.loadBalance);
            for (int i2 = 0; i2 < this.activeChildren.size(); i2++) {
                try {
                    ((NavSchemaObject) this.activeChildren.elementAt(i2)).navigate(cs, sBuf);
                } catch (NetException e3) {
                    this.stashedException = e3;
                }
            }
            closeNVPair(cs);
        }
        if (cs.currentDescription().getConnectOptions().size() == 0) {
            sBuf.replace(sBuf.length() - "(DESCRIPTION=".length(), sBuf.length(), "");
            throw this.stashedException;
        }
        if (this.expireTime != null) {
            desc.setExpireTime(getIntValue(this.expireTime, -1));
        }
        if (this.loadBalance) {
            cs.addSocketOptions_FORCE_DNS_LOAD_BALANCING_OFF();
        }
        desc.setTokenAuthentication(this.tokenAuthentication);
        desc.setTokenLocation(this.tokenLocation);
        desc.setPasswordAuthentication(this.passwordAuthentication);
        desc.setOciIamUrl(this.ociIamUrl);
        desc.setOciTenancy(this.ociTenancy);
        desc.setOciCompartment(this.ociCompartment);
        desc.setOciDatabase(this.ociDatabase);
        desc.setOciConfigFile(this.ociConfigFile);
        desc.setOciProfile(this.ociProfile);
        desc.setAzureDbAppIdUri(this.azureDbAppIdUri);
        desc.setTenantId(this.tenantId);
        desc.setClientId(this.clientId);
        desc.setClientCertificate(this.clientCertificate);
        desc.setRedirectUri(this.redirectUri);
        desc.setAzureCredentials(this.azureCredentials);
        desc.setWalletLocation(this.myWalletDirectory);
        if (this.BEQServerProcessEnvironmentVars != null) {
            if (!$assertionsDisabled && (desc.getConnectOptions() == null || desc.getConnectOptions().size() < 1)) {
                throw new AssertionError("ConnOption not properly set for BEQ");
            }
            desc.getConnectOptions().get(0).serverProcessEnvironmentVars = getKeyValueCommaListValue(this.BEQServerProcessEnvironmentVars);
        }
        if (this.BEQServerProcessArguments != null) {
            if (!$assertionsDisabled && (desc.getConnectOptions() == null || desc.getConnectOptions().size() < 1)) {
                throw new AssertionError("ConnOption not properly set for BEQ");
            }
            desc.getConnectOptions().get(0).serverProcessArguments = this.BEQServerProcessArguments;
        }
        cs.closeDescription();
    }

    private Map<String, String> getKeyValueCommaListValue(String environmentVars) {
        Map<String, String> result = new HashMap<>();
        if (!$assertionsDisabled && environmentVars == null) {
            throw new AssertionError("env vars string cannot be null");
        }
        String[] commaSplitted = environmentVars.split(",");
        for (int i = 0; i < commaSplitted.length; i++) {
            String[] equalSplitted = commaSplitted[i].split("=");
            if (equalSplitted.length != 2) {
                throw new IllegalArgumentException(String.format("unexpected format %s", commaSplitted[i]));
            }
            result.put(equalSplitted[0], equalSplitted[1]);
        }
        return result;
    }

    public void closeNVPair(ConnStrategy cs) {
        if (cs.currentDescription() == null) {
            return;
        }
        ArrayList<ConnOption> cOpts = cs.currentDescription().getConnectOptions();
        Iterator<ConnOption> it = cOpts.iterator();
        while (it.hasNext()) {
            ConnOption connOpt = it.next();
            if (this.sourceRoute) {
                connOpt.conn_data.append(NavSchemaObject.SR);
            }
            if (this.connectData == null) {
                this.connectData = "(SERVICE_NAME=)";
            }
            connOpt.conn_data.append(NavSchemaObject.CD);
            connOpt.conn_data.append(NavSchemaObject.CID1v2);
            connOpt.conn_data.append(cs.getProgramName());
            connOpt.conn_data.append(NavSchemaObject.CID2v2);
            connOpt.conn_data.append(cs.getHostname());
            connOpt.conn_data.append(NavSchemaObject.CID3v2);
            connOpt.conn_data.append(cs.getOSUsername());
            connOpt.conn_data.append(NavSchemaObject.CID4v2);
            connOpt.conn_data.append(this.connectData);
            connOpt.conn_data.append(")");
            connOpt.sourceRoute = this.sourceRoute;
            if (this.SID != null) {
                connOpt.sid = this.SID;
            }
            if (this.serviceName != null) {
                connOpt.service_name = this.serviceName;
            }
            if (this.instanceName != null) {
                connOpt.instance_name = this.instanceName;
            }
            if (this.sslServerCertDN != null) {
                connOpt.sslServerCertDN = this.sslServerCertDN;
            }
            if (this.myWalletDirectory != null) {
                connOpt.walletDirectory = this.myWalletDirectory;
            }
            if (this.sslServerDNMatch != null) {
                connOpt.sslServerDNMatch = this.sslServerDNMatch;
            }
            if (this.sslAllowWeakDNMatch != null) {
                connOpt.sslAllowWeakDNMatch = this.sslAllowWeakDNMatch;
            }
            if (this.sslVersion != null) {
                connOpt.sslVersion = this.sslVersion;
            }
            if (this.sslCiphers != null) {
                connOpt.sslCiphers = this.sslCiphers;
            }
            if (this.sslCertAlias != null) {
                connOpt.sslCertAlias = this.sslCertAlias;
            }
            if (this.sslCertThumbprint != null) {
                connOpt.sslCertThumbprint = this.sslCertThumbprint;
            }
            if (this.useTcpFastOpen != null) {
                connOpt.useTcpFastOpen = this.useTcpFastOpen;
            }
            if (this.colocationTag != null) {
                connOpt.colocationTag = this.colocationTag;
            }
            if (this.server != null) {
                connOpt.serverMode = this.server;
            }
            connOpt.conn_data.append(")");
            connOpt.done = true;
        }
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void addToString(ConnStrategy cs) {
    }

    private int getIntValue(String stringInt, int defaultValue) {
        try {
            return Integer.parseInt(stringInt);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private boolean backwardCompatibilityCase(Vector children, ConnStrategy cs) {
        NavAddressList addrl;
        int numChildren;
        if (children.size() != 1 || ((NavSchemaObject) children.elementAt(0)).isA() != 1 || ((NavAddressList) children.elementAt(0)).sourceRoute || (numChildren = (addrl = (NavAddressList) children.elementAt(0)).getChildrenSize()) == 0) {
            return false;
        }
        for (int i = 0; i < numChildren; i++) {
            if (addrl.getChildrenType(i) != 0) {
                return false;
            }
        }
        return true;
    }

    private void setConnectionOption(ConnStrategy cs, StringBuffer sBuf) throws NetException {
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, getClass().getName(), "setConnectionOption", "Dealing with a Back.Compat. Case", null, null);
        sBuf.append("(ADDRESS_LIST=");
        NavAddressList addrl = (NavAddressList) this.children.elementAt(0);
        NavAddress first = addrl.getChild(0);
        int numChildren = addrl.getChildrenSize();
        first.navigate(cs, sBuf);
        for (int i = 1; i < numChildren; i++) {
            addrl.getChild(i).addToString(cs);
        }
        cs.currentDescription().getConnectOptions().get(0).conn_data.append(")");
    }
}
