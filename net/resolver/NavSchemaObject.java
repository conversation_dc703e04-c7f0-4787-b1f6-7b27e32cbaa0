package oracle.net.resolver;

import oracle.net.jdbc.TNSAddress.SchemaObject;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnStrategy;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavSchemaObject.class */
public interface NavSchemaObject extends SchemaObject {
    public static final boolean DEBUG = false;
    public static final String SR = "(SOURCE_ROUTE=yes)";
    public static final String LB = "(LOAD_BALANCE=yes)";
    public static final String NFO = "(FAILOVER=false)";
    public static final String CD = "(CONNECT_DATA=";
    public static final String CID1v2 = "(CID=(PROGRAM=";
    public static final String CID2v2 = ")(HOST=";
    public static final String CID3v2 = ")(USER=";
    public static final String CID4v2 = "))";

    void navigate(ConnStrategy connStrategy, <PERSON><PERSON><PERSON><PERSON> stringBuffer) throws NetException;

    void addToString(ConnStrategy connStrategy);
}
