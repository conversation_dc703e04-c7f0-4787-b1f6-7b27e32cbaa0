package oracle.net.resolver;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Vector;
import oracle.net.jdbc.TNSAddress.AddressList;
import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnOption;
import oracle.net.nt.ConnStrategy;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavAddressList.class */
public class NavAddressList extends AddressList implements NavSchemaObject {
    private Vector activeChildren;
    private int sBuflength;
    private NetException stashedException;

    public NavAddressList(SchemaObjectFactoryInterface fac) {
        super(fac);
        this.stashedException = null;
        this.activeChildren = new Vector(1, 10);
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void navigate(ConnStrategy cs, StringBuffer sBuf) throws NetException {
        navigate2(cs, sBuf, 0);
    }

    private void navigate2(ConnStrategy cs, StringBuffer sBuf, int reCurCnt) throws NetException {
        boolean nested = false;
        int reCurCnt2 = reCurCnt + 1;
        this.sBuflength = sBuf.length();
        sBuf.append("(ADDRESS_LIST=");
        if (this.sourceRoute) {
            this.activeChildren = this.children;
            try {
                ((NavSchemaObject) this.activeChildren.elementAt(0)).navigate(cs, sBuf);
                for (int i = 1; i < this.activeChildren.size(); i++) {
                    ((NavSchemaObject) this.activeChildren.elementAt(i)).addToString(cs);
                }
            } catch (NetException e) {
                this.stashedException = e;
                throw e;
            }
        } else {
            this.activeChildren = NavDescriptionList.setActiveChildren(this.children, this.failover, this.loadBalance);
            for (int i2 = 0; i2 < this.activeChildren.size(); i2++) {
                if (getChildrenType(i2) == 1) {
                    nested = true;
                    ((NavAddressList) this.activeChildren.elementAt(i2)).navigate2(cs, sBuf, reCurCnt2);
                } else {
                    try {
                        ((NavSchemaObject) this.activeChildren.elementAt(i2)).navigate(cs, sBuf);
                    } catch (NetException e2) {
                        this.stashedException = e2;
                    }
                }
            }
            if (cs.currentDescription().getConnectOptions().size() == 0) {
                sBuf.replace(sBuf.length() - "(ADDRESS_LIST=".length(), sBuf.length(), "");
                throw this.stashedException;
            }
        }
        if ((reCurCnt2 - 1 != 0 || !nested) && !this.sourceRoute) {
            closeNVPair(cs, false);
        } else {
            closeNVPair(cs, true);
        }
        sBuf.setLength(this.sBuflength);
        if (this.loadBalance) {
            cs.addSocketOptions_FORCE_DNS_LOAD_BALANCING_OFF();
        }
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void addToString(ConnStrategy cs) {
        String NVString = toString();
        ArrayList<ConnOption> cOpts = cs.currentDescription().getConnectOptions();
        Iterator<ConnOption> it = cOpts.iterator();
        while (it.hasNext()) {
            ConnOption co = it.next();
            if (!co.done) {
                co.conn_data.append(NVString);
            }
        }
    }

    @Override // oracle.net.jdbc.TNSAddress.AddressList, oracle.net.jdbc.TNSAddress.SchemaObject
    public String toString() {
        String s = "(ADDRESS_LIST=";
        for (int i = 0; i < this.children.size(); i++) {
            s = s + ((NavSchemaObject) this.children.elementAt(i)).toString();
        }
        if (this.sourceRoute) {
            s = s + NavSchemaObject.SR;
        }
        if (this.loadBalance) {
            s = s + NavSchemaObject.LB;
        }
        if (!this.failover) {
            s = s + NavSchemaObject.NFO;
        }
        return s + ")";
    }

    public int getChildrenSize() {
        return this.children.size();
    }

    public int getChildrenType(int childNum) {
        return ((NavSchemaObject) this.children.elementAt(childNum)).isA();
    }

    public NavAddress getChild(int childNum) {
        return (NavAddress) this.children.elementAt(childNum);
    }

    private void closeNVPair(ConnStrategy cs, boolean mode) {
        ArrayList<ConnOption> cOpts = cs.currentDescription().getConnectOptions();
        for (int i = cOpts.size() - 1; i >= 0 && !cOpts.get(i).done; i--) {
            if (mode || (cOpts.size() - 1) - i < getChildrenSize()) {
                if (this.sourceRoute) {
                    cOpts.get(i).conn_data.append(NavSchemaObject.SR);
                    cOpts.get(i).done = true;
                }
                cOpts.get(i).conn_data.append(")");
            } else {
                return;
            }
        }
    }
}
