package oracle.net.resolver;

import java.io.File;
import java.security.AccessController;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/net/resolver/EnvVariableResolver.class */
public class EnvVariableResolver {
    private static final boolean IS_WINDOWS_FILE_SYSTEM = File.separator.equals("\\");
    private static final Pattern PLACE_HOLDER_PATTERN = Pattern.compile("(\\$\\{.*?\\})");
    public static final String TNS_ADMIN = "TNS_ADMIN";
    private static final List<Character> SUPPORTED_PRE_CHARS;

    static {
        if (IS_WINDOWS_FILE_SYSTEM) {
            SUPPORTED_PRE_CHARS = Arrays.asList('\\', '/', ' ', ':');
        } else {
            SUPPORTED_PRE_CHARS = Arrays.asList('/', ' ', ':');
        }
    }

    public static String resolveEnvPlaceHolders(String inputString, @Blind(PropertiesBlinder.class) Properties userProps) {
        Matcher matcher = PLACE_HOLDER_PATTERN.matcher(inputString);
        StringBuffer outputBuffer = new StringBuffer();
        while (matcher.find()) {
            String match = matcher.group(1);
            if (match != null && match.length() >= 4) {
                String key = match.substring(2, match.length() - 1);
                String value = getEnvValue(key, userProps);
                if (value != null) {
                    matcher.appendReplacement(outputBuffer, Matcher.quoteReplacement(value));
                }
            }
        }
        matcher.appendTail(outputBuffer);
        return outputBuffer.toString();
    }

    public static String resolveFilePath(String filePath, @Blind(PropertiesBlinder.class) Properties userProps) {
        if (checkIfFileExists(filePath)) {
            return filePath;
        }
        return new EnvKeyParser(filePath.toCharArray()).traverse(userProps);
    }

    private static boolean checkIfFileExists(String inputString) {
        File file = new File(inputString);
        return file.exists();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static String getEnvValue(String envKey, @Blind(PropertiesBlinder.class) Properties userProps) {
        if (envKey.equalsIgnoreCase(TNS_ADMIN) && userProps.containsKey(OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN)) {
            return userProps.getProperty(OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN);
        }
        String returnValue = System.getProperty(envKey);
        if (returnValue == null) {
            try {
                returnValue = (String) AccessController.doPrivileged(() -> {
                    return System.getenv(envKey);
                });
            } catch (SecurityException e) {
                return null;
            }
        }
        return returnValue;
    }

    /* loaded from: ojdbc8.jar:oracle/net/resolver/EnvVariableResolver$EnvKeyParser.class */
    private static class EnvKeyParser {
        private final char[] input;
        private final StringBuilder outputBuffer;
        private int currentIndex;

        private EnvKeyParser(char[] input) {
            this.currentIndex = 0;
            this.input = input;
            this.outputBuffer = new StringBuilder();
        }

        public String traverse(@Blind(PropertiesBlinder.class) Properties userProp) {
            char prevChar = ' ';
            while (this.currentIndex < this.input.length) {
                switch (this.input[this.currentIndex]) {
                    case '$':
                        if (EnvVariableResolver.SUPPORTED_PRE_CHARS.contains(Character.valueOf(prevChar))) {
                            lookForLinuxEnvReplacement(userProp);
                            break;
                        } else {
                            this.outputBuffer.append(this.input[this.currentIndex]);
                            break;
                        }
                    case '%':
                        if (EnvVariableResolver.SUPPORTED_PRE_CHARS.contains(Character.valueOf(prevChar))) {
                            lookForWindowsEnvReplacement(userProp);
                            break;
                        } else {
                            this.outputBuffer.append(this.input[this.currentIndex]);
                            break;
                        }
                    default:
                        this.outputBuffer.append(this.input[this.currentIndex]);
                        break;
                }
                if (this.currentIndex < this.input.length && !Character.isWhitespace(this.input[this.currentIndex])) {
                    prevChar = this.input[this.currentIndex];
                }
                this.currentIndex++;
            }
            return this.outputBuffer.toString();
        }

        private boolean lookForWindowsEnvReplacement(@Blind(PropertiesBlinder.class) Properties userProps) {
            StringBuilder key = new StringBuilder();
            StringBuilder actualSeq = new StringBuilder();
            actualSeq.append(this.input[this.currentIndex]);
            boolean envTerminatorFound = false;
            this.currentIndex++;
            while (true) {
                if (this.currentIndex >= this.input.length) {
                    break;
                }
                actualSeq.append(this.input[this.currentIndex]);
                if (this.input[this.currentIndex] == '%') {
                    envTerminatorFound = true;
                    break;
                }
                if (!Character.isWhitespace(this.input[this.currentIndex])) {
                    key.append(this.input[this.currentIndex]);
                }
                this.currentIndex++;
            }
            if (envTerminatorFound) {
                String keyStr = key.toString();
                String keyVal = EnvVariableResolver.getEnvValue(keyStr, userProps);
                if (keyVal != null) {
                    this.outputBuffer.append(keyVal);
                    return true;
                }
            }
            this.outputBuffer.append(actualSeq.toString());
            return false;
        }

        private boolean lookForLinuxEnvReplacement(@Blind(PropertiesBlinder.class) Properties userProps) {
            StringBuilder actualSeq = new StringBuilder();
            actualSeq.append(this.input[this.currentIndex]);
            StringBuilder key = new StringBuilder();
            this.currentIndex++;
            while (this.currentIndex < this.input.length) {
                actualSeq.append(this.input[this.currentIndex]);
                if (this.input[this.currentIndex] == '/') {
                    break;
                }
                if (!Character.isWhitespace(this.input[this.currentIndex])) {
                    key.append(this.input[this.currentIndex]);
                }
                this.currentIndex++;
            }
            String keyStr = key.toString();
            String keyVal = EnvVariableResolver.getEnvValue(keyStr, userProps);
            if (keyVal != null) {
                this.outputBuffer.append(keyVal);
                if (this.currentIndex < this.input.length) {
                    this.outputBuffer.append(this.input[this.currentIndex]);
                    return true;
                }
                return true;
            }
            this.outputBuffer.append(actualSeq.toString());
            return false;
        }
    }
}
