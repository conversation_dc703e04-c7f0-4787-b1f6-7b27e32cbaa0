package oracle.net.resolver;

import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/resolver/HostnameNamingAdapter.class */
public class HostnameNamingAdapter implements NamingAdapterInterface {
    public static final int DEFAULT_DATABASE_PORT = 1521;
    public static final String DEFAULT_PROTOCOL = "TCP";
    String osuser;
    String programName;
    private static final boolean DEBUG = false;
    private boolean allowDefaultPort;

    private HostnameNamingAdapter() {
        this.allowDefaultPort = true;
    }

    public HostnameNamingAdapter(String _osuser, String _programName, boolean _allowDefaultPort) {
        this.allowDefaultPort = true;
        this.osuser = _osuser;
        this.programName = _programName;
        this.allowDefaultPort = _allowDefaultPort;
    }

    @Override // oracle.net.resolver.NamingAdapterInterface
    public String resolve(String TNSdesc) throws NumberFormatException, NetException {
        int hostOff;
        int sSlashOff;
        String host;
        int port;
        String service_name;
        int colonOff = 0;
        int rightSqurBrktOff = 0;
        boolean secondFormat = false;
        if (TNSdesc.startsWith("//")) {
            TNSdesc = TNSdesc.substring(2);
        } else if (TNSdesc.indexOf("//") != -1) {
            throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, null, false, TNSdesc);
        }
        if (TNSdesc.charAt(0) == '[') {
            secondFormat = true;
            rightSqurBrktOff = TNSdesc.indexOf(93);
            if (rightSqurBrktOff != -1) {
                colonOff = TNSdesc.indexOf(58, rightSqurBrktOff);
            }
            if (rightSqurBrktOff == -1 || (colonOff != -1 && colonOff != rightSqurBrktOff + 1)) {
                throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, null, false, TNSdesc);
            }
            hostOff = 1;
            sSlashOff = TNSdesc.indexOf(47, rightSqurBrktOff);
        } else {
            hostOff = 0;
            colonOff = TNSdesc.indexOf(58);
            sSlashOff = TNSdesc.indexOf(47, 0);
        }
        if ((sSlashOff != -1 && colonOff > sSlashOff) || TNSdesc.endsWith("/") || TNSdesc.endsWith(":")) {
            throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, null, false, TNSdesc);
        }
        try {
            if (secondFormat) {
                host = TNSdesc.substring(hostOff, rightSqurBrktOff);
            } else if (colonOff != -1) {
                host = TNSdesc.substring(hostOff, colonOff);
            } else if (sSlashOff != -1) {
                host = TNSdesc.substring(hostOff, sSlashOff);
            } else {
                host = TNSdesc.substring(hostOff);
            }
            if (colonOff != -1) {
                if (sSlashOff != -1) {
                    port = Integer.parseInt(TNSdesc.substring(colonOff + 1, sSlashOff));
                } else {
                    port = Integer.parseInt(TNSdesc.substring(colonOff + 1));
                }
            } else {
                if (!this.allowDefaultPort) {
                    throw new NetException(NetException.PORT_NUMBER_ERROR);
                }
                port = 1521;
            }
            boolean isServerModePresent = false;
            String serverMode = "";
            if (sSlashOff != -1) {
                int pIndex = TNSdesc.indexOf(58, sSlashOff + 1);
                if (pIndex != -1) {
                    serverMode = TNSdesc.substring(pIndex + 1);
                    isServerModePresent = AddrResolution.SERVER_MODES.contains(serverMode.toUpperCase());
                }
                if (isServerModePresent) {
                    service_name = TNSdesc.substring(sSlashOff + 1, pIndex);
                } else {
                    service_name = TNSdesc.substring(sSlashOff + 1);
                }
            } else {
                service_name = "";
            }
            String addressStr = String.format("(ADDRESS=(PROTOCOL=%s)(HOST=%s)(PORT=%s))", "TCP", host, Integer.valueOf(port));
            String serverModeStr = isServerModePresent ? String.format("(SERVER=%s)", serverMode) : "";
            String serviceNameStr = String.format("(SERVICE_NAME=%s)", service_name);
            String connStr = String.format("(DESCRIPTION=%s%s)", addressStr, String.format("(CONNECT_DATA=%s%s%s)", serviceNameStr, serverModeStr, ""));
            return connStr;
        } catch (NumberFormatException e) {
            throw new NetException(NetException.PORT_NUMBER_ERROR);
        }
    }
}
