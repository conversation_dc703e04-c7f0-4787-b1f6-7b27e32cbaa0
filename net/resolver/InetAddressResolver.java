package oracle.net.resolver;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.NoSuchElementException;
import java.util.Properties;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.internal.Monitor;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.nt.DownHostsCache;

/* loaded from: ojdbc8.jar:oracle/net/resolver/InetAddressResolver.class */
public class InetAddressResolver {
    private static Hashtable<String, InetAddress[]> inetAddressesCache = new Hashtable<>();
    private static Hashtable<String, Integer> circularOffsets = new Hashtable<>();
    private static final Monitor CIRCULAR_OFFSETS_MONITOR = Monitor.newInstance();

    protected static final Iterator<InetSocketAddress> resolveInetSocketAddresses(String host, int port, OracleHostnameResolver hostnameResolver, Properties socketOptions, String protocol, String addr, String httpsProxyHost) throws NetException, UnknownHostException {
        InetAddress[] resolvedAddresses;
        InetSocketAddress[] resolvedSocketAddresses = null;
        if (!isInetAddressResolutionRequired(addr, protocol, socketOptions, httpsProxyHost)) {
            InetSocketAddress[] resolvedSocketAddresses2 = {InetSocketAddress.createUnresolved(host, port)};
            return new InetSocketAddressIterator(resolvedSocketAddresses2);
        }
        if (hostnameResolver != null) {
            resolvedAddresses = hostnameResolver.getAllByName(host);
        } else {
            resolvedAddresses = InetAddress.getAllByName(host);
        }
        boolean forceDNSLoadBalancing = Boolean.parseBoolean((String) socketOptions.get(18));
        if (forceDNSLoadBalancing && resolvedAddresses.length > 1) {
            resolvedAddresses = getAddressesInCircularOrder(host, resolvedAddresses);
        }
        DownHostsCache.getInstance().reorderAddresses(resolvedAddresses, port);
        if (resolvedAddresses.length > 0) {
            resolvedSocketAddresses = new InetSocketAddress[resolvedAddresses.length];
            for (int i = 0; i < resolvedAddresses.length; i++) {
                resolvedSocketAddresses[i] = new InetSocketAddress(resolvedAddresses[i], port);
            }
        }
        return new InetSocketAddressIterator(resolvedSocketAddresses);
    }

    protected static boolean isInetAddressResolutionRequired(String addr, String protocol, Properties socketOptions, String httpsProxyHost) throws NetException {
        if (!protocol.equalsIgnoreCase("tcp") && !protocol.equalsIgnoreCase("tcps") && !protocol.equalsIgnoreCase("wss")) {
            return true;
        }
        boolean isRemoteDNS = Boolean.parseBoolean((String) socketOptions.getOrDefault(45, "true"));
        if (!isRemoteDNS) {
            return true;
        }
        boolean isProxyConfigured = httpsProxyHost != null || socketOptions.containsKey(30) || socketOptions.containsKey(36) || parseHTTPSProxyHost(addr) != null || isSOCKSSystemPropertyConfigured();
        return !isProxyConfigured;
    }

    private static String parseHTTPSProxyHost(String addr) {
        try {
            NVNavigator nav = new NVNavigator();
            NVPair nvpAddr = new NVFactory().createNVPair(addr);
            NVPair proxyNode = nav.findNVPair(nvpAddr, "HTTPS_PROXY");
            if (proxyNode != null) {
                return proxyNode.getAtom();
            }
            return null;
        } catch (NLException e) {
            return null;
        }
    }

    private static boolean isSOCKSSystemPropertyConfigured() {
        try {
            return System.getProperty("socksProxyHost") != null;
        } catch (Exception e) {
            return false;
        }
    }

    static final InetAddress[] getAddressesInCircularOrder(String hostname, InetAddress[] inetAddressesFromJVM) {
        Monitor.CloseableLock lock = CIRCULAR_OFFSETS_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                InetAddress[] cachedAddresses = inetAddressesCache.get(hostname);
                Integer offset = circularOffsets.get(hostname);
                if (cachedAddresses == null || !areEquals(cachedAddresses, inetAddressesFromJVM)) {
                    offset = new Integer(0);
                    cachedAddresses = inetAddressesFromJVM;
                    inetAddressesCache.put(hostname, inetAddressesFromJVM);
                    circularOffsets.put(hostname, offset);
                }
                InetAddress[] addrb = getCopyAddresses(cachedAddresses, offset.intValue());
                circularOffsets.put(hostname, new Integer((offset.intValue() + 1) % cachedAddresses.length));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return addrb;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static final boolean areEquals(InetAddress[] add1, InetAddress[] add2) {
        if (add1.length != add2.length) {
            return false;
        }
        for (int i = 0; i < add1.length; i++) {
            if (!add1[i].equals(add2[i])) {
                return false;
            }
        }
        return true;
    }

    private static final InetAddress[] getCopyAddresses(InetAddress[] add, int nbOfRotation) {
        InetAddress[] addcp = new InetAddress[add.length];
        for (int i = 0; i < add.length; i++) {
            addcp[i] = add[(i + nbOfRotation) % add.length];
        }
        return addcp;
    }

    /* loaded from: ojdbc8.jar:oracle/net/resolver/InetAddressResolver$InetSocketAddressIterator.class */
    private static final class InetSocketAddressIterator implements Iterator<InetSocketAddress> {
        private final InetSocketAddress[] socketAddresses;
        private int socketAddressIndex;

        private InetSocketAddressIterator(InetSocketAddress[] socketAddresses) {
            this.socketAddressIndex = 0;
            this.socketAddresses = socketAddresses;
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.socketAddressIndex < this.socketAddresses.length;
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // java.util.Iterator
        public InetSocketAddress next() {
            if (!hasNext()) {
                throw new NoSuchElementException();
            }
            InetSocketAddress[] inetSocketAddressArr = this.socketAddresses;
            int i = this.socketAddressIndex;
            this.socketAddressIndex = i + 1;
            return inetSocketAddressArr[i];
        }

        public String toString() {
            return "(" + this.socketAddressIndex + "/" + this.socketAddresses.length + ")";
        }
    }
}
