package oracle.net.resolver;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.net.jndi.JndiAttrs;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/resolver/EZConnectResolver.class */
public class EZConnectResolver {
    private static final String DESCRIPTION_FORMAT = "(DESCRIPTION=%s%s%s%s)";
    private static final String ADDRESS_LIST_FORMAT = "(ADDRESS_LIST=(LOAD_BALANCE=ON)%s)";
    private static final String ADDRESS_FORMAT = "(ADDRESS=(PROTOCOL=%s)(HOST=%s)(PORT=%s)%s)";
    private static final String HTTPS_PROXY_FORMAT = "(HTTPS_PROXY=%s)";
    private static final String HTTPS_PROXY_PORT_FORMAT = "(HTTPS_PROXY_PORT=%s)";
    private static final String CONNECT_DATA_FORMAT = "(CONNECT_DATA=%s%s%s%s%s%s%s)";
    private static final String SERVICE_NAME_FORMAT = "(SERVICE_NAME=%s)";
    private static final String SERVER_MODE_FORMAT = "(SERVER=%s)";
    private static final String INSTANCE_NAME_FORMAT = "(INSTANCE_NAME=%s)";
    private static final String SERVICE_TAG_FORMAT = "(SERVICE_TAG=%s)";
    private static final String POOL_CONNECTION_CLASS_FORMAT = "(POOL_CONNECTION_CLASS=%s)";
    private static final String POOL_PURITY_FORMAT = "(POOL_PURITY=%s)";
    private static final String CONNECTION_ID_PREFIX_FORMAT = "(CONNECTION_ID_PREFIX=%s)";
    private static final String SECURITY_FORMAT = "(SECURITY=%s)";
    private static final String SERVER_DN_MATCH_FORMAT = "(SSL_SERVER_DN_MATCH=%s)";
    private static final String SERVER_DN_FORMAT = "(SSL_SERVER_CERT_DN=%s)";
    private static final String MY_WALLET_DIR_FORMAT = "(MY_WALLET_DIRECTORY=%s)";
    private static final String ENCRYPTION_CLIENT_FORMAT = "(ENCRYPTION_CLIENT=%s)";
    private static final String ENCRYPTION_TYPES_CLIENT_FORMAT = "(ENCRYPTION_TYPES_CLIENT=%s)";
    private static final String CRYPTO_CHECKSUM_CLIENT_FORMAT = "(CRYPTO_CHECKSUM_CLIENT=%s)";
    private static final String CRYPTO_CHECKSUM_TYPES_CLIENT_FORMAT = "(CRYPTO_CHECKSUM_TYPES_CLIENT=%s)";
    private static final String EMPTY_STRING = "";
    private static final String KEY_VALUE_FORMAT = "(%s=%s)";
    private static final String CONNECTION_PROPERTY_THIN_LDAP_AUTH_BIND = "AUTHENTICATE_BIND";
    private static final String LDAP_BIND_TYPE_SIMPLE = "LDAPS_SIMPLE_AUTH";
    private static final String LDAP_BIND_TYPE_NONE = "NONE";
    private static final String CONNECTION_PROPERTY_THIN_LDAP_DIRECTORY_SERVER_TYPE = "DIRECTORY_SERVER_TYPE";
    private static final String EXT_TNS_ADMIN_KEYWORD = "TNS_ADMIN";
    private static final char EXT_DOUBLE_QT = '\"';
    private static final char EXT_KEY_VAL_SEP = '=';
    private static final char EXT_PARAM_SEP = '&';
    private static final char EXT_ESCAPE_CHAR = '\\';
    private final String url;
    private String resolvedUrl;
    private final Properties connectionProps = new Properties();
    private final Properties urlProps = new Properties();
    private final String urlPrefix;
    private final Properties defaultProperties;
    private boolean useAddressList;
    private static final boolean DEBUG = false;
    private static final Pattern HOSTNAMES_PATTERN = Pattern.compile("(?<hostnames>(((\\[[A-z0-9:]+\\])|([A-z0-9][A-z0-9._-]+))[,]?)++)(:(?<port>\\d+))?");
    private static final Pattern EZ_URL_PATTERN = Pattern.compile("((?<protocol>tcp|tcps):)?(//)?(?<hostinfo>(" + HOSTNAMES_PATTERN.pattern() + "[;]?)+)(/(?<servicename>[A-z][A-z0-9,-.]+))?(:(?<servermode>dedicated|shared|pooled))?(/(?<instance>[A-z][A-z0-9]+))?", 2);
    private static final Map<String, String> URL_PROPS_ALIAS = initializeUrlAlias();
    private static final Map<String, String> CONNECTION_PROPS_ALIAS = initializeConnectionPropertiesAlias();
    private static final List<String> DESCRIPTION_PARAMS = Collections.unmodifiableList(Arrays.asList("ENABLE", "FAILOVER", "LOAD_BALANCE", "RECV_BUF_SIZE", "SEND_BUF_SIZE", "SDU", "SOURCE_ROUTE", "RETRY_COUNT", "RETRY_DELAY", "CONNECT_TIMEOUT", "TRANSPORT_CONNECT_TIMEOUT"));

    private EZConnectResolver(String url, @Blind(PropertiesBlinder.class) Properties defaultProperties) throws NetException {
        this.defaultProperties = (Properties) defaultProperties.clone();
        int jdbcUrlPrefix = url.indexOf(64);
        if (jdbcUrlPrefix != -1) {
            this.url = url.substring(jdbcUrlPrefix + 1);
            this.urlPrefix = url.substring(0, jdbcUrlPrefix + 1);
        } else {
            this.url = url;
            this.urlPrefix = "";
        }
        parse();
    }

    public static EZConnectResolver newInstance(String url) throws NetException {
        return newInstance(url, new Properties());
    }

    public static EZConnectResolver newInstance(String url, @Blind(PropertiesBlinder.class) Properties defaultProperties) throws NetException {
        return new EZConnectResolver(url, defaultProperties);
    }

    public String getResolvedUrl() {
        return this.resolvedUrl;
    }

    @Blind(PropertiesBlinder.class)
    public Properties getProperties() {
        return this.connectionProps;
    }

    private void parse() throws NetException {
        String parsedUrl = parseExtendedSettings(this.url);
        if (this.connectionProps.isEmpty() && this.urlProps.isEmpty()) {
            parsedUrl = this.url;
        }
        if (parsedUrl.startsWith("(")) {
            this.resolvedUrl = this.urlPrefix + parsedUrl;
        } else if (parsedUrl.startsWith("ldap://") || parsedUrl.startsWith("ldaps://")) {
            resolveLDAPProperties();
            this.resolvedUrl = this.urlPrefix + parsedUrl;
        } else {
            this.resolvedUrl = this.urlPrefix + resolveToLongURLFormat(parsedUrl);
        }
    }

    private void resolveLDAPProperties() throws NetException {
        String isAuthBindEnabled = this.connectionProps.getProperty(CONNECTION_PROPERTY_THIN_LDAP_AUTH_BIND);
        String authType = null;
        if (isAuthBindEnabled != null) {
            if (isAuthBindEnabled.equalsIgnoreCase("true")) {
                authType = getLdapAuthType();
            } else if (isAuthBindEnabled.equalsIgnoreCase("false")) {
                authType = LDAP_BIND_TYPE_NONE;
            } else {
                throw new NetException(NetException.INVALID_LDAP_CONFIG, null, false, CONNECTION_PROPERTY_THIN_LDAP_AUTH_BIND, isAuthBindEnabled);
            }
            this.connectionProps.setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_AUTHENTICATION, authType);
        }
        String walletDir = this.urlProps.getProperty("MY_WALLET_DIRECTORY");
        if (walletDir != null) {
            this.connectionProps.setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SSL_WALLET_LOCATION, walletDir);
            if (authType != null && authType.equalsIgnoreCase("simple")) {
                getLDAPAuthDetailsFromWallet();
            }
        }
    }

    private String getLdapAuthType() throws NetException {
        String result;
        String authBindType = this.connectionProps.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_AUTHENTICATION, LDAP_BIND_TYPE_SIMPLE);
        if (LDAP_BIND_TYPE_SIMPLE.equalsIgnoreCase(authBindType)) {
            result = "simple";
        } else if (LDAP_BIND_TYPE_NONE.equalsIgnoreCase(authBindType)) {
            result = "none";
        } else {
            throw new NetException(NetException.INVALID_LDAP_CONFIG, null, false, "AUTHENTICATE_BIND_METHOD", authBindType);
        }
        return result;
    }

    private void getLDAPAuthDetailsFromWallet() throws NetException {
        Object[] authParams = JndiAttrs.getAuthDetailsFromWallet(this.connectionProps);
        if (authParams != null) {
            if (authParams[0] != null) {
                this.connectionProps.setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_PRINCIPAL, (String) authParams[0]);
            }
            if (authParams[1] != null) {
                this.connectionProps.setProperty(OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_CREDENTIALS, ((OpaqueString) authParams[1]).get());
            }
        }
    }

    private String resolveToLongURLFormat(String url) {
        String urlWithoutWhiteSpaces = url.replaceAll("\\s+", "");
        Matcher matcher = EZ_URL_PATTERN.matcher(urlWithoutWhiteSpaces);
        if (!matcher.matches()) {
            return url;
        }
        String protocol = matcher.group("protocol");
        String hostInfo = matcher.group("hostinfo");
        String serviceName = matcher.group("servicename");
        String serverMode = matcher.group("servermode");
        String instanceName = matcher.group("instance");
        if (hostInfo == null) {
            return url;
        }
        if (protocol == null && serviceName == null && serverMode == null && instanceName == null) {
            return url;
        }
        String proxyHost = this.urlProps.getProperty("HTTPS_PROXY");
        String proxyPort = this.urlProps.getProperty("HTTPS_PROXY_PORT");
        String addressInfo = buildAddressList(hostInfo, protocol, proxyHost, proxyPort);
        if (addressInfo == null) {
            return url;
        }
        if (!this.useAddressList && !this.urlProps.containsKey("LOAD_BALANCE")) {
            this.urlProps.setProperty("LOAD_BALANCE", "ON");
        }
        String connectionIdPrefix = this.urlProps.getProperty("CONNECTION_ID_PREFIX");
        return String.format(DESCRIPTION_FORMAT, buildDescriptionParams(), addressInfo, buildConnectData(serviceName, serverMode, instanceName, connectionIdPrefix), buildSecurityInfo(protocol));
    }

    private String buildConnectData(String serviceName, String serverMode, String instanceName, String connectionIdPrefix) {
        String poolConnectionClass = this.urlProps.getProperty("POOL_CONNECTION_CLASS");
        String poolPurity = this.urlProps.getProperty("POOL_PURITY");
        String serviceTag = this.urlProps.getProperty("SERVICE_TAG");
        Object[] objArr = new Object[7];
        Object[] objArr2 = new Object[1];
        objArr2[0] = serviceName == null ? "" : serviceName;
        objArr[0] = String.format(SERVICE_NAME_FORMAT, objArr2);
        objArr[1] = serverMode == null ? "" : String.format(SERVER_MODE_FORMAT, serverMode);
        objArr[2] = instanceName == null ? "" : String.format(INSTANCE_NAME_FORMAT, instanceName);
        objArr[3] = poolConnectionClass == null ? "" : String.format(POOL_CONNECTION_CLASS_FORMAT, poolConnectionClass);
        objArr[4] = poolPurity == null ? "" : String.format(POOL_PURITY_FORMAT, poolPurity);
        objArr[5] = serviceTag == null ? "" : String.format(SERVICE_TAG_FORMAT, serviceTag);
        objArr[6] = connectionIdPrefix == null ? "" : String.format(CONNECTION_ID_PREFIX_FORMAT, connectionIdPrefix);
        return String.format(CONNECT_DATA_FORMAT, objArr);
    }

    private String buildAddressList(String hostInfo, String protocol, String proxyHost, String proxyPort) {
        StringBuilder builder = new StringBuilder();
        String proxyInfo = "";
        if (proxyHost != null && proxyPort != null) {
            proxyInfo = String.format(HTTPS_PROXY_FORMAT, proxyHost) + String.format(HTTPS_PROXY_PORT_FORMAT, proxyPort);
        }
        if (protocol == null) {
            protocol = "TCP";
        }
        String[] addressLists = hostInfo.split(";");
        this.useAddressList = hostInfo.indexOf(59) != -1;
        for (String addressList : addressLists) {
            int addressNodeCount = 0;
            StringBuilder addressListBuilder = new StringBuilder();
            Matcher matcher = HOSTNAMES_PATTERN.matcher(addressList);
            while (matcher.find()) {
                String[] hostNames = matcher.group("hostnames").split(",");
                String port = matcher.group("port");
                if (port == null) {
                    port = "1521";
                }
                for (String hostName : hostNames) {
                    addressListBuilder.append(getAddrStr(hostName, port, protocol, proxyInfo));
                    addressNodeCount++;
                }
            }
            builder.append(this.useAddressList ? String.format(ADDRESS_LIST_FORMAT, addressListBuilder.toString()) : addressListBuilder.toString());
        }
        return builder.toString();
    }

    private String getAddrStr(String hostName, String port, String protocol, String proxyInfo) {
        String host = hostName.trim();
        if (host.startsWith("[") && host.endsWith("]")) {
            host = host.substring(1, host.length() - 1);
        }
        return String.format(ADDRESS_FORMAT, protocol, host, port, proxyInfo);
    }

    private String buildDescriptionParams() {
        if (this.urlProps.isEmpty()) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        this.urlProps.forEach((k, v) -> {
            if (DESCRIPTION_PARAMS.contains(k)) {
                builder.append(String.format(KEY_VALUE_FORMAT, k, v));
            }
        });
        return builder.toString();
    }

    private String buildSecurityInfo(String protocol) {
        StringBuilder securityInfo = new StringBuilder();
        if (protocol != null && protocol.equalsIgnoreCase("tcps")) {
            String serverDNMatch = this.connectionProps.getProperty("oracle.net.ssl_server_dn_match");
            if (serverDNMatch == null) {
                serverDNMatch = this.defaultProperties.getProperty("oracle.net.ssl_server_dn_match", "TRUE");
                this.connectionProps.setProperty("oracle.net.ssl_server_dn_match", serverDNMatch);
            }
            securityInfo.append(String.format(SERVER_DN_MATCH_FORMAT, serverDNMatch));
            String serverCertDN = this.urlProps.getProperty("SSL_SERVER_CERT_DN");
            if (serverCertDN != null) {
                securityInfo.append(String.format(SERVER_DN_FORMAT, serverCertDN));
            }
            String walletDir = this.urlProps.getProperty("MY_WALLET_DIRECTORY");
            if (walletDir != null) {
                securityInfo.append(String.format(MY_WALLET_DIR_FORMAT, walletDir));
            }
        } else {
            String encryptionClient = this.urlProps.getProperty("ENCRYPTION_CLIENT");
            String encryptionClientTypes = this.urlProps.getProperty("ENCRYPTION_TYPES_CLIENT");
            String checksumClient = this.urlProps.getProperty("CRYPTO_CHECKSUM_CLIENT");
            String checksumClientTypes = this.urlProps.getProperty("CRYPTO_CHECKSUM_TYPES_CLIENT");
            if (encryptionClient != null) {
                securityInfo.append(String.format(ENCRYPTION_CLIENT_FORMAT, encryptionClient));
            }
            if (encryptionClientTypes != null) {
                securityInfo.append(String.format(ENCRYPTION_TYPES_CLIENT_FORMAT, encryptionClientTypes));
            }
            if (checksumClient != null) {
                securityInfo.append(String.format(CRYPTO_CHECKSUM_CLIENT_FORMAT, checksumClient));
            }
            if (checksumClientTypes != null) {
                securityInfo.append(String.format(CRYPTO_CHECKSUM_TYPES_CLIENT_FORMAT, checksumClientTypes));
            }
        }
        return securityInfo.length() == 0 ? "" : String.format(SECURITY_FORMAT, securityInfo.toString());
    }

    private String parseExtendedSettings(String urlStr) throws NetException {
        char[] urlBytes = urlStr.trim().toCharArray();
        int extendedSettingsIndex = findExtendedSettingPosition(urlBytes);
        if (extendedSettingsIndex == -1) {
            return urlStr;
        }
        parseExtendedProperties(urlBytes, extendedSettingsIndex + 1);
        return urlStr.substring(0, extendedSettingsIndex);
    }

    private void parseExtendedProperties(char[] urlChars, int extIndex) throws NetException {
        String key = null;
        char[] token = new char[urlChars.length];
        int tokenIndx = 0;
        int i = extIndex;
        while (i < urlChars.length) {
            if (!Character.isWhitespace(urlChars[i])) {
                switch (urlChars[i]) {
                    case '\"':
                        int[] indicies = parseQuotedString(i, urlChars, tokenIndx, token);
                        tokenIndx = indicies[1];
                        i = indicies[0];
                        break;
                    case '&':
                        if (key == null) {
                            String errMsg = "Unable to parse url \"" + new String(token, 0, tokenIndx) + "\"";
                            throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, errMsg, false, String.valueOf(urlChars));
                        }
                        String value = new String(token, 0, tokenIndx).trim();
                        addParam(key, value);
                        key = null;
                        tokenIndx = 0;
                        break;
                    case '=':
                        if (key != null) {
                            if (!key.equals("wallet_location")) {
                                String errMsg2 = "Unable to parse url \"" + new String(token, 0, tokenIndx) + "\"";
                                throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, errMsg2, false, String.valueOf(urlChars));
                            }
                            break;
                        } else {
                            key = new String(token, 0, tokenIndx).trim();
                            tokenIndx = 0;
                            break;
                        }
                    case '\\':
                        if (i + 1 < urlChars.length && isValidEscapeChar(urlChars[i + 1])) {
                            int i2 = tokenIndx;
                            tokenIndx++;
                            i++;
                            token[i2] = urlChars[i];
                            break;
                        } else {
                            throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, "Invalid character at " + i + " : " + urlChars[i], false, String.valueOf(urlChars));
                        }
                        break;
                    default:
                        int i3 = tokenIndx;
                        tokenIndx++;
                        token[i3] = urlChars[i];
                        break;
                }
            }
            i++;
        }
        if (key != null) {
            String value2 = new String(token, 0, tokenIndx).trim();
            addParam(key, value2);
        }
    }

    private int[] parseQuotedString(int startIndex, char[] urlChars, int tokenIndex, char[] token) throws NetException {
        int i = startIndex + 1;
        while (i < urlChars.length) {
            char curChar = urlChars[i];
            if (curChar == '\\') {
                if (i + 1 < urlChars.length && isValidEscapeChar(urlChars[i + 1])) {
                    int i2 = tokenIndex;
                    tokenIndex++;
                    i++;
                    token[i2] = urlChars[i];
                } else {
                    throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, "Invalid character at " + i + " : " + urlChars[i], false, String.valueOf(urlChars));
                }
            } else {
                if (curChar == '\"') {
                    return new int[]{i, tokenIndex};
                }
                int i3 = tokenIndex;
                tokenIndex++;
                token[i3] = curChar;
            }
            i++;
        }
        throw new NetException(NetException.EZ_CONNECT_SYNTAX_ERROR, "Quote at " + startIndex + " not closed.", false, String.valueOf(urlChars));
    }

    private boolean isValidEscapeChar(char currentChar) {
        if (currentChar == '\\' || currentChar == '\"') {
            return true;
        }
        return false;
    }

    private void addParam(String key, String value) {
        if (key.equalsIgnoreCase("TNS_ADMIN")) {
            addTNSAdmin(value);
            return;
        }
        String aliasKeyName = key.toLowerCase();
        String propertyName = URL_PROPS_ALIAS.get(aliasKeyName);
        if (propertyName != null) {
            this.urlProps.put(propertyName, value);
        } else {
            this.connectionProps.put(CONNECTION_PROPS_ALIAS.getOrDefault(aliasKeyName, key), value);
        }
    }

    private void addTNSAdmin(String tnsAdminValue) {
        if (!new File(tnsAdminValue).exists()) {
            tnsAdminValue = EnvVariableResolver.resolveEnvPlaceHolders(tnsAdminValue, this.connectionProps);
        }
        this.connectionProps.put(OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN, tnsAdminValue);
    }

    private int findExtendedSettingPosition(char[] urlBytes) {
        int urlNodeDepth = 0;
        for (int i = 0; i < urlBytes.length; i++) {
            if (urlBytes[i] == '(') {
                urlNodeDepth++;
            } else if (urlBytes[i] == ')') {
                urlNodeDepth--;
            } else if (urlBytes[i] == '?' && urlNodeDepth == 0) {
                return i;
            }
        }
        return -1;
    }

    private static final Map<String, String> initializeUrlAlias() {
        Map<String, String> aliasMap = new HashMap<>();
        aliasMap.put("enable", "ENABLE");
        aliasMap.put("failover", "FAILOVER");
        aliasMap.put("load_balance", "LOAD_BALANCE");
        aliasMap.put("recv_buf_size", "RECV_BUF_SIZE");
        aliasMap.put("send_buf_size", "SEND_BUF_SIZE");
        aliasMap.put("sdu", "SDU");
        aliasMap.put("source_route", "SOURCE_ROUTE");
        aliasMap.put("retry_count", "RETRY_COUNT");
        aliasMap.put("retry_delay", "RETRY_DELAY");
        aliasMap.put("https_proxy", "HTTPS_PROXY");
        aliasMap.put("https_proxy_port", "HTTPS_PROXY_PORT");
        aliasMap.put("connect_timeout", "CONNECT_TIMEOUT");
        aliasMap.put("transport_connect_timeout", "TRANSPORT_CONNECT_TIMEOUT");
        aliasMap.put("ssl_server_cert_dn", "SSL_SERVER_CERT_DN");
        aliasMap.put("wallet_location", "MY_WALLET_DIRECTORY");
        aliasMap.put("encryption_client", "ENCRYPTION_CLIENT");
        aliasMap.put("encryption_types_client", "ENCRYPTION_TYPES_CLIENT");
        aliasMap.put("crypto_checksum_client", "CRYPTO_CHECKSUM_CLIENT");
        aliasMap.put("crypto_checksum_types_client", "CRYPTO_CHECKSUM_TYPES_CLIENT");
        aliasMap.put("pool_connection_class", "POOL_CONNECTION_CLASS");
        aliasMap.put("pool_purity", "POOL_PURITY");
        aliasMap.put("service_tag", "SERVICE_TAG");
        aliasMap.put("connection_id_prefix", "CONNECTION_ID_PREFIX");
        return aliasMap;
    }

    private static final Map<String, String> initializeConnectionPropertiesAlias() {
        Map<String, String> aliasMap = new HashMap<>();
        aliasMap.put("keystore_type", "javax.net.ssl.keyStoreType");
        aliasMap.put("keystore_password", "javax.net.ssl.keyStorePassword");
        aliasMap.put("keystore", "javax.net.ssl.keyStore");
        aliasMap.put("truststore_type", "javax.net.ssl.trustStoreType");
        aliasMap.put("truststore_password", "javax.net.ssl.trustStorePassword");
        aliasMap.put("truststore", "javax.net.ssl.trustStore");
        aliasMap.put("ssl_version", "oracle.net.ssl_version");
        aliasMap.put("ssl_ciphers", "oracle.net.ssl_cipher_suites");
        aliasMap.put("ssl_server_dn_match", "oracle.net.ssl_server_dn_match");
        aliasMap.put("allow_weak_crypto", OracleConnection.CONNECTION_PROPERTY_THIN_NET_ALLOW_WEAK_CRYPTO);
        aliasMap.put("ssl_certificate_alias", "oracle.net.ssl_certificate_alias");
        aliasMap.put("ssl_certificate_thumbprint", OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CERTIFICATE_THUMBPRINT);
        aliasMap.put("authenticate_bind_method", OracleConnection.CONNECTION_PROPERTY_THIN_LDAP_SECURITY_AUTHENTICATION);
        aliasMap.put("authenticate_bind", CONNECTION_PROPERTY_THIN_LDAP_AUTH_BIND);
        aliasMap.put("directory_server_type", CONNECTION_PROPERTY_THIN_LDAP_DIRECTORY_SERVER_TYPE);
        aliasMap.put("token_auth", OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION);
        aliasMap.put("token_location", OracleConnection.CONNECTION_PROPERTY_TOKEN_LOCATION);
        aliasMap.put("password_auth", OracleConnection.CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION);
        aliasMap.put("oci_iam_url", OracleConnection.CONNECTION_PROPERTY_OCI_IAM_URL);
        aliasMap.put("oci_tenancy", OracleConnection.CONNECTION_PROPERTY_OCI_TENANCY);
        aliasMap.put("oci_compartment", OracleConnection.CONNECTION_PROPERTY_OCI_COMPARTMENT);
        aliasMap.put("oci_database", OracleConnection.CONNECTION_PROPERTY_OCI_DATABASE);
        aliasMap.put("oci_config_file", OracleConnection.CONNECTION_PROPERTY_OCI_CONFIG_FILE);
        aliasMap.put("oci_profile", OracleConnection.CONNECTION_PROPERTY_OCI_PROFILE);
        aliasMap.put("azure_db_app_id_uri", OracleConnection.CONNECTION_PROPERTY_AZURE_DB_APP_ID_URI);
        aliasMap.put("tenant_id", OracleConnection.CONNECTION_PROPERTY_TENANT_ID);
        aliasMap.put("client_id", OracleConnection.CONNECTION_PROPERTY_CLIENT_ID);
        aliasMap.put("client_certificate", OracleConnection.CONNECTION_PROPERTY_CLIENT_CERTIFICATE);
        aliasMap.put("redirect_uri", OracleConnection.CONNECTION_PROPERTY_REDIRECT_URI);
        return aliasMap;
    }
}
