package oracle.net.resolver;

import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Paths;
import java.security.AccessController;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import oracle.net.jdbc.nl.InvalidSyntaxException;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NameResolver.class */
public class NameResolver {
    private String tnsAdmin;
    private String[] readPath;
    private Hashtable adapterHash;
    String osuser;
    String programName;
    private static final boolean DEBUG = false;
    private static final String[] DEFAULT_SEARCH_PATH;
    private static final String READ_PATH_PROPERTY = "oracle.net.names.directory_path";
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !NameResolver.class.desiredAssertionStatus();
        DEFAULT_SEARCH_PATH = new String[]{"TNSNAMES", "HOSTNAME"};
    }

    private NameResolver() {
    }

    protected NameResolver(String tnsValue, String _osuser, String _programName) throws NetException {
        this.tnsAdmin = tnsValue;
        bootNameResolver();
        this.osuser = _osuser;
        this.programName = _programName;
    }

    public String resolveName(String connId) throws NumberFormatException, NetException {
        if (connId == null) {
            throw new NetException(NetException.CONNECT_STRING_EMPTY);
        }
        String tempConnId = connId.trim();
        if (tempConnId.length() == 0) {
            throw new NetException(NetException.CONNECT_STRING_EMPTY);
        }
        String connStr = null;
        if (this.tnsAdmin == null) {
            HostnameNamingAdapter h = new HostnameNamingAdapter(this.osuser, this.programName, true);
            String connStr2 = h.resolve(tempConnId);
            return connStr2;
        }
        List<NetException> exceptions = new ArrayList<>(0);
        for (int adapNum = 0; connStr == null && adapNum < this.readPath.length; adapNum++) {
            NamingAdapterInterface adapter = (NamingAdapterInterface) this.adapterHash.get(this.readPath[adapNum]);
            try {
                connStr = adapter.resolve(tempConnId);
            } catch (NetException ne) {
                exceptions.add(ne);
            }
        }
        if (connStr == null) {
            throw createUnresolvedException(connId, exceptions);
        }
        return connStr;
    }

    private NetException createUnresolvedException(String url, List<NetException> exceptions) {
        if (!$assertionsDisabled && (exceptions == null || exceptions.isEmpty())) {
            throw new AssertionError("No exceptions occurred. The readPath length is: " + (this.readPath == null ? "readpath is null!" : Integer.valueOf(this.readPath.length)));
        }
        String exceptionParamPath = this.tnsAdmin;
        if (Files.isDirectory(Paths.get(this.tnsAdmin, new String[0]), new LinkOption[0])) {
            exceptionParamPath = exceptionParamPath + "/tnsnames.ora";
        }
        boolean isTnsNamesPresent = Files.exists(Paths.get(exceptionParamPath, new String[0]), new LinkOption[0]) || this.tnsAdmin.endsWith("tnsnames.ora");
        if (isTnsNamesPresent) {
            return new NetException(NetException.CONNECT_ALIAS_NOTFOUND, null, false, url, exceptionParamPath);
        }
        NetException netException = exceptions.get(0);
        for (int i = 1; i < exceptions.size(); i++) {
            netException.addSuppressed(exceptions.get(i));
        }
        return netException;
    }

    private void addAdapters() {
        if (this.adapterHash == null) {
            this.adapterHash = new Hashtable();
        }
        this.adapterHash.put("TNSNAMES", new TNSNamesNamingAdapter(this.tnsAdmin));
        this.adapterHash.put("HOSTNAME", new HostnameNamingAdapter(this.osuser, this.programName, false));
    }

    private void bootNameResolver() throws NetException {
        if (this.tnsAdmin != null) {
            setReadPath();
            addAdapters();
        }
    }

    private boolean checkForValidAdapter(String adaptername) {
        int totAdapters = DEFAULT_SEARCH_PATH.length;
        for (int iterCnt = 0; iterCnt < totAdapters; iterCnt++) {
            if (DEFAULT_SEARCH_PATH[iterCnt].equalsIgnoreCase(adaptername)) {
                return true;
            }
        }
        return false;
    }

    private void setDefaultPath() {
        this.readPath = DEFAULT_SEARCH_PATH;
    }

    private void setReadPath() throws NetException {
        String[] adapterList = getUserReadPath();
        if (adapterList == null) {
            setDefaultPath();
            return;
        }
        ArrayList tempList = new ArrayList();
        for (int iterCnt = 0; iterCnt < adapterList.length; iterCnt++) {
            if (checkForValidAdapter(adapterList[iterCnt])) {
                String tempStr = adapterList[iterCnt];
                if (!tempList.contains(tempStr.toUpperCase())) {
                    tempList.add(adapterList[iterCnt].toUpperCase());
                }
            }
        }
        int validCnt = tempList.size();
        if (validCnt == 0) {
            throw new NetException(NetException.INVALID_READ_PATH, " The Read path did not contain any valid adapters.");
        }
        this.readPath = new String[validCnt];
        tempList.toArray(this.readPath);
    }

    private String[] getUserReadPath() throws NetException {
        String nameStr = (String) AccessController.doPrivileged(() -> {
            return System.getProperty(READ_PATH_PROPERTY);
        });
        if (nameStr == null) {
            return null;
        }
        String nameStr2 = nameStr.trim();
        if (nameStr2.length() == 0) {
            return null;
        }
        StringBuffer buffStr = new StringBuffer(nameStr2);
        if (buffStr.charAt(0) == '(') {
            buffStr.insert(0, "(path=").append(')');
        } else {
            buffStr.insert(0, "(path=(").append(NavSchemaObject.CID4v2);
        }
        String nvStr = buffStr.toString();
        NVFactory nvf = new NVFactory();
        try {
            NVPair nvp = nvf.createNVPair(nvStr);
            if (nvp.getRHSType() != NVPair.RHS_LIST || nvp.getListType() != NVPair.LIST_COMMASEP) {
                throw new NetException(NetException.INVALID_READ_PATH, " Read path specified is " + nameStr2);
            }
            int entries = nvp.getListSize();
            String[] path = new String[entries];
            for (int iterCnt = 0; iterCnt < entries; iterCnt++) {
                path[iterCnt] = nvp.getListElement(iterCnt).getName();
            }
            return path;
        } catch (InvalidSyntaxException e) {
            throw new NetException(NetException.INVALID_READ_PATH, " Read path specified is " + nameStr2);
        } catch (NLException e2) {
            throw new NetException(NetException.INVALID_READ_PATH, " Read path specified is " + nvStr);
        }
    }
}
