package oracle.net.resolver;

import oracle.net.jdbc.TNSAddress.SchemaObject;
import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavSchemaObjectFactory.class */
public class NavSchemaObjectFactory implements SchemaObjectFactoryInterface {
    @Override // oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface
    public SchemaObject create(int type) {
        switch (type) {
            case 0:
                return new NavAddress(this);
            case 1:
                return new NavAddressList(this);
            case 2:
                return new NavDescription(this);
            case 3:
                return new NavDescriptionList(this);
            case 4:
                return new NavServiceAlias(this);
            default:
                return null;
        }
    }
}
