package oracle.net.resolver;

import oracle.net.jdbc.nl.NLParamParser;

/* compiled from: TNSNamesNamingAdapter.java */
/* loaded from: ojdbc8.jar:oracle/net/resolver/TNSFile.class */
class TNSFile {
    private NLParamParser tnsEntriesHdl;
    private long lastModifiedTime;
    private String tnsFileName;

    public TNSFile(String file, long modtime, NLParamParser tnsEntries) {
        this.tnsEntriesHdl = tnsEntries;
        this.lastModifiedTime = modtime;
        this.tnsFileName = file;
    }

    public String getFileName() {
        return this.tnsFileName;
    }

    public NLParamParser getEntries() {
        return this.tnsEntriesHdl;
    }

    public long getLastModifiedTime() {
        return this.lastModifiedTime;
    }
}
