package oracle.net.resolver;

import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;
import oracle.net.jdbc.TNSAddress.ServiceAlias;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnStrategy;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavServiceAlias.class */
public class NavServiceAlias extends ServiceAlias implements NavSchemaObject {
    public NavServiceAlias(SchemaObjectFactoryInterface fac) {
        super(fac);
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void navigate(ConnStrategy cs, StringBuffer sb) throws NetException {
        StringBuffer sBuf = new StringBuffer("");
        ((NavSchemaObject) this.child).navigate(cs, sBuf);
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void addToString(ConnStrategy cs) {
    }
}
