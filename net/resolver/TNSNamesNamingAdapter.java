package oracle.net.resolver;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import oracle.jdbc.internal.Monitor;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NLParamParser;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/resolver/TNSNamesNamingAdapter.class */
public class TNSNamesNamingAdapter implements NamingAdapterInterface, Monitor {
    private final String tnsDir;
    private long nextPollTime;
    private ArrayList<TNSFile> tnsFiles;
    private static final String TNSFILE = "tnsnames.ora";
    private static final long REFRESH_INTERVAL = 0;
    private static final int MAX_DEPTH = 5;
    private static final Pattern regexp = Pattern.compile("^(IFILE\\s*=\\s*)(.*)", 2);
    private final Monitor.CloseableLock monitorLock = Monitor.newDefaultLock();

    public TNSNamesNamingAdapter(String tnsAdmin) {
        this.tnsDir = tnsAdmin;
        resetAttr();
    }

    private void resetAttr() {
        this.tnsFiles = new ArrayList<>(3);
        this.nextPollTime = REFRESH_INTERVAL;
    }

    @Override // oracle.net.resolver.NamingAdapterInterface
    public String resolve(String connId) throws NetException {
        NVPair nvp = null;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                checkAndReload();
                Iterator<TNSFile> it = this.tnsFiles.iterator();
                while (it.hasNext()) {
                    TNSFile tnsFile = it.next();
                    nvp = tnsFile.getEntries().getNLPListElement(connId);
                    if (nvp != null) {
                        break;
                    }
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                if (nvp != null) {
                    return nvp.valueToString();
                }
                if (this.tnsFiles.isEmpty()) {
                    throw new NetException(NetException.TNS_NAMES_INACCESSIBLE, null, false, this.tnsDir);
                }
                throw new NetException(NetException.CONNECT_ALIAS_NOTFOUND, null, false, connId, this.tnsFiles.toString());
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private void loadFiles() throws NetException {
        File f = new File(this.tnsDir, TNSFILE);
        String filename = f.getAbsolutePath();
        loadFiles(filename, 0);
    }

    private void loadFiles(String filename, int depth) throws NetException {
        if (depth >= 5) {
            return;
        }
        File f = new File(filename);
        if (!f.isFile() || !f.canRead()) {
            return;
        }
        try {
            NLParamParser tnsEntriesHdl = new NLParamParser(filename, (byte) 1);
            this.tnsFiles.add(new TNSFile(filename, f.lastModified(), tnsEntriesHdl));
            NVPair nvpIFile = tnsEntriesHdl.getNLPListElement("ifile");
            if (nvpIFile != null) {
                ArrayList<String> iFiles = getIFile(filename);
                Iterator<String> it = iFiles.iterator();
                while (it.hasNext()) {
                    String iFile = it.next();
                    loadFiles(iFile, depth + 1);
                }
            }
        } catch (IOException e) {
        } catch (NLException e2) {
        }
    }

    private ArrayList<String> getIFile(String filename) throws IOException {
        FileReader fr = new FileReader(filename);
        BufferedReader br = new BufferedReader(fr);
        Matcher pmatch = regexp.matcher("");
        ArrayList<String> iFiles = new ArrayList<>(2);
        while (true) {
            String currentLine = br.readLine();
            if (currentLine != null) {
                pmatch.reset(currentLine);
                if (pmatch.find()) {
                    String iFile = pmatch.group(2).trim();
                    File f = new File(iFile);
                    if (!f.isAbsolute()) {
                        File f2 = new File(this.tnsDir, iFile);
                        iFile = f2.getAbsolutePath();
                    }
                    iFiles.add(iFile);
                }
            } else {
                return iFiles;
            }
        }
    }

    private void checkAndReload() throws NetException {
        long currTime = System.currentTimeMillis();
        if (currTime > this.nextPollTime) {
            this.nextPollTime = REFRESH_INTERVAL;
            reloadFiles();
        }
    }

    private void reloadFiles() throws NetException {
        if (this.tnsFiles.isEmpty()) {
            loadFiles();
            return;
        }
        Iterator<TNSFile> it = this.tnsFiles.iterator();
        while (it.hasNext()) {
            TNSFile tnsFile = it.next();
            long modifiedTime = tnsFile.getLastModifiedTime();
            String fileName = tnsFile.getFileName();
            File f = new File(fileName);
            boolean isFileChanged = (f.isFile() && f.canRead() && f.lastModified() == modifiedTime) ? false : true;
            if (isFileChanged) {
                resetAttr();
                loadFiles();
                return;
            }
        }
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }
}
