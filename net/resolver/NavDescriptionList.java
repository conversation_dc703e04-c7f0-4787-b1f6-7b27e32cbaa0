package oracle.net.resolver;

import java.util.BitSet;
import java.util.Random;
import java.util.Vector;
import oracle.net.jdbc.TNSAddress.DescriptionList;
import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnStrategy;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavDescriptionList.class */
public class NavDescriptionList extends DescriptionList implements NavSchemaObject {
    private Vector activeChildren;
    private int descProcessed;
    private boolean done;
    private NetException stashedException;

    public NavDescriptionList(SchemaObjectFactoryInterface fac) {
        super(fac);
        this.stashedException = null;
        this.activeChildren = new Vector(1, 10);
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void navigate(ConnStrategy cs, StringBuffer sBuf) throws NetException {
        sBuf.append("(DESCRIPTION_LIST=");
        this.activeChildren = setActiveChildren(this.children, this.failover, this.loadBalance);
        while (this.descProcessed < this.activeChildren.size()) {
            try {
                try {
                    ((NavSchemaObject) this.activeChildren.elementAt(this.descProcessed)).navigate(cs, sBuf);
                    this.descProcessed++;
                } catch (NetException e) {
                    this.stashedException = e;
                    this.descProcessed++;
                }
            } catch (Throwable th) {
                this.descProcessed++;
                throw th;
            }
        }
        if (cs.getAllDescriptions().size() == 0) {
            sBuf.replace(sBuf.length() - "(DESCRIPTION_LIST=".length(), sBuf.length(), "");
            throw this.stashedException;
        }
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void addToString(ConnStrategy cs) {
    }

    public static Vector setActiveChildren(Vector children, boolean failover, boolean loadBalance) {
        int randNumber;
        int listsize = children.size();
        Vector tmpChildren = new Vector(1, 10);
        Random rand = new Random();
        BitSet bs = new BitSet(listsize);
        if (failover) {
            if (loadBalance) {
                for (int i = 0; i < listsize; i++) {
                    do {
                        randNumber = Math.abs(rand.nextInt()) % listsize;
                    } while (bs.get(randNumber));
                    bs.set(randNumber);
                    tmpChildren.addElement(children.elementAt(randNumber));
                }
            } else {
                tmpChildren = children;
            }
        } else if (loadBalance) {
            tmpChildren.addElement(children.elementAt(Math.abs(rand.nextInt()) % listsize));
        } else {
            tmpChildren.addElement(children.elementAt(0));
        }
        return tmpChildren;
    }
}
