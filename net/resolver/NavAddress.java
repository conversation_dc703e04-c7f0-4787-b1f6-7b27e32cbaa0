package oracle.net.resolver;

import java.util.ArrayList;
import java.util.Iterator;
import oracle.jdbc.OracleHostnameResolver;
import oracle.net.jdbc.TNSAddress.Address;
import oracle.net.jdbc.TNSAddress.SchemaObjectFactoryInterface;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.ns.NetException;
import oracle.net.nt.ConnOption;
import oracle.net.nt.ConnStrategy;
import oracle.net.nt.ConnectDescription;

/* loaded from: ojdbc8.jar:oracle/net/resolver/NavAddress.class */
public class NavAddress extends Address implements NavSchemaObject {
    final NVNavigator nav;
    public static final String CONNECT_DATA_ADDRESS = "(ADDRESS=$$REPLACE_THIS$$)";

    public NavAddress(SchemaObjectFactoryInterface fac) {
        super(fac);
        this.nav = new NVNavigator();
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void navigate(ConnStrategy cs, StringBuffer sBuf) throws NetException {
        boolean needToCloseDescription = false;
        if (cs.currentDescription() == null) {
            cs.newConnectDescription();
            needToCloseDescription = true;
        }
        try {
            parseAddressParamsAndNavigate(cs.getHostnameResolver(), cs, sBuf);
            if (needToCloseDescription) {
                String descriptionString = cs.currentDescription().getConnectOptions().get(0).conn_data.toString();
                if (descriptionString.contains(CONNECT_DATA_ADDRESS)) {
                    descriptionString = descriptionString.replace(CONNECT_DATA_ADDRESS, toString());
                }
                cs.currentDescription().setDescriptionString(descriptionString);
                cs.closeDescription();
            }
        } catch (NetException e) {
            throw e;
        }
    }

    @Override // oracle.net.resolver.NavSchemaObject
    public void addToString(ConnStrategy cs) {
        String NVString = toString();
        ArrayList<ConnOption> cOpts = cs.currentDescription().getConnectOptions();
        Iterator<ConnOption> it = cOpts.iterator();
        while (it.hasNext()) {
            ConnOption co = it.next();
            if (!co.done) {
                co.conn_data.append(NVString);
            }
        }
    }

    private void parseAddressParamsAndNavigate(OracleHostnameResolver hostnameResolver, ConnStrategy cs, StringBuffer sBuf) throws NetException {
        readHttpsProxyConfig(cs);
        ConnOption co = new ConnOption();
        co.addr = String.format("(ADDRESS=(PROTOCOL=%s)(HOST=%s)(PORT=%s))", this.prot, this.host, Integer.valueOf(this.port));
        co.host = this.host;
        co.port = this.port;
        co.httpsProxy = this.httpsProxy;
        co.httpsProxyPort = this.httpsProxyPort;
        co.inetSocketAddress = null;
        co.webSocketUri = this.webSocketURI;
        co.protocol = this.prot;
        co.conn_data.append(sBuf.toString());
        if (!InetAddressResolver.isInetAddressResolutionRequired(this.addr, this.prot, cs.socketOptions, this.httpsProxy) || this.prot.equalsIgnoreCase("beq")) {
            co.conn_data.append(toString());
        } else {
            co.conn_data.append(CONNECT_DATA_ADDRESS);
        }
        cs.currentDescription().addConnectOption(co);
    }

    private void readHttpsProxyConfig(ConnStrategy cs) {
        ConnectDescription desc = cs.currentDescription();
        if (this.httpsProxy == null) {
            this.httpsProxy = desc.getHttpsProxy();
        }
        if (this.httpsProxy != null && this.httpsProxyPort == -1) {
            this.httpsProxyPort = desc.getHttpsProxyPort();
        }
    }
}
