package oracle.net.ano;

import java.io.IOException;
import oracle.jdbc.driver.BuildInfo;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/net/ano/AnoComm.class */
public abstract class AnoComm {
    static final int STRING_TYPE = 0;
    static final int RAW_TYPE = 1;
    static final int UB1_TYPE = 2;
    static final int UB2_TYPE = 3;
    static final int UB4_TYPE = 4;
    static final int VERSION_TYPE = 5;
    static final int STATUS_TYPE = 6;
    static final int ARRAY_TYPE = 7;
    static final int MIN_TYPE = 0;
    static final int MAX_TYPE = 7;
    static final int UB1_LENGTH = 1;
    static final int UB2_LENGTH = 2;
    static final int UB4_LENGTH = 4;
    static final int VERSION_LENGTH = 4;
    static final int STATUS_LENGTH = 2;
    static final int NA_MAGIC_SIZE = 4;
    static final long DEADBEEF = -559038737;
    static final int NA_HEADER_SIZE = 13;
    static final int ARRAY_PACKET_HEADER_LENGTH = 10;
    static final int SERVICE_HEADER_LENGTH = 8;
    static final int SUBPACKET_LENGTH = 4;
    static final long NA_MAGIC = -559038737;
    static final short NO_ERROR = 0;
    private static final boolean DEBUG = false;
    private static long version;

    protected abstract void flush() throws IOException;

    protected abstract void sendUB1(short s) throws IOException;

    protected abstract void sendString(String str) throws IOException;

    protected abstract void sendRaw(byte[] bArr) throws IOException;

    protected abstract void writeUB1(short s) throws IOException;

    protected abstract void writeUB2(int i) throws IOException;

    protected abstract void writeUB4(long j) throws IOException;

    protected abstract short readUB1() throws IOException;

    protected abstract byte[] receiveByteArray(int i) throws IOException;

    protected abstract long buffer2Value(byte[] bArr) throws IOException;

    static {
        String[] versionParts = BuildInfo.getDriverVersion().split("\\.");
        version = (Integer.parseInt(versionParts[0]) << 24) | (Integer.parseInt(versionParts[1]) << 20) | (Integer.parseInt(versionParts[2]) << 12) | (Integer.parseInt(versionParts[3]) << 8) | (Integer.parseInt(versionParts[4]) << 0);
    }

    protected long getVersion() {
        return version;
    }

    protected void sendUB2(int value) throws IOException {
        sendPktHeader(2, 3);
        writeUB2(value);
    }

    protected void sendUB4(long value) throws IOException {
        sendPktHeader(4, 4);
        writeUB4(value);
    }

    protected void sendUB2Array(int[] nArray) throws IOException {
        sendPktHeader(10 + (nArray.length * 2), 1);
        writeUB4(-559038737L);
        writeUB2(3);
        writeUB4(nArray.length);
        for (int i : nArray) {
            writeUB2(i & 65535);
        }
    }

    protected void sendStatus(int status) throws IOException {
        sendPktHeader(2, 6);
        writeUB2(status);
    }

    protected void sendVersion() throws IOException {
        sendPktHeader(4, 5);
        writeUB4(getVersion());
    }

    protected void sendPktHeader(int length, int type) throws IOException {
        validateType(length, type);
        writeUB2(length);
        writeUB2(type);
    }

    protected void writeVersion() throws IOException {
        writeUB4(getVersion());
    }

    protected short receiveUB1() throws IOException {
        receivePktHeader(2);
        short value = readUB1();
        return value;
    }

    protected int receiveUB2() throws IOException {
        receivePktHeader(3);
        int value = readUB2();
        return value & 65535;
    }

    protected long receiveUB4() throws IOException {
        receivePktHeader(4);
        long value = readUB4();
        return value;
    }

    protected int[] receiveUB2Array() throws IOException {
        receivePktHeader(1);
        long deadbeef = readUB4();
        int type = readUB2();
        long aLength = readUB4();
        int[] intArray = new int[(int) aLength];
        if (deadbeef != -559038737 || type != 3) {
            throw new NetException(NetException.ARRAY_HEADER_ERROR);
        }
        for (int i = 0; i < intArray.length; i++) {
            intArray[i] = readUB2();
        }
        return intArray;
    }

    protected int receiveStatus() throws IOException {
        receivePktHeader(6);
        return readUB2();
    }

    protected long receiveVersion() throws IOException {
        receivePktHeader(5);
        return readUB4();
    }

    protected String receiveString() throws IOException {
        int receivedLength = receivePktHeader(0);
        return new String(receiveByteArray(receivedLength));
    }

    protected byte[] receiveRaw() throws IOException {
        int receivedLength = receivePktHeader(1);
        return receiveByteArray(receivedLength);
    }

    protected int readUB2() throws IOException {
        byte[] tmpBuffer = new byte[2];
        int value = (int) buffer2Value(tmpBuffer);
        return value & 65535;
    }

    protected long readUB4() throws IOException {
        byte[] tmpBuffer = new byte[4];
        long value = buffer2Value(tmpBuffer);
        return value;
    }

    private int receivePktHeader(int type) throws IOException {
        int length = readUB2();
        int receivedType = readUB2();
        validateReceivedType(length, receivedType, type);
        return length;
    }

    private void validateReceivedType(int length, int receivedType, int type) throws NetException {
        if (receivedType < 0 || receivedType > 7) {
            throw new NetException(NetException.INVALID_NA_PACKET_TYPE);
        }
        if (receivedType != type) {
            throw new NetException(NetException.UNEXPECTED_NA_PACKET_TYPE_RECEIVED);
        }
        switch (type) {
            case 0:
            case 1:
                return;
            case 2:
                if (length > 1) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 3:
            case 6:
                if (length > 2) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 4:
            case 5:
                if (length > 4) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 7:
                if (length < 10) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            default:
                throw new NetException(NetException.INVALID_NA_PACKET_TYPE);
        }
    }

    private void validateType(int length, int type) throws NetException {
        if (type < 0 || type > 7) {
            throw new NetException(NetException.INVALID_NA_PACKET_TYPE);
        }
        switch (type) {
            case 0:
            case 1:
                return;
            case 2:
                if (length > 1) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 3:
            case 6:
                if (length > 2) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 4:
            case 5:
                if (length > 4) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            case 7:
                if (length < 10) {
                    throw new NetException(NetException.INVALID_NA_PACKET_TYPE_LENGTH);
                }
                return;
            default:
                throw new NetException(NetException.INVALID_NA_PACKET_TYPE);
        }
    }

    byte value2Buffer(int value, byte[] tmpBuffer) {
        byte bytes = 0;
        for (int i = tmpBuffer.length - 1; i >= 0; i--) {
            byte b = bytes;
            bytes = (byte) (bytes + 1);
            tmpBuffer[b] = (byte) ((value >>> (8 * i)) & 255);
        }
        return bytes;
    }
}
