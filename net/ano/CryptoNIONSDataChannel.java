package oracle.net.ano;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.logging.Level;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.aso.DataIntegrityAlgorithm;
import oracle.net.aso.EncryptionAlgorithm;
import oracle.net.ns.NIONSDataChannel;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/CryptoNIONSDataChannel.class */
class CryptoNIONSDataChannel extends NIONSDataChannel implements SQLnetDef {
    private static final String CLASS_NAME = CryptoNIONSDataChannel.class.getName();
    private EncryptionAlgorithm encryptionAlg;
    private DataIntegrityAlgorithm dataIntegrityAlg;
    private byte foldinByte;
    private int dataExpansionBytes;
    private Ano ano;

    public CryptoNIONSDataChannel(SessionAtts sAtts) {
        super(sAtts);
        this.encryptionAlg = null;
        this.dataIntegrityAlg = null;
        this.foldinByte = (byte) 0;
        this.dataExpansionBytes = 0;
        this.ano = null;
        this.ano = sAtts.ano;
        if (sAtts.ano.encryptionAlg != null) {
            this.encryptionAlg = sAtts.ano.encryptionAlg;
            this.dataExpansionBytes += this.encryptionAlg.maxDelta();
        }
        if (sAtts.ano.dataIntegrityAlg != null) {
            this.dataIntegrityAlg = sAtts.ano.dataIntegrityAlg;
            this.dataExpansionBytes += this.dataIntegrityAlg.size();
        }
        this.dataExpansionBytes++;
        if (sAtts.isNetworkCompressionEnabled()) {
            initializeNetworkCompressionBuffers();
        }
    }

    @Override // oracle.net.ns.NIONSDataChannel
    public void readDataFromSocketChannel() throws Exception {
        super.readDataFromSocketChannel();
        this.ano = this.session.ano;
        this.dataExpansionBytes = 0;
        if (this.ano.encryptionAlg != null) {
            this.encryptionAlg = this.ano.encryptionAlg;
            this.dataExpansionBytes += this.encryptionAlg.maxDelta();
            if (this.ano.getRenewKey()) {
                this.encryptionAlg.setSessionKey(null, null);
            }
        }
        if (this.ano.dataIntegrityAlg != null) {
            this.dataIntegrityAlg = this.ano.dataIntegrityAlg;
            this.dataExpansionBytes += this.dataIntegrityAlg.size();
            if (this.ano.getRenewKey()) {
                this.dataIntegrityAlg.renew();
            }
        }
        this.dataExpansionBytes++;
        this.ano.setRenewKey(false);
        try {
            decryptAndChecksum();
        } catch (Exception e) {
            this.ano.checkForAnoNegotiationFailure();
            throw e;
        }
    }

    @Override // oracle.net.ns.NIONSDataChannel
    public void writeDataToSocketChannel(int dataFlags) throws IOException {
        try {
            if (this.foldinByte == 0) {
                this.foldinByte = this.ano.foldinKey();
            }
            checksumAndEncrypt();
            super.writeDataToSocketChannel(dataFlags);
        } catch (IOException e) {
            throw e;
        }
    }

    @Override // oracle.net.ns.NIONSDataChannel
    public int getDataExpansionByteSize() {
        return this.dataExpansionBytes;
    }

    @Override // oracle.net.ns.NIOPacket
    protected void processMarker() throws IOException {
        this.session.ano.setRenewKey(true);
    }

    protected void checksumAndEncrypt() throws IOException {
        int initialPosition = this.session.payloadDataBufferForWrite.position();
        byte[] clearBuffer = new byte[this.session.payloadDataBufferForWrite.position() - 0];
        this.session.payloadDataBufferForWrite.limit(this.session.payloadDataBufferForWrite.position());
        this.session.payloadDataBufferForWrite.position(0);
        this.session.payloadDataBufferForWrite.get(clearBuffer);
        this.session.payloadDataBufferForWrite.position(0);
        this.session.payloadDataBufferForWrite.limit(this.session.payloadDataBufferForWrite.capacity());
        tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "checksumAndEncrypt", "Packet size before encryption {0}.", "Packet size before encryption {0}. Packet Dump : \n{1}", null, () -> {
            if (isSensitiveEnabled()) {
                return new Object[]{Integer.valueOf(clearBuffer.length), Parameter.arg(Format.Style.PACKET_DUMP, ByteBuffer.wrap(clearBuffer), 0, clearBuffer.length)};
            }
            return new Object[]{Integer.valueOf(clearBuffer.length)};
        });
        byte[] checksum = null;
        int dataLen = initialPosition - 0;
        if (this.dataIntegrityAlg != null) {
            checksum = this.dataIntegrityAlg.compute(clearBuffer, clearBuffer.length);
            if (checksum != null) {
                dataLen += checksum.length;
            }
        }
        byte[] bufferToEncrypt = new byte[dataLen];
        System.arraycopy(clearBuffer, 0, bufferToEncrypt, 0, clearBuffer.length);
        if (checksum != null) {
            System.arraycopy(checksum, 0, bufferToEncrypt, clearBuffer.length, checksum.length);
        }
        if (this.encryptionAlg != null) {
            byte[] encryptedBuffer = this.encryptionAlg.encrypt(bufferToEncrypt);
            if (encryptedBuffer == null) {
                throw new IOException("Fail to encrypt buffer");
            }
            dataLen = encryptedBuffer.length;
            this.session.payloadDataBufferForWrite.put(encryptedBuffer);
        } else if (this.dataIntegrityAlg != null) {
            this.session.payloadDataBufferForWrite.put(bufferToEncrypt);
        }
        if (dataLen > 0) {
            this.session.payloadDataBufferForWrite.put(this.foldinByte);
        }
    }

    protected void decryptAndChecksum() throws IOException {
        byte[] clearBuffer;
        int initialPosition = this.session.payloadDataBufferForRead.position();
        ByteOrder originalOrder = this.session.payloadDataBufferForRead.order();
        this.session.payloadDataBufferForRead.order(ByteOrder.BIG_ENDIAN);
        int dataLen = this.session.payloadDataBufferForRead.limit();
        if (dataLen > 0) {
            this.session.payloadDataBufferForRead.position(dataLen - 1);
            this.session.payloadDataBufferForRead.get();
            this.session.payloadDataBufferForRead.position(initialPosition);
            this.session.payloadDataBufferForRead.order(originalOrder);
            dataLen--;
        }
        byte[] dataBuffer = new byte[dataLen];
        int initialLimit = this.session.payloadDataBufferForRead.limit();
        this.session.payloadDataBufferForRead.get(dataBuffer);
        this.session.payloadDataBufferForRead.position(initialPosition);
        this.session.payloadDataBufferForRead.limit(initialLimit);
        if (this.encryptionAlg != null && dataLen > 0) {
            clearBuffer = this.encryptionAlg.decrypt(dataBuffer);
        } else {
            clearBuffer = dataBuffer;
        }
        if (clearBuffer == null) {
            throw new IOException("Bad buffer - Fail to decrypt buffer");
        }
        int dataLen2 = clearBuffer.length;
        if (this.dataIntegrityAlg != null && dataLen2 > 0) {
            byte[] checksum = new byte[this.dataIntegrityAlg.size()];
            int dataLen3 = dataLen2 - this.dataIntegrityAlg.size();
            System.arraycopy(clearBuffer, dataLen3, checksum, 0, this.dataIntegrityAlg.size());
            byte[] xsumBuff = new byte[dataLen3];
            System.arraycopy(clearBuffer, 0, xsumBuff, 0, dataLen3);
            if (this.dataIntegrityAlg.compare(xsumBuff, checksum)) {
                throw new IOException("Checksum fail");
            }
            this.session.payloadDataBufferForRead = ByteBuffer.wrap(xsumBuff, 0, dataLen3);
            this.session.payloadDataBufferForRead.limit(dataLen3);
            this.session.payloadDataBufferForRead.order(originalOrder);
        } else {
            this.session.payloadDataBufferForRead = ByteBuffer.wrap(clearBuffer, 0, dataLen2);
            this.session.payloadDataBufferForRead.limit(dataLen2);
            this.session.payloadDataBufferForRead.order(originalOrder);
        }
        this.session.payloadDataBufferForRead.position(initialPosition);
        byte[] bArr = clearBuffer;
        tracep(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "decryptAndChecksum", "Packet size after decryption {0}.", "Packet size after decryption {0}. Packet Dump : \n{1}", null, () -> {
            if (isSensitiveEnabled()) {
                return new Object[]{Integer.valueOf(bArr.length), Parameter.arg(Format.Style.PACKET_DUMP, ByteBuffer.wrap(bArr), 0, bArr.length)};
            }
            return new Object[]{Integer.valueOf(bArr.length)};
        });
    }
}
