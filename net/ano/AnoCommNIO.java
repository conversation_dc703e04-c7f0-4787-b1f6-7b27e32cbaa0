package oracle.net.ano;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import oracle.net.ns.NIONSDataChannel;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/AnoCommNIO.class */
class AnoCommNIO extends AnoComm {
    private static final boolean DEBUG = false;
    private boolean bytesReadyToGo = false;
    NIONSDataChannel nsDataChannel;
    SessionAtts sAtts;

    public AnoCommNIO(SessionAtts sAtts) {
        this.sAtts = sAtts;
        this.nsDataChannel = sAtts.dataChannel;
    }

    private void prepareForUnmarshall() throws IOException {
        if (this.bytesReadyToGo) {
            flush();
        }
        if (this.sAtts.payloadDataBufferForRead.hasRemaining()) {
            return;
        }
        this.nsDataChannel.readDataFromSocketChannel();
    }

    private void prepareForMarshall(int numberBytesToBeWritten) throws IOException {
        if (this.sAtts.payloadDataBufferForWrite.remaining() < numberBytesToBeWritten) {
            if (this.bytesReadyToGo) {
                flush();
            }
            this.sAtts.prepareWriteBuffer();
        }
    }

    @Override // oracle.net.ano.AnoComm
    protected void flush() throws IOException {
        if (this.bytesReadyToGo) {
            this.nsDataChannel.writeDataToSocketChannel();
        }
        this.bytesReadyToGo = false;
    }

    @Override // oracle.net.ano.AnoComm
    protected void sendUB1(short num) throws IOException {
        sendPktHeader(1, 2);
        prepareForMarshall(1);
        this.sAtts.payloadDataBufferForWrite.put((byte) num);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected void sendString(String str) throws IOException {
        sendPktHeader(str.length(), 0);
        prepareForMarshall(str.length());
        this.sAtts.payloadDataBufferForWrite.put(str.getBytes(StandardCharsets.US_ASCII));
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected void sendRaw(byte[] rawdata) throws IOException {
        sendPktHeader(rawdata.length, 1);
        prepareForMarshall(rawdata.length);
        this.sAtts.payloadDataBufferForWrite.put(rawdata);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected void writeUB1(short num) throws IOException {
        prepareForMarshall(1);
        this.sAtts.payloadDataBufferForWrite.put((byte) num);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected void writeUB2(int value) throws IOException {
        byte[] tmpBuffer = new byte[2];
        byte bytes = value2Buffer((short) (65535 & value), tmpBuffer);
        prepareForMarshall(tmpBuffer.length);
        this.sAtts.payloadDataBufferForWrite.put(tmpBuffer, 0, bytes);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected void writeUB4(long value) throws IOException {
        byte[] tmpBuffer = new byte[4];
        byte bytes = value2Buffer((int) ((-1) & value), tmpBuffer);
        prepareForMarshall(tmpBuffer.length);
        this.sAtts.payloadDataBufferForWrite.put(tmpBuffer, 0, bytes);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.net.ano.AnoComm
    protected short readUB1() throws IOException {
        prepareForUnmarshall();
        short value = (short) (this.sAtts.payloadDataBufferForRead.get() & 255);
        return value;
    }

    @Override // oracle.net.ano.AnoComm
    protected byte[] receiveByteArray(int n) throws IOException {
        byte[] tmpBuffer = new byte[n];
        prepareForUnmarshall();
        this.sAtts.payloadDataBufferForRead.get(tmpBuffer);
        return tmpBuffer;
    }

    protected int receiveByteArray(byte[] buf, int off, int len) throws IOException {
        this.sAtts.payloadDataBufferForRead.get(buf, off, len);
        return 0;
    }

    @Override // oracle.net.ano.AnoComm
    protected long buffer2Value(byte[] tmpBuffer) throws IOException {
        long value = 0;
        prepareForUnmarshall();
        this.sAtts.payloadDataBufferForRead.get(tmpBuffer);
        for (int i = 0; i < tmpBuffer.length; i++) {
            value |= (tmpBuffer[i] & 255) << (8 * ((tmpBuffer.length - 1) - i));
        }
        return value & (-1);
    }
}
