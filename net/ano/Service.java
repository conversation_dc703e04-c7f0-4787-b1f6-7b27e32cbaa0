package oracle.net.ano;

import java.io.IOException;
import java.util.Vector;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/Service.class */
public abstract class Service implements SQLnetDef, Diagnosable {
    static final int AUTHENTICATION = 1;
    static final int ENCRYPTION = 2;
    static final int DATAINTEGRITY = 3;
    static final int SUPERVISOR = 4;
    static final String[] SERVICES_CLASSNAME = {"", "SupervisorService", "AuthenticationService", "EncryptionService", "DataIntegrityService"};
    protected Ano ano;
    protected AnoComm comm;
    protected SessionAtts sAtts;
    protected int level;
    protected int[] userChoiceDriversId;
    protected byte[] selectedDrivers;
    protected int service;
    protected long version;
    protected short algID;

    abstract void receiveServiceData(int i) throws IOException;

    abstract void validateResponse() throws IOException;

    int init(SessionAtts sAtts) throws NetException {
        this.sAtts = sAtts;
        this.ano = sAtts.ano;
        this.comm = sAtts.ano.anoComm;
        this.level = 0;
        this.selectedDrivers = new byte[0];
        return 1;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.sAtts.getDiagnosable();
    }

    void verifyInit(String[] drivers) throws NetException {
    }

    void sendHeader(int serviceSubPackets) throws IOException {
        this.comm.writeUB2(this.service);
        this.comm.writeUB2(serviceSubPackets);
        this.comm.writeUB4(0L);
    }

    void sendServiceData() throws IOException {
        sendHeader(2);
        this.comm.sendVersion();
        this.comm.sendRaw(this.selectedDrivers);
    }

    int getServiceDataLength() {
        return 12 + this.selectedDrivers.length;
    }

    void activateAlgorithm() throws IOException {
    }

    int howManyBytesNeeded() {
        return 8 + getServiceDataLength();
    }

    static int[] receiveHeader(AnoComm comm) throws IOException {
        int receivedServiceL = comm.readUB2();
        int numSubPacketsL = comm.readUB2();
        int oracleErrorL = (int) comm.readUB4();
        int[] ret = {receivedServiceL, numSubPacketsL, oracleErrorL};
        return ret;
    }

    void receiveSelection(int numSubPackets) throws IOException {
        receiveServiceData(numSubPackets);
        validateResponse();
    }

    int[] createDriversListWithLevel(int[] userChoiceDriversId, int level) throws NetException {
        int[] ret;
        switch (level) {
            case 0:
                ret = new int[userChoiceDriversId.length + 1];
                ret[0] = 0;
                for (int i = 0; i < userChoiceDriversId.length; i++) {
                    ret[i + 1] = userChoiceDriversId[i];
                }
                break;
            case 1:
                ret = new int[]{0};
                break;
            case 2:
                ret = new int[userChoiceDriversId.length + 1];
                int i2 = 0;
                while (i2 < userChoiceDriversId.length) {
                    ret[i2] = userChoiceDriversId[i2];
                    i2++;
                }
                ret[i2] = 0;
                break;
            case 3:
                ret = userChoiceDriversId;
                break;
            default:
                throw new NetException(NetException.INVALID_ENCRYPTION_PARAMETER);
        }
        return ret;
    }

    String[] getValidUserChoices(String[] userList, String[] availList) throws NetException {
        if (userList == null || userList.length == 0) {
            if (availList[0] == "") {
                userList = new String[availList.length - 1];
                for (int i = 0; i < userList.length; i++) {
                    userList[i] = availList[i + 1];
                }
            } else {
                userList = availList;
            }
        }
        Vector vec = new Vector(10);
        for (int i2 = 0; i2 < userList.length; i2++) {
            if (userList[i2].equals("")) {
                throw new NetException(NetException.UNKNOWN_ALGORITHM_12649);
            }
            int j = 0;
            while (true) {
                if (j >= availList.length) {
                    break;
                }
                if (!availList[j].equalsIgnoreCase(userList[i2])) {
                    j++;
                } else {
                    vec.addElement(availList[j]);
                    break;
                }
            }
            if (j == availList.length) {
                throw new NetException(NetException.UNKNOWN_ALGORITHM_12649);
            }
        }
        int vec_size = vec.size();
        String[] finalList = new String[vec_size];
        for (int i3 = 0; i3 < vec_size; i3++) {
            finalList[i3] = (String) vec.elementAt(i3);
        }
        return finalList;
    }

    byte getDriverID(String[] driverClasses, String str) throws NetException {
        byte b = 0;
        while (true) {
            byte i = b;
            if (i < driverClasses.length) {
                if (!str.equals(driverClasses[i])) {
                    b = (byte) (i + 1);
                } else {
                    return i;
                }
            } else {
                throw new NetException(NetException.INVALID_DRIVER);
            }
        }
    }

    public static String getLevelString(int level) throws NetException {
        String sLevel;
        switch (level) {
            case 0:
                sLevel = AnoServices.ANO_ACCEPTED;
                break;
            case 1:
                sLevel = AnoServices.ANO_REJECTED;
                break;
            case 2:
                sLevel = AnoServices.ANO_REQUESTED;
                break;
            case 3:
                sLevel = AnoServices.ANO_REQUIRED;
                break;
            default:
                throw new NetException(NetException.INVALID_LEVEL);
        }
        return sLevel;
    }

    public static String getServiceName(int service) throws NetException {
        String serviceName;
        switch (service) {
            case 1:
                serviceName = "AUTHENTICATION";
                break;
            case 2:
                serviceName = "ENCRYPTION";
                break;
            case 3:
                serviceName = "DATAINTEGRITY";
                break;
            case 4:
                serviceName = "SUPERVISOR";
                break;
            default:
                throw new NetException(NetException.INVALID_SERVICE);
        }
        return serviceName;
    }

    public boolean isActive() {
        return false;
    }
}
