package oracle.net.ano;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.DigestException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.function.Function;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.internal.OpaqueString;
import oracle.net.aso.Radius;
import oracle.net.ns.ClientProfile;
import oracle.net.ns.NetException;
import oracle.net.ns.SessionAtts;
import oracle.net.nt.NTAdapter;

/* loaded from: ojdbc8.jar:oracle/net/ano/RadiusAuthenticationService.class */
class RadiusAuthenticationService {
    private static final byte NAURA_ACCESS_REQUEST = 1;
    private static final byte NAURA_ACCESS_ACCEPT = 2;
    private static final byte NAURA_ACCESS_REJECT = 3;
    private static final byte NAURA_ACCESS_CHALLENGE = 11;
    private static final Charset TARGET_CHARSET = StandardCharsets.UTF_8;
    private static final int HEADER_LENGTH = 21;
    private final SessionAtts session;
    private final AnoComm anoComm;
    private final Ano ano;
    private ClientProfile profile;
    private final AuthenticationService authService;
    private final Function<byte[], byte[]> authHandler = getRadiusChallegeResponseHandler();
    private boolean isTCPS;
    private final String username;
    private final OpaqueString password;

    RadiusAuthenticationService(SessionAtts session, AuthenticationService authService) throws NetException {
        this.session = session;
        this.anoComm = session.ano.anoComm;
        this.ano = session.ano;
        this.profile = session.profile;
        this.authService = authService;
        this.isTCPS = session.getNTAdapter().getNetworkAdapterType().equals(NTAdapter.NetworkAdapterType.TCPS);
        this.username = session.profile.getProperty(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_USER);
        this.password = (OpaqueString) session.profile.get(AnoServices.AUTHENTICATION_PROPERTY_RADIUS_PWD);
    }

    void handleRadiusAuthentication() throws IOException {
        if (this.authHandler != null) {
            handleCR();
        } else {
            this.anoComm.readUB2();
            this.anoComm.readUB2();
        }
    }

    private void handleCR() throws NoSuchAlgorithmException, DigestException, IOException {
        if (negotiateCRMode()) {
            if (this.username == null || this.password == null) {
                throw new NetException(NetException.AUTHENTICATION_STATUS_FAILURE, "RADIUS authentication failed. Username or Password is null.");
            }
            authChallengeReponse(authUserPassword());
            this.ano.setTwoFactorAuthenticationComplete(true);
        }
    }

    private boolean negotiateCRMode() throws IOException {
        short isCRSupported = this.ano.anoComm.receiveUB1();
        this.ano.sendANOHeader(26, 1, (short) 0);
        this.authService.sendHeader(1);
        this.ano.anoComm.sendUB1(isCRSupported);
        this.ano.receiveANOHeader();
        receiveHeader();
        this.ano.anoComm.receiveRaw();
        return isCRSupported == 1;
    }

    private byte[] authUserPassword() throws NoSuchAlgorithmException, DigestException, IOException {
        byte[] userNameBytes = this.username.getBytes(TARGET_CHARSET);
        byte[] pwdBytes = this.password.get().getBytes(TARGET_CHARSET);
        if (!this.isTCPS) {
            pwdBytes = obfuscatePassword(pwdBytes);
        }
        byte[] usrPwd = new byte[2 + userNameBytes.length + pwdBytes.length];
        usrPwd[0] = (byte) userNameBytes.length;
        System.arraycopy(userNameBytes, 0, usrPwd, 1, userNameBytes.length);
        usrPwd[userNameBytes.length + 1] = (byte) pwdBytes.length;
        System.arraycopy(pwdBytes, 0, usrPwd, userNameBytes.length + 2, pwdBytes.length);
        int packetLength = 25 + usrPwd.length;
        this.ano.sendANOHeader(packetLength, 1, (short) 0);
        this.authService.sendHeader(1);
        this.ano.anoComm.sendRaw(usrPwd);
        this.ano.anoComm.flush();
        this.ano.receiveANOHeader();
        receiveHeader();
        byte[] serverResponse = this.ano.anoComm.receiveRaw();
        verifyResponse(serverResponse, 11);
        return Arrays.copyOfRange(serverResponse, 1, serverResponse.length);
    }

    private void authChallengeReponse(byte[] pwdHint) throws NoSuchAlgorithmException, DigestException, IOException {
        byte[] userNameBytes = this.username.getBytes(TARGET_CHARSET);
        byte[] challengeResponse = this.authHandler.apply(pwdHint);
        if (!this.isTCPS) {
            challengeResponse = obfuscatePassword(challengeResponse);
        }
        byte[] userChallengeResponse = new byte[2 + userNameBytes.length + challengeResponse.length];
        userChallengeResponse[0] = (byte) userNameBytes.length;
        System.arraycopy(userNameBytes, 0, userChallengeResponse, 1, userNameBytes.length);
        userChallengeResponse[userNameBytes.length + 1] = (byte) challengeResponse.length;
        System.arraycopy(challengeResponse, 0, userChallengeResponse, userNameBytes.length + 2, challengeResponse.length);
        int packetLength = 25 + userChallengeResponse.length;
        this.ano.sendANOHeader(packetLength, 1, (short) 0);
        this.authService.sendHeader(1);
        this.ano.anoComm.sendRaw(userChallengeResponse);
        this.ano.anoComm.flush();
        this.ano.receiveANOHeader();
        receiveHeader();
        byte[] crResultRaw = this.ano.anoComm.receiveRaw();
        verifyResponse(crResultRaw, 2);
    }

    private void verifyResponse(byte[] response, int expectedStatus) throws NetException {
        byte b = response[0];
        String errMsg = response.length > 1 ? new String(response, 1, response.length - 1, TARGET_CHARSET) : null;
        if (b != expectedStatus) {
            if (errMsg == null || errMsg.isEmpty()) {
                errMsg = "-" + (b == 3 ? " Access-Reject " : " ") + "error during RADIUS exchange (Error Status = " + ((int) b) + ")";
            }
            throw new NetException(NetException.AUTHENTICATION_STATUS_FAILURE, errMsg);
        }
    }

    private Function<byte[], byte[]> getRadiusChallegeResponseHandler() throws NetException {
        Object challengeResponseHandler = this.profile.get(OracleConnection.CONNECTION_PROPERTY_THIN_NET_RADIUS_CHALLENGE_RESPONSE_HANDLER);
        if (challengeResponseHandler == null) {
            return null;
        }
        if (challengeResponseHandler instanceof String) {
            try {
                return (Function) Class.forName((String) challengeResponseHandler).newInstance();
            } catch (Exception e) {
                NetException ex = new NetException(NetException.AUTHENTICATION_STATUS_FAILURE, "Unable to initialize Radius Authentication Handler " + challengeResponseHandler);
                ex.initCause(e);
                throw ex;
            }
        }
        if (challengeResponseHandler instanceof Function) {
            return (Function) challengeResponseHandler;
        }
        throw new NetException(NetException.AUTHENTICATION_STATUS_FAILURE, "Unable to initialize Radius Authentication Handler. Invalid Type " + challengeResponseHandler.getClass());
    }

    private void receiveHeader() throws IOException {
        int[] headerPkts = Service.receiveHeader(this.ano.anoComm);
        if (headerPkts[2] != 0) {
            throw new NetException(headerPkts[2]);
        }
    }

    private byte[] obfuscatePassword(byte[] pwd) throws NoSuchAlgorithmException, DigestException {
        int i;
        int i2;
        byte[] opwd = Radius.obfuscatePassword(pwd);
        byte[] password = new byte[opwd.length * 2];
        for (int i3 = 0; i3 < opwd.length; i3++) {
            byte b1 = (byte) ((opwd[i3] & 240) >> 4);
            byte b2 = (byte) (opwd[i3] & 15);
            password[i3 * 2] = (byte) (b1 < 10 ? b1 + 48 : (b1 - 10) + 97);
            int i4 = (i3 * 2) + 1;
            if (b2 < 10) {
                i = b2;
                i2 = 48;
            } else {
                i = b2 - 10;
                i2 = 97;
            }
            password[i4] = (byte) (i + i2);
        }
        return password;
    }
}
