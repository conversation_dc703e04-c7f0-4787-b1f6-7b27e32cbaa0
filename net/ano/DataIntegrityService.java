package oracle.net.ano;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.aso.DataIntegrityAlgorithm;
import oracle.net.aso.Di<PERSON>ie<PERSON><PERSON>man;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/DataIntegrityService.class */
public class DataIntegrityService extends Service implements SQLnetDef {
    private static final String CLASS_NAME = DataIntegrityService.class.getName();
    static final String[] DATAINTEGRITY_JAVA_ANO_ID = {"", AnoServices.CHECKSUM_SHA1, AnoServices.CHECKSUM_SHA512, AnoServices.CHECKSUM_SHA256, AnoServices.CHECKSUM_SHA384};
    private static final byte[] DATAINTEGRITY_ORACLE_ID = {0, 3, 4, 5, 6};
    private boolean checkSummingActivated = false;
    private byte[] clientPK;
    static final int NUM_DATAINTEGRITY_SUBPACKETS = 2;

    @Override // oracle.net.ano.Service
    int init(SessionAtts sAtts) throws NetException {
        super.init(sAtts);
        this.service = 3;
        this.level = sAtts.profile.getDataIntegrityLevelNum();
        String[] userChoiceDrivers = getValidUserChoices(removeWeakChecksumDrivers(sAtts.profile.getDataIntegrityServices()), DATAINTEGRITY_JAVA_ANO_ID);
        this.userChoiceDriversId = new int[userChoiceDrivers.length];
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            this.userChoiceDriversId[i] = getDriverID(DATAINTEGRITY_JAVA_ANO_ID, userChoiceDrivers[i]);
        }
        this.userChoiceDriversId = createDriversListWithLevel(this.userChoiceDriversId, this.level);
        this.selectedDrivers = new byte[this.userChoiceDriversId.length];
        for (int i2 = 0; i2 < this.selectedDrivers.length; i2++) {
            this.selectedDrivers[i2] = DATAINTEGRITY_ORACLE_ID[this.userChoiceDriversId[i2]];
        }
        int flags = 1;
        if (this.userChoiceDriversId.length == 0) {
            if (this.level == 3) {
                throw new NetException(NetException.UNKNOWN_ENC_OR_DATAINT_ALGORITHM);
            }
            flags = 1 | 8;
        } else if (this.level == 3) {
            flags = 1 | 16;
        }
        return flags;
    }

    private String[] removeWeakChecksumDrivers(String[] userChoiceDrivers) throws NetException {
        if (userChoiceDrivers == null || userChoiceDrivers.length == 0) {
            return userChoiceDrivers;
        }
        List<String> strongChecksumDrivers = new ArrayList<>();
        for (String driver : userChoiceDrivers) {
            if (!AnoServices.CHECKSUM_MD5.equalsIgnoreCase(driver)) {
                strongChecksumDrivers.add(driver);
            }
        }
        if (this.level != 1 && strongChecksumDrivers.size() == 0) {
            throw new NetException(NetException.UNKNOWN_ALGORITHM_12649, (String) this.sAtts.profile.get("oracle.net.crypto_checksum_types_client"));
        }
        return (String[]) strongChecksumDrivers.toArray(new String[strongChecksumDrivers.size()]);
    }

    @Override // oracle.net.ano.Service
    void receiveServiceData(int numSubPackets) throws IOException {
        this.version = this.comm.receiveVersion();
        this.sAtts.profile.setANOVersion(this.version);
        int receiveDriverId = this.comm.receiveUB1();
        this.algID = (short) -1;
        for (int i = 0; i < DATAINTEGRITY_JAVA_ANO_ID.length; i++) {
            if (DATAINTEGRITY_ORACLE_ID[i] == receiveDriverId) {
                this.algID = (short) i;
            }
        }
        if (numSubPackets != 2 && numSubPackets == 8) {
            short ebits = (short) this.comm.receiveUB2();
            short mbits = (short) this.comm.receiveUB2();
            byte[] base = this.comm.receiveRaw();
            byte[] modulus = this.comm.receiveRaw();
            byte[] svrPK = this.comm.receiveRaw();
            byte[] iv = this.comm.receiveRaw();
            if (ebits <= 0 || mbits <= 0) {
                throw new IOException("Bad parameters from server");
            }
            int key_size = (mbits + 7) / 8;
            if (svrPK.length != key_size || modulus.length != key_size) {
                throw new IOException("DiffieHellman negotiation out of synch");
            }
            if (!this.sAtts.profile.isWeakCryptoEnabled() && (modulus.length < 256 || base.length < 256 || svrPK.length < 256)) {
                debug(Level.WARNING, SecurityLabel.INTERNAL, CLASS_NAME, "receiveServiceData", "Received weak DiffieHellman initialization parameters from server.", null, null);
                throw new NetException(NetException.SERVER_CRYPTO_VERSION_MISMATCH);
            }
            DiffieHellman dh = DiffieHellman.newInstance(base, modulus, ebits, mbits, this.sAtts.profile.isFIPSMode());
            this.clientPK = dh.getPublicKey(this.sAtts.profile.useWeakCrypto());
            this.sAtts.ano.setClientPK(this.clientPK);
            byte[] dhSessionKey = dh.getSessionKey(svrPK, svrPK.length);
            this.sAtts.ano.setSessionKey(dhSessionKey);
            this.sAtts.ano.setInitializationVector(iv);
        }
        this.checkSummingActivated = this.algID > 0;
    }

    @Override // oracle.net.ano.Service
    void validateResponse() throws IOException {
        if (this.algID < 0) {
            throw new NetException(NetException.INVALID_DATAINTEGRITY_ALGORITHM_FROM_SERVER);
        }
        if (this.checkSummingActivated) {
            if (this.sAtts.profile.isServerUsingWeakCrypto() && !this.sAtts.profile.isWeakCryptoEnabled()) {
                throw new NetException(NetException.SERVER_CRYPTO_VERSION_MISMATCH);
            }
        } else if (this.level == 3) {
            throw new NetException(NetException.INCOMPLETE_SERVICES_FROM_SERVER, "Checksumming is REQUIRED but activation failed.");
        }
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            if (this.userChoiceDriversId[i] == this.algID) {
                return;
            }
        }
        throw new NetException(NetException.INVALID_DATAINTEGRITY_ALGORITHM_FROM_SERVER);
    }

    @Override // oracle.net.ano.Service
    public boolean isActive() {
        return this.checkSummingActivated;
    }

    @Override // oracle.net.ano.Service
    void activateAlgorithm() throws IOException {
        if (this.checkSummingActivated) {
            this.ano.dataIntegrityAlg = new DataIntegrityAlgorithm(this.ano.getSessionKey(), this.ano.getInitializationVector(), this.ano.getDataIntegrityName(), this.sAtts.profile.useWeakCrypto());
            this.sAtts.isChecksumActive = true;
        }
    }

    public static void printInHex(int value) {
        byte[] hexValue = toHex(value);
        System.out.print(new String(hexValue));
    }

    public static byte[] toHex(int value) {
        byte[] hex = new byte[8];
        for (int i = 8 - 1; i >= 0; i--) {
            hex[i] = nibbleToHex((byte) (value & 15));
            value >>= 4;
        }
        return hex;
    }

    public static byte nibbleToHex(byte nibble) {
        byte nibble2 = (byte) (nibble & 15);
        return (byte) (nibble2 < 10 ? nibble2 + 48 : (nibble2 - 10) + 65);
    }

    public static String bArray2String(byte[] array) {
        StringBuffer result = new StringBuffer(array.length * 2);
        for (int i = 0; i < array.length; i++) {
            result.append((char) nibbleToHex((byte) ((array[i] & 240) >> 4)));
            result.append((char) nibbleToHex((byte) (array[i] & 15)));
        }
        return result.toString();
    }
}
