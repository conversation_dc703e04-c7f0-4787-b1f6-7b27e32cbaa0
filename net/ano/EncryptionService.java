package oracle.net.ano;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import oracle.net.aso.EncryptionAlgorithm;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/EncryptionService.class */
public class EncryptionService extends Service implements SQLnetDef {
    static final String[] ENCRYPTION_JAVA_ANO_ID = {"", AnoServices.ENCRYPTION_AES128, AnoServices.ENCRYPTION_AES192, AnoServices.ENCRYPTION_AES256};
    private final byte[] ENCRYPTION_ORACLE_ID = {0, 15, 16, 17};
    private boolean encryptionActivated = false;
    static final int NUM_ENCRYPTION_SUBPACKETS = 2;

    @Override // oracle.net.ano.Service
    int init(SessionAtts sAtts) throws NetException {
        super.init(sAtts);
        this.service = 2;
        this.level = sAtts.profile.getEncryptionLevelNum();
        String[] userChoiceDrivers = getValidUserChoices(removeWeakEncryptionDrivers(sAtts.profile.getEncryptionServices()), ENCRYPTION_JAVA_ANO_ID);
        this.userChoiceDriversId = new int[userChoiceDrivers.length];
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            this.userChoiceDriversId[i] = getDriverID(ENCRYPTION_JAVA_ANO_ID, userChoiceDrivers[i]);
        }
        this.userChoiceDriversId = createDriversListWithLevel(this.userChoiceDriversId, this.level);
        this.selectedDrivers = new byte[this.userChoiceDriversId.length];
        for (int i2 = 0; i2 < this.selectedDrivers.length; i2++) {
            this.selectedDrivers[i2] = this.ENCRYPTION_ORACLE_ID[this.userChoiceDriversId[i2]];
        }
        int flags = 1;
        if (this.userChoiceDriversId.length == 0) {
            if (this.level == 3) {
                throw new NetException(NetException.UNKNOWN_ENC_OR_DATAINT_ALGORITHM);
            }
            flags = 1 | 8;
        } else if (this.level == 3) {
            flags = 1 | 16;
        }
        return flags;
    }

    @Override // oracle.net.ano.Service
    public boolean isActive() {
        return this.encryptionActivated;
    }

    @Override // oracle.net.ano.Service
    void receiveServiceData(int numSubPackets) throws IOException {
        if (numSubPackets != 2) {
            throw new NetException(NetException.WRONG_SERVICE_SUBPACKETS);
        }
        this.version = this.comm.receiveVersion();
        this.sAtts.profile.setANOVersion(this.version);
        int receiveDriverId = this.comm.receiveUB1();
        this.algID = (short) -1;
        for (int i = 0; i < ENCRYPTION_JAVA_ANO_ID.length; i++) {
            if (this.ENCRYPTION_ORACLE_ID[i] == receiveDriverId) {
                this.algID = (short) i;
            }
        }
        this.encryptionActivated = this.algID > 0;
    }

    @Override // oracle.net.ano.Service
    void validateResponse() throws IOException {
        if (this.algID < 0) {
            throw new NetException(NetException.INVALID_ENCRYPTION_ALGORITHM_FROM_SERVER);
        }
        if (this.encryptionActivated) {
            if (this.sAtts.profile.isServerUsingWeakCrypto() && !this.sAtts.profile.isWeakCryptoEnabled()) {
                throw new NetException(NetException.SERVER_CRYPTO_VERSION_MISMATCH);
            }
        } else if (this.level == 3) {
            throw new NetException(NetException.INCOMPLETE_SERVICES_FROM_SERVER, "Encryption is REQUIRED but activation failed.");
        }
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            if (this.userChoiceDriversId[i] == this.algID) {
                return;
            }
        }
        throw new NetException(NetException.INVALID_ENCRYPTION_ALGORITHM_FROM_SERVER);
    }

    @Override // oracle.net.ano.Service
    void activateAlgorithm() throws IOException {
        if (this.encryptionActivated) {
            this.ano.encryptionAlg = EncryptionAlgorithm.newInstance(ENCRYPTION_JAVA_ANO_ID[this.algID], this.ano.getSessionKey(), this.ano.getInitializationVector(), this.sAtts.profile.useWeakCrypto());
            this.sAtts.isEncryptionActive = true;
        }
    }

    private String[] removeWeakEncryptionDrivers(String[] userChoiceDrivers) throws NetException {
        if (userChoiceDrivers == null || userChoiceDrivers.length == 0) {
            return userChoiceDrivers;
        }
        List<String> strongCryptoDrivers = new ArrayList<>();
        for (String driver : userChoiceDrivers) {
            if (!isWeakEncryptionDriver(driver)) {
                strongCryptoDrivers.add(driver);
            }
        }
        if (this.level != 1 && strongCryptoDrivers.size() == 0) {
            throw new NetException(NetException.UNKNOWN_ALGORITHM_12649, (String) this.sAtts.profile.get("oracle.net.encryption_types_client"));
        }
        return (String[]) strongCryptoDrivers.toArray(new String[strongCryptoDrivers.size()]);
    }

    private boolean isWeakEncryptionDriver(String driver) {
        if (AnoServices.ENCRYPTION_AES128.equalsIgnoreCase(driver) || AnoServices.ENCRYPTION_AES192.equalsIgnoreCase(driver) || AnoServices.ENCRYPTION_AES256.equalsIgnoreCase(driver)) {
            return false;
        }
        return true;
    }
}
