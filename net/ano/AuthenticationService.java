package oracle.net.ano;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.security.AccessControlContext;
import java.security.AccessController;
import java.security.Principal;
import java.security.PrivilegedActionException;
import java.security.PrivilegedExceptionAction;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.logging.Level;
import javax.security.auth.Subject;
import javax.security.auth.callback.Callback;
import javax.security.auth.callback.CallbackHandler;
import javax.security.auth.callback.NameCallback;
import javax.security.auth.callback.PasswordCallback;
import javax.security.auth.kerberos.KerberosPrincipal;
import javax.security.auth.kerberos.KerberosTicket;
import javax.security.auth.login.AppConfigurationEntry;
import javax.security.auth.login.Configuration;
import javax.security.auth.login.LoginContext;
import javax.security.auth.login.LoginException;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.OpaqueString;
import oracle.net.aso.Radius;
import oracle.net.ns.NetException;
import oracle.net.ns.SessionAtts;
import org.ietf.jgss.GSSContext;
import org.ietf.jgss.GSSCredential;
import org.ietf.jgss.GSSException;
import org.ietf.jgss.GSSManager;
import org.ietf.jgss.GSSName;
import org.ietf.jgss.Oid;
import sun.security.krb5.Asn1Exception;
import sun.security.krb5.Checksum;
import sun.security.krb5.EncryptedData;
import sun.security.krb5.EncryptionKey;
import sun.security.krb5.KrbCryptoException;
import sun.security.krb5.RealmException;
import sun.security.krb5.internal.APReq;
import sun.security.krb5.internal.Authenticator;
import sun.security.krb5.internal.KRBCred;
import sun.security.krb5.internal.KdcErrException;
import sun.security.krb5.internal.KrbApErrException;

/* loaded from: ojdbc8.jar:oracle/net/ano/AuthenticationService.class */
public class AuthenticationService extends Service implements PrivilegedExceptionAction {
    private static final String CLASS_NAME = AuthenticationService.class.getName();
    static final String[] AUTH_JAVA_ANO_ID = {"", AnoServices.AUTHENTICATION_RADIUS, AnoServices.AUTHENTICATION_KERBEROS5, "TCPS", "BEQ"};
    private static final String[] AUTH_ORACLE_NAME = {"", AnoServices.AUTHENTICATION_RADIUS, AnoServices.AUTHENTICATION_KERBEROS5, "tcps", "beq"};
    private static final byte[] AUTH_ORACLE_ID = {0, 1, 1, 2, 2};
    private static Method resetMethod = null;
    private static Method getEncodedMethod = null;
    private static boolean isExKrbSupportedAvailable;
    private static boolean isInternalSunAPIAvailable;
    private int status;
    static final int NAU_OK = 64255;
    static final int NAU_DONT_USE_AUTH = 64511;
    static final int NAU_AUTH_NOT_REQUIRED = 64767;
    static final int NAU_AUTH_REQUIRED = 65023;
    static final int NAU_NO_DRIVERS_LINKED_IN = 65279;
    static final int NAU_USE_IMPLICIT_AUTH = 63999;
    static final int NAU_PROXY_NO_AUTH = 63743;
    static final int NAU_AUTH_DISABLED = 63487;
    static final int NAUCX_CLIENT_SERVER = 57569;
    private boolean authenticationActivated = false;
    private Subject jdbcUserSubject = null;
    private String servicePrincipal4Kerberos = null;
    private String servicePrincipal4KerberosNTFormat = null;
    private String mapRealm = null;
    private GSSCredential userGSSCredential = null;

    static {
        isExKrbSupportedAvailable = false;
        isInternalSunAPIAvailable = true;
        try {
            Class.forName("javax.security.auth.kerberos.KerberosCredMessage");
            isExKrbSupportedAvailable = true;
        } catch (Exception e) {
            isExKrbSupportedAvailable = false;
        }
        try {
            Class.forName("sun.security.krb5.internal.APReq");
            isInternalSunAPIAvailable = true;
        } catch (Exception e2) {
            isInternalSunAPIAvailable = false;
        }
    }

    @Override // oracle.net.ano.Service
    int init(SessionAtts sAtts) throws NetException {
        super.init(sAtts);
        this.service = 1;
        this.status = NAU_AUTH_NOT_REQUIRED;
        CommonDiagnosable.getInstance().debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "init", "Entering init for Authentication, service={0} status={1}", (String) null, null, Integer.valueOf(this.service), Integer.valueOf(this.status));
        String[] userChoiceDrivers = sAtts.profile.getAuthenticationServices();
        getValidUserChoices(userChoiceDrivers, AUTH_JAVA_ANO_ID);
        this.userChoiceDriversId = new int[userChoiceDrivers.length];
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            this.userChoiceDriversId[i] = getDriverID(AUTH_JAVA_ANO_ID, userChoiceDrivers[i]);
        }
        return 1;
    }

    @Override // oracle.net.ano.Service
    void sendServiceData() throws IOException {
        int nbOfSubPack = 3 + (this.userChoiceDriversId.length * 2);
        sendHeader(nbOfSubPack);
        this.comm.sendVersion();
        this.comm.sendUB2(NAUCX_CLIENT_SERVER);
        this.comm.sendStatus(this.status);
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            this.comm.sendUB1(AUTH_ORACLE_ID[this.userChoiceDriversId[i]]);
            this.comm.sendString(AUTH_ORACLE_NAME[this.userChoiceDriversId[i]]);
        }
    }

    @Override // oracle.net.ano.Service
    int getServiceDataLength() {
        int len = 20;
        for (int i = 0; i < this.userChoiceDriversId.length; i++) {
            len = len + 5 + 4 + AUTH_ORACLE_NAME[this.userChoiceDriversId[i]].length();
        }
        return len;
    }

    @Override // oracle.net.ano.Service
    void receiveServiceData(int numSubPackets) throws IOException {
        this.version = this.comm.receiveVersion();
        this.sAtts.profile.setANOVersion(this.version);
        int statusReceived = this.comm.receiveStatus();
        if (statusReceived == NAU_OK && numSubPackets > 2) {
            this.comm.receiveUB1();
            String r2 = this.comm.receiveString();
            this.algID = getDriverID(AUTH_ORACLE_NAME, r2);
            if (numSubPackets > 4) {
                this.comm.receiveVersion();
                this.comm.receiveUB4();
                this.comm.receiveUB4();
            }
            this.authenticationActivated = true;
            return;
        }
        if (statusReceived == NAU_DONT_USE_AUTH) {
            this.authenticationActivated = false;
            return;
        }
        throw new NetException(NetException.INVALID_SERVICE, "Authentication service received status failure");
    }

    @Override // oracle.net.ano.Service
    public boolean isActive() {
        return this.authenticationActivated;
    }

    byte[] getSessionKey() {
        if (this.jdbcUserSubject == null) {
            return null;
        }
        return (byte[]) Subject.doAs(this.jdbcUserSubject, () -> {
            byte[] sKey = null;
            KerberosTicket kt = getKerberosTicket();
            if (kt != null) {
                sKey = kt.getSessionKey().getEncoded();
            }
            return sKey;
        });
    }

    private KerberosTicket getKerberosTicket() {
        if (this.jdbcUserSubject != null) {
            for (Object ticket : this.jdbcUserSubject.getPrivateCredentials()) {
                if (ticket instanceof KerberosTicket) {
                    KerberosTicket kerbTicket = (KerberosTicket) ticket;
                    String serverPrincipalName = kerbTicket.getServer().getName();
                    if (serverPrincipalName.startsWith(this.servicePrincipal4Kerberos) || serverPrincipalName.startsWith(this.servicePrincipal4KerberosNTFormat)) {
                        return kerbTicket;
                    }
                }
            }
            return null;
        }
        return null;
    }

    int bytesNeededForActivationPhase1() {
        if (isActive()) {
            if (this.algID == 1) {
                return 32;
            }
            if (this.algID == 2) {
                return 37;
            }
            return 0;
        }
        return 0;
    }

    void activateAuthenticatorPhase1() throws IOException {
        if (this.authenticationActivated) {
            if (this.algID == 1) {
                sendHeader(3);
                this.comm.sendVersion();
                this.comm.sendUB4(2L);
                this.comm.sendUB4(2L);
                return;
            }
            if (this.algID == 2) {
                sendHeader(4);
                this.comm.sendVersion();
                this.comm.sendUB4(2L);
                this.comm.sendUB4(2L);
                this.comm.sendUB1((short) 0);
            }
        }
    }

    void activateAuthenticatorPhase2(GSSCredential gssCredential) throws PrivilegedActionException, IOException {
        NetException netex;
        if (this.authenticationActivated) {
            this.sAtts.ano.receiveANOHeader();
            int[] serviceHeader = Service.receiveHeader(this.comm);
            if (serviceHeader[2] != 0) {
                throw new NetException(serviceHeader[2]);
            }
            if (this.algID == 1) {
                RadiusAuthenticationService radiusAuthService = new RadiusAuthenticationService(this.sAtts, this);
                radiusAuthService.handleRadiusAuthentication();
                return;
            }
            if (this.algID == 2) {
                String servicePrincipal4Kerberos1 = this.comm.receiveString();
                String serviceHostname4Kerberos2 = this.comm.receiveString();
                this.servicePrincipal4Kerberos = servicePrincipal4Kerberos1 + "/" + serviceHostname4Kerberos2;
                this.servicePrincipal4KerberosNTFormat = servicePrincipal4Kerberos1 + "@" + serviceHostname4Kerberos2;
                try {
                    String canonicalized = InetAddress.getByName(serviceHostname4Kerberos2).getCanonicalHostName();
                    if (canonicalized.toLowerCase().startsWith(serviceHostname4Kerberos2.toLowerCase() + OracleConnection.CLIENT_INFO_KEY_SEPARATOR)) {
                    }
                } catch (UnknownHostException e) {
                    serviceHostname4Kerberos2.toLowerCase();
                }
                this.mapRealm = (String) this.sAtts.profile.get(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_REALM);
                if (this.mapRealm != null && this.mapRealm.indexOf(64) != -1) {
                    this.mapRealm = this.mapRealm.substring(this.mapRealm.indexOf(64));
                }
                this.userGSSCredential = gssCredential;
                AccessControlContext currentControlContext = AccessController.getContext();
                if (this.userGSSCredential == null) {
                    if (currentControlContext != null) {
                        this.jdbcUserSubject = Subject.getSubject(currentControlContext);
                    }
                    if (this.jdbcUserSubject == null) {
                        this.jdbcUserSubject = jaasKerberosAuthenticateUsingCacheOnly();
                    }
                } else {
                    this.jdbcUserSubject = new Subject();
                }
                try {
                    Subject.doAs(this.jdbcUserSubject, this);
                } catch (PrivilegedActionException e2) {
                    Exception originalException = e2.getException();
                    if (originalException instanceof NetException) {
                        netex = (NetException) originalException;
                    } else {
                        netex = new NetException(NetException.INVALID_SERVICE, e2.getMessage());
                        netex.initCause(e2);
                    }
                    throw netex;
                }
            }
        }
    }

    private final Subject jaasKerberosAuthenticateUsingCacheOnly() throws NetException {
        String configModuleName = this.sAtts.profile.getProperty(OracleConnection.CONNECTION_PROPERTY_THIN_NET_AUTHENTICATION_KRB_JAAS_LOGIN_MODULE);
        try {
            if (configModuleName == null) {
                return getSubject(getDefaultJAASConfig(), "defaultModule");
            }
            return getSubject(Configuration.getConfiguration(), configModuleName);
        } catch (Exception e) {
            NetException nete = new NetException(NetException.AUTHENTICATION_KERBEROS5_FAILURE);
            nete.initCause(e);
            throw nete;
        }
    }

    private Subject getSubject(Configuration config, String configModuleName) throws LoginException {
        LoginContext login = new LoginContext(configModuleName, (Subject) null, getDefaultCredentialCallBack(), config);
        login.login();
        return login.getSubject();
    }

    private CallbackHandler getDefaultCredentialCallBack() {
        char[] pwd = this.sAtts.profile.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD) ? ((OpaqueString) this.sAtts.profile.get(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD)).getChars() : null;
        String principal = this.sAtts.profile.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER) ? this.sAtts.profile.getProperty(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER) : null;
        if (pwd == null) {
            return null;
        }
        return callbacks -> {
            for (Callback cb : callbacks) {
                if (cb instanceof PasswordCallback) {
                    ((PasswordCallback) cb).setPassword(pwd);
                } else if (principal != null && (cb instanceof NameCallback)) {
                    ((NameCallback) cb).setName(principal);
                }
            }
        };
    }

    private Configuration getDefaultJAASConfig() {
        return new Configuration() { // from class: oracle.net.ano.AuthenticationService.1
            public AppConfigurationEntry[] getAppConfigurationEntry(String moduleName) {
                HashMap<String, String> options = new HashMap<>();
                options.put("useTicketCache", "true");
                if (AuthenticationService.this.sAtts.profile.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER)) {
                    options.put("principal", AuthenticationService.this.sAtts.profile.getProperty(AnoServices.AUTHENTICATION_PROPERTY_KRB5_USER));
                }
                if (!AuthenticationService.this.sAtts.profile.containsKey(AnoServices.AUTHENTICATION_PROPERTY_KRB5_PWD)) {
                    options.put("doNotPrompt", "true");
                }
                String credentialCache = (String) AuthenticationService.this.sAtts.profile.get("oracle.net.kerberos5_cc_name");
                if (credentialCache != null && !credentialCache.isEmpty()) {
                    options.put("ticketCache", credentialCache);
                }
                return new AppConfigurationEntry[]{new AppConfigurationEntry("com.sun.security.auth.module.Krb5LoginModule", AppConfigurationEntry.LoginModuleControlFlag.REQUIRED, options)};
            }
        };
    }

    @Override // java.security.PrivilegedExceptionAction
    public Object run() throws Exception {
        GSSName serverName;
        GSSCredential userCreds;
        byte[] forwardableTicketRaw;
        try {
            GSSManager manager = GSSManager.getInstance();
            Oid krb5Mechanism = new Oid("1.2.840.113554.1.2.2");
            Oid krb5PrincipalNameType = new Oid("1.2.840.113554.*******");
            byte[] krbASN = krb5Mechanism.getDER();
            KerberosPrincipal userPrincipal4Kerberos = null;
            if (this.userGSSCredential == null) {
                Set principalsSet = this.jdbcUserSubject.getPrincipals();
                Iterator it = principalsSet.iterator();
                if (it.hasNext()) {
                    Principal princ = it.next();
                    if (princ instanceof KerberosPrincipal) {
                        userPrincipal4Kerberos = (KerberosPrincipal) princ;
                    }
                }
                if (userPrincipal4Kerberos == null) {
                    throw new NetException(NetException.INVALID_SERVICE, "Unable to find valid kerberos principal for authentication");
                }
            }
            String userN = userPrincipal4Kerberos != null ? userPrincipal4Kerberos.getName() : null;
            if (this.mapRealm != null) {
                serverName = manager.createName(this.servicePrincipal4Kerberos, krb5PrincipalNameType);
            } else {
                serverName = manager.createName(this.servicePrincipal4KerberosNTFormat, GSSName.NT_HOSTBASED_SERVICE);
            }
            if (this.userGSSCredential == null) {
                GSSName userName = manager.createName(userN, krb5PrincipalNameType);
                userCreds = manager.createCredential(userName, 0, krb5Mechanism, 1);
            } else {
                userCreds = this.userGSSCredential;
            }
            GSSContext context = manager.createContext(serverName, krb5Mechanism, userCreds, 0);
            boolean mutualAuthenticationRequested = true;
            String mutualAuthStr = (String) this.sAtts.profile.get("oracle.net.kerberos5_mutual_authentication");
            if (mutualAuthStr != "true") {
                mutualAuthenticationRequested = false;
            }
            context.requestMutualAuth(mutualAuthenticationRequested);
            context.requestConf(false);
            context.requestInteg(false);
            if (this.userGSSCredential == null) {
                context.requestCredDeleg(true);
            } else {
                context.requestCredDeleg(false);
            }
            byte[] token = context.initSecContext(new byte[0], 0, 0);
            byte[] apReq = new byte[token.length - 17];
            System.arraycopy(token, 17, apReq, 0, apReq.length);
            InetAddress thisIp = InetAddress.getLocalHost();
            byte[] clientIPRaw = thisIp.getAddress();
            int pktLength = 39 + clientIPRaw.length + 4 + apReq.length;
            this.sAtts.ano.sendANOHeader(pktLength, this.service, (short) 0);
            sendHeader(4);
            this.comm.sendUB2(2);
            this.comm.sendUB4(4L);
            this.comm.sendRaw(clientIPRaw);
            this.comm.sendRaw(apReq);
            this.comm.flush();
            this.sAtts.ano.receiveANOHeader();
            int[] hdr = Service.receiveHeader(this.comm);
            this.comm.receiveUB1();
            if (mutualAuthenticationRequested) {
                if (hdr[1] < 2) {
                    throw new NetException(NetException.INVALID_SERVICE, "Mutual authentication failed during Kerberos5 authentication");
                }
                byte[] token2 = this.comm.receiveRaw();
                byte[] gss_krb_ap_rep = new byte[krbASN.length + 2 + token2.length];
                System.arraycopy(krbASN, 0, gss_krb_ap_rep, 0, krbASN.length);
                gss_krb_ap_rep[krbASN.length] = 2;
                gss_krb_ap_rep[krbASN.length + 1] = 0;
                System.arraycopy(token2, 0, gss_krb_ap_rep, krbASN.length + 2, token2.length);
                byte[] lengthByte = getLength(gss_krb_ap_rep.length);
                byte[] gss_krb_ap_rep_der = new byte[1 + lengthByte.length + gss_krb_ap_rep.length];
                gss_krb_ap_rep_der[0] = 96;
                System.arraycopy(lengthByte, 0, gss_krb_ap_rep_der, 1, lengthByte.length);
                System.arraycopy(gss_krb_ap_rep, 0, gss_krb_ap_rep_der, lengthByte.length + 1, gss_krb_ap_rep.length);
                try {
                    context.initSecContext(gss_krb_ap_rep_der, 0, gss_krb_ap_rep_der.length);
                    if (!context.getMutualAuthState()) {
                        throw new NetException(NetException.INVALID_SERVICE, "Mutual authentication failed during Kerberos5 authentication");
                    }
                } catch (GSSException e) {
                    NetException nete = new NetException(NetException.INVALID_SERVICE, e.getMessage());
                    nete.initCause(e);
                    throw nete;
                }
            }
            if (!context.isEstablished()) {
                throw new NetException(NetException.INVALID_SERVICE, "Kerberos5 adaptor couldn't create context");
            }
            if (this.userGSSCredential == null) {
                forwardableTicketRaw = isInternalSunAPIAvailable ? getKRBCredForDelegation(context, apReq) : null;
            } else {
                forwardableTicketRaw = null;
            }
            if (forwardableTicketRaw == null) {
                forwardableTicketRaw = new byte[0];
            }
            int pktLength2 = 25 + forwardableTicketRaw.length;
            this.sAtts.ano.sendANOHeader(pktLength2, this.service, (short) 0);
            sendHeader(1);
            this.comm.sendRaw(forwardableTicketRaw);
            this.comm.flush();
            return null;
        } catch (GSSException e2) {
            NetException nete2 = new NetException(NetException.INVALID_SERVICE, e2.getMessage());
            nete2.initCause(e2);
            throw nete2;
        }
    }

    private final byte[] getKRBCredForDelegation(GSSContext context, byte[] apReqBytes) throws KrbCryptoException, KdcErrException, KrbApErrException, Asn1Exception, RealmException, IOException {
        byte[] temp;
        byte[] nonGSS_krb_cred = null;
        if (context.getCredDelegState() && this.jdbcUserSubject != null) {
            byte[] sessionKey = null;
            int sessionKeyType = -1;
            KerberosTicket ticket = getKerberosTicket();
            if (ticket != null) {
                sessionKey = ticket.getSessionKey().getEncoded();
                sessionKeyType = ticket.getSessionKeyType();
            }
            APReq sunApReq = new APReq(apReqBytes);
            EncryptionKey sunEncryptionKey = new EncryptionKey(sessionKeyType, sessionKey);
            byte[] authenticatorBytes2 = sunApReq.authenticator.decrypt(sunEncryptionKey, 11);
            byte[] authenticatorBytesReset = reset(sunApReq.authenticator, authenticatorBytes2, true);
            Authenticator authenticator = new Authenticator(authenticatorBytesReset);
            Checksum cksum = authenticator.getChecksum();
            byte[] cksumBytes = cksum.getBytes();
            if (cksumBytes.length >= 26) {
                int dlgLength = ((cksumBytes[27] & 255) << 8) + (cksumBytes[26] & 255);
                byte[] krb_cred = new byte[dlgLength];
                System.arraycopy(cksumBytes, 28, krb_cred, 0, dlgLength);
                KRBCred credMessg = new KRBCred(krb_cred);
                try {
                    temp = credMessg.encPart.decrypt(EncryptionKey.NULL_KEY, 14);
                } catch (Exception e) {
                    temp = credMessg.encPart.decrypt(sunEncryptionKey, 14);
                }
                byte[] plainText = reset(credMessg.encPart, temp, true);
                EncryptedData new_encPart = new EncryptedData(sunEncryptionKey, plainText, 14);
                KRBCred credMessgToSend = new KRBCred(credMessg.tickets, new_encPart);
                nonGSS_krb_cred = credMessgToSend.asn1Encode();
            }
        }
        return nonGSS_krb_cred;
    }

    private byte[] reset(EncryptedData ed, Object... arguments) {
        byte[] resBytes = null;
        if (resetMethod == null) {
            resetMethod = getResetMethod();
        }
        try {
            resBytes = resetMethod.getParameterTypes().length == 1 ? (byte[]) resetMethod.invoke(ed, arguments[0]) : (byte[]) resetMethod.invoke(ed, arguments);
        } catch (IllegalAccessException e) {
        } catch (InvocationTargetException e2) {
        }
        return resBytes;
    }

    private static Method getResetMethod() throws NoSuchMethodException, ClassNotFoundException, SecurityException {
        Method resetMethod2 = null;
        try {
            Class c = Class.forName("sun.security.krb5.EncryptedData");
            Class[] argTypes = {byte[].class, Boolean.TYPE};
            try {
                resetMethod2 = c.getDeclaredMethod("reset", argTypes);
            } catch (NoSuchMethodException e) {
                resetMethod2 = c.getDeclaredMethod("reset", argTypes[0]);
            }
        } catch (ClassNotFoundException e2) {
        } catch (NoSuchMethodException e3) {
        }
        return resetMethod2;
    }

    @Override // oracle.net.ano.Service
    void validateResponse() throws IOException {
        if (this.authenticationActivated) {
        }
    }

    private byte[] getLength(int len) throws IOException {
        byte[] length;
        if (len < 128) {
            length = new byte[]{(byte) len};
        } else if (len < 256) {
            length = new byte[]{-127, (byte) len};
        } else if (len < 65536) {
            length = new byte[]{-126, (byte) (len >> 8), (byte) len};
        } else if (len < 16777216) {
            length = new byte[]{-125, (byte) (len >> 16), (byte) (len >> 8), (byte) len};
        } else {
            length = new byte[]{-124, (byte) (len >> 24), (byte) (len >> 16), (byte) (len >> 8), (byte) len};
        }
        return length;
    }

    public static final byte[] obfuscatePasswordForRadius(byte[] paddedPwd) {
        return Radius.obfuscatePassword(paddedPwd);
    }

    static String dump(byte[] buffer, int offset, int len) {
        String strTemp;
        Charset asciiCs = Charset.forName("ASCII");
        int bytes = 0;
        StringBuffer sb = new StringBuffer();
        sb.append("Buffer dump\n");
        sb.append("buffer.length=" + buffer.length + "\n");
        sb.append("offset       =" + offset + "\n");
        sb.append("len          =" + len + "\n");
        ByteBuffer buff = ByteBuffer.allocate(8);
        buff.position(0);
        buff.limit(buff.capacity());
        for (int i = offset; i < len; i += 8) {
            int j = 0;
            while (j < 8 && bytes < len - 1) {
                bytes = i + j;
                String hexString = Integer.toHexString(buffer[bytes] & 255);
                while (true) {
                    strTemp = hexString;
                    if (strTemp.length() >= 2) {
                        break;
                    }
                    hexString = "0" + strTemp;
                }
                sb.append(strTemp);
                sb.append(" ");
                if (buffer[bytes] > 33 && buffer[bytes] < Byte.MAX_VALUE) {
                    buff.put(buffer[bytes]);
                } else {
                    buff.put((byte) 46);
                }
                j++;
            }
            while (j <= 7) {
                sb.append("   ");
                j++;
            }
            sb.append("|");
            buff.rewind();
            CharBuffer cb = asciiCs.decode(buff);
            buff.rewind();
            sb.append(" " + cb.toString() + " |\n");
        }
        sb.append("finish dump\n");
        return sb.toString();
    }
}
