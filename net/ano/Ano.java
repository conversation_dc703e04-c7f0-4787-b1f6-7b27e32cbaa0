package oracle.net.ano;

import java.io.IOException;
import oracle.net.aso.DataIntegrityAlgorithm;
import oracle.net.aso.EncryptionAlgorithm;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;
import org.ietf.jgss.GSSCredential;

/* loaded from: ojdbc8.jar:oracle/net/ano/Ano.class */
public class Ano implements SQLnetDef {
    static final boolean VERIFYING = false;
    protected SessionAtts sAtts;
    protected AnoComm anoComm;
    protected byte[] clientPK;
    protected byte[] iv;
    protected byte[] skey;
    protected EncryptionAlgorithm encryptionAlg;
    protected DataIntegrityAlgorithm dataIntegrityAlg;
    private Service[] listOfServices;
    private byte[] authSessionKey;
    private boolean renewKey;
    protected boolean cryptoNeeded = false;
    private int naFlags = 1;
    private boolean isTwoFactorAuthDone = false;

    public void init(SessionAtts sAtts, boolean useNio) throws NetException {
        this.sAtts = sAtts;
        this.sAtts.ano = this;
        this.listOfServices = new Service[Service.SERVICES_CLASSNAME.length];
        this.anoComm = new AnoCommNIO(sAtts);
        for (int i = 1; i < Service.SERVICES_CLASSNAME.length; i++) {
            try {
                Service tmpService = (Service) Class.forName("oracle.net.ano." + Service.SERVICES_CLASSNAME[i]).newInstance();
                this.naFlags |= tmpService.init(sAtts);
                this.listOfServices[tmpService.service] = tmpService;
            } catch (Exception e) {
                throw new NetException(NetException.SERVICE_CLASSES_NOT_INSTALLED);
            }
        }
        if ((this.naFlags & 16) > 0 && (this.naFlags & 8) > 0) {
            this.naFlags &= -17;
        }
    }

    public void negotiation(boolean redirectedConnection, boolean useNio, GSSCredential gssCredential) throws IOException {
        int bytesNeededByServices = 0;
        for (int i = 1; i < 5; i++) {
            bytesNeededByServices += this.listOfServices[i].howManyBytesNeeded();
        }
        int naPktLength = 13 + bytesNeededByServices;
        if (this.sAtts.poolEnabled && redirectedConnection) {
            naPktLength += 16;
        }
        sendANOHeader(naPktLength, this.listOfServices.length - 1, (short) 0);
        this.listOfServices[4].sendServiceData();
        this.listOfServices[1].sendServiceData();
        this.listOfServices[2].sendServiceData();
        this.listOfServices[3].sendServiceData();
        this.anoComm.flush();
        int[] anoHeader = receiveANOHeader();
        for (int i2 = 0; i2 < anoHeader[2]; i2++) {
            int[] serviceHeader = Service.receiveHeader(this.anoComm);
            if (serviceHeader[2] != 0) {
                throw new NetException(serviceHeader[2]);
            }
            this.listOfServices[serviceHeader[0]].receiveSelection(serviceHeader[1]);
        }
        for (int i3 = 1; i3 < 5; i3++) {
            this.listOfServices[i3].activateAlgorithm();
        }
        int pktLength = 0;
        int nbOfServices = 0;
        if (this.clientPK != null) {
            pktLength = 0 + 12 + this.clientPK.length;
            nbOfServices = 0 + 1;
        }
        int authPktLength = ((AuthenticationService) this.listOfServices[1]).bytesNeededForActivationPhase1();
        if (authPktLength > 0) {
            pktLength += authPktLength;
            nbOfServices++;
        }
        if (pktLength > 0) {
            sendANOHeader(pktLength + 13, nbOfServices, (short) 0);
            if (this.clientPK != null) {
                this.listOfServices[3].sendHeader(1);
                this.anoComm.sendRaw(this.clientPK);
            }
            if (authPktLength > 0) {
                ((AuthenticationService) this.listOfServices[1]).activateAuthenticatorPhase1();
            }
            this.anoComm.flush();
            ((AuthenticationService) this.listOfServices[1]).activateAuthenticatorPhase2(gssCredential);
        }
        this.cryptoNeeded = this.listOfServices[2].isActive() || this.listOfServices[3].isActive();
        if (this.cryptoNeeded) {
            this.sAtts.turnEncryptionOn(new CryptoNIONSDataChannel(this.sAtts));
        }
        if (useNio) {
            this.sAtts.payloadDataBufferForRead.position(this.sAtts.payloadDataBufferForRead.limit());
        }
    }

    public String getEncryptionProvider() {
        if (this.encryptionAlg != null) {
            return this.encryptionAlg.getProviderName();
        }
        return null;
    }

    public String getChecksumProvider() {
        if (this.dataIntegrityAlg != null) {
            return this.dataIntegrityAlg.getProviderName();
        }
        return null;
    }

    public byte[] getExternalAuthSessionKey() {
        if (this.listOfServices[1] != null && this.listOfServices[1].isActive()) {
            return ((AuthenticationService) this.listOfServices[1]).getSessionKey();
        }
        return null;
    }

    public int getNAFlags() {
        return this.naFlags;
    }

    public void setAuthSessionKey(byte[] authSessionkey) {
        this.authSessionKey = authSessionkey;
    }

    public byte[] getAuthSessionKey() {
        return this.authSessionKey;
    }

    public EncryptionAlgorithm getEncryptionAlg() {
        return this.encryptionAlg;
    }

    public DataIntegrityAlgorithm getDataIntegrityAlg() {
        return this.dataIntegrityAlg;
    }

    public String getEncryptionName() {
        if (this.listOfServices == null || this.listOfServices.length <= 2) {
            return "";
        }
        return EncryptionService.ENCRYPTION_JAVA_ANO_ID[this.listOfServices[2].algID];
    }

    public String getDataIntegrityName() {
        if (this.listOfServices == null || this.listOfServices.length <= 3) {
            return "";
        }
        return DataIntegrityService.DATAINTEGRITY_JAVA_ANO_ID[this.listOfServices[3].algID];
    }

    public String getAuthenticationAdaptorName() {
        if (this.listOfServices == null || this.listOfServices.length <= 1) {
            return "";
        }
        return AuthenticationService.AUTH_JAVA_ANO_ID[this.listOfServices[1].algID];
    }

    public void setRenewKey(boolean renew) {
        this.renewKey = renew;
    }

    public boolean getRenewKey() {
        return this.renewKey;
    }

    protected void setClientPK(byte[] clientPK) {
        this.clientPK = clientPK;
    }

    protected void setInitializationVector(byte[] iv) {
        this.iv = iv;
    }

    protected void setSessionKey(byte[] skey) {
        this.skey = skey;
    }

    protected byte[] getInitializationVector() {
        return this.iv;
    }

    protected byte[] getSessionKey() {
        return this.skey;
    }

    void setTwoFactorAuthenticationComplete(boolean status) {
        this.isTwoFactorAuthDone = status;
    }

    public boolean isTwoFactorAuthenticationDone() {
        return this.isTwoFactorAuthDone;
    }

    protected void sendANOHeader(int pktLength, int numServices, short errorFlags) throws IOException {
        this.anoComm.writeUB4(-559038737L);
        this.anoComm.writeUB2(pktLength);
        this.anoComm.writeVersion();
        this.anoComm.writeUB2(numServices);
        this.anoComm.writeUB1(errorFlags);
    }

    public void checkForAnoNegotiationFailure() throws IOException {
        int payloadBufferPosition = this.sAtts.payloadDataBufferForRead.position();
        try {
            this.sAtts.payloadDataBufferForRead.rewind();
            try {
                int[] anoHeader = receiveANOHeader();
                for (int i = 0; i < anoHeader[2]; i++) {
                    int[] serviceHeader = Service.receiveHeader(this.anoComm);
                    if (serviceHeader[2] != 0) {
                        throw new NetException(serviceHeader[2]);
                    }
                }
                this.sAtts.payloadDataBufferForRead.position(payloadBufferPosition);
            } catch (NetException ne) {
                if (ne.getErrorNumber() != 18902) {
                    throw ne;
                }
                this.sAtts.payloadDataBufferForRead.position(payloadBufferPosition);
            }
        } catch (Throwable th) {
            this.sAtts.payloadDataBufferForRead.position(payloadBufferPosition);
            throw th;
        }
    }

    int[] receiveANOHeader() throws IOException {
        long magic = this.anoComm.readUB4();
        if (magic != -559038737) {
            throw new NetException(NetException.WRONG_MAGIC_NUMBER);
        }
        int naPktLengthL = this.anoComm.readUB2();
        int versionL = (int) this.anoComm.readUB4();
        int serverServicesL = this.anoComm.readUB2();
        int errorFlagsL = this.anoComm.readUB1();
        int[] ret = {naPktLengthL, versionL, serverServicesL, errorFlagsL};
        return ret;
    }

    byte foldinKey() throws IOException {
        byte foldinByte = 0;
        if (this.authSessionKey != null) {
            if (this.sAtts.profile.useWeakCrypto()) {
                int xorLength = Math.min(this.skey.length, this.authSessionKey.length);
                for (int i = 0; i < xorLength; i++) {
                    byte[] bArr = this.skey;
                    int i2 = i;
                    bArr[i2] = (byte) (bArr[i2] ^ this.authSessionKey[i]);
                }
            } else {
                int xorLength2 = Math.min(32, this.authSessionKey.length);
                for (int i3 = 0; i3 < xorLength2; i3++) {
                    byte[] bArr2 = this.skey;
                    int i4 = i3;
                    bArr2[i4] = (byte) (bArr2[i4] ^ this.authSessionKey[i3]);
                }
                for (int i5 = 0; i5 < xorLength2; i5++) {
                    byte[] bArr3 = this.skey;
                    int i6 = 32 + i5;
                    bArr3[i6] = (byte) (bArr3[i6] ^ this.authSessionKey[i5]);
                }
            }
            if (this.encryptionAlg != null) {
                this.encryptionAlg.setSessionKey(this.skey, this.iv);
            }
            if (this.dataIntegrityAlg != null) {
                this.dataIntegrityAlg.takeSessionKey(this.skey, this.iv);
            }
            foldinByte = 1;
        }
        return foldinByte;
    }
}
