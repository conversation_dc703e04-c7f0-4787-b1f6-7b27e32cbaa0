package oracle.net.ano;

import java.io.IOException;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/net/ano/SupervisorService.class */
public class SupervisorService extends Service implements SQLnetDef {
    static final int NAS_OK = 31;
    static final int NAS_CLIENT_SERVICES_UNAVAILABLE = 47;
    static final int NAS_SERVER_SERVICES_UNAVAILABLE = 63;
    static final int NAS_NO_SERVICES_AVAILABLE = 79;
    static final int NAS_SERVICE_REQUIRED = 95;
    static final int NAS_REQUIRED_SERVICE_UNAVAILABL = 111;
    static final int NAS_SERVICE_UNAVAILABLE = 127;
    private byte[] cid;
    private int[] servicesArray;
    private int[] serverServices;
    private int servicesValidated;
    private int servicesWanted;

    @Override // oracle.net.ano.Service
    int init(SessionAtts sAtts) throws NetException {
        super.init(sAtts);
        this.service = 4;
        this.cid = createCID();
        this.servicesValidated = 0;
        this.servicesWanted = 2;
        this.servicesArray = new int[4];
        this.servicesArray[0] = 4;
        this.servicesArray[1] = 1;
        this.servicesArray[2] = 2;
        this.servicesArray[3] = 3;
        return 1;
    }

    byte[] createCID() {
        byte[] cid = new byte[8];
        for (int i = 0; i < cid.length; i++) {
            cid[i] = 9;
        }
        return cid;
    }

    @Override // oracle.net.ano.Service
    void sendServiceData() throws IOException {
        sendHeader(3);
        this.comm.sendVersion();
        this.comm.sendRaw(this.cid);
        this.comm.sendUB2Array(this.servicesArray);
    }

    @Override // oracle.net.ano.Service
    int getServiceDataLength() {
        return 12 + this.cid.length + 4 + 10 + (this.servicesArray.length * 2);
    }

    @Override // oracle.net.ano.Service
    void receiveServiceData(int numSubPackets) throws IOException {
        this.version = this.comm.receiveVersion();
        int statusReceived = this.comm.receiveStatus();
        if (statusReceived != 31) {
            throw new NetException(NetException.SUPERVISOR_STATUS_FAILURE);
        }
        this.serverServices = this.comm.receiveUB2Array();
    }

    @Override // oracle.net.ano.Service
    void validateResponse() throws IOException {
        for (int i = 0; i < this.serverServices.length; i++) {
            int j = 0;
            while (true) {
                if (j >= this.servicesArray.length) {
                    break;
                }
                if (this.serverServices[i] == this.servicesArray[j]) {
                    this.servicesValidated++;
                    break;
                }
                j++;
            }
            if (j == this.servicesArray.length) {
                throw new NetException(NetException.INVALID_SERVICES_FROM_SERVER);
            }
        }
        if (this.servicesValidated != this.servicesWanted) {
            throw new NetException(NetException.INCOMPLETE_SERVICES_FROM_SERVER);
        }
    }
}
